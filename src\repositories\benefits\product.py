# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/product.py
# created: 2025-03-23 17:07:15
# updated: 2025-04-14 17:07:35

from typing import Optional, Sequence, Tuple

from pydantic import BaseModel, Field

from src.databases.models.benefits import BenefitsProduct, BenefitsProductSku, BenefitsSku
from src.domains.benefits.dto import BenefitsProductCreateDTO, BenefitsProductUpdateDTO


class ProductFilters(BaseModel):
    name: Optional[str] = Field(None, description="产品名称")
    code: Optional[str] = Field(None, description="产品编码")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页条数")


class ProductRepository:

    @classmethod
    async def delete_product_sku_relations(
        cls, product: BenefitsProduct, sku: BenefitsSku
    ) -> Optional[BenefitsProductSku]:
        product_sku = await BenefitsProductSku.get_or_none(product=product, sku=sku)
        if not product_sku:
            return None
        await product_sku.delete()
        return product_sku

    @classmethod
    async def create_product_sku_relations(
        cls, product: BenefitsProduct, sku: BenefitsSku, count: int
    ) -> BenefitsProductSku:
        product_sku, bool = await BenefitsProductSku.get_or_create(product=product, sku=sku)
        product_sku.count = count
        await product_sku.save()
        return product_sku

    @classmethod
    async def create_product(cls, product: BenefitsProductCreateDTO) -> BenefitsProduct:
        return await BenefitsProduct.create(**product.model_dump())

    @classmethod
    async def update_product(cls, product_code: str, update_fields: BenefitsProductUpdateDTO) -> BenefitsProduct | None:
        product = await BenefitsProduct.get_or_none(code=product_code)
        if not product:
            return None
        for field, value in update_fields.model_dump(exclude_none=True).items():
            if value is not None:
                setattr(product, field, value)
        await product.save()
        return product

    @classmethod
    async def get_by_code(cls, code: str) -> Optional[BenefitsProduct]:
        return await BenefitsProduct.get_or_none(code=code).prefetch_related("skus")

    @classmethod
    async def filter_products(cls, filters: ProductFilters) -> Tuple[int, Sequence[BenefitsProduct]]:
        query = BenefitsProduct.all()
        if filters.name:
            query = query.filter(name__icontains=filters.name)
        if filters.code:
            query = query.filter(code=filters.code)
        total = await query.count()
        products = await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).all()
        return total, products

    @classmethod
    async def get_relations_by_product_code(cls, product_code: str) -> Sequence[Tuple[int, BenefitsSku]]:
        product_skus = await BenefitsProductSku.filter(product__code=product_code).prefetch_related("sku").all()
        return [(sku.count, sku.sku) for sku in product_skus]
