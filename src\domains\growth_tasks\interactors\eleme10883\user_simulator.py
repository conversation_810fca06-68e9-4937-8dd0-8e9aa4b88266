# encoding: utf-8
# src/domains/growth_tasks/interactors/eleme10883/user_simulator.py
# created: 2025-08-08 01:30:15

import asyncio
import random
from typing import TYPE_CHECKING, List

from loguru import logger
from playwright.async_api import ElementHandle

if TYPE_CHECKING:
    from src.infrastructures.browsers import PageSession


class UserSimulator:
    """用户行为模拟器"""

    def __init__(self, session: "PageSession"):
        self.session = session
        if not session.page:
            raise ValueError("PageSession must have a valid page")

    async def simulate_browsing(self) -> None:
        """模拟用户浏览页面的行为 - 真实手势滑动"""
        if not self.session.page:
            return

        # 获取页面高度和视口信息
        page_height = await self.session.page.evaluate("document.body.scrollHeight")
        viewport_height = await self.session.page.evaluate("window.innerHeight")
        viewport_width = await self.session.page.evaluate("window.innerWidth")

        # 随机滑动1-3次
        scroll_times = random.randint(1, 3)

        for _ in range(scroll_times):
            # 模拟真实的触摸滑动手势
            result = await self.perform_realistic_swipe(viewport_width, viewport_height)
            if not result:
                break

            # 模拟用户阅读停留时间（真实用户会停下来查看内容）
            reading_time = random.uniform(1.0, 3.0)
            await asyncio.sleep(reading_time)

            # 检查是否已经接近底部
            current_scroll = await self.session.page.evaluate("window.scrollY")
            if current_scroll + viewport_height >= page_height - 100:
                break

        # 最后的思考停留时间
        think_time = random.uniform(0.8, 1.8)
        await asyncio.sleep(think_time)

    async def perform_realistic_swipe(self, viewport_width: int, viewport_height: int) -> bool:
        """执行真实的触摸滑动手势"""
        if not self.session.page:
            return False

        # 滑动起点：在屏幕中央偏下的位置
        start_x = viewport_width // 2 + random.randint(-50, 50)
        start_y = viewport_height * 0.7 + random.randint(-30, 30)

        # 滑动距离：模拟真实的手指滑动距离
        swipe_distance = random.randint(150, 400)

        # 滑动终点
        end_x = start_x + random.randint(-20, 20)  # 水平方向略有偏移
        end_y = start_y - swipe_distance  # 向上滑动

        # 确保终点在有效范围内
        end_y = max(50, end_y)

        try:
            # 首先全面禁用文本选择
            await self.session.page.evaluate(
                """
                // 全面禁用文本选择和拖拽
                document.documentElement.style.webkitUserSelect = 'none';
                document.documentElement.style.userSelect = 'none';
                document.documentElement.style.webkitTouchCallout = 'none';
                document.body.style.webkitUserSelect = 'none';
                document.body.style.userSelect = 'none';
                document.body.style.webkitTouchCallout = 'none';

                // 禁用拖拽选择
                document.ondragstart = function() { return false; };
                document.onselectstart = function() { return false; };
                document.oncontextmenu = function() { return false; };
            """
            )

            # 使用简单有效的滚动操作代替复杂的触摸事件
            scroll_result = await self.session.page.evaluate(
                """
                async (params) => {
                    const { startX, startY, endX, endY } = params;

                    // 计算滚动距离
                    const scrollDistance = startY - endY;

                    // 获取当前滚动位置
                    const currentScroll = window.scrollY;

                    // 分步滚动以模拟真实滑动
                    const steps = 8;
                    const stepDistance = scrollDistance / steps;

                    for (let i = 1; i <= steps; i++) {
                        const newScroll = currentScroll + (stepDistance * i);
                        window.scrollTo({
                            top: newScroll,
                            behavior: 'auto'  // 使用立即滚动而不是平滑滚动
                        });
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    return true;
                }
                """,
                {"startX": start_x, "startY": start_y, "endX": end_x, "endY": end_y},
            )

            if scroll_result:
                # 滚动后的短暂停顿
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            else:
                # 滚动浏览操作执行失败，尝试备选方案
                # 备选方案：使用传统的鼠标滑动
                await self.session.page.mouse.move(start_x, start_y)
                await self.session.page.mouse.down()

                steps = random.randint(8, 15)
                for step in range(1, steps + 1):
                    progress = step / steps
                    current_x = self._bezier_interpolate(start_x, end_x, progress)
                    current_y = self._bezier_interpolate(start_y, end_y, progress)

                    await self.session.page.mouse.move(current_x, current_y)
                    await asyncio.sleep(random.uniform(0.01, 0.03))

                await self.session.page.mouse.up()
                await asyncio.sleep(random.uniform(0.2, 0.5))
                return True

        except Exception:
            # 滑动手势失败
            return False
        finally:
            # 恢复文本选择功能
            try:
                await self.session.page.evaluate(
                    """
                    // 恢复文本选择功能
                    document.documentElement.style.webkitUserSelect = '';
                    document.documentElement.style.userSelect = '';
                    document.documentElement.style.webkitTouchCallout = '';
                    document.body.style.webkitUserSelect = '';
                    document.body.style.userSelect = '';
                    document.body.style.webkitTouchCallout = '';

                    document.ondragstart = null;
                    document.onselectstart = null;
                    document.oncontextmenu = null;
                """
                )
            except Exception:
                pass

    def _bezier_interpolate(self, start: float, end: float, t: float) -> float:
        """使用二次贝塞尔曲线插值，让滑动轨迹更自然"""
        # 控制点在中间位置附近
        control = (start + end) / 2 + random.uniform(-10, 10)

        # 二次贝塞尔曲线公式
        return (1 - t) ** 2 * start + 2 * (1 - t) * t * control + t**2 * end

    async def scroll_to_top(self) -> None:
        """滚动回到页面顶部"""
        if not self.session.page:
            return

        # 平滑滚动到顶部
        await self.session.page.evaluate(
            """
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        """
        )

        # 等待滚动动画完成
        wait_time = random.uniform(1.0, 2.0)
        await asyncio.sleep(wait_time)

        # 确保已经到达顶部
        current_scroll = await self.session.page.evaluate("window.scrollY")

        if current_scroll > 10:  # 允许一点误差
            await self.session.page.evaluate("window.scrollTo(0, 0);")
            await asyncio.sleep(0.5)

    async def perform_realistic_touch(self, element: ElementHandle) -> bool:
        """执行真实的H5触摸操作 - 针对饿了么优化版本"""
        if not self.session.page:
            return False

        try:
            # 获取按钮位置和大小
            button_box = await element.bounding_box()
            if not button_box:
                # 无法获取按钮位置信息
                return False

            # 计算触摸点，选择按钮中心位置并添加微小偏移
            touch_x = button_box["x"] + button_box["width"] * 0.5 + random.uniform(-3, 3)
            touch_y = button_box["y"] + button_box["height"] * 0.5 + random.uniform(-3, 3)

            # 方案1：真实的H5触摸事件序列 - 最优先
            try:
                touch_result = await self.session.page.evaluate(
                    """
                    async (params) => {
                        const { element, touchX, touchY } = params;

                        // 确保元素可见和可交互
                        element.scrollIntoView({ behavior: 'auto', block: 'center' });
                        element.style.pointerEvents = 'auto';
                        element.style.visibility = 'visible';

                        // 移除可能的覆盖层
                        const overlays = document.querySelectorAll('.overlay, .modal, .popup, .mask, [style*="z-index: 9999"]');
                        overlays.forEach(overlay => {
                            if (overlay && overlay !== element && !element.contains(overlay)) {
                                overlay.style.display = 'none';
                            }
                        });

                        // 等待短暂时间确保覆盖层移除
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 创建真实的触摸事件
                        const touch = new Touch({
                            identifier: Date.now(),
                            target: element,
                            clientX: touchX,
                            clientY: touchY,
                            radiusX: 2.5,
                            radiusY: 2.5,
                            rotationAngle: 0,
                            force: 0.5
                        });

                        // 触摸开始事件
                        const touchStartEvent = new TouchEvent('touchstart', {
                            cancelable: true,
                            bubbles: true,
                            touches: [touch],
                            targetTouches: [touch],
                            changedTouches: [touch]
                        });

                        // 触摸结束事件
                        const touchEndEvent = new TouchEvent('touchend', {
                            cancelable: true,
                            bubbles: true,
                            touches: [],
                            targetTouches: [],
                            changedTouches: [touch]
                        });

                        // 点击事件
                        const clickEvent = new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            clientX: touchX,
                            clientY: touchY,
                            button: 0
                        });

                        // 执行触摸序列
                        let success = false;

                        try {
                            // 1. 触摸开始
                            element.dispatchEvent(touchStartEvent);
                            await new Promise(resolve => setTimeout(resolve, 50));

                            // 2. 触摸结束
                            element.dispatchEvent(touchEndEvent);
                            await new Promise(resolve => setTimeout(resolve, 50));

                            // 3. 点击事件
                            element.dispatchEvent(clickEvent);
                            await new Promise(resolve => setTimeout(resolve, 100));

                            // 4. 直接调用click方法作为备选
                            element.click();

                            success = true;
                        } catch (e) {
                            console.log('Touch event error:', e);
                        }

                        return success;
                    }
                    """,
                    {"element": element, "touchX": touch_x, "touchY": touch_y},
                )

                if touch_result:
                    await asyncio.sleep(random.uniform(0.3, 0.8))  # 等待响应
                    return True

            except Exception:
                # H5触摸事件失败
                pass

            # 方案2：Playwright原生tap方法
            try:
                await element.tap(force=True, timeout=3000)
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            except Exception:
                # Playwright tap失败
                pass

            # 方案3：坐标点击
            try:
                await self.session.page.mouse.click(touch_x, touch_y, button="left", delay=100)
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            except Exception:
                # 坐标点击失败
                pass

            # 所有触摸操作方案都失败了
            return False

        except Exception:
            # 触摸操作执行失败
            return False

    async def visit_shop_detail(self) -> None:
        """访问店铺详情页面 - 优化版本"""
        if not self.session.page:
            return

        try:
            # 查找店铺名称元素
            shop_name_elements: List[ElementHandle] = []

            # 优先查找.shop-name_v2元素
            shop_name_v2 = await self.session.page.query_selector_all(".shop-name_v2")
            if shop_name_v2:
                shop_name_elements.extend(shop_name_v2)

            # 查找.es-shop-name元素
            es_shop_name = await self.session.page.query_selector_all(".es-shop-name")
            if es_shop_name:
                shop_name_elements.extend(es_shop_name)

            if not shop_name_elements:
                return

            # 随机选择一个店铺名称元素
            selected_name_element = random.choice(shop_name_elements)

            # 记录初始URL
            initial_url = self.session.page.url

            # 使用触摸事件点击店铺名称元素
            try:
                # 检查元素是否可见和可点击
                if not await selected_name_element.is_visible():
                    return

                # 滚动到元素位置（确保完全可见）
                await selected_name_element.scroll_into_view_if_needed()
                wait_time = random.randint(300, 800)
                await self.session.page.wait_for_timeout(wait_time)

                # 执行真实的触摸操作
                touch_result = await self.perform_realistic_touch(selected_name_element)
                if not touch_result:
                    return

                # 等待触摸响应
                response_wait = random.randint(800, 1500)
                await self.session.page.wait_for_timeout(response_wait)

                # 优化：使用asyncio.wait_for添加超时保护，避免死循环
                try:
                    result = await asyncio.wait_for(self._wait_for_shop_page_change(), timeout=10.0)

                    if result and random.random() < 0.2:
                        menu_items = await self._wait_for_menu_item()

                        if menu_items:
                            selected_menu_item = random.choice(menu_items)

                            await selected_menu_item.scroll_into_view_if_needed()
                            wait_time = random.randint(300, 800)

                            await self.session.page.wait_for_timeout(wait_time)

                            touch_result = await self.perform_realistic_touch(selected_menu_item)
                            if not touch_result:
                                return

                            response_wait = random.randint(1500, 2000)
                            await self.session.page.wait_for_timeout(response_wait)
                        else:
                            return
                except asyncio.TimeoutError:
                    # 超时后尝试备选方案
                    await self._try_alternative_shop_click(selected_name_element, initial_url)

            except Exception as e:
                # 记录具体异常信息而不是静默吞掉
                logger.debug(f"店铺访问失败: {e}")
                # 尝试备选方案
                await self._try_alternative_shop_click(selected_name_element, initial_url)

        except Exception as e:
            logger.debug(f"店铺详情访问异常: {e}")

    async def _wait_for_shop_page_change(self) -> bool:
        """等待店铺页面变化 - 优化版本，避免死循环"""
        if not self.session.page:
            return False

        max_attempts = 10  # 最多检查10次，每次1秒

        for attempt in range(max_attempts):
            # 检查是否出现shop__detail元素
            shop_detail_elements = await self.session.page.query_selector_all(".menuItem")
            shop_detail_exists = len(shop_detail_elements) > 0

            if shop_detail_exists:
                return True

            # 如果不是最后一次尝试，等待1秒后继续
            if attempt < max_attempts - 1:
                await asyncio.sleep(1.0)  # 使用asyncio.sleep而不是page.wait_for_timeout

        # 如果所有尝试都失败，不抛出异常，直接返回
        logger.debug("店铺页面变化检测超时，跳过")
        return False

    async def _wait_for_menu_item(self) -> List[ElementHandle]:
        """等待菜单项 - 优化版本，避免死循环"""
        if not self.session.page:
            return []

        max_attempts = 10  # 最多检查10次，每次1秒

        for attempt in range(max_attempts):
            # 检查是否出现shop__detail元素
            menu_items = await self.session.page.query_selector_all(".menuItem")
            shop_detail_exists = len(menu_items) > 0

            if shop_detail_exists:
                return menu_items

            # 如果不是最后一次尝试，等待1秒后继续
            if attempt < max_attempts - 1:
                await asyncio.sleep(1.0)  # 使用asyncio.sleep而不是page.wait_for_timeout

        return []

    async def _try_alternative_shop_click(self, selected_name_element: ElementHandle, initial_url: str) -> None:
        """尝试备选的店铺点击方案"""
        if not self.session.page:
            return

        try:
            # 备选方案：使用JavaScript点击
            await selected_name_element.evaluate("el => el.click()")

            # 等待加载
            wait_time = random.randint(800, 1500)
            await asyncio.sleep(wait_time / 1000)  # 转换为秒

            # 简单检查URL是否变化，不进入循环
            current_url = self.session.page.url
            if current_url != initial_url:
                logger.debug("备选方案成功：URL已变化")
            else:
                logger.debug("备选方案：URL未变化，跳过")

        except Exception as e:
            logger.debug(f"备选点击方案失败: {e}")
