# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/delivery/schemas.py
# created: 2025-02-06 12:34:37
# updated: 2025-05-26 11:02:59

from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field

from src.databases.models.delivery import DeliveryPageTypeEnum
from src.domains.delivery.dto import DeliveryPageDTO, StatisticsDTO, UnionOrderDTO
from src.infrastructures.fastapi.response import BaseResponse


class ElemePagesUrlData(BaseModel):
    alipay_link: Optional[str] = Field(None, description="支付宝链接")
    h5_link: Optional[str] = Field(None, description="H5链接")
    h5_short_link: Optional[str] = Field(None, description="H5短链接")
    taobao_link: Optional[str] = Field(None, description="淘宝链接")
    wechat_promotion: Optional[Any] = Field(None, description="微信推广链接")
    wechat_link: Optional[Any] = Field(None, description="微信链接")


class DeliveryPageListData(BaseModel):
    total: int
    data: list[DeliveryPageDTO]  # type: ignore


class DeliveryOrderParams(BaseModel):
    page: Optional[int] = Field(default=1, description="页码")
    page_size: Optional[int] = Field(default=10, description="每页条数")
    start_time: Optional[datetime] = Field(default=datetime.now() - timedelta(days=2), description="开始时间")
    end_time: Optional[datetime] = Field(default=datetime.now(), description="结束时间")


class DailyTaskAggregatedParams(BaseModel):
    date: str = Field(..., description="日期")


class DailyTaskRecoveryStatsParams(BaseModel):
    date: str = Field(..., description="日期")


class DailyTaskStatusByDateRangeParams(BaseModel):
    start_date: str = Field(..., description="开始日期")
    end_date: str = Field(..., description="结束日期")


class DeliveryPagePayload(BaseModel):
    type: DeliveryPageTypeEnum = Field(..., description="页面类型")
    code: Optional[str] = Field("", description="页面编码")
    name: str = Field(..., description="页面名称")
    description: Optional[str] = Field("", description="页面描述")
    comment: Optional[str] = Field("", description="页面备注")
    union_active_id: str = Field(..., description="联盟活动ID")
    union_zone_pid: str = Field(..., description="联盟资源位PID")
    url: Optional[str] = Field("", description="页面链接")
    custom_behavior: Optional[str] = Field("", description="取链行为")


class DeliveryOrderListData(BaseModel):
    total: int
    data: list[UnionOrderDTO]  # type: ignore


DeliveryPageListResponse = BaseResponse[DeliveryPageListData]
DeliveryPageResponse = BaseResponse[DeliveryPageDTO]  # type: ignore

StatisticsResponse = BaseResponse[Dict[str, StatisticsDTO]]
OrderListResponse = BaseResponse[DeliveryOrderListData]
ElemePagesUrlResponse = BaseResponse[ElemePagesUrlData]


class DeliveryPageUrlPayload(BaseModel):
    page_code: str = Field(..., description="页面编码")
    mobile: str = Field(..., description="手机号")
