# encoding: utf-8
# src/interfaces/growth_hacker/tasks/common.py
# created: 2025-08-09 07:38:34

import asyncio
from datetime import datetime

import click
from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.applications.growth_hacker.services.build_task import BuildTaskService
from src.infrastructures.rabbitmq import RabbitMQProducer

from ..container import Container, lifespan
from ..schemas import TaskMessage

cities = [
    "上海",
    "杭州",
    "北京",
    "广州",
    "深圳",
    "武汉",
    "苏州",
    "成都",
    "营口",
    "南京",
    "宁波",
    "温州",
    "重庆",
    "合肥",
    "长沙",
    "天津",
    "郑州",
    "福州",
    "厦门",
    "西安",
    "嘉兴",
    "东莞",
    "无锡",
    "沈阳",
    "哈尔滨",
    "青岛",
    "泉州",
    "金华",
    "南昌",
    "佛山",
    "绍兴",
    "济南",
    "台州",
    "常州",
    "扬州",
    "湖州",
    "汕头",
    "惠州",
    "南通",
    "南宁",
    "石家庄",
    "大连",
    "潍坊",
    "盐城",
    "长春",
    "漳州",
    "徐州",
    "镇江",
    "昆明",
    "中山",
    "丽水",
    "烟台",
    "泰州",
    "芜湖",
    "揭阳",
    "舟山",
    "保定",
    "临沂",
    "绵阳",
    "珠海",
    "赣州",
    "江门",
    "宁德",
    "太原",
    "海口",
    "三亚",
    "兰州",
]


@inject
async def common_generate_tasks(
    city: str,
    lifecycle: str,
    page_code: str = "benefits_demo",
    batch_size: int = 100,
    build_task_service: "BuildTaskService" = Provide[Container.applications.growth_hacker_build_task_service],
    producer: "RabbitMQProducer" = Provide[Container.infrastructures.rabbitmq_producer],
) -> None:
    """通用的任务生成方法

    Args:
        city: 城市名称，如 "上海", "北京"
        lifecycle: 用户生命周期，如 "活跃", "流失", "沉睡"
        page_code: 页面代码，默认 "benefits_demo"
        batch_size: 批处理大小，默认 100
    """
    batch_name = f"{city}-{datetime.now().strftime('%Y%m%d')}_{lifecycle}"

    # 获取符合条件的用户总数
    total_count = await build_task_service.eleme_user_repo.get_count_by_city_lifecycle(city, lifecycle)
    logger.info(f"城市: {city}, 生命周期: {lifecycle}, 用户总数: {total_count}")

    if total_count == 0:
        logger.warning(f"没有找到符合条件的用户 (城市: {city}, 生命周期: {lifecycle})")
        return

    offset = 0
    processed_count = 0

    while offset < total_count:
        # 获取一批任务
        tasks = await build_task_service.build_by_city_lifecycle(
            batch_name, page_code, city, lifecycle, offset, batch_size
        )

        if not tasks:
            logger.info(f"没有更多任务了 (offset: {offset})")
            break

        # 发布任务到消息队列
        for task in tasks:
            await producer.publish_message(TaskMessage(payload=task.model_dump()))
            processed_count += 1

        logger.info(
            f"已发布 {len(tasks)} 个任务 | "
            f"进度: {processed_count}/{total_count} | "
            f"城市: {city}, 生命周期: {lifecycle}"
        )

        offset += batch_size

    logger.info(
        f"任务生成完成 | 总计: {processed_count} 个任务 | " f"城市: {city}, 生命周期: {lifecycle}, 批次: {batch_name}"
    )


@click.command()
@click.option("--city", required=True, help="城市名称，如：北京、上海")
@click.option("--lifecycle", required=True, help="生命周期，如：活跃、流失、沉睡")
@click.option("--page-code", default="benefits_demo", help="页面代码")
@click.option("--batch-size", default=100, type=int, help="批处理大小")
def main(city: str, lifecycle: str, page_code: str, batch_size: int):
    """Growth Hacker 任务生成器

    示例:
        python -m src.interfaces.growth_hacker.tasks --city=北京 --lifecycle=活跃
    """
    logger.info(f"启动任务生成: 城市={city}, 生命周期={lifecycle}")

    async def run():
        async with lifespan() as container:
            container.wire(modules=[__name__])
            await common_generate_tasks(city, lifecycle, page_code, batch_size)

    asyncio.run(run())


if __name__ == "__main__":
    main()
