# encoding: utf-8
# src/infrastructures/browsers/pools/__init__.py
# created: 2025-08-07 22:54:04

from typing import Optional

from ..settings import BrowserConfig
from .pool import BrowserPool, browser_pool


def init_browser_pool(config: BrowserConfig) -> BrowserPool:
    """初始化浏览器池"""
    global browser_pool
    if browser_pool is None:
        browser_pool = BrowserPool(config)
    return browser_pool


def get_browser_pool() -> Optional[BrowserPool]:
    """获取浏览器池实例"""
    return browser_pool


__all__ = ["browser_pool", "BrowserPool", "init_browser_pool", "get_browser_pool"]
