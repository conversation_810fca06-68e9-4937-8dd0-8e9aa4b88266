# encoding: utf-8
# src/interfaces/growth_hacker/container.py
# created: 2025-07-27 16:14:55

import contextlib

from deploys.growth_hacker.settings import config
from src.containers import Container


@contextlib.asynccontextmanager
async def lifespan():
    """上下文管理器，用于初始化和清理资源"""
    # 初始化资源
    container = Container()
    container.config.from_pydantic(config)
    await container.infrastructures.init_tortoise()

    yield container

    # 清理资源
    from tortoise import Tortoise

    await Tortoise.close_connections()
