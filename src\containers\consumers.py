# encoding: utf-8
# src/containers/consumers.py
# created: 2025-08-19 22:30:00

from dependency_injector import containers, providers


class Consumers(containers.DeclarativeContainer):
    """消费者容器，集中管理所有消费者的依赖注入"""

    # 依赖容器
    applications = providers.DependenciesContainer()
    infrastructures = providers.DependenciesContainer()
    repositories = providers.DependenciesContainer()
    domains = providers.DependenciesContainer()

    # ========================
    # Delivery 消费者
    # ========================
    from src.interfaces.consumers.delivery import (
        SyncElemeOrderConsumer,
        UnionOrderNotifyConsumer,
        UnionOrderSyncConsumer,
    )

    # 联盟订单同步消费者
    union_order_sync_consumer = providers.Factory(
        UnionOrderSyncConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        order_sync_service=applications.common_order_sync_command_service,
    )

    # 饿了么订单详情同步消费者
    sync_eleme_order_consumer = providers.Factory(
        SyncElemeOrderConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        order_sync_service=applications.common_order_sync_command_service,
    )

    # 联盟订单通知消费者
    union_order_notify_consumer = providers.Factory(
        UnionOrderNotifyConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        order_notify_service=applications.openapi_order_notify_command_service,
    )

    # ========================
    # Benefits 消费者
    # ========================
    from src.interfaces.consumers.benefits import (
        BenefitsOrderNoticeConsumer,
        ProductOrderExportConsumer,
        SkuChargeCheckConsumer,
        SkuChargeConsumer,
        UnionPurchaseTicketSyncConsumer,
        UnionSKUSyncConsumer,
    )

    # 联盟SKU同步消费者
    union_sku_sync_consumer = providers.Factory(
        UnionSKUSyncConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        sync_service=applications.common_union_sku_sync_command_service,
    )

    # SKU充值消费者
    sku_charge_consumer = providers.Factory(
        SkuChargeConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        charge_service=applications.common_sku_charge_command_service,
    )

    # SKU充值检查消费者
    sku_charge_check_consumer = providers.Factory(
        SkuChargeCheckConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        check_service=applications.common_sku_charge_check_command_service,
    )

    # 联盟采购票据同步消费者
    union_purchase_ticket_sync_consumer = providers.Factory(
        UnionPurchaseTicketSyncConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        sync_service=applications.common_union_purchase_sync_command_service,
    )

    # 权益订单通知消费者
    benefits_order_notice_consumer = providers.Factory(
        BenefitsOrderNoticeConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        order_notice_service=applications.common_order_notice_command_service,
    )

    # 产品订单导出消费者
    product_order_export_consumer = providers.Factory(
        ProductOrderExportConsumer,
        conn_pool=infrastructures.rabbitmq_manager.provided.connection_pool,
        producer=infrastructures.rabbitmq_producer,
        export_service=applications.common_product_order_export_command_service,
    )
