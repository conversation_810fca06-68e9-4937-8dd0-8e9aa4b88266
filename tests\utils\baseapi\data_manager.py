# encoding: utf-8
# tests/utils/baseapi/data_manager.py
# created: 2025-08-02 10:45:00

import asyncio
import sqlite3
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Optional, Type, TypeVar

import factory
from loguru import logger

from .environment_manager import TestEnvironmentConfig

T = TypeVar('T')


class BaseTestFactory(factory.Factory):
    """基础测试数据工厂"""
    
    class Meta:
        abstract = True
        strategy = factory.BUILD_STRATEGY


class UserFactory(BaseTestFactory):
    """用户测试数据工厂"""
    
    class Meta:
        model = dict
    
    id = factory.Sequence(lambda n: n + 1)
    username = factory.Faker('user_name')
    email = factory.Faker('email')
    tenant_id = factory.Faker('uuid4')
    app_id = factory.Faker('uuid4')
    created_at = factory.Faker('date_time')


class SessionFactory(BaseTestFactory):
    """会话测试数据工厂"""
    
    class Meta:
        model = dict
    
    id = factory.Faker('uuid4')
    user_id = factory.Sequence(lambda n: n + 1)
    token = factory.Faker('sha256')
    expires_at = factory.Faker('future_datetime')


class BaseAPIRequestFactory(BaseTestFactory):
    """BaseAPI请求数据工厂"""
    
    class Meta:
        model = dict
    
    # 通用认证头部
    @factory.LazyFunction
    def authorization():
        from faker import Faker
        fake = Faker()
        return f"Bearer {fake.sha256()}"
    
    tenant_id = factory.Faker('uuid4')
    app_id = factory.Faker('uuid4')
    
    # 请求参数
    longitude = factory.Faker('longitude')
    latitude = factory.Faker('latitude')
    page_size = factory.Faker('random_int', min=1, max=50)


class StoreDataFactory(BaseTestFactory):
    """店铺数据工厂"""
    
    class Meta:
        model = dict
    
    shop_id = factory.Faker('uuid4')
    title = factory.Faker('company')
    category_1_name = factory.Faker('word')
    service_rating = factory.Faker('pyfloat', left_digits=1, right_digits=1, min_value=3.0, max_value=5.0)
    commission_rate = factory.Faker('pyfloat', left_digits=2, right_digits=2, min_value=0.01, max_value=0.15)
    delivery_distance = factory.Faker('random_int', min=100, max=8000)
    monthly_sales = factory.Faker('random_int', min=10, max=1000)


class OrderDataFactory(BaseTestFactory):
    """订单数据工厂"""
    
    class Meta:
        model = dict
    
    order_id = factory.Faker('uuid4')
    user_id = factory.Sequence(lambda n: n + 1)
    shop_id = factory.Faker('uuid4')
    amount = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    status = factory.Faker('random_element', elements=['pending', 'confirmed', 'delivered', 'cancelled'])
    created_at = factory.Faker('date_time')


class DataManager:
    """测试数据管理器
    
    负责管理测试数据的生命周期，包括创建、清理和隔离
    """
    
    def __init__(self, config: TestEnvironmentConfig):
        self.config = config
        self.db_path = config.db_path
        self._created_data: Dict[str, List[Dict[str, Any]]] = {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def initialize(self):
        """初始化数据管理器"""
        self._created_data = {}
        logger.debug(f"数据管理器初始化完成: {self.config.environment_id}")
    
    def create_user(self, **kwargs) -> Dict[str, Any]:
        """创建用户测试数据"""
        user_data = UserFactory.build(**kwargs)
        self._save_to_db('test_users', user_data)
        self._track_created_data('users', user_data)
        return user_data
    
    def create_users(self, count: int, **kwargs) -> List[Dict[str, Any]]:
        """批量创建用户测试数据"""
        users = []
        for _ in range(count):
            user = self.create_user(**kwargs)
            users.append(user)
        return users
    
    def create_session(self, user_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """创建会话测试数据"""
        if user_id:
            kwargs['user_id'] = user_id
        session_data = SessionFactory.build(**kwargs)
        self._save_to_db('test_sessions', session_data)
        self._track_created_data('sessions', session_data)
        return session_data
    
    def create_store(self, **kwargs) -> Dict[str, Any]:
        """创建店铺测试数据"""
        store_data = StoreDataFactory.build(**kwargs)
        self._track_created_data('stores', store_data)
        return store_data
    
    def create_stores(self, count: int, **kwargs) -> List[Dict[str, Any]]:
        """批量创建店铺测试数据"""
        stores = []
        for _ in range(count):
            store = self.create_store(**kwargs)
            stores.append(store)
        return stores
    
    def create_order(self, **kwargs) -> Dict[str, Any]:
        """创建订单测试数据"""
        order_data = OrderDataFactory.build(**kwargs)
        self._track_created_data('orders', order_data)
        return order_data
    
    def create_request_headers(self, **kwargs) -> Dict[str, str]:
        """创建请求头部数据"""
        request_data = BaseAPIRequestFactory.build(**kwargs)
        headers = {
            'Authorization': request_data['authorization'],
            'X-Tenant-ID': request_data['tenant_id'],
            'X-App-ID': request_data['app_id'],
            'Content-Type': 'application/json'
        }
        return headers
    
    def create_api_request_data(self, **kwargs) -> Dict[str, Any]:
        """创建API请求数据"""
        return BaseAPIRequestFactory.build(**kwargs)
    
    def _save_to_db(self, table_name: str, data: Dict[str, Any]):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if table_name == 'test_users':
                cursor.execute("""
                    INSERT INTO test_users (id, username, email, tenant_id, app_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    data['id'], data['username'], data['email'],
                    data['tenant_id'], data['app_id'], data['created_at']
                ))
            elif table_name == 'test_sessions':
                cursor.execute("""
                    INSERT INTO test_sessions (id, user_id, token, expires_at)
                    VALUES (?, ?, ?, ?)
                """, (
                    data['id'], data['user_id'], data['token'], data['expires_at']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.warning(f"保存数据到数据库失败: {e}")
    
    def _track_created_data(self, data_type: str, data: Dict[str, Any]):
        """跟踪创建的数据"""
        if data_type not in self._created_data:
            self._created_data[data_type] = []
        self._created_data[data_type].append(data)
    
    def get_created_data(self, data_type: str) -> List[Dict[str, Any]]:
        """获取已创建的数据"""
        return self._created_data.get(data_type, [])
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM test_users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'tenant_id': row[3],
                    'app_id': row[4],
                    'created_at': row[5]
                }
            return None
        except Exception as e:
            logger.warning(f"查询用户失败: {e}")
            return None
    
    def clear_data_type(self, data_type: str):
        """清理特定类型的数据"""
        if data_type in self._created_data:
            del self._created_data[data_type]
        logger.debug(f"已清理 {data_type} 类型的数据")
    
    async def cleanup(self):
        """清理所有测试数据"""
        try:
            # 清理数据库表
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清空测试表
            cursor.execute("DELETE FROM test_users")
            cursor.execute("DELETE FROM test_sessions")
            
            conn.commit()
            conn.close()
            
            # 清理内存中的数据追踪
            self._created_data.clear()
            
            logger.debug(f"测试数据清理完成: {self.config.environment_id}")
            
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")


@asynccontextmanager
async def get_data_manager(config: TestEnvironmentConfig) -> AsyncGenerator[DataManager, None]:
    """获取数据管理器的上下文管理器"""
    async with DataManager(config) as manager:
        yield manager


class DataTestMixin:
    """数据测试混入类
    
    为测试类提供便捷的数据管理方法
    """
    
    data_manager: DataManager
    
    def setup_test_data(self):
        """设置测试数据 - 在测试前调用"""
        pass
    
    def teardown_test_data(self):
        """清理测试数据 - 在测试后调用"""
        if hasattr(self, 'data_manager'):
            asyncio.create_task(self.data_manager.cleanup())
    
    def create_test_user(self, **kwargs) -> Dict[str, Any]:
        """创建测试用户的便捷方法"""
        return self.data_manager.create_user(**kwargs)
    
    def create_test_session(self, user_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """创建测试会话的便捷方法"""
        return self.data_manager.create_session(user_id=user_id, **kwargs)
    
    def create_test_headers(self, **kwargs) -> Dict[str, str]:
        """创建测试请求头的便捷方法"""
        return self.data_manager.create_request_headers(**kwargs)