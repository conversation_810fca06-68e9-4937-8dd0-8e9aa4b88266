# 性能优化策略

## 📊 性能现状分析

### 当前性能瓶颈识别
基于代码分析和系统监控，识别出以下关键性能瓶颈：

```mermaid
graph TB
    subgraph "性能瓶颈分析"
        A[浏览器资源池] --> A1[串行初始化 15-45s]
        A --> A2[健康检查开销 10-15% CPU]
        A --> A3[生命周期管理过于激进]
        
        B[IP代理池] --> B1[Redis操作密集]
        B --> B2[分布式锁竞争]
        B --> B3[并发限制不灵活]
        
        C[消息队列] --> C1[预取配置过低 prefetch=2]
        C --> C2[连接池限制 pool_size=2]
        C --> C3[实例数不足]
        
        D[数据访问层] --> D1[缺少连接池优化]
        D --> D2[N+1查询问题]
        D --> D3[未分离读写]
    end
```

### 性能基准数据
| 指标类型 | 当前值 | 行业标准 | 目标值 | 改善空间 |
|---------|--------|----------|--------|----------|
| **浏览器初始化时间** | 15-45秒 | <10秒 | <8秒 | 🔴 60-80% |
| **任务执行时间** | 待测量 | <30秒 | <25秒 | 🟡 20% |
| **并发处理能力** | ~2任务/分钟 | 10任务/分钟 | 8任务/分钟 | 🔴 300% |
| **内存使用** | 150-200MB/实例 | <120MB | <100MB | 🟡 30% |
| **代理分配时间** | 待测量 | <1秒 | <0.5秒 | 🟡 50% |

## 🚀 浏览器资源池优化

### 1. 并发初始化优化

#### 问题分析
```python
# 当前问题：串行初始化
async def _ensure_pool_ready(self):
    for i in range(self.max_browsers):  # 串行创建，耗时累积
        browser = await self._create_browser_instance()
        self.instances.append(browser)
```

#### 优化方案：并发初始化
```python
# 文件: src/infrastructures/browsers/optimized/concurrent_initializer.py
import asyncio
from typing import List, Optional
from loguru import logger

class ConcurrentBrowserInitializer:
    """并发浏览器初始化器"""
    
    def __init__(self, max_browsers: int = 3, max_concurrent: int = 2):
        self.max_browsers = max_browsers
        self.max_concurrent = max_concurrent  # 限制并发数防止资源耗尽
    
    async def initialize_pool(self) -> List["BrowserInstance"]:
        """并发初始化浏览器池"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def create_with_semaphore():
            async with semaphore:
                try:
                    return await self._create_browser_instance()
                except Exception as e:
                    logger.error(f"浏览器创建失败: {e}")
                    return None
        
        # 创建并发任务
        tasks = [create_with_semaphore() for _ in range(self.max_browsers)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful_instances = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"浏览器实例 {i} 创建异常: {result}")
            elif result is not None:
                successful_instances.append(result)
        
        logger.info(f"并发初始化完成: {len(successful_instances)}/{self.max_browsers} 成功")
        return successful_instances
    
    async def _create_browser_instance(self) -> "BrowserInstance":
        """创建单个浏览器实例"""
        start_time = asyncio.get_event_loop().time()
        
        # 实际的浏览器创建逻辑
        instance = await BrowserInstance.create()
        
        creation_time = asyncio.get_event_loop().time() - start_time
        logger.debug(f"浏览器实例创建耗时: {creation_time:.2f}s")
        
        return instance
```

**预期效果**：
- 初始化时间从 15-45秒 降至 8-15秒 (减少60-70%)
- 资源利用率提升，避免CPU/内存峰值

### 2. 智能健康检查优化

#### 问题分析
当前每次获取实例都进行完整健康检查，包括创建临时页面和JavaScript执行，造成10-15%的额外CPU开销。

#### 优化方案：分层健康检查
```python
# 文件: src/infrastructures/browsers/optimized/health_checker.py
import time
import asyncio
from typing import Dict, Optional
from enum import Enum

class HealthCheckLevel(Enum):
    """健康检查级别"""
    BASIC = "basic"      # 基础连接检查 (~1ms)
    MEDIUM = "medium"    # 版本信息检查 (~10ms)  
    DEEP = "deep"        # 完整JavaScript检查 (~100ms)

class IntelligentHealthChecker:
    """智能健康检查器"""
    
    def __init__(self):
        self.last_deep_check: Dict[str, float] = {}
        self.check_intervals = {
            HealthCheckLevel.BASIC: 0,      # 每次都检查
            HealthCheckLevel.MEDIUM: 30,    # 30秒间隔
            HealthCheckLevel.DEEP: 300,     # 5分钟间隔
        }
    
    async def check_browser_health(self, browser: "Browser") -> bool:
        """智能分层健康检查"""
        browser_id = id(browser)
        
        # Level 1: 基础连接检查 (1ms)
        if not await self._basic_health_check(browser):
            return False
        
        # Level 2: 版本信息检查 (10ms) - 30秒间隔
        if self._should_check_level(browser_id, HealthCheckLevel.MEDIUM):
            if not await self._medium_health_check(browser):
                return False
            self._update_check_time(browser_id, HealthCheckLevel.MEDIUM)
        
        # Level 3: 深度JavaScript检查 (100ms) - 5分钟间隔  
        if self._should_check_level(browser_id, HealthCheckLevel.DEEP):
            if not await self._deep_health_check(browser):
                return False
            self._update_check_time(browser_id, HealthCheckLevel.DEEP)
        
        return True
    
    async def _basic_health_check(self, browser: "Browser") -> bool:
        """基础连接检查 - 最快速度"""
        try:
            return browser is not None and not getattr(browser, '_is_closed', False)
        except Exception:
            return False
    
    async def _medium_health_check(self, browser: "Browser") -> bool:
        """中等级别检查 - 版本信息"""
        try:
            version = await asyncio.wait_for(browser.version(), timeout=0.1)
            return version is not None
        except (asyncio.TimeoutError, Exception):
            return False
    
    async def _deep_health_check(self, browser: "Browser") -> bool:
        """深度检查 - JavaScript执行"""
        try:
            # 创建临时页面执行简单JavaScript
            context = await browser.new_context()
            page = await context.new_page()
            
            result = await asyncio.wait_for(
                page.evaluate("() => window.navigator.userAgent"), 
                timeout=2.0
            )
            
            await context.close()
            return bool(result)
            
        except Exception:
            return False
    
    def _should_check_level(self, browser_id: int, level: HealthCheckLevel) -> bool:
        """判断是否需要执行该级别检查"""
        if level == HealthCheckLevel.BASIC:
            return True
        
        last_check = self.last_deep_check.get(f"{browser_id}_{level.value}", 0)
        interval = self.check_intervals[level]
        
        return time.time() - last_check >= interval
    
    def _update_check_time(self, browser_id: int, level: HealthCheckLevel) -> None:
        """更新检查时间"""
        self.last_deep_check[f"{browser_id}_{level.value}"] = time.time()
```

**预期效果**：
- CPU开销从 10-15% 降至 2-3%
- 健康检查时间从 100ms 降至 1-10ms (平均)
- 保持同等的健康检查准确性

### 3. 动态生命周期管理

#### 问题分析
当前固定30次使用或20分钟强制替换策略过于激进，没有考虑实际性能表现。

#### 优化方案：基于性能指标的生命周期管理
```python
# 文件: src/infrastructures/browsers/optimized/lifecycle_manager.py
import time
from dataclasses import dataclass
from typing import Dict, Optional
from statistics import mean

@dataclass
class BrowserMetrics:
    """浏览器性能指标"""
    response_times: list[float]
    error_count: int
    success_count: int
    memory_usage: float
    cpu_usage: float
    last_used: float
    
    @property
    def avg_response_time(self) -> float:
        """平均响应时间"""
        return mean(self.response_times) if self.response_times else 0.0
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        total = self.error_count + self.success_count
        return self.error_count / total if total > 0 else 0.0
    
    @property
    def performance_score(self) -> float:
        """性能评分 (0-100)"""
        base_score = 100.0
        
        # 响应时间惩罚
        if self.avg_response_time > 3000:  # 超过3秒
            base_score -= 30
        elif self.avg_response_time > 1000:  # 超过1秒
            base_score -= 15
        
        # 错误率惩罚
        base_score -= self.error_rate * 50
        
        # 内存使用惩罚
        if self.memory_usage > 300 * 1024 * 1024:  # 超过300MB
            base_score -= 20
        
        return max(base_score, 0.0)

class DynamicLifecycleManager:
    """动态生命周期管理器"""
    
    def __init__(self):
        self.browser_metrics: Dict[str, BrowserMetrics] = {}
        self.performance_thresholds = {
            'min_performance_score': 60.0,  # 最低性能分数
            'max_response_time': 5000.0,     # 最大响应时间(ms)
            'max_error_rate': 0.15,          # 最大错误率
            'max_idle_time': 3600.0,         # 最大空闲时间(秒)
        }
    
    def should_replace_browser(self, browser_id: str) -> bool:
        """基于性能指标决定是否替换浏览器"""
        metrics = self.browser_metrics.get(browser_id)
        if not metrics:
            return False
        
        thresholds = self.performance_thresholds
        
        # 性能评分过低
        if metrics.performance_score < thresholds['min_performance_score']:
            return True
        
        # 响应时间过慢
        if metrics.avg_response_time > thresholds['max_response_time']:
            return True
        
        # 错误率过高
        if metrics.error_rate > thresholds['max_error_rate']:
            return True
        
        # 空闲时间过长
        if time.time() - metrics.last_used > thresholds['max_idle_time']:
            return True
        
        return False
    
    def record_browser_usage(
        self, 
        browser_id: str, 
        response_time: float, 
        success: bool,
        memory_usage: Optional[float] = None,
        cpu_usage: Optional[float] = None
    ) -> None:
        """记录浏览器使用指标"""
        if browser_id not in self.browser_metrics:
            self.browser_metrics[browser_id] = BrowserMetrics(
                response_times=[],
                error_count=0,
                success_count=0,
                memory_usage=0.0,
                cpu_usage=0.0,
                last_used=time.time()
            )
        
        metrics = self.browser_metrics[browser_id]
        
        # 更新指标
        metrics.response_times.append(response_time)
        if len(metrics.response_times) > 100:  # 保留最近100次记录
            metrics.response_times = metrics.response_times[-100:]
        
        if success:
            metrics.success_count += 1
        else:
            metrics.error_count += 1
        
        if memory_usage is not None:
            metrics.memory_usage = memory_usage
        if cpu_usage is not None:
            metrics.cpu_usage = cpu_usage
        
        metrics.last_used = time.time()
    
    def get_adaptive_usage_limit(self, browser_id: str) -> int:
        """获取自适应的使用次数限制"""
        metrics = self.browser_metrics.get(browser_id)
        if not metrics:
            return 30  # 默认值
        
        # 根据性能表现调整限制
        if metrics.performance_score > 90:
            return 100  # 性能优秀，可以使用更久
        elif metrics.performance_score > 75:
            return 60   # 性能良好
        else:
            return 30   # 性能一般，使用标准限制
    
    def get_replacement_reason(self, browser_id: str) -> Optional[str]:
        """获取替换原因"""
        if not self.should_replace_browser(browser_id):
            return None
        
        metrics = self.browser_metrics.get(browser_id)
        if not metrics:
            return "无指标数据"
        
        reasons = []
        thresholds = self.performance_thresholds
        
        if metrics.performance_score < thresholds['min_performance_score']:
            reasons.append(f"性能分数过低({metrics.performance_score:.1f})")
        
        if metrics.avg_response_time > thresholds['max_response_time']:
            reasons.append(f"响应时间过慢({metrics.avg_response_time:.0f}ms)")
        
        if metrics.error_rate > thresholds['max_error_rate']:
            reasons.append(f"错误率过高({metrics.error_rate:.1%})")
        
        idle_time = time.time() - metrics.last_used
        if idle_time > thresholds['max_idle_time']:
            reasons.append(f"空闲时间过长({idle_time/60:.1f}分钟)")
        
        return "; ".join(reasons)
```

**预期效果**：
- 根据实际性能动态调整生命周期，避免过度替换
- 性能优秀的实例可使用更长时间，减少重建开销
- 自动识别问题实例，及时替换保证服务质量

## 📡 IP代理池优化

### 1. Redis操作批量化

#### 问题分析
```python
# 当前问题：多次Redis往返
async def alloc(self, city: str) -> Optional[IpProxy]:
    # 操作1: 获取代理列表
    proxies = await self.redis.zrangebyscore(...)
    
    for proxy_json in proxies:
        # 操作2: 检查并发数
        concurrent = await self.redis.hget(...)
        # 操作3: 增加并发数
        new_concurrent = await self.redis.hincrby(...)
        # 操作4: 可能的回滚操作
        if new_concurrent > max_concurrent:
            await self.redis.hincrby(...)
```

#### 优化方案：批量操作减少网络往返
```python
# 文件: src/infrastructures/ip_proxy/optimized/batch_allocator.py
import asyncio
from typing import List, Optional, Dict, Any
import json
from datetime import datetime

class BatchProxyAllocator:
    """批量代理分配器"""
    
    def __init__(self, redis_manager, max_batch_size: int = 10):
        self.redis = redis_manager.client
        self.max_batch_size = max_batch_size
    
    async def batch_alloc(
        self, 
        city: str, 
        count: int = 1, 
        max_concurrent: int = 5
    ) -> List["IpProxy"]:
        """批量分配代理，减少Redis往返次数"""
        if count > self.max_batch_size:
            # 分批处理大量请求
            batches = [count // self.max_batch_size] * (count // self.max_batch_size)
            if count % self.max_batch_size:
                batches.append(count % self.max_batch_size)
            
            results = []
            for batch_size in batches:
                batch_result = await self._alloc_single_batch(city, batch_size, max_concurrent)
                results.extend(batch_result)
                
            return results
        else:
            return await self._alloc_single_batch(city, count, max_concurrent)
    
    async def _alloc_single_batch(
        self, 
        city: str, 
        count: int, 
        max_concurrent: int
    ) -> List["IpProxy"]:
        """单批次分配"""
        
        # 1. 使用Lua脚本进行原子操作，减少网络往返
        lua_script = """
        local key = KEYS[1]
        local concurrent_key = KEYS[2]
        local current_ts = ARGV[1]
        local count = tonumber(ARGV[2])
        local max_concurrent = tonumber(ARGV[3])
        
        -- 获取未过期的代理
        local proxies = redis.call('ZRANGEBYSCORE', key, current_ts + 30, '+inf', 'LIMIT', 0, count * 3)
        
        local allocated = {}
        local concurrent_updates = {}
        
        for i, proxy_json in ipairs(proxies) do
            if #allocated >= count then
                break
            end
            
            local proxy = cjson.decode(proxy_json)
            local concurrent = tonumber(redis.call('HGET', concurrent_key, proxy.identify) or 0)
            
            if concurrent < max_concurrent then
                -- 记录分配的代理
                table.insert(allocated, proxy_json)
                -- 记录需要更新的并发数
                concurrent_updates[proxy.identify] = concurrent + 1
            end
        end
        
        -- 批量更新并发计数
        for identify, new_count in pairs(concurrent_updates) do
            redis.call('HSET', concurrent_key, identify, new_count)
        end
        
        return allocated
        """
        
        try:
            # 执行Lua脚本
            current_ts = datetime.now().timestamp()
            allocated_proxies_json = await self.redis.eval(
                lua_script,
                keys=[
                    self._get_proxy_key(city),
                    self._get_concurrent_key()
                ],
                args=[str(current_ts), str(count), str(max_concurrent)]
            )
            
            # 转换为代理对象
            allocated_proxies = []
            for proxy_json in allocated_proxies_json:
                try:
                    proxy_data = json.loads(proxy_json)
                    proxy = IpProxy.model_validate(proxy_data)
                    allocated_proxies.append(proxy)
                except Exception as e:
                    logger.error(f"代理数据解析失败: {e}")
                    continue
            
            logger.info(f"批量分配代理成功: {len(allocated_proxies)}/{count}")
            return allocated_proxies
            
        except Exception as e:
            logger.error(f"批量代理分配失败: {e}")
            # 降级到单个分配
            return await self._fallback_single_alloc(city, count, max_concurrent)
    
    async def _fallback_single_alloc(
        self, 
        city: str, 
        count: int, 
        max_concurrent: int
    ) -> List["IpProxy"]:
        """降级方案：单个分配"""
        allocated = []
        for _ in range(count):
            try:
                proxy = await self._alloc_single_proxy(city, max_concurrent)
                if proxy:
                    allocated.append(proxy)
                else:
                    break  # 没有可用代理，停止尝试
            except Exception as e:
                logger.error(f"单个代理分配失败: {e}")
                break
        
        return allocated
    
    def _get_proxy_key(self, city: str) -> str:
        """获取代理键名"""
        return f"ip_proxy:pool:{city}"
    
    def _get_concurrent_key(self) -> str:
        """获取并发键名"""
        return "ip_proxy:concurrent"
```

**预期效果**：
- 网络往返次数从 3-4次 减少至 1次
- 代理分配时间从 50-100ms 降至 10-20ms
- 支持批量分配，提高并发处理能力

### 2. 智能负载均衡策略

#### 基于质量评分的代理分配
```python
# 文件: src/infrastructures/ip_proxy/optimized/quality_manager.py
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass, field
import random
import math

@dataclass
class ProxyQualityMetrics:
    """代理质量指标"""
    success_count: int = 0
    failure_count: int = 0
    total_response_time: float = 0.0
    request_count: int = 0
    last_success_time: float = field(default_factory=time.time)
    consecutive_failures: int = 0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
    
    @property
    def avg_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / self.request_count if self.request_count > 0 else 0.0
    
    @property
    def quality_score(self) -> float:
        """质量评分 (0-100)"""
        base_score = 100.0
        
        # 成功率权重 50%
        success_rate_score = self.success_rate * 50
        
        # 响应时间权重 30%
        if self.avg_response_time > 0:
            # 响应时间越短分数越高，使用负指数函数
            time_score = 30 * math.exp(-self.avg_response_time / 2000)  # 2秒为基准
        else:
            time_score = 30
        
        # 稳定性权重 20%
        stability_score = max(0, 20 - self.consecutive_failures * 5)
        
        return min(base_score, success_rate_score + time_score + stability_score)

class QualityBasedProxyManager:
    """基于质量的代理管理器"""
    
    def __init__(self, redis_manager):
        self.redis = redis_manager.client
        self.quality_metrics: Dict[str, ProxyQualityMetrics] = {}
        self.metrics_sync_interval = 300  # 5分钟同步一次指标到Redis
        self.last_sync_time = time.time()
    
    async def alloc_with_quality(self, city: str, count: int = 1) -> List["IpProxy"]:
        """基于质量评分分配代理"""
        # 1. 获取候选代理
        candidates = await self._get_available_proxies(city)
        if not candidates:
            return []
        
        # 2. 计算质量评分并排序
        scored_candidates = []
        for proxy in candidates:
            score = await self._get_proxy_quality_score(proxy.identify)
            scored_candidates.append((score, proxy))
        
        # 3. 使用加权随机选择（避免总是选择最好的代理）
        selected_proxies = self._weighted_random_selection(scored_candidates, count)
        
        return selected_proxies
    
    async def _get_available_proxies(self, city: str) -> List["IpProxy"]:
        """获取可用代理列表"""
        current_ts = datetime.now().timestamp()
        proxy_key = f"ip_proxy:pool:{city}"
        
        # 获取未过期的代理
        proxy_jsons = await self.redis.zrangebyscore(
            proxy_key, 
            current_ts + 30, 
            "+inf", 
            start=0, 
            num=50  # 获取更多候选代理
        )
        
        proxies = []
        for proxy_json in proxy_jsons:
            try:
                proxy_data = json.loads(proxy_json)
                proxy = IpProxy.model_validate(proxy_data)
                proxies.append(proxy)
            except Exception as e:
                logger.debug(f"代理数据解析失败: {e}")
                continue
        
        return proxies
    
    async def _get_proxy_quality_score(self, proxy_identify: str) -> float:
        """获取代理质量评分"""
        # 1. 从内存缓存获取
        if proxy_identify in self.quality_metrics:
            return self.quality_metrics[proxy_identify].quality_score
        
        # 2. 从Redis获取历史指标
        metrics_key = f"proxy:quality:{proxy_identify}"
        metrics_data = await self.redis.hgetall(metrics_key)
        
        if metrics_data:
            try:
                metrics = ProxyQualityMetrics(
                    success_count=int(metrics_data.get('success_count', 0)),
                    failure_count=int(metrics_data.get('failure_count', 0)),
                    total_response_time=float(metrics_data.get('total_response_time', 0)),
                    request_count=int(metrics_data.get('request_count', 0)),
                    last_success_time=float(metrics_data.get('last_success_time', time.time())),
                    consecutive_failures=int(metrics_data.get('consecutive_failures', 0))
                )
                self.quality_metrics[proxy_identify] = metrics
                return metrics.quality_score
            except (ValueError, TypeError) as e:
                logger.debug(f"代理指标数据解析失败: {e}")
        
        # 3. 新代理，返回默认分数
        return 75.0  # 新代理给予中等分数
    
    def _weighted_random_selection(
        self, 
        scored_candidates: List[Tuple[float, "IpProxy"]], 
        count: int
    ) -> List["IpProxy"]:
        """加权随机选择"""
        if len(scored_candidates) <= count:
            return [proxy for _, proxy in scored_candidates]
        
        # 按分数排序
        scored_candidates.sort(key=lambda x: x[0], reverse=True)
        
        # 使用加权选择，分数高的代理被选中概率更大，但不是100%
        selected = []
        remaining_candidates = scored_candidates.copy()
        
        for _ in range(count):
            if not remaining_candidates:
                break
            
            # 计算权重
            weights = []
            for score, _ in remaining_candidates:
                # 使用softmax函数计算权重，温度系数控制选择的随机性
                temperature = 2.0  # 温度越高，选择越随机
                weight = math.exp(score / temperature)
                weights.append(weight)
            
            # 加权随机选择
            total_weight = sum(weights)
            rand_value = random.random() * total_weight
            
            cumulative_weight = 0
            for i, weight in enumerate(weights):
                cumulative_weight += weight
                if rand_value <= cumulative_weight:
                    selected.append(remaining_candidates[i][1])
                    remaining_candidates.pop(i)
                    break
        
        return selected
    
    async def record_proxy_result(
        self, 
        proxy_identify: str, 
        success: bool, 
        response_time: float
    ) -> None:
        """记录代理使用结果"""
        if proxy_identify not in self.quality_metrics:
            self.quality_metrics[proxy_identify] = ProxyQualityMetrics()
        
        metrics = self.quality_metrics[proxy_identify]
        
        # 更新指标
        metrics.request_count += 1
        metrics.total_response_time += response_time
        
        if success:
            metrics.success_count += 1
            metrics.last_success_time = time.time()
            metrics.consecutive_failures = 0
        else:
            metrics.failure_count += 1
            metrics.consecutive_failures += 1
        
        # 定期同步指标到Redis
        await self._maybe_sync_metrics_to_redis()
    
    async def _maybe_sync_metrics_to_redis(self) -> None:
        """可能同步指标到Redis"""
        current_time = time.time()
        if current_time - self.last_sync_time >= self.metrics_sync_interval:
            await self._sync_all_metrics_to_redis()
            self.last_sync_time = current_time
    
    async def _sync_all_metrics_to_redis(self) -> None:
        """同步所有指标到Redis"""
        pipe = self.redis.pipeline()
        
        for proxy_identify, metrics in self.quality_metrics.items():
            metrics_key = f"proxy:quality:{proxy_identify}"
            pipe.hset(metrics_key, mapping={
                'success_count': metrics.success_count,
                'failure_count': metrics.failure_count,
                'total_response_time': metrics.total_response_time,
                'request_count': metrics.request_count,
                'last_success_time': metrics.last_success_time,
                'consecutive_failures': metrics.consecutive_failures
            })
            # 设置过期时间，避免Redis内存无限增长
            pipe.expire(metrics_key, 86400 * 7)  # 7天过期
        
        await pipe.execute()
        logger.debug(f"同步了 {len(self.quality_metrics)} 个代理的质量指标到Redis")
```

**预期效果**：
- 代理选择更智能，自动避开质量差的代理
- 系统整体成功率提升 15-20%
- 平均响应时间减少 20-30%

### 3. 分布式锁优化

#### 问题分析
当前使用粗粒度锁 `ip_proxy:fetch_lock:{city}`，在高并发时存在热点竞争。

#### 优化方案：细粒度锁和无锁优化
```python
# 文件: src/infrastructures/ip_proxy/optimized/lock_optimizer.py
import hashlib
import asyncio
from typing import Optional
import time

class OptimizedProxyAllocator:
    """优化的代理分配器 - 减少锁竞争"""
    
    def __init__(self, redis_manager, lock_shards: int = 4):
        self.redis = redis_manager.client
        self.lock_shards = lock_shards  # 锁分片数量
    
    async def alloc_with_reduced_contention(self, city: str) -> Optional["IpProxy"]:
        """减少锁竞争的分配策略"""
        # 1. 先尝试无锁分配
        proxy = await self._try_lockless_alloc(city)
        if proxy:
            return proxy
        
        # 2. 使用细粒度锁
        shard_key = self._get_lock_shard(city)
        lock_key = f"ip_proxy:fetch_lock:{city}:{shard_key}"
        
        # 使用较短的锁超时，减少等待时间
        async with RedisDistributedLock(self.redis, lock_key, timeout=2) as acquired:
            if acquired:
                return await self._fetch_with_lock(city)
            else:
                # 锁获取失败时的降级策略
                return await self._try_lockless_alloc(city, retry=True)
    
    async def _try_lockless_alloc(self, city: str, retry: bool = False) -> Optional["IpProxy"]:
        """无锁分配尝试"""
        # 使用乐观锁策略：先获取代理，再检查并发数
        proxy_key = f"ip_proxy:pool:{city}"
        concurrent_key = "ip_proxy:concurrent"
        
        # 获取一些候选代理
        current_ts = datetime.now().timestamp()
        candidate_count = 10 if retry else 5
        
        proxy_jsons = await self.redis.zrangebyscore(
            proxy_key,
            current_ts + 30,
            "+inf",
            start=0,
            num=candidate_count
        )
        
        # 随机打乱顺序，减少多个进程选择同一个代理的概率
        random.shuffle(proxy_jsons)
        
        for proxy_json in proxy_jsons:
            try:
                proxy_data = json.loads(proxy_json)
                proxy = IpProxy.model_validate(proxy_data)
                
                # 乐观地尝试分配
                new_concurrent = await self.redis.hincrby(
                    concurrent_key, 
                    proxy.identify, 
                    1
                )
                
                if new_concurrent <= 5:  # 并发数检查
                    return proxy
                else:
                    # 回滚并发数
                    await self.redis.hincrby(
                        concurrent_key, 
                        proxy.identify, 
                        -1
                    )
                    
            except Exception as e:
                logger.debug(f"无锁分配尝试失败: {e}")
                continue
        
        return None
    
    async def _fetch_with_lock(self, city: str) -> Optional["IpProxy"]:
        """在锁保护下获取代理"""
        # 在锁保护下的常规分配逻辑
        return await self._standard_alloc_logic(city)
    
    def _get_lock_shard(self, city: str) -> str:
        """获取锁分片标识"""
        # 基于城市名计算哈希，分配到不同的锁分片
        hash_value = int(hashlib.md5(city.encode()).hexdigest(), 16)
        shard_id = hash_value % self.lock_shards
        return str(shard_id)

class RedisDistributedLock:
    """Redis分布式锁"""
    
    def __init__(self, redis_client, lock_key: str, timeout: int = 10):
        self.redis = redis_client
        self.lock_key = lock_key
        self.timeout = timeout
        self.lock_value = None
        
    async def __aenter__(self) -> bool:
        """获取锁"""
        import uuid
        self.lock_value = str(uuid.uuid4())
        
        # 使用SET命令的NX和EX参数实现分布式锁
        result = await self.redis.set(
            self.lock_key,
            self.lock_value,
            nx=True,  # 只在键不存在时设置
            ex=self.timeout  # 过期时间
        )
        
        return result is not None
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """释放锁"""
        if self.lock_value:
            # 使用Lua脚本确保只释放自己的锁
            lua_script = """
            if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
            else
                return 0
            end
            """
            
            await self.redis.eval(
                lua_script,
                keys=[self.lock_key],
                args=[self.lock_value]
            )
```

**预期效果**：
- 锁竞争减少 60-70%
- 代理分配延迟减少 30-50%
- 系统并发处理能力提升 2-3倍

## ⚡ 消息队列优化

### 配置优化
```yaml
# 优化后的RabbitMQ配置
rabbitmq_settings:
  # 连接池优化
  pool_size: 4  # 从2增加到4
  max_channels_per_connection: 100  # 从50增加到100
  
  # 预取优化  
  prefetch_count: 10  # 从2增加到10
  
  # 实例数优化
  consumer_instances: 4  # 基于CPU核心数，从1增加到4
  
  # 新增配置
  connection_retry_max: 5
  connection_retry_delay: 1.0
  channel_max_age_seconds: 3600
  
  # 性能调优
  heartbeat_interval: 60
  blocked_connection_timeout: 300
```

### 消费者并发优化
```python
# 文件: src/infrastructures/rabbitmq/optimized/concurrent_consumer.py
import asyncio
from typing import List
from concurrent.futures import ThreadPoolExecutor

class OptimizedGrowthHackerConsumer(BaseConsumer):
    """优化的Growth Hacker消费者"""
    
    def __init__(self):
        super().__init__()
        self.semaphore = asyncio.Semaphore(10)  # 并发任务限制
        self.task_queue = asyncio.Queue(maxsize=100)  # 任务缓冲队列
        self.worker_tasks: List[asyncio.Task] = []
        self.is_running = False
    
    async def start_workers(self, worker_count: int = 4):
        """启动工作协程池"""
        self.is_running = True
        
        for i in range(worker_count):
            task = asyncio.create_task(
                self._worker_loop(f"worker-{i}")
            )
            self.worker_tasks.append(task)
        
        logger.info(f"启动了 {worker_count} 个工作协程")
    
    async def _worker_loop(self, worker_name: str):
        """工作协程循环"""
        logger.info(f"工作协程 {worker_name} 已启动")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_data = await asyncio.wait_for(
                    self.task_queue.get(), 
                    timeout=5.0
                )
                
                # 使用信号量控制并发
                async with self.semaphore:
                    await self._process_single_task(task_data, worker_name)
                    
                self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                # 队列空闲，继续循环
                continue
            except Exception as e:
                logger.error(f"工作协程 {worker_name} 异常: {e}")
                await asyncio.sleep(1)  # 短暂休息后继续
    
    async def process(self, message: "AbstractIncomingMessage") -> None:
        """将任务放入队列，由工作协程处理"""
        task_data = {
            'message': message,
            'received_at': time.time()
        }
        
        try:
            # 非阻塞地放入队列
            self.task_queue.put_nowait(task_data)
        except asyncio.QueueFull:
            # 队列满时的处理策略
            logger.warning("任务队列已满，拒绝新任务")
            # 可以选择丢弃任务或等待
            await message.nack(requeue=True)
    
    async def _process_single_task(self, task_data: dict, worker_name: str):
        """处理单个任务"""
        message = task_data['message']
        queue_wait_time = time.time() - task_data['received_at']
        
        logger.debug(f"{worker_name} 处理任务，队列等待时间: {queue_wait_time:.2f}s")
        
        try:
            # 解析任务消息
            task = TaskMessage.model_validate_json(message.body).payload
            
            # 设置日志上下文
            set_task_id(task.task_id)
            set_phone(task.phone)
            
            logger.info(f"{worker_name} 开始处理任务: {task.task_id}")
            
            # 执行任务（使用原有逻辑）
            task_service = Container.applications.task_service()
            
            valid = await task_service.check_build_task(task)
            if not valid:
                logger.warning(f"任务构建失败: {task.task_id}")
                return
            
            await task_service.execute_task(task)
            logger.success(f"{worker_name} 任务完成: {task.task_id}")
            
        except Exception as e:
            logger.error(f"{worker_name} 任务处理失败: {e}")
            raise
        finally:
            clear_context()
    
    async def stop_workers(self):
        """停止所有工作协程"""
        self.is_running = False
        
        # 等待所有工作协程完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
            self.worker_tasks.clear()
        
        logger.info("所有工作协程已停止")
```

**预期效果**：
- 并发处理能力从 2任务/分钟 提升至 8-10任务/分钟
- 任务响应延迟减少 50-70%
- 资源利用率显著提升

## 📊 性能监控和指标收集

### 实时性能监控
```python
# 文件: src/infrastructures/monitoring/performance_monitor.py
import time
import asyncio
from typing import Dict, List
from dataclasses import dataclass, field
from collections import deque
import psutil

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    task_count: int
    avg_response_time: float
    error_rate: float
    
    def to_dict(self) -> dict:
        return {
            'timestamp': self.timestamp,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'task_count': self.task_count,
            'avg_response_time': self.avg_response_time,
            'error_rate': self.error_rate
        }

class RealTimePerformanceMonitor:
    """实时性能监控器"""
    
    def __init__(self, redis_manager):
        self.redis = redis_manager.client
        self.metrics_history = deque(maxlen=1440)  # 保留24小时数据(每分钟一个点)
        self.is_monitoring = False
        self.monitor_task = None
        
        # 性能计数器
        self.task_counter = 0
        self.response_times = deque(maxlen=100)
        self.error_counter = 0
        self.success_counter = 0
        
    async def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(
            self._monitoring_loop(interval)
        )
        logger.info("性能监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("性能监控已停止")
    
    async def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = await self._collect_metrics()
                await self._store_metrics(metrics)
                await self._check_performance_alerts(metrics)
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        # 系统指标
        cpu_usage = psutil.cpu_percent(interval=1)
        memory_info = psutil.virtual_memory()
        memory_usage = memory_info.percent
        
        # 业务指标
        total_tasks = self.task_counter
        avg_response_time = (
            sum(self.response_times) / len(self.response_times)
            if self.response_times else 0
        )
        
        total_requests = self.error_counter + self.success_counter
        error_rate = (
            self.error_counter / total_requests 
            if total_requests > 0 else 0
        )
        
        metrics = PerformanceMetrics(
            timestamp=time.time(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            task_count=total_tasks,
            avg_response_time=avg_response_time,
            error_rate=error_rate
        )
        
        # 添加到历史记录
        self.metrics_history.append(metrics)
        
        return metrics
    
    async def _store_metrics(self, metrics: PerformanceMetrics):
        """存储指标到Redis"""
        metrics_key = f"performance:metrics:{int(metrics.timestamp)}"
        
        await self.redis.hset(
            metrics_key, 
            mapping=metrics.to_dict()
        )
        
        # 设置过期时间（7天）
        await self.redis.expire(metrics_key, 86400 * 7)
        
        # 添加到时间序列
        await self.redis.zadd(
            "performance:timeline",
            {metrics_key: metrics.timestamp}
        )
        
        # 清理旧数据
        cutoff_time = time.time() - 86400 * 7
        await self.redis.zremrangebyscore(
            "performance:timeline",
            0,
            cutoff_time
        )
    
    async def _check_performance_alerts(self, metrics: PerformanceMetrics):
        """检查性能告警"""
        alerts = []
        
        if metrics.cpu_usage > 80:
            alerts.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")
        
        if metrics.memory_usage > 85:
            alerts.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")
        
        if metrics.avg_response_time > 30000:  # 30秒
            alerts.append(f"响应时间过慢: {metrics.avg_response_time:.1f}ms")
        
        if metrics.error_rate > 0.1:  # 10%
            alerts.append(f"错误率过高: {metrics.error_rate:.1%}")
        
        for alert in alerts:
            await self._send_alert(alert, metrics)
    
    async def _send_alert(self, alert_message: str, metrics: PerformanceMetrics):
        """发送告警"""
        alert_data = {
            'message': alert_message,
            'timestamp': metrics.timestamp,
            'metrics': metrics.to_dict()
        }
        
        # 发送到告警队列
        await self.redis.lpush("performance:alerts", json.dumps(alert_data))
        
        logger.warning(f"性能告警: {alert_message}")
    
    def record_task_completion(self, success: bool, response_time: float):
        """记录任务完成情况"""
        self.task_counter += 1
        self.response_times.append(response_time)
        
        if success:
            self.success_counter += 1
        else:
            self.error_counter += 1
    
    async def get_recent_metrics(self, hours: int = 24) -> List[Dict]:
        """获取最近的性能指标"""
        cutoff_time = time.time() - 3600 * hours
        
        # 从Redis获取时间范围内的指标
        timeline_keys = await self.redis.zrangebyscore(
            "performance:timeline",
            cutoff_time,
            "+inf"
        )
        
        metrics_list = []
        for key in timeline_keys:
            metrics_data = await self.redis.hgetall(key)
            if metrics_data:
                # 转换数据类型
                for field in ['timestamp', 'cpu_usage', 'memory_usage', 'avg_response_time', 'error_rate']:
                    if field in metrics_data:
                        metrics_data[field] = float(metrics_data[field])
                for field in ['task_count']:
                    if field in metrics_data:
                        metrics_data[field] = int(metrics_data[field])
                
                metrics_list.append(metrics_data)
        
        return sorted(metrics_list, key=lambda x: x['timestamp'])
```

**预期效果**：
- 实时监控系统性能，快速发现问题
- 历史数据分析，识别性能趋势
- 自动告警机制，及时响应异常

## 🎯 性能优化实施计划

### Phase 1: 关键瓶颈优化 (3天)

#### Day 1: 浏览器池并发初始化
- 实现并发初始化机制
- 部署测试环境验证
- 性能基准对比测试

#### Day 2: IP代理池批量操作
- 实现Lua脚本批量分配
- 集成质量评分系统
- 压力测试验证效果

#### Day 3: 消息队列配置优化
- 调整prefetch和连接池配置
- 实现并发消费者
- 吞吐量测试验证

### Phase 2: 智能优化 (2天)

#### Day 4: 智能生命周期管理
- 实现动态生命周期管理
- 集成性能指标收集
- A/B测试对比效果

#### Day 5: 监控和告警完善
- 部署性能监控系统
- 配置告警规则
- 性能报告生成

### 预期性能提升目标

| 指标 | 当前值 | 目标值 | 提升幅度 |
|------|--------|--------|----------|
| **浏览器初始化** | 15-45秒 | <8秒 | ⬇️ 70% |
| **任务执行时间** | 基线值 | -20% | ⬇️ 20% |
| **并发处理能力** | 2任务/分钟 | 8任务/分钟 | ⬆️ 300% |
| **代理分配时间** | 基线值 | <500ms | ⬇️ 50% |
| **系统资源使用** | 基线值 | -30% | ⬇️ 30% |

---

*本性能优化策略通过系统性的瓶颈识别和针对性优化，预期实现显著的性能提升，为系统的高并发处理能力奠定基础。*