# encoding: utf-8
# src/applications/common/commands/benefits/sku_charge.py
# created: 2025-08-19 10:00:00

from typing import TYPE_CHECKING

from loguru import logger
from tortoise.transactions import atomic

from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.domains.benefits.services import ChargeStrategyFactory
from src.infrastructures import errors

if TYPE_CHECKING:
    from src.repositories.benefits import SkuChargeRecordRepository


class SkuChargeCommandService:
    """SKU充值命令服务"""

    def __init__(self, sku_charge_record_repo: "SkuChargeRecordRepository"):
        self.sku_charge_record_repo = sku_charge_record_repo

    @atomic()
    async def execute_charge(self, record_id: int) -> tuple[RecordStatus, bool]:
        """
        执行充值操作

        Args:
            record_id: 充值记录ID

        Returns:
            tuple[RecordStatus, bool]: (充值状态, 是否需要检查)

        Raises:
            ChargeRecordNotFoundError: 充值记录不存在时抛出
        """
        logger.info(f"开始执行充值: record_id={record_id}")

        # 获取充值记录
        charge_record = await self.sku_charge_record_repo.get_by_id(record_id)
        if not charge_record:
            logger.error(f"充值记录未找到: {record_id}")
            raise errors.ChargeRecordNotFoundError

        # 执行充值策略
        charge_strategy = ChargeStrategyFactory.get_strategy(charge_record.sku.charge_func)
        record = await charge_strategy.charge(charge_record)

        logger.info(f"充值操作完成 - 记录[{record.id}]，状态: {record.status}，充值方式: {record.sku.charge_func}")

        # 判断是否需要继续处理
        if record.status == RecordStatus.PENDING:
            # 待处理状态，需要重试
            return RecordStatus.PENDING, False
        else:
            # 其他状态，需要检查
            # 确保返回的是 RecordStatus 枚举类型
            status = record.status
            if isinstance(status, str):
                status = RecordStatus(status)
            return status, True
