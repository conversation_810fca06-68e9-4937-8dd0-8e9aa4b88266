# encoding: utf-8
# src/interfaces/http/baseapi/ai/agent_apis/eleme_store.py
# created: 2025-07-23 00:00:00

import asyncio
import random
import urllib.parse
from typing import Any, Dict, List, Optional
from urllib.parse import parse_qs

import requests  # type: ignore

from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient, TopException
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_storepromotion_get_request import (
    AlibabaAlscUnionElemePromotionStorepromotionGetRequest,
    AlibabaAlscUnionElemePromotionStorepromotionGetSingleStorePromotionRequest,
)
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_storepromotion_query_request import (
    AlibabaAlscUnionElemePromotionStorepromotionQueryPromotionQueryRequest,
    AlibabaAlscUnionElemePromotionStorepromotionQueryRequest,
)

from ..utils.base import LLMClient
from .classify import classify


class ElemeStoreClient:
    """饿了么店铺列表查询客户端"""

    def __init__(self, app_key: str, app_secret: str, top_gateway_url: str, verify_ssl: bool = True):
        """
        初始化饿了么店铺列表查询客户端

        Args:
            app_key (str): 应用的AppKey
            app_secret (str): 应用的AppSecret
            top_gateway_url (str): TOP网关URL
            verify_ssl (bool, optional): 是否验证SSL证书. 默认为True.
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.top_gateway_url = top_gateway_url
        self.verify_ssl = verify_ssl
        self.client = TopApiClient(
            appkey=app_key, app_sercet=app_secret, top_gateway_url=top_gateway_url, verify_ssl=verify_ssl
        )
        self.ability = Defaultability(client=self.client)

    def get_store_list(
        self,
        pid: str,
        longitude: str,
        latitude: str,
        session_id: Optional[str] = None,
        city_id: Optional[str] = None,
        sort_type: str = "normal",
        in_activity: bool = False,
        has_bonus_stock: bool = False,
        min_commission_rate: str = "0.01",
        page_size: int = 20,
        sid: Optional[str] = None,
        biz_type: Optional[str] = None,
        filter_first_categories: Optional[str] = None,
        filter_one_point_five_categories: Optional[str] = None,
        media_activity_id: Optional[str] = None,
        search_content: Optional[str] = None,
        include_dynamic: bool = False,
    ) -> Dict[str, Any]:
        """
        获取饿了么店铺列表

        Args:
            pid (str): 渠道PID
            longitude (str): 经度
            latitude (str): 纬度
            session_id (Optional[str], optional): 会话ID（分页场景首次请求结果返回，后续请求必须携带，服务根据session_id相同请求次数自动翻页返回）
            city_id (Optional[str], optional): 城市编码（只用于经纬度覆盖多个城市时过滤）
            sort_type (str, optional): 排序类型，默认normal 排序规则包括:
                {"normal":"佣金倒序","distance":"距离由近到远","commission":"佣金倒序",
                "monthlySale":"月销量","couponAmount":"叠加券金额倒序",
                "activityReward":"奖励金金额倒序","commissionRate":"佣金比例倒序"}
            in_activity (bool, optional): 是否参与奖励金活动（默认false不做过滤）
            has_bonus_stock (bool, optional): 否当前有c端奖励金活动库存（默认false不做过滤）
            min_commission_rate (str, optional): 店铺佣金比例下限，代表筛选店铺全店佣金大于等于0.01的店铺
            page_size (int, optional): 每页数量（include_dynamic=true时，范围1~20；include_dynamic=false时，范围1~100）
            sid (Optional[str], optional): 三方扩展id
            biz_type (Optional[str], optional): 指定召回供给枚举，例如："activityCps|ordinaryCps"
            filter_first_categories (Optional[str], optional): 以一级类目进行类目限定，以,或者|进行类目分隔
            filter_one_point_five_categories (Optional[str], optional): 1.5级类目查询，以"|"分隔
            media_activity_id (Optional[str], optional): 媒体出资活动ID
            search_content (Optional[str], optional): 检索内容（支持门店名称）
            include_dynamic (bool, optional): 是否返回门店动态信息，默认返回（false-不返回门店动态信息，page_size最大支持100；true-返回门店动态信息，page_size最大支持20）

        Returns:
            Dict[str, Any]: 店铺列表查询结果
        """
        try:
            # 创建查询请求对象
            query_request = AlibabaAlscUnionElemePromotionStorepromotionQueryPromotionQueryRequest(
                session_id=session_id or "",
                pid=pid,
                longitude=longitude,
                latitude=latitude,
                city_id=city_id or "",
                sort_type=sort_type,
                in_activity=in_activity,
                has_bonus_stock=has_bonus_stock,
                min_commission_rate=min_commission_rate,
                page_size=page_size,
                sid=sid or "",
                biz_type=biz_type or "",
                filter_first_categories=filter_first_categories or "",
                filter_one_point_five_categories=filter_one_point_five_categories or "",
                media_activity_id=media_activity_id or "",
                search_content=search_content or "",
                include_dynamic=include_dynamic,
            )

            # 创建请求
            request = AlibabaAlscUnionElemePromotionStorepromotionQueryRequest()
            request.query_request = query_request

            # 发送请求
            response = self.ability.alibaba_alsc_union_eleme_promotion_storepromotion_query(request)
            return response
        except TopException as e:
            print(f"TopException: {e}")
            return {"error": str(e)}
        except requests.exceptions.ConnectionError as e:
            print(f"连接错误: {e}")
            return {"error": f"连接错误: {str(e)}"}
        except Exception as e:
            print(f"其他错误: {e}")
            return {"error": f"其他错误: {str(e)}"}

    def get_store_list_with_pagination(
        self, pid: str, longitude: str, latitude: str, max_pages: int = 5, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取饿了么店铺列表并自动处理分页

        Args:
            pid (str): 渠道PID
            longitude (str): 经度
            latitude (str): 纬度
            max_pages (int, optional): 最大获取页数. 默认为5.
            **kwargs: 其他参数，与get_store_list方法参数相同

        Returns:
            List[Dict[str, Any]]: 所有页的店铺列表
        """
        all_stores = []
        session_id = None

        for page in range(1, max_pages + 1):
            # 第一页请求
            if page == 1:
                response = self.get_store_list(pid=pid, longitude=longitude, latitude=latitude, **kwargs)
            # 后续页请求，需要使用session_id
            else:
                if not session_id:
                    break
                response = self.get_store_list(
                    pid=pid, longitude=longitude, latitude=latitude, session_id=session_id, **kwargs
                )

            # 检查响应是否成功
            if "error" in response:
                print(f"获取第{page}页数据失败: {response['error']}")
                break

            # 提取店铺数据
            if "data" in response:
                data = response["data"]
                # 检查是否包含stores字段，如果没有则尝试使用records字段
                stores = data.get("stores", data.get("records", []))
                all_stores.extend(stores)

                # 获取session_id用于下一页请求
                session_id = data.get("session_id")

                # 如果没有更多数据，退出循环
                if not session_id or not stores:
                    break

                print(f"已获取第{page}页数据，共{len(stores)}条记录")
            else:
                print(f"第{page}页数据格式异常: {response}")
                break

        return all_stores

    def search_stores_by_name(
        self, pid: str, longitude: str, latitude: str, store_name: str, max_pages: int = 3, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        根据店铺名称搜索饿了么店铺

        Args:
            pid (str): 渠道PID
            longitude (str): 经度
            latitude (str): 纬度
            store_name (str): 店铺名称
            max_pages (int, optional): 最大获取页数. 默认为3.
            **kwargs: 其他参数，与get_store_list方法参数相同

        Returns:
            List[Dict[str, Any]]: 符合条件的店铺列表
        """
        kwargs["search_content"] = store_name
        return self.get_store_list_with_pagination(
            pid=pid, longitude=longitude, latitude=latitude, max_pages=max_pages, **kwargs
        )

    def get_store_detail(
        self,
        pid: str,
        shop_id: str,
        activity_id: Optional[str] = None,
        sid: Optional[str] = None,
        include_wx_img: bool = False,
        media_activity_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取饿了么单个店铺详情

        Args:
            pid (str): 渠道PID
            shop_id (str): 门店ID（加密，具有时效性，建议每天更新一次）
            activity_id (Optional[str], optional): 活动ID
            sid (Optional[str], optional): 三方扩展id
            include_wx_img (bool, optional): 是否返回微信推广图片，默认为False
            media_activity_id (Optional[str], optional): 媒体出资活动ID

        Returns:
            Dict[str, Any]: 店铺详情信息
        """
        try:
            # 创建查询请求对象
            query_request = AlibabaAlscUnionElemePromotionStorepromotionGetSingleStorePromotionRequest(
                pid=pid,
                shop_id=shop_id,
                activity_id=activity_id or "",
                sid=sid or "",
                include_wx_img=include_wx_img,
                media_activity_id=media_activity_id or "",
            )

            # 创建请求
            request = AlibabaAlscUnionElemePromotionStorepromotionGetRequest()
            request.query_request = query_request

            # 发送请求
            response = self.ability.alibaba_alsc_union_eleme_promotion_storepromotion_get(request)
            return response
        except TopException as e:
            print(f"TopException: {e}")
            return {"error": str(e)}
        except requests.exceptions.ConnectionError as e:
            print(f"连接错误: {e}")
            return {"error": f"连接错误: {str(e)}"}
        except Exception as e:
            print(f"其他错误: {e}")
            return {"error": f"其他错误: {str(e)}"}


APP_KEY = "34813436"
APP_SECRET = "929eafdde0fb34c57ff0290e271b54c3"
TOP_GATEWAY_URL = "https://eco.taobao.com/router/rest"
PID = "alsc_28806810_9518007_26738131"
lat = "30.285743"
lon = "120.024741"
# 创建客户端
client = ElemeStoreClient(app_key=APP_KEY, app_secret=APP_SECRET, top_gateway_url=TOP_GATEWAY_URL, verify_ssl=True)


def search_store_by_name(
    search: str,
    longitude: str,
    latitude: str,
    skip_store_names: Optional[list[str]] = None,
    max_pages: int = 3,
):
    if skip_store_names is None:
        skip_store_names = []

    # 配置参数
    stores = client.search_stores_by_name(
        pid=PID,
        longitude=longitude or lon,
        latitude=latitude or lat,
        store_name=search,
        max_pages=max_pages,
        sort_type="monthlySale",
        page_size=20,
        include_dynamic=True,
    )
    if not stores:
        classify_result = classify(search)
        stores = client.search_stores_by_name(
            pid=PID,
            longitude=longitude or lon,
            latitude=latitude or lat,
            store_name=classify_result,
            max_pages=max_pages,
            sort_type="monthlySale",
            page_size=20,
            include_dynamic=True,
        )

    print(f"初始获取到 {len(stores)} 个店铺")

    if len(stores) > 20:
        # 过滤delivery_distance大于5000的店铺
        stores = [store for store in stores if store.get("delivery_distance", 0) <= 5000]
        print(f"距离过滤后剩余 {len(stores)} 个店铺")

    # 过滤skip_store_names中的店铺
    print(f"需要过滤的店铺名: {skip_store_names}")
    filtered_stores = []
    for store in stores:
        store_name = store["title"][:4]
        if store_name not in [name[:4] for name in skip_store_names]:
            filtered_stores.append(store)
        else:
            print(f"店铺 {store['title']} 被过滤掉，因为前4个字 {store_name} 在过滤列表中")
    stores = filtered_stores
    print(f"店铺名过滤后剩余 {len(stores)} 个店铺")

    # 对title去重，但保留完整的店铺信息
    unique_titles = set()
    unique_stores = []
    for store in stores:
        store_name = store["title"][:4]
        if store_name not in unique_titles:
            unique_titles.add(store_name)
            unique_stores.append(store)
        else:
            print(f"店铺 {store['title']} 被去重，因为前4个字 {store_name} 已存在")
    stores = unique_stores
    print(f"去重后剩余 {len(stores)} 个店铺")

    # 排序获得service_rating最高top20
    stores = sorted(stores, key=lambda x: x["service_rating"], reverse=True)[:20]
    print(f"评分排序后剩余 {len(stores)} 个店铺")

    # 再次排序获得commission_rate最高top10
    stores = sorted(stores, key=lambda x: x["commission_rate"], reverse=True)[:10]
    print(f"佣金排序后剩余 {len(stores)} 个店铺")

    # 随机选出3个后 根据service_rating 排序从高到低
    if len(stores) > 3:
        stores = random.sample(stores, 3)
    stores = sorted(stores, key=lambda x: x["service_rating"], reverse=True)
    return stores


def get_store_detail(shop_id: str):
    """
    获取饿了么店铺详情示例

    Args:
        shop_id (str): 店铺ID

    Returns:
        Dict[str, Any]: 店铺详情信息
    """
    # 配置参数
    store_detail = client.get_store_detail(pid=PID, shop_id=shop_id, include_wx_img=True)

    return store_detail


def get_store_detail_llm(detail: Dict[str, Any]):
    # 使用zhipu api 获取店铺详情
    prompt = f"""
你是一位专业的美食博主，擅长撰写小红书风格的美食推荐文案。

请根据以下店铺信息，撰写一篇100字以内的小红书风格推荐文案，文案应该活泼、生动、有吸引力，使用emoji表情，突出店铺特色和推荐理由。

店铺名称: {detail.get('data', {}).get('title', '')}
店铺类型: {detail.get('data', {}).get('category_1_name', '')}
店铺评分: {detail.get('data', {}).get('service_rating', '')}
月售: {detail.get('data', {}).get('indistinct_monthly_sales', '')}
推荐理由: {', '.join(detail.get('data', {}).get('recommend_reasons', []))}
热门商品: {', '.join([item.get('title', '') for item in detail.get('data', {}).get('items', [])][:3])}

文案风格要求:
1. 使用emoji表情增加活泼感
2. 使用感叹号和省略号增加情感表达
3. 加入个人体验感受，如"太爱了"、"绝绝子"等小红书常用词
4. 突出店铺特色和推荐商品
5. 总字数控制在30字以内
6. 不要以emoji开头
7. 文案中不要再次出现商店名字和销量 
    """
    llm_client = LLMClient("doubao", "default")
    response = llm_client.complete(prompt=prompt, stream=False)
    # 将response中的文字全部变为红色
    # response = f"<span style='color: red;'>{response}</span>"
    return response


async def async_get_store_detail(shop_id: str):
    return await asyncio.to_thread(get_store_detail, shop_id)


async def async_get_store_detail_llm(shop_detail: Dict[str, Any]):
    return await asyncio.to_thread(get_store_detail_llm, shop_detail)


def get_store_h5_url(rawurl: str):
    # 从URL中解析shopId
    parsed_url = urllib.parse.urlparse(rawurl)
    # 获取查询参数
    query_params = parse_qs(parsed_url.query)
    # 获取shopId
    shop_id = query_params.get("shopId", [None])[0]

    if not shop_id:
        return None

    # 构建新的URL
    url = f"https://base.hicaspian.com/deliveryLanding?from=beaverAI&shopId={shop_id}"
    return url


# 为了兼容性，添加驼峰式命名的别名
async_getStoreDetail = async_get_store_detail  # noqa: N816
async_getStoreDetailLLM = async_get_store_detail_llm  # noqa: N816
getStoreDetail = get_store_detail  # noqa: N816
searchStoreByName = search_store_by_name  # noqa: N816
getStoreH5Url = get_store_h5_url  # noqa: N816
