# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/messages.py
# created: 2025-01-26 23:12:31
# updated: 2025-06-02 23:48:22

from datetime import datetime
from enum import StrEnum
from typing import Optional

from pydantic import AliasChoices, BaseModel, Field, field_validator

from src.infrastructures.rabbitmq import message_creator


class ChargeSkuMessage(BaseModel):
    record_id: int
    account: str


class CheckChargeStatusMessage(BaseModel):
    record_id: int


class NoticeChargeResultMessage(BaseModel):
    order_id: str


class PurchaseTicketSubType(StrEnum):
    DELIVER_SUCCESS = "deliver_success"
    DELIVER_FAIL = "deliver_fail"
    REFUND_SUCCESS = "refund_success"
    REFUND_FAIL = "refund_fail"


class PurchaseTicketOpType(StrEnum):
    DELIVER = "ticket_deliver"
    REFUND = "ticket_refund"


class UnionPurchaseTicketMessageContent(BaseModel):
    extra_info: Optional[str] = Field(default="", description="额外信息")
    purchase_id: str = Field(..., description="采购单ID")
    sub_type: PurchaseTicketSubType = Field(..., description="子类型")
    ticket_id: str = Field(..., description="凭证ID")
    op_type: PurchaseTicketOpType = Field(..., description="操作类型", validation_alias=AliasChoices("type", "op_type"))

    @field_validator("op_type", "sub_type", mode="before")
    @classmethod
    def validate_enum_fields(cls, value, info):
        field_name = info.field_name
        enum_class = PurchaseTicketOpType if field_name == "op_type" else PurchaseTicketSubType

        if isinstance(value, str):
            try:
                return enum_class(value)
            except ValueError as e:
                valid_values = [e.value for e in enum_class]
                raise ValueError(f"无效的{field_name}值: '{value}'，有效值为: {valid_values}") from e
        return value


class UnionSKUSyncOpType(StrEnum):
    CREATE = "create"
    UPDATE = "update"
    REMOVE = "remove"


class UnionSKUSyncMessageContent(BaseModel):
    extra_info: Optional[str] = Field(default="", description="额外信息")
    item_id: str = Field(..., description="商品ID")
    sub_type: Optional[str] = Field(default="", description="子类型, activity_stock_empty,activity_promo_change")
    op_type: UnionSKUSyncOpType = Field(..., description="操作类型", validation_alias=AliasChoices("type", "op_type"))

    @field_validator("op_type", mode="before")
    @classmethod
    def validate_enum_fields(cls, value, info):
        if isinstance(value, str):
            return UnionSKUSyncOpType(value)


class SkuChargeMessageContent(BaseModel):
    record_id: int
    retry_count: int = Field(default=0, description="重试次数")
    last_retry_at: Optional[datetime] = Field(default=None, description="最后重试时间")


class BenefitsOrderNoticeMessageContent(BaseModel):
    order_id: str


class ProductOrderExportMessageContent(BaseModel):
    """产品订单导出消息内容"""

    export_id: str = Field(..., description="导出任务ID")
    user_id: str = Field(..., description="用户ID")
    user_name: str = Field(..., description="用户名称")
    filters: dict = Field(..., description="导出过滤条件")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


# 饿了么联盟采购单状态同步消息
UnionPurchaseTicketSyncMessage = message_creator(
    "UnionPurchaseTicketSyncMessage",
    UnionPurchaseTicketMessageContent,
    "eleme.union.purchase_ticket_sync",
    exchange_name="benefits.topic",
)
# 饿了么联盟SKU状态同步消息
UnionSKUSyncMessage = message_creator(
    "UnionSKUSyncMessage", UnionSKUSyncMessageContent, "eleme.union.sku_sync", exchange_name="benefits.topic"
)
# 权益SKU充值消息
SkuChargeMessage = message_creator(
    "SkuChargeMessage", SkuChargeMessageContent, "benefits.sku_charge", exchange_name="benefits.topic"
)
# 权益SKU充值状态消息
SkuChargeCheckMessage = message_creator(
    "SkuChargeCheckMessage", SkuChargeMessageContent, "benefits.sku_charge_check", exchange_name="benefits.topic"
)
# 权益订单通知消息
BenefitsOrderNoticeMessage = message_creator(
    "BenefitsOrderNoticeMessage",
    BenefitsOrderNoticeMessageContent,
    "benefits.order_notice",
    exchange_name="benefits.topic",
)
# 产品订单导出消息
ProductOrderExportMessage = message_creator(
    "ProductOrderExportMessage",
    ProductOrderExportMessageContent,
    "benefits.product_order_export",
    exchange_name="benefits.topic",
)
