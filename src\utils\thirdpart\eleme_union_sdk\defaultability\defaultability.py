from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest, TopApiClient


class Defaultability:

    def __init__(self, client: TopApiClient):
        self._client = client

    """
        本地生活媒体平台口碑选品筛选项集合
    """

    def alibaba_alsc_union_kb_item_promotion_filter_list(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广位查询
    """

    def alibaba_alsc_union_media_zone_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体平台口碑选品
    """

    def alibaba_alsc_union_kb_item_promotion(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么归因排查工具
    """

    def alibaba_alsc_union_eleme_tool_order_attrbute_check(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广位创建
    """

    def alibaba_alsc_union_media_zone_add(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单品推广门店列表（尚未开放）
    """

    def alibaba_alsc_union_eleme_promotion_itempromotion_store_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟商品编辑（未开放）
    """

    def alibaba_alsc_union_supply_outer_item_edit(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟商品状态修改（未开放）
    """

    def alibaba_alsc_union_supply_outer_item_state_update(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广口碑CPA用户反作弊订单数据报表
    """

    def alibaba_alsc_union_kbcpa_punish_order_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟媒体出资活动红包发放（已废弃，不再开放对接）
    """

    def alibaba_alsc_union_eleme_media_activity_coupon_send(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟商品发布（未开放）
    """

    def alibaba_alsc_union_supply_outer_item_publish(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        获取商家所在分组及其已授权(广播)消息topics
    """

    def taobao_tmc_user_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么推广其他推广链接获取，抖音等
    """

    def alibaba_alsc_union_eleme_promotion_otherchannel_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单店推广店铺列表
    """

    def alibaba_alsc_union_eleme_promotion_storepromotion_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单店推广单店铺查询
    """

    def alibaba_alsc_union_eleme_promotion_storepromotion_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么推广官方活动查询
    """

    def alibaba_alsc_union_eleme_promotion_officialactivity_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单店推广批量店铺查询
    """

    def alibaba_alsc_union_eleme_promotion_storepromotion_batch_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广口碑CPA用户维权订单数据报表
    """

    def alibaba_alsc_union_kbcpa_refund_order_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑商户列表
    """

    def alibaba_alsc_union_kb_store_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑门店商品列表
    """

    def alibaba_alsc_union_kb_store_item_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟商品获取分享链接（未开放）
    """

    def alibaba_alsc_union_supply_outer_item_share(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单品推广详情（餐饮）
    """

    def alibaba_alsc_union_eleme_promotion_itempromotion_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单品推广列表（餐饮）
    """

    def alibaba_alsc_union_eleme_promotion_itempromotion_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广订单明细查询
    """

    def alibaba_alsc_union_kbcpa_order_details_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单品推广详情（零售）
    """

    def alibaba_alsc_union_eleme_promotion_retail_itempromotion_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑商品详情
    """

    def alibaba_alsc_union_kb_item_detail_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑商品门店关系
    """

    def alibaba_alsc_union_kb_item_store_relation_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广订单明细报表查询
    """

    def alibaba_alsc_union_kbcpx_positive_order_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑门店详情
    """

    def alibaba_alsc_union_kb_item_store_detail_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么单品推广列表（零售）
    """

    def alibaba_alsc_union_eleme_promotion_retail_itempromotion_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广用户维权订单数据报表
    """

    def alibaba_alsc_union_kbcpx_refund_order_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体推广用户反作弊订单数据报表
    """

    def alibaba_alsc_union_kbcpx_punish_order_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟主站口令实时数据查询接口(未开放)
    """

    def alibaba_alsc_union_eleme_word_data_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券包采购列表
    """

    def alibaba_alsc_union_eleme_couponpackage_purchase_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券包采购凭证详情
    """

    def alibaba_alsc_union_eleme_couponpackage_purchase_ticket_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券包采购凭证撤销
    """

    def alibaba_alsc_union_eleme_couponpackage_purchase_ticket_refund(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券包采购凭证发放
    """

    def alibaba_alsc_union_eleme_couponpackage_purchase_ticket_create(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券包采购详情
    """

    def alibaba_alsc_union_eleme_couponpackage_purchase_detail_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券详情
    """

    def alibaba_alsc_union_eleme_coupon_detail_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券门店
    """

    def alibaba_alsc_union_eleme_coupon_store_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟卡券列表
    """

    def alibaba_alsc_union_eleme_coupon_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        饿了么卡券openapi订单退款
    """

    def alibaba_alsc_union_eleme_coupon_order_refund(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        饿了么卡券openapi订单查询
    """

    def alibaba_alsc_union_eleme_coupon_order_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟口碑商品列表
    """

    def alibaba_alsc_union_kb_item_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        饿了么卡券openapi订单创建
    """

    def alibaba_alsc_union_eleme_coupon_order_create(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有礼库存锁定
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_stock_lock(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有礼锁定库存释放
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_stock_release(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有礼店铺列表
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_query(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有礼店铺详情
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_detail_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        饿了么卡券openapi订单支付
    """

    def alibaba_alsc_union_eleme_coupon_order_pay(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        发布单条消息
    """

    def taobao_tmc_message_produce(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地生活媒体创建商品推广链接（废弃勿用）
    """

    def alibaba_alsc_union_kb_item_promotion_share_create(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        取消用户的消息服务
    """

    def taobao_tmc_user_cancel(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        为已授权的用户开通消息服务
    """

    def taobao_tmc_user_permit(self, request: BaseRequest, session: str):
        return self._client.execute_with_session(
            request.get_api_name(), request.to_dict(), request.get_file_param_dict(), session
        )

    """
        本地联盟饿了么推广官方活动生成微信短链
    """

    def alibaba_alsc_union_eleme_promotion_officialactivity_wxscheme(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟推广链接推广对象解析
    """

    def alibaba_alsc_union_promotion_link_analyze(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有礼诊断
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_diagnose(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        本地联盟饿了么评价有身份绑定
    """

    def alibaba_alsc_union_eleme_storepromotion_reviewbwc_bind_link_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())
