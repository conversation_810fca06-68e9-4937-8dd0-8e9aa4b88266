from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponOrderPayRequest(BaseRequest):

    def __init__(self, order_pay_dto: object = None):
        """
        订单支付对象
        """
        self._order_pay_dto = order_pay_dto

    @property
    def order_pay_dto(self):
        return self._order_pay_dto

    @order_pay_dto.setter
    def order_pay_dto(self, order_pay_dto):
        if isinstance(order_pay_dto, object):
            self._order_pay_dto = order_pay_dto
        else:
            raise TypeError("order_pay_dto must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.coupon.order.pay"

    def to_dict(self):
        request_dict = {}
        if self._order_pay_dto is not None:
            request_dict["order_pay_dto"] = convert_struct(self._order_pay_dto)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponOrderPayCouponOrderPayDto:
    def __init__(self, outer_order_id: str = None, biz_order_id: str = None):
        """
        渠道订单号
        """
        self.outer_order_id = outer_order_id
        """
            本地生活订单号
        """
        self.biz_order_id = biz_order_id
