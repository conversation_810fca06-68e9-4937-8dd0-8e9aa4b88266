# encoding: utf-8
# src/interfaces/growth_hacker/schemas.py
# created: 2025-07-27 19:09:06

from pydantic import BaseModel, Field

from src.infrastructures.rabbitmq import message_creator


class TaskMessageContent(BaseModel):
    phone: str = Field(..., description="用户手机号")
    city: str = Field(default="", description="城市")
    lat: str | float = Field(..., description="纬度")
    lng: str | float = Field(..., description="经度")
    access_url: str = Field(..., description="访问URL")
    task_id: str = Field(..., description="任务ID")
    batch_name: str = Field(default="", description="批次名称")


TaskMessage = message_creator("GrowthHackerTaskMessage", TaskMessageContent, "gh.task", exchange_name="tasks")
