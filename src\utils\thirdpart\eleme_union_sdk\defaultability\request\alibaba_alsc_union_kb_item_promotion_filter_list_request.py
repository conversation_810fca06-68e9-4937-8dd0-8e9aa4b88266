from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbItemPromotionFilterListRequest(BaseRequest):

    def __init__(self, filter_type: str = None, biz_unit: int = None):
        """
        获取筛选项集合的类型。city-城市列表；category-类目列表
        """
        self._filter_type = filter_type
        """
            1-cpa,2-cps.默认不填为cpa
        """
        self._biz_unit = biz_unit

    @property
    def filter_type(self):
        return self._filter_type

    @filter_type.setter
    def filter_type(self, filter_type):
        if isinstance(filter_type, str):
            self._filter_type = filter_type
        else:
            raise TypeError("filter_type must be str")

    @property
    def biz_unit(self):
        return self._biz_unit

    @biz_unit.setter
    def biz_unit(self, biz_unit):
        if isinstance(biz_unit, int):
            self._biz_unit = biz_unit
        else:
            raise TypeError("biz_unit must be int")

    def get_api_name(self):
        return "alibaba.alsc.union.kb.item.promotion.filter.list"

    def to_dict(self):
        request_dict = {}
        if self._filter_type is not None:
            request_dict["filter_type"] = convert_basic(self._filter_type)

        if self._biz_unit is not None:
            request_dict["biz_unit"] = convert_basic(self._biz_unit)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
