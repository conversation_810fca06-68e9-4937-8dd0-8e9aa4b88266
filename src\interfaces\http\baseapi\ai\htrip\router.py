from typing import TYPE_CHECKING, List, Optional

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, HTTPException
from loguru import logger
from pydantic import BaseModel, Field

from src.containers import Container
from src.infrastructures.fastapi.response import BaseResponse

from .authorization import agent_api_authentication

if TYPE_CHECKING:
    from redis.asyncio import Redis

router = APIRouter()


class HotelLocation(BaseModel):
    latitude: float = Field(..., description="酒店纬度")
    longitude: float = Field(..., description="酒店经度")


class UserInfo(BaseModel):
    phone: Optional[str] = Field(None, description="用户手机号", min_length=11, max_length=11)


class ChatRequest(BaseModel):
    hotel_id: str = Field(..., description="酒店ID", min_length=1)
    hotel_location: HotelLocation = Field(..., description="酒店位置信息")
    room_id: str = Field(..., description="房间ID", min_length=1)
    user_info: UserInfo = Field(..., description="用户信息")
    prompt: str = Field(..., description="用户输入的提示词", min_length=1)


class ShopItem(BaseModel):
    picture: str
    price: str
    title: str


class RecommendReason(BaseModel):
    content: str


class ShopInfo(BaseModel):
    category: str
    indistinct_monthly_sales: str
    h5_qrcode_url: str
    service_rating: str
    shop_logo: str
    title: str
    delivery_distance: int
    delivery_price: str
    delivery_time: int
    recommend_description: str
    recommend_reasons: List[RecommendReason]
    items: Optional[List[ShopItem]] = None


class CouponInfo(BaseModel):
    title: str
    picture: str
    description: str
    h5_qrcode_url: str


class ChatData(BaseModel):
    content: str
    items: Optional[List[dict]] = None
    itemtype: str


class ChatResponse(BaseResponse):
    data: ChatData


# Mock数据
MOCK_SHOPS = [
    {
        "category": "甜点饮品",
        "indistinct_monthly_sales": "800+",
        "h5_qrcode_url": "https://base.hicaspian.com/deliveryLanding?from=beaverAI&shopId=E3728035523094105251",
        "service_rating": "5.0",
        "shop_logo": "https://img.alicdn.com/imgextra/i3/2216744392149/O1CN01BYD07A1RkI1so3OBN_!!2216744392149-2-koubei.png",
        "title": "Blues千层蛋糕甜品店",
        "delivery_distance": 2909,
        "delivery_price": "30.0",
        "delivery_time": 27,
        "recommend_description": "#蛋糕控必看🔥！# 千层蛋糕，绝绝子好吃！🥰单人套餐+主食自选，太爱了！🤤",
        "recommend_reasons": [{"content": "蛋糕中的人气飙升店铺"}, {"content": "味道很不错,毛巾卷很好吃喜欢抹茶的"}],
        "skus": [
            {
                "picture": "https://img.alicdn.com/i4/2216744392149/O1CN01FW5MhG1RkI29xFNjT_!!2216744392149-0-koubei.jpg",
                "price": "18.9",
                "title": "醇厚  |  榛果巧克力千层-下午茶 甜品 小蛋糕",
            },
            {
                "origin_price": "21.0",
                "picture": "https://img.alicdn.com/i1/2215931037395/O1CN01l88qPO24UxkiOylYl_!!2215931037395-0-koubei.jpg",
                "price": "19.0",
                "title": "澳芒  |  芒果多多千层",
            },
            {
                "picture": "https://img.alicdn.com/i4/2215931037395/O1CN01XiGp0R24Uxkg3chRr_!!2215931037395-0-koubei.jpg",
                "price": "11.0",
                "title": "mini  |  芒果班戟",
            },
        ],
    }
]

MOCK_COUPONS = [
    {
        "title": "小谷姐姐全国品牌日",
        "picture": "https://img.alicdn.com/imgextra/i1/6000000002393/O1CN01OFUzh51TY2ordekKE_!!6000000002393-2-o2oad.png",
        "description": "满30减8，叠加吃货红包最高减18元~",
        "h5_qrcode_url": "https://r.ele.me/midd#wx?id=stkxs25m&scene=4be182cfe4d041e6a5a46b9b229d469c",
    }
]


@router.post("/chat", response_model=ChatResponse)
@inject
async def chat(
    request: ChatRequest,
    agent: str = Depends(agent_api_authentication),
    redis: "Redis" = Depends(Provide[Container.infrastructures.redis_manager.provided.client]),
) -> ChatResponse:
    """
    处理用户的聊天请求，根据prompt返回相应的内容

    Args:
        request: 包含用户信息、酒店信息和提示词的请求体
        agent_token: AI agent的认证token
        redis: Redis客户端实例

    Returns:
        ChatResponse: 包含回复内容的响应

    Raises:
        HTTPException: 当处理请求出现错误时抛出
    """
    try:
        prompt = request.prompt.lower()
        logger.info(f"HTRIPAI: {agent} request: {request.model_dump()}")
        # 判断用户意图
        if "优惠券" in prompt or "券" in prompt:
            return ChatResponse(
                data=ChatData(
                    content="已帮您找到几张大额优惠券，你可以扫描电视上的二维码进行领取使用",
                    items=MOCK_COUPONS,
                    itemtype="coupons",
                )
            )
        elif "外卖" in prompt or "吃" in prompt:
            return ChatResponse(
                data=ChatData(
                    content="找到了附近几家评分很高的外卖店铺，你可以扫描电视上的二维码进行下单",
                    items=MOCK_SHOPS,
                    itemtype="shop",
                )
            )
        else:
            return ChatResponse(
                data=ChatData(content="请问您是想查询优惠券还是点外卖呢？", items=None, itemtype="other")
            )
    except Exception as e:
        # 记录错误日志
        logger.error(f"处理聊天请求时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="处理请求时发生错误") from e
