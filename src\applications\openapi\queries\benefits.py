# encoding: utf-8
# src/applications/openapi/queries/benefits.py
# created: 2025-07-29 08:36:20

from typing import TYPE_CHECKING

from src.repositories.customer import BenefitsRepository

from ..dto import BenefitsOrderDTO, BenefitsProductDTO
from ..errors import BenefitOrderNotFoundError

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsProductService
    from src.domains.customer.entities import CustomerEntity


class BenefitsQueryService:

    def __init__(
        self,
        product_service: "BenefitsProductService",
        customer_benefits_repo: "BenefitsRepository",
    ):
        self.product_service = product_service
        self.customer_benefits_repo = customer_benefits_repo

    async def get_order_by_order_id(
        self,
        customer: "CustomerEntity",
        order_id: str,
    ) -> "BenefitsOrderDTO":
        """根据订单id获取订单, 并检查订单来源是否匹配"""
        order = await self.product_service.get_order_by_order_id(order_id)
        if not customer.validate_source(order.app_id, order.tenant_id):  # type: ignore
            raise BenefitOrderNotFoundError
        return BenefitsOrderDTO.model_validate(order.model_dump())  # type: ignore

    async def get_order_by_out_order_id(
        self,
        customer: "CustomerEntity",
        out_order_id: str,
    ) -> "BenefitsOrderDTO":
        """根据第三方订单号获取订单, 并检查订单来源是否匹配"""
        order = await self.product_service.get_order_by_out_order_id(out_order_id)
        if not customer.validate_source(order.app_id, order.tenant_id):  # type: ignore
            raise BenefitOrderNotFoundError
        return BenefitsOrderDTO.model_validate(order.model_dump())  # type: ignore

    async def get_products(self, customer: "CustomerEntity") -> list[BenefitsProductDTO]:
        """获取客户权益产品列表"""
        products = await self.customer_benefits_repo.gets_all_by_customer(customer_code=customer.code)
        return [
            BenefitsProductDTO(
                id=product.id,
                code=product.product.code,
                name=product.product.name,
                price=product.sale_price,
                stock=product.stock,
            )
            for product in products
        ]
