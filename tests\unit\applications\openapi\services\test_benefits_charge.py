# encoding: utf-8
# tests/unit/applications/openapi/services/test_benefits_charge.py
# created: 2025-08-02 17:15:00

"""OpenAPI 权益充值服务测试"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.applications.openapi.dto import BenefitsOrderDTO
from src.applications.openapi.errors import (
    BenefitOrderNotFoundError, 
    BenefitProductNotFoundError,
    BenefitProductNotUseableError
)
from src.applications.openapi.services.benefits_charge import BenefitsChargeService
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory


@pytest.mark.benefits
@pytest.mark.unit
class TestBenefitsChargeService(BaseOpenapiUnitTest):
    """测试权益充值服务"""

    @pytest.fixture
    def mock_customer_benefit_repo(self):
        """Mock 客户权益仓储"""
        return AsyncMock()

    @pytest.fixture
    def mock_benefits_product_service(self):
        """Mock 权益产品服务"""
        return AsyncMock()

    @pytest.fixture
    def mock_benefits_product_order_repo(self):
        """Mock 权益产品订单仓储"""
        return AsyncMock()

    @pytest.fixture
    def benefits_charge_service(
        self,
        mock_customer_benefit_repo,
        mock_benefits_product_service,
        mock_benefits_product_order_repo,
    ):
        """权益充值服务实例"""
        return BenefitsChargeService(
            customer_benefit_repo=mock_customer_benefit_repo,
            benefits_product_service=mock_benefits_product_service,
            benefits_product_order_repo=mock_benefits_product_order_repo,
        )

    @pytest.fixture
    def sample_customer(self):
        """示例客户"""
        return CustomerEntity(
            id=1,
            code="customer_001",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=50000,  # 500元
        )

    @pytest.fixture
    def sample_benefit_data(self):
        """示例权益数据"""
        return {
            "id": 1,
            "customer_id": 1,
            "sale_price": 1000,  # 10元
            "stock": 100,
            "product": MagicMock(code="benefit_product_001"),
        }

    @pytest.fixture
    def sample_order_data(self):
        """示例订单数据"""
        return {
            "id": 1,
            "order_id": "ORDER_123456",
            "out_order_id": "THIRD_789012",
            "status": "processing",
            "product_code": "benefit_product_001",
            "price": 1000,
            "account": "test_account",
            "notify_url": "https://test.com/notify",
        }

    @pytest.mark.asyncio
    async def test_init(
        self,
        mock_customer_benefit_repo,
        mock_benefits_product_service,
        mock_benefits_product_order_repo,
    ):
        """测试服务初始化"""
        service = BenefitsChargeService(
            customer_benefit_repo=mock_customer_benefit_repo,
            benefits_product_service=mock_benefits_product_service,
            benefits_product_order_repo=mock_benefits_product_order_repo,
        )
        
        assert service.customer_benefit_repo == mock_customer_benefit_repo
        assert service.benefits_product_service == mock_benefits_product_service
        assert service.benefits_product_order_repo == mock_benefits_product_order_repo

    @patch('tortoise.transactions.atomic')
    @patch('src.applications.openapi.services.benefits_charge.BenefitProductEntity.from_model')
    @pytest.mark.asyncio
    async def test_charge_benefit_success_financial_mode(
        self,
        mock_from_model,
        mock_atomic,
        benefits_charge_service,
        mock_customer_benefit_repo,
        mock_benefits_product_service,
        sample_customer,
        sample_benefit_data,
        sample_order_data,
    ):
        """测试权益充值成功 - 金融模式"""
        # Make atomic decorator pass through
        mock_atomic.return_value = lambda func: func
        
        product_code = "benefit_product_001"
        account = "test_account"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 创建mock权益
        mock_benefit = MagicMock(**sample_benefit_data)
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.return_value = mock_benefit
        
        # 创建mock订单
        mock_order = MagicMock()
        mock_order.model_dump.return_value = sample_order_data
        mock_benefits_product_service.charge_product.return_value = mock_order
        
        # Configure product mock
        mock_product = MagicMock()
        mock_product.sale_mode = "finacial"
        mock_product.sale_price = 1000
        mock_product.code = product_code
        mock_from_model.return_value = mock_product
        
        with patch('src.domains.customer.entities.CustomerEntity.validate_benefit', return_value=True) as mock_validate, \
             patch('src.domains.customer.entities.CustomerEntity.payment_benefit', return_value=49000) as mock_payment:
            
            # 执行充值
            result = await benefits_charge_service.charge_benefit(
                customer=sample_customer,
                product_code=product_code,
                account=account,
                out_order_id=out_order_id,
                notify_url=notify_url,
            )
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == sample_order_data["order_id"]
        assert result.status == sample_order_data["status"]
        
        # 验证方法调用
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.assert_called_once_with(
            sample_customer.id, product_code
        )
        mock_validate.assert_called_once()
        mock_payment.assert_called_once_with(mock_product)

    @patch('tortoise.transactions.atomic')
    @patch('src.applications.openapi.services.benefits_charge.BenefitProductEntity.from_model')
    @pytest.mark.asyncio
    async def test_charge_benefit_success_stock_mode(
        self,
        mock_from_model,
        mock_atomic,
        benefits_charge_service,
        mock_customer_benefit_repo,
        mock_benefits_product_service,
        sample_customer,
        sample_benefit_data,
        sample_order_data,
    ):
        """测试权益充值成功 - 库存模式"""
        # Make atomic decorator pass through
        mock_atomic.return_value = lambda func: func
        
        product_code = "benefit_product_001"
        account = "test_account"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 创建mock权益
        mock_benefit = MagicMock(**sample_benefit_data)
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.return_value = mock_benefit
        
        # 创建mock订单
        mock_order = MagicMock()
        mock_order.model_dump.return_value = sample_order_data
        mock_benefits_product_service.charge_product.return_value = mock_order
        
        # Configure product mock
        mock_product = MagicMock()
        mock_product.sale_mode = "stock"
        mock_product.sale_price = 1000
        mock_product.code = product_code
        mock_product.decrease_stock = MagicMock()
        mock_from_model.return_value = mock_product
        
        with patch('src.domains.customer.entities.CustomerEntity.validate_benefit', return_value=True) as mock_validate:
            
            # 执行充值
            result = await benefits_charge_service.charge_benefit(
                customer=sample_customer,
                product_code=product_code,
                account=account,
                out_order_id=out_order_id,
                notify_url=notify_url,
            )
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        
        # 验证库存减少被调用
        mock_product.decrease_stock.assert_called_once()

    @patch('tortoise.transactions.atomic')
    @pytest.mark.asyncio
    async def test_charge_benefit_product_not_found(
        self,
        mock_atomic,
        benefits_charge_service,
        mock_customer_benefit_repo,
        sample_customer,
    ):
        """测试权益充值失败 - 产品不存在"""
        # Make atomic decorator pass through
        mock_atomic.return_value = lambda func: func
        
        product_code = "nonexistent_product"
        account = "test_account"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 设置mock返回值 - 产品不存在
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.return_value = None
        
        # 验证抛出异常
        with pytest.raises(BenefitProductNotFoundError):
            await benefits_charge_service.charge_benefit(
                customer=sample_customer,
                product_code=product_code,
                account=account,
                out_order_id=out_order_id,
                notify_url=notify_url,
            )

    @patch('tortoise.transactions.atomic')
    @patch('src.applications.openapi.services.benefits_charge.BenefitProductEntity.from_model')
    @pytest.mark.asyncio
    async def test_charge_benefit_product_not_useable(
        self,
        mock_from_model,
        mock_atomic,
        benefits_charge_service,
        mock_customer_benefit_repo,
        sample_customer,
        sample_benefit_data,
    ):
        """测试权益充值失败 - 产品不可用"""
        # Make atomic decorator pass through
        mock_atomic.return_value = lambda func: func
        
        product_code = "benefit_product_001"
        account = "test_account"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 创建mock权益
        mock_benefit = MagicMock(**sample_benefit_data)
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.return_value = mock_benefit
        
        # Configure product mock
        mock_product = MagicMock()
        mock_from_model.return_value = mock_product
        
        with patch.object(sample_customer, 'validate_benefit', return_value=False) as mock_validate:
            # 验证抛出异常
            with pytest.raises(BenefitProductNotUseableError):
                await benefits_charge_service.charge_benefit(
                    customer=sample_customer,
                    product_code=product_code,
                    account=account,
                    out_order_id=out_order_id,
                    notify_url=notify_url,
                )

    @pytest.mark.asyncio
    async def test_charge_benefit_again_success(
        self,
        benefits_charge_service,
        mock_benefits_product_order_repo,
        sample_customer,
        sample_order_data,
    ):
        """测试重新充值权益成功"""
        order_id = "ORDER_123456"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 创建mock订单
        mock_order = MagicMock()
        mock_order.out_order_id = out_order_id
        mock_order.model_dump.return_value = sample_order_data
        mock_benefits_product_order_repo.get_by_order_id.return_value = mock_order
        
        # 执行重新充值
        result = await benefits_charge_service.charge_benefit_again(
            customer=sample_customer,
            order_id=order_id,
            out_order_id=out_order_id,
            notify_url=notify_url,
        )
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == sample_order_data["order_id"]
        
        # 验证方法调用
        mock_benefits_product_order_repo.get_by_order_id.assert_called_once_with(order_id)

    @pytest.mark.asyncio
    async def test_charge_benefit_again_order_not_found(
        self,
        benefits_charge_service,
        mock_benefits_product_order_repo,
        sample_customer,
    ):
        """测试重新充值权益失败 - 订单不存在"""
        order_id = "ORDER_123456"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 设置mock返回值 - 订单不存在
        mock_benefits_product_order_repo.get_by_order_id.return_value = None
        
        # 验证抛出异常
        with pytest.raises(BenefitOrderNotFoundError):
            await benefits_charge_service.charge_benefit_again(
                customer=sample_customer,
                order_id=order_id,
                out_order_id=out_order_id,
                notify_url=notify_url,
            )

    @pytest.mark.asyncio
    async def test_charge_benefit_again_order_id_mismatch(
        self,
        benefits_charge_service,
        mock_benefits_product_order_repo,
        sample_customer,
    ):
        """测试重新充值权益失败 - 订单ID不匹配"""
        order_id = "ORDER_123456"
        out_order_id = "THIRD_789012"
        notify_url = "https://test.com/notify"
        
        # 创建mock订单 - 但out_order_id不匹配
        mock_order = MagicMock()
        mock_order.out_order_id = "DIFFERENT_ID"
        mock_benefits_product_order_repo.get_by_order_id.return_value = mock_order
        
        # 验证抛出异常
        with pytest.raises(BenefitOrderNotFoundError):
            await benefits_charge_service.charge_benefit_again(
                customer=sample_customer,
                order_id=order_id,
                out_order_id=out_order_id,
                notify_url=notify_url,
            )


@pytest.mark.benefits
@pytest.mark.integration
class TestBenefitsChargeServiceIntegration:
    """权益充值服务集成测试"""

    @patch('tortoise.transactions.atomic')
    @patch('src.applications.openapi.services.benefits_charge.BenefitProductEntity.from_model')
    @pytest.mark.asyncio
    async def test_complete_charge_flow(self, mock_from_model, mock_atomic):
        """测试完整的充值流程"""
        # Make atomic decorator pass through
        mock_atomic.return_value = lambda func: func
        
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data(balance=50000)
        order_data = OpenAPITestDataFactory.create_order_data()
        
        # 创建mock对象
        mock_customer_benefit_repo = AsyncMock()
        mock_benefits_product_service = AsyncMock()
        mock_benefits_product_order_repo = AsyncMock()
        
        # 创建服务
        service = BenefitsChargeService(
            customer_benefit_repo=mock_customer_benefit_repo,
            benefits_product_service=mock_benefits_product_service,
            benefits_product_order_repo=mock_benefits_product_order_repo,
        )
        
        # 创建客户实体
        customer = CustomerEntity(**customer_data)
        
        # 设置mock返回值
        mock_benefit = MagicMock(
            id=1,
            customer_id=customer.id,
            sale_price=1000,
            stock=100,
            product=MagicMock(code="test_product"),
        )
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.return_value = mock_benefit
        
        mock_order = MagicMock()
        mock_order.model_dump.return_value = order_data
        mock_benefits_product_service.charge_product.return_value = mock_order
        
        # Configure product mock
        mock_product = MagicMock()
        mock_product.sale_mode = "finacial"
        mock_product.sale_price = 1000
        mock_product.code = "test_product"
        mock_from_model.return_value = mock_product
        
        with patch('src.domains.customer.entities.CustomerEntity.validate_benefit', return_value=True) as mock_validate, \
             patch('src.domains.customer.entities.CustomerEntity.payment_benefit', return_value=49000) as mock_payment:
            
            # 执行充值
            result = await service.charge_benefit(
                customer=customer,
                product_code="test_product",
                account="test_account",
                out_order_id="test_order_id",
                notify_url="https://test.com/notify",
            )
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == order_data["order_id"]
        
        # 验证调用链
        mock_customer_benefit_repo.get_by_customer_and_product_for_update.assert_called_once()
        mock_validate.assert_called_once()
        mock_payment.assert_called_once()
        mock_benefits_product_service.charge_product.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """测试错误传播机制"""
        # 创建minimal服务实例
        service = BenefitsChargeService(
            customer_benefit_repo=AsyncMock(),
            benefits_product_service=AsyncMock(),
            benefits_product_order_repo=AsyncMock(),
        )
        
        customer = CustomerEntity(
            id=1, code="test", name="test", description="test",
            app_id="test", tenant_id="test", balance=50000
        )
        
        # 测试BenefitOrderNotFoundError
        service.customer_benefit_repo.get_by_customer_and_product_for_update.return_value = None
        
        with patch('tortoise.transactions.atomic') as mock_atomic:
            mock_atomic.return_value = lambda func: func
            
            with pytest.raises(BenefitProductNotFoundError):
                await service.charge_benefit(
                    customer=customer,
                    product_code="test_product",
                    account="test_account",
                    out_order_id="test_order",
                    notify_url="https://test.com/notify",
                )