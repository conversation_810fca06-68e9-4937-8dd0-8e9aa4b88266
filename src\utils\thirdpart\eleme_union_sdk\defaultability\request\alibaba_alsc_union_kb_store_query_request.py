from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbStoreQueryRequest(BaseRequest):

    def __init__(
        self,
        page_number: int = None,
        page_size: int = None,
        session_id: str = None,
        biz_type: str = None,
        sort_type: str = None,
        city_id: str = None,
        kb_category_2_ids: str = None,
        kb_category_3_ids: str = None,
        longitude: str = None,
        latitude: str = None,
        range: int = None,
        kb_category_1_ids: str = None,
    ):
        """
        页码（默认1）
        """
        self._page_number = page_number
        """
            每页数目（默认10）
        """
        self._page_size = page_size
        """
            会话ID（分页场景首次请求结果返回，后续请求必须携带，服务根据session_id相同请求次数自动翻页返回）
        """
        self._session_id = session_id
        """
            场景类型（"kb_natural";）
        """
        self._biz_type = biz_type
        """
            排序类型，默认normal（"normal"-门店创建时间倒序;"distance_asc"-距离最近）
        """
        self._sort_type = sort_type
        """
            城市ID
        """
        self._city_id = city_id
        """
            口碑二级类目（逗号分隔）
        """
        self._kb_category_2_ids = kb_category_2_ids
        """
            口碑三级类目（逗号分隔）
        """
        self._kb_category_3_ids = kb_category_3_ids
        """
            经度（经纬度、范围配合使用）
        """
        self._longitude = longitude
        """
            纬度（经纬度、范围配合使用）
        """
        self._latitude = latitude
        """
            范围（单位：米，经纬度、范围配合使用）
        """
        self._range = range
        """
            口碑一级类目（逗号分隔）
        """
        self._kb_category_1_ids = kb_category_1_ids

    @property
    def page_number(self):
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        if isinstance(page_number, int):
            self._page_number = page_number
        else:
            raise TypeError("page_number must be int")

    @property
    def page_size(self):
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        if isinstance(page_size, int):
            self._page_size = page_size
        else:
            raise TypeError("page_size must be int")

    @property
    def session_id(self):
        return self._session_id

    @session_id.setter
    def session_id(self, session_id):
        if isinstance(session_id, str):
            self._session_id = session_id
        else:
            raise TypeError("session_id must be str")

    @property
    def biz_type(self):
        return self._biz_type

    @biz_type.setter
    def biz_type(self, biz_type):
        if isinstance(biz_type, str):
            self._biz_type = biz_type
        else:
            raise TypeError("biz_type must be str")

    @property
    def sort_type(self):
        return self._sort_type

    @sort_type.setter
    def sort_type(self, sort_type):
        if isinstance(sort_type, str):
            self._sort_type = sort_type
        else:
            raise TypeError("sort_type must be str")

    @property
    def city_id(self):
        return self._city_id

    @city_id.setter
    def city_id(self, city_id):
        if isinstance(city_id, str):
            self._city_id = city_id
        else:
            raise TypeError("city_id must be str")

    @property
    def kb_category_2_ids(self):
        return self._kb_category_2_ids

    @kb_category_2_ids.setter
    def kb_category_2_ids(self, kb_category_2_ids):
        if isinstance(kb_category_2_ids, str):
            self._kb_category_2_ids = kb_category_2_ids
        else:
            raise TypeError("kb_category_2_ids must be str")

    @property
    def kb_category_3_ids(self):
        return self._kb_category_3_ids

    @kb_category_3_ids.setter
    def kb_category_3_ids(self, kb_category_3_ids):
        if isinstance(kb_category_3_ids, str):
            self._kb_category_3_ids = kb_category_3_ids
        else:
            raise TypeError("kb_category_3_ids must be str")

    @property
    def longitude(self):
        return self._longitude

    @longitude.setter
    def longitude(self, longitude):
        if isinstance(longitude, str):
            self._longitude = longitude
        else:
            raise TypeError("longitude must be str")

    @property
    def latitude(self):
        return self._latitude

    @latitude.setter
    def latitude(self, latitude):
        if isinstance(latitude, str):
            self._latitude = latitude
        else:
            raise TypeError("latitude must be str")

    @property
    def range(self):
        return self._range

    @range.setter
    def range(self, range):
        if isinstance(range, int):
            self._range = range
        else:
            raise TypeError("range must be int")

    @property
    def kb_category_1_ids(self):
        return self._kb_category_1_ids

    @kb_category_1_ids.setter
    def kb_category_1_ids(self, kb_category_1_ids):
        if isinstance(kb_category_1_ids, str):
            self._kb_category_1_ids = kb_category_1_ids
        else:
            raise TypeError("kb_category_1_ids must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.kb.store.query"

    def to_dict(self):
        request_dict = {}
        if self._page_number is not None:
            request_dict["page_number"] = convert_basic(self._page_number)

        if self._page_size is not None:
            request_dict["page_size"] = convert_basic(self._page_size)

        if self._session_id is not None:
            request_dict["session_id"] = convert_basic(self._session_id)

        if self._biz_type is not None:
            request_dict["biz_type"] = convert_basic(self._biz_type)

        if self._sort_type is not None:
            request_dict["sort_type"] = convert_basic(self._sort_type)

        if self._city_id is not None:
            request_dict["city_id"] = convert_basic(self._city_id)

        if self._kb_category_2_ids is not None:
            request_dict["kb_category_2_ids"] = convert_basic(self._kb_category_2_ids)

        if self._kb_category_3_ids is not None:
            request_dict["kb_category_3_ids"] = convert_basic(self._kb_category_3_ids)

        if self._longitude is not None:
            request_dict["longitude"] = convert_basic(self._longitude)

        if self._latitude is not None:
            request_dict["latitude"] = convert_basic(self._latitude)

        if self._range is not None:
            request_dict["range"] = convert_basic(self._range)

        if self._kb_category_1_ids is not None:
            request_dict["kb_category_1_ids"] = convert_basic(self._kb_category_1_ids)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
