# encoding: utf-8
# src/domains/customer/entities/secrect.py
# created: 2025-07-29 17:34:30

from typing import TYPE_CHECKING

from pydantic import BaseModel

if TYPE_CHECKING:
    from src.databases.models.customer import CustomerSecret


class CustomerSecretEntity(BaseModel):

    id: int
    name: str
    access_key: str
    access_secret: str
    jwt_secrect: str
    api_token: str

    @classmethod
    async def from_model(cls, secret: "CustomerSecret") -> "CustomerSecretEntity":
        return cls(
            id=secret.id,
            name=secret.name,
            access_key=secret.access_key,
            access_secret=secret.access_secret,
            jwt_secrect=secret.app_key,
            api_token=secret.app_secret,
        )
