# encoding: utf-8
# src/infrastructures/browsers/types.py
# created: 2025-08-08 00:03:19

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MobileModel(Enum):
    """iPhone 型号枚举"""

    IPHONE_12 = "iPhone 12"
    IPHONE_13 = "iPhone 13"
    IPHONE_14 = "iPhone 14"
    IPHONE_15 = "iPhone 15"
    IPHONE_15_PRO = "iPhone 15 Pro"
    IPHONE_15_PRO_MAX = "iPhone 15 Pro Max"


class WebViewType(Enum):
    """WebView 类型枚举"""

    SAFARI = "safari"
    WECHAT = "wechat"
    ALIPAY = "alipay"
    ELEME = "eleme"


class DeviceInfo(BaseModel):
    """设备配置"""

    name: str
    viewport: dict[str, int]
    user_agent: str
    device_scale_factor: float
    is_mobile: bool
    has_touch: bool


class WebViewInfo(BaseModel):
    """WebView 配置"""

    name: str
    user_agent_suffix: str
    extra_headers: dict[str, str]
    javascript_enabled: bool = True


DEVICE_INFO = {
    MobileModel.IPHONE_12: DeviceInfo(
        name="iPhone 12",
        viewport={"width": 390, "height": 844},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
    MobileModel.IPHONE_13: DeviceInfo(
        name="iPhone 13",
        viewport={"width": 390, "height": 844},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
    MobileModel.IPHONE_14: DeviceInfo(
        name="iPhone 14",
        viewport={"width": 390, "height": 844},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
    MobileModel.IPHONE_15: DeviceInfo(
        name="iPhone 15",
        viewport={"width": 393, "height": 852},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
    MobileModel.IPHONE_15_PRO: DeviceInfo(
        name="iPhone 15 Pro",
        viewport={"width": 393, "height": 852},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
    MobileModel.IPHONE_15_PRO_MAX: DeviceInfo(
        name="iPhone 15 Pro Max",
        viewport={"width": 430, "height": 932},
        user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        device_scale_factor=3.0,
        is_mobile=True,
        has_touch=True,
    ),
}


WEBVIEW_CONFIGS = {
    WebViewType.SAFARI: WebViewInfo(
        name="Safari",
        user_agent_suffix="",
        extra_headers={
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        },
    )
}


# ================================
# 附加数据模型（从 schemas.py 合并）
# ================================


class ProxyConfig(BaseModel):
    """代理配置"""

    server: str
    username: Optional[str] = None
    password: Optional[str] = None


class GeolocationData(BaseModel):
    """地理位置数据"""

    latitude: float = Field(..., ge=-90, le=90, description="纬度")
    longitude: float = Field(..., ge=-180, le=180, description="经度")
    accuracy: Optional[float] = Field(default=10.0, ge=0, description="精度(米)")
    altitude: Optional[float] = Field(default=None, description="海拔(米)")


class ExtraData(BaseModel):
    """额外数据"""

    geolocation: GeolocationData
    local_storage: Dict[str, str]
    session_storage: Dict[str, str]
    cookies: List[Dict[str, Any]]


# 浏览器管理相关数据模型
@dataclass
class BrowserSessionConfig:
    """浏览器会话配置"""

    proxy: Optional[ProxyConfig] = None
    device_config: Optional[DeviceInfo] = None
    webview_config: Optional[WebViewInfo] = None
    extra_data: Optional[ExtraData] = None


# ================================
# 状态枚举
# ================================


class ManagerState(Enum):
    """管理器状态"""

    CREATED = "created"
    INITIALIZING = "initializing"
    RUNNING = "running"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"


class SessionState(Enum):
    """会话状态"""

    CREATED = "created"
    INITIALIZING = "initializing"
    READY = "ready"
    BUSY = "busy"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"


class PoolState(Enum):
    """浏览器池状态"""

    CREATED = "created"
    INITIALIZING = "initializing"
    RUNNING = "running"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"


class BrowserInstanceState(Enum):
    """浏览器实例状态"""

    CREATED = "created"
    INITIALIZING = "initializing"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"
    CLOSED = "closed"


# ================================
# 结果类
# ================================


@dataclass
class NavigationResult:
    """导航结果"""

    success: bool
    url: str
    duration: Optional[float] = None
    error: Optional[str] = None


@dataclass
class ScriptResult:
    """脚本执行结果"""

    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None


@dataclass
class ScreenshotResult:
    """截图结果"""

    success: bool
    filepath: Optional[str] = None
    error: Optional[str] = None


@dataclass
class PageContentResult:
    """页面内容结果"""

    success: bool
    html_content: Optional[str] = None
    text_content: Optional[str] = None
    url: Optional[str] = None
    error: Optional[str] = None


@dataclass
class BrowserInstanceData:
    """浏览器实例数据"""

    cookies: List[Dict[str, Any]]
    local_storage: Dict[str, str]
    session_storage: Dict[str, str]
    device_config: Dict[str, Any]
    webview_config: Dict[str, Any]

    @classmethod
    def empty(cls) -> "BrowserInstanceData":
        """创建空的实例数据"""
        return cls(cookies=[], local_storage={}, session_storage={}, device_config={}, webview_config={})


@dataclass
class BrowserStats:
    """浏览器统计信息"""

    pool_stats: Optional[Dict[str, Any]]
    session_stats: Dict[str, Any]
    metrics_stats: Dict[str, Any]
