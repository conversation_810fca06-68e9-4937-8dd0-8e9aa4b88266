# encoding: utf-8
# src/infrastructures/ip_proxy/manager.py
# created: 2025-07-26 12:13:37

import random
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import AsyncGenerator, Optional, Union

from loguru import logger
from redis.asyncio import Redis

from src.infrastructures.databases import RedisManager

from .resources.qg_short import QingguoShortResources
from .settings import ProxySettings
from .types import IpProxy


@asynccontextmanager
async def redis_lock(
    redis_client: "Redis", lock_key: str, timeout: int = 15, wait_timeout: int = 9
) -> AsyncGenerator[bool, None]:
    """Redis分布式锁上下文管理器, 支持阻塞等待"""
    import asyncio

    lock_value = str(uuid.uuid4())
    acquired = False
    start_time = datetime.now().timestamp()

    try:
        # 阻塞等待获取锁
        while datetime.now().timestamp() - start_time <= wait_timeout:
            result = await redis_client.set(lock_key, lock_value, nx=True, ex=timeout)
            if result:
                acquired = True
                break
            else:
                # 等待一小段时间后重试
                await asyncio.sleep(0.01)  # 10ms

        if not acquired:
            logger.warning(f"等待锁超时: {lock_key} (等待时间: {wait_timeout}s)")

        yield acquired

    finally:
        # 简化版释放锁
        if acquired:
            try:
                await redis_client.delete(lock_key)
            except Exception as e:
                logger.error(f"释放锁失败: {lock_key}, {e}")


class IpProxyPool:

    def __init__(self, redis_manager: RedisManager, city: str):
        self.redis = redis_manager.client
        self.city = city
        self._key = f"ip_proxy:pool:{city}"
        self._concurrent_key = f"ip_proxy:concurrent:{city}"
        self._failure_key = f"ip_proxy:failures:{city}"

    async def add(self, ip: IpProxy) -> None:
        """添加代理到池中"""
        await self.redis.zadd(self._key, {ip.model_dump_json(): ip.expired_at.timestamp()})
        await self.redis.hset(self._concurrent_key, ip.identify, 0)  # type: ignore
        await self.redis.hset(self._failure_key, ip.identify, 0)  # type: ignore

    async def alloc(self, max_concurrent: int = 10) -> Optional[IpProxy]:
        """从池中分配一个并发数未满的代理"""
        # 获取所有未过期的代理
        current_ts = datetime.now().timestamp()
        proxies = await self.redis.zrangebyscore(self._key, current_ts + 30, "+inf")

        # 随机化候选代理顺序，避免顺序偏倚
        if proxies:
            random.shuffle(proxies)

        for proxy_json in proxies:
            proxy = IpProxy.model_validate_json(proxy_json)

            # 使用原子操作：先增加，再检查是否超限
            new_concurrent = await self.redis.hincrby(self._concurrent_key, proxy.identify, 1)  # type: ignore
            if new_concurrent <= max_concurrent:
                logger.info(f"分配代理 {proxy.identify}，当前并发: {new_concurrent}/{max_concurrent}")
                return proxy
            else:  # 超限了，需要回滚
                await self.redis.hincrby(self._concurrent_key, proxy.identify, -1)  # type: ignore
                logger.debug(f"代理 {proxy.identify} 并发已满 ({new_concurrent-1}/{max_concurrent})，尝试下一个")
        return None

    async def release(self, proxy_id: str) -> None:
        """释放代理使用，减少并发计数"""
        current = await self.redis.hincrby(self._concurrent_key, proxy_id, -1)  # type: ignore
        if current < 0:
            await self.redis.hset(self._concurrent_key, proxy_id, 0)  # type: ignore
            current = 0
        logger.debug(f"释放代理 {proxy_id}，当前并发: {current}")

    async def record_failure(self, proxy_id: str, max_failures: int = 5) -> bool:
        """记录代理失败次数，返回是否应该移除代理"""
        try:
            failure_count = await self.redis.hincrby(self._failure_key, proxy_id, 1)  # type: ignore
            if failure_count >= max_failures:
                logger.warning(f"代理 {proxy_id} 失败次数达到上限({failure_count})，将移除 (城市: {self.city})")
                await self.remove(proxy_id)
                return True
            return False
        except Exception as e:
            logger.error(f"记录代理失败失败: {proxy_id}, {e}")
            return False

    async def remove(self, proxy_id: str) -> bool:
        """从池中移除指定的代理"""
        proxies = await self.redis.zrange(self._key, 0, -1)  # 获取所有代理
        for proxy_json in proxies:
            try:
                proxy = IpProxy.model_validate_json(proxy_json)
                if proxy.identify == proxy_id:
                    await self.redis.zrem(self._key, proxy_json)  # 从有序集合中移除代理
                    await self.redis.hdel(self._concurrent_key, proxy_id)  # type: ignore
                    await self.redis.hdel(self._failure_key, proxy_id)  # type: ignore
                    logger.info(f"已移除代理 {proxy_id} (城市: {self.city})")
                    return True
            except Exception:
                continue
        logger.warning(f"未找到要移除的代理 {proxy_id} (城市: {self.city})")
        return False

    async def clear_expired(self, now: Optional[datetime] = None) -> None:
        """清理过期的代理"""
        threshold_ts = 30 + int((now or datetime.now()).timestamp())
        expired_proxies = await self.redis.zrangebyscore(self._key, "-inf", threshold_ts)  # 获取过期的代理

        if expired_proxies:
            await self.redis.zremrangebyscore(self._key, "-inf", threshold_ts)  # 清理代理
            for proxy_json in expired_proxies:
                try:
                    proxy = IpProxy.model_validate_json(proxy_json)
                    await self.redis.hdel(self._concurrent_key, proxy.identify)  # type: ignore
                    await self.redis.hdel(self._failure_key, proxy.identify)  # type: ignore
                except Exception:
                    pass
            logger.info(f"清理 {self.city} 过期代理: {len(expired_proxies) if expired_proxies else 0} 个")


class ProxyManager:

    def __init__(self, redis_manager: RedisManager, settings_config: Union["ProxySettings", dict, None] = None):
        if settings_config is None:
            from .settings import proxy_settings

            settings = proxy_settings
        elif isinstance(settings_config, dict):
            settings = ProxySettings(**settings_config)
        else:
            settings = settings_config
        self.redis_manager = redis_manager
        self.city_pools: dict[str, IpProxyPool] = {}
        # 初始化青果短效代理提供者
        self.qg_short_provider = QingguoShortResources(settings.qg_short_settings)

    async def alloc(self, city: str, max_concurrent: int = 10, _retry_count: int = 0) -> Optional[IpProxy]:
        """分配一个代理，支持并发复用"""
        if city not in self.city_pools:
            self.city_pools[city] = IpProxyPool(self.redis_manager, city)

        # 只在第一次调用时清理过期代理
        if _retry_count == 0:
            await self.city_pools[city].clear_expired()

        # 尝试从池中获取可用代理
        if proxy := await self.city_pools[city].alloc(max_concurrent):
            logger.debug(f"从池中分配代理 {proxy.identify} 给城市 {city}")
            return proxy

        # 如果池中没有可用代理且是第一次尝试，使用分布式锁从provider获取新代理
        if _retry_count == 0:
            lock_key = f"ip_proxy:fetch_lock:{city}"
            # 调整锁参数：锁有效期15s，等待获取锁9s（保持 wait_timeout < timeout）
            async with redis_lock(self.redis_manager.client, lock_key, timeout=15, wait_timeout=9) as acquired:

                if not acquired:
                    logger.warning(f"等待 {city} 代理锁超时，返回空代理")
                    return None

                try:
                    logger.debug(f"持锁获取 {city} 新代理")
                    new_proxy = await self.qg_short_provider.get_proxy(city)
                    if new_proxy:
                        await self.city_pools[city].add(new_proxy)
                        # 递归调用，尝试分配刚添加的代理
                        return await self.alloc(city, max_concurrent, _retry_count + 1)
                    else:
                        logger.warning(f"青果短效返回空代理 (城市: {city})")
                except Exception as e:
                    logger.error(f"从青果短效获取代理失败: {e}")

        logger.warning(f"无法为城市 {city} 分配代理")
        return None

    async def release(self, proxy_id: str, city: str, success: bool) -> None:
        """释放代理，减少并发计数"""
        if city in self.city_pools:
            await self.city_pools[city].release(proxy_id)
            if success:
                logger.debug(f"代理 {proxy_id} 使用成功 (城市: {city})")
            else:
                logger.warning(f"代理 {proxy_id} 使用失败 (城市: {city})")
                await self.city_pools[city].record_failure(proxy_id)  # 记录失败次数，超过5次才移除
        else:
            logger.warning(f"尝试释放未知城市 {city} 的代理 {proxy_id}")
