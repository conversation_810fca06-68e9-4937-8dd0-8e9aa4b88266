# encoding: utf-8
# src/applications/openapi/dto.py
# created: 2025-07-29 08:41:53

from typing import Union

from pydantic import BaseModel, Field, field_validator

from src.domains.activity.schemas import ActivityLinks


class BenefitsProductDTO(BaseModel):
    id: int = Field(..., description="产品ID")
    code: str = Field(..., description="产品编码")
    name: str = Field(..., description="产品名称")
    price: int = Field(..., description="商品标价(单位分)")
    stock: int = Field(..., description="库存")


class BenefitsOrderDTO(BaseModel):
    order_id: Union[str, int] = Field(..., description="订单ID")
    third_order_id: str = Field(..., description="第三方订单ID", alias="out_order_id")
    status: str = Field(..., description="订单状态")
    product_code: str = Field(..., description="产品编码")
    price: int = Field(..., description="价格(分)")

    class Config:
        extra = "ignore"  # 忽略未定义的字段

    @field_validator("order_id")
    @classmethod
    def validate_order_id(cls, v):
        if isinstance(v, int):
            return str(v)
        return v


class AccountInfoDTO(BaseModel):
    balance: int = Field(..., description="账户余额(单位分)")
    code: str = Field(..., description="客户编码")


ActivityLinksDTO = ActivityLinks


class BenefitsProductChargePageDTO(BaseModel):
    charge_url: str = Field(..., description="充值页面链接")
    ticket_code: str = Field(..., description="充值code")
    expire_time: int = Field(..., description="过期时间")
