# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/benefits/__init__.py
# created: 2025-01-11 20:06:37
# updated: 2025-01-11 21:35:34

from fastapi import APIRouter

from .orders import router as orders_router
from .products import router as products_router
from .skus import router as skus_router
from .suppliers import router as suppliers_router

router = APIRouter(tags=["benefits"])

router.include_router(suppliers_router)
router.include_router(products_router)
router.include_router(orders_router)
router.include_router(skus_router)
