import base64
import json

from Crypto.Cipher import AES


class AlipayMpGateway:
    def __init__(self, alipay_aes_key: str):
        self.alipay_aes_key = alipay_aes_key

    def alipay_aes_decrypt_base64(self, encrypted_b64: str, key_b64: str, charset: str = "utf-8") -> dict:
        """Decrypt Alipay ciphertext and return typed content."""
        ciphertext = base64.b64decode(encrypted_b64)
        key = base64.b64decode(key_b64)
        iv = ("\0" * AES.block_size).encode("utf8")
        plaintext = AES.new(key, AES.MODE_CBC, iv).decrypt(ciphertext)
        pad_len = plaintext[-1]
        plaintext = plaintext[:-pad_len]
        text = plaintext.decode(charset)
        data = json.loads(text)
        return data
