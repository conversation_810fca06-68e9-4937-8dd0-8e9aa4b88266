# 第二阶段：基础设施迁移任务

## 任务1：创建新的目录结构

### 执行脚本：
```bash
#!/bin/bash
# scripts/create_new_structure.sh

# 创建主要目录结构
mkdir -p src/{interfaces,applications,domains,repositories,databases,infra}
mkdir -p src/interfaces/{http,consumers,schedulers}
mkdir -p src/infra/{config,logger,middleware,exceptions,containers,utils}
mkdir -p tests/{unit,integration,e2e}
mkdir -p tests/unit/{domains,applications}
mkdir -p tests/integration/{interfaces,applications}
mkdir -p deploys

# 创建各个服务的目录
for service in benefits_api delivery_api mis_api internal_api; do
    mkdir -p src/interfaces/http/$service
    mkdir -p deploys/$service
done

# 创建各个领域的目录
for domain in benefits passport delivery customer aiagent activity; do
    mkdir -p src/domains/$domain/{services,dto,exceptions}
    mkdir -p src/repositories/$domain
    mkdir -p src/applications/$domain/{commands,queries,handlers}
    mkdir -p tests/unit/domains/$domain
done

# 创建 __init__.py 文件
find src tests -type d -exec touch {}/__init__.py \;

echo "新目录结构创建完成！"
```

### 验证步骤：
```bash
# 验证目录结构
tree src -d -L 3
tree deploys -d -L 2
tree tests -d -L 3
```

## 任务2：迁移 infra 层基础设施

### 2.1 迁移配置管理
```python
# scripts/migrate_config.py
import shutil
import os
from pathlib import Path

def migrate_config():
    """迁移配置相关文件"""
    migrations = [
        ('core/config.py', 'src/infra/config/settings.py'),
        ('core/config_loader.py', 'src/infra/config/loader.py'),
        ('.env.toml', 'src/infra/config/.env.toml.example'),
    ]
    
    for src, dst in migrations:
        if os.path.exists(src):
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            print(f"✅ 迁移 {src} -> {dst}")
        else:
            print(f"⚠️  源文件不存在: {src}")
    
    # 创建新的配置基类
    config_base = '''"""基础配置类"""
from pydantic import BaseSettings
from typing import Optional

class BaseConfig(BaseSettings):
    """基础配置类，所有服务配置都应继承此类"""
    
    # 基础配置
    env: str = "development"
    debug: bool = False
    
    # 服务配置
    service_name: str
    service_port: int = 8000
    
    # 数据库配置
    database_url: str
    database_pool_size: int = 10
    
    # Redis配置
    redis_url: str = "redis://localhost:6379"
    
    # RabbitMQ配置
    rabbitmq_url: str = "amqp://guest:guest@localhost:5672/"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
'''
    
    with open('src/infra/config/base.py', 'w') as f:
        f.write(config_base)
    print("✅ 创建配置基类")

if __name__ == "__main__":
    migrate_config()
```

### 2.2 迁移日志系统
```python
# scripts/migrate_logger.py
import os
import shutil

def migrate_logger():
    """迁移日志相关文件"""
    # 复制现有日志模块
    if os.path.exists('core/logger'):
        shutil.copytree('core/logger', 'src/infra/logger', dirs_exist_ok=True)
        print("✅ 迁移日志模块")
    
    # 创建统一的日志接口
    logger_interface = '''"""统一日志接口"""
from abc import ABC, abstractmethod
from typing import Any, Dict

class ILogger(ABC):
    """日志接口定义"""
    
    @abstractmethod
    def debug(self, message: str, **kwargs: Any) -> None:
        """调试日志"""
        pass
    
    @abstractmethod
    def info(self, message: str, **kwargs: Any) -> None:
        """信息日志"""
        pass
    
    @abstractmethod
    def warning(self, message: str, **kwargs: Any) -> None:
        """警告日志"""
        pass
    
    @abstractmethod
    def error(self, message: str, **kwargs: Any) -> None:
        """错误日志"""
        pass
    
    @abstractmethod
    def critical(self, message: str, **kwargs: Any) -> None:
        """严重错误日志"""
        pass
'''
    
    with open('src/infra/logger/interface.py', 'w') as f:
        f.write(logger_interface)
    print("✅ 创建日志接口")

if __name__ == "__main__":
    migrate_logger()
```

### 2.3 迁移中间件
```python
# scripts/migrate_middleware.py
def migrate_middleware():
    """迁移和重组中间件"""
    # 创建中间件基类
    middleware_base = '''"""中间件基类和接口"""
from abc import ABC, abstractmethod
from typing import Callable, Any
from fastapi import Request, Response

class IMiddleware(ABC):
    """中间件接口"""
    
    @abstractmethod
    async def __call__(
        self,
        request: Request,
        call_next: Callable,
    ) -> Response:
        """处理请求"""
        pass

class BaseMiddleware(IMiddleware):
    """中间件基类"""
    
    def __init__(self):
        pass
    
    async def before_request(self, request: Request) -> None:
        """请求前处理"""
        pass
    
    async def after_request(self, request: Request, response: Response) -> None:
        """请求后处理"""
        pass
    
    async def __call__(
        self,
        request: Request,
        call_next: Callable,
    ) -> Response:
        await self.before_request(request)
        response = await call_next(request)
        await self.after_request(request, response)
        return response
'''
    
    os.makedirs('src/infra/middleware', exist_ok=True)
    with open('src/infra/middleware/base.py', 'w') as f:
        f.write(middleware_base)
    
    # 迁移现有中间件
    middleware_files = [
        'core/middleware/auth.py',
        'core/middleware/cors.py',
        'core/middleware/error_handler.py',
        'core/middleware/request_id.py',
    ]
    
    for file in middleware_files:
        if os.path.exists(file):
            filename = os.path.basename(file)
            shutil.copy2(file, f'src/infra/middleware/{filename}')
            print(f"✅ 迁移中间件: {filename}")

if __name__ == "__main__":
    migrate_middleware()
```

### 2.4 迁移异常处理
```python
# scripts/migrate_exceptions.py
def create_exception_hierarchy():
    """创建统一的异常层次结构"""
    base_exceptions = '''"""基础异常定义"""
from typing import Optional, Dict, Any

class BaseError(Exception):
    """基础异常类"""
    
    def __init__(
        self,
        message: str,
        code: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.code = code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)

class DomainError(BaseError):
    """领域层异常"""
    pass

class ApplicationError(BaseError):
    """应用层异常"""
    pass

class InfrastructureError(BaseError):
    """基础设施层异常"""
    pass

class ValidationError(BaseError):
    """验证异常"""
    
    def __init__(self, message: str, field: str, **kwargs):
        super().__init__(
            message=message,
            code="VALIDATION_ERROR",
            status_code=400,
            **kwargs
        )
        self.field = field

class NotFoundError(BaseError):
    """资源未找到异常"""
    
    def __init__(self, resource: str, identifier: Any):
        super().__init__(
            message=f"{resource} not found: {identifier}",
            code="NOT_FOUND",
            status_code=404,
        )
        self.resource = resource
        self.identifier = identifier

class ConflictError(BaseError):
    """冲突异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            code="CONFLICT",
            status_code=409,
            **kwargs
        )

class UnauthorizedError(BaseError):
    """未授权异常"""
    
    def __init__(self, message: str = "Unauthorized"):
        super().__init__(
            message=message,
            code="UNAUTHORIZED",
            status_code=401,
        )

class ForbiddenError(BaseError):
    """禁止访问异常"""
    
    def __init__(self, message: str = "Forbidden"):
        super().__init__(
            message=message,
            code="FORBIDDEN",
            status_code=403,
        )
'''
    
    with open('src/infra/exceptions/base.py', 'w') as f:
        f.write(base_exceptions)
    print("✅ 创建异常层次结构")

if __name__ == "__main__":
    create_exception_hierarchy()
```

### 2.5 迁移依赖注入容器
```python
# scripts/migrate_containers.py
def migrate_containers():
    """迁移依赖注入容器"""
    # 迁移现有容器
    if os.path.exists('core/containers.py'):
        shutil.copy2('core/containers.py', 'src/infra/containers/main.py')
    
    # 创建容器接口
    container_interface = '''"""依赖注入容器接口"""
from abc import ABC, abstractmethod
from typing import Type, TypeVar, Any

T = TypeVar('T')

class IContainer(ABC):
    """容器接口"""
    
    @abstractmethod
    def register(self, interface: Type[T], implementation: Type[T]) -> None:
        """注册服务"""
        pass
    
    @abstractmethod
    def resolve(self, interface: Type[T]) -> T:
        """解析服务"""
        pass
    
    @abstractmethod
    def register_singleton(self, interface: Type[T], instance: T) -> None:
        """注册单例"""
        pass
'''
    
    with open('src/infra/containers/interface.py', 'w') as f:
        f.write(container_interface)
    print("✅ 创建容器接口")

if __name__ == "__main__":
    migrate_containers()
```

## 任务3：创建基础工具类

### 3.1 创建常用工具
```python
# src/infra/utils/datetime.py
"""日期时间工具"""
from datetime import datetime, timedelta, timezone
from typing import Optional

def now() -> datetime:
    """获取当前UTC时间"""
    return datetime.now(timezone.utc)

def to_timestamp(dt: datetime) -> int:
    """转换为时间戳"""
    return int(dt.timestamp())

def from_timestamp(ts: int) -> datetime:
    """从时间戳转换"""
    return datetime.fromtimestamp(ts, timezone.utc)
```

```python
# src/infra/utils/crypto.py
"""加密工具"""
import hashlib
import hmac
import secrets
from typing import Optional

def generate_token(length: int = 32) -> str:
    """生成安全令牌"""
    return secrets.token_urlsafe(length)

def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
    """密码哈希"""
    if salt is None:
        salt = secrets.token_hex(16)
    
    hashed = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000
    )
    return hashed.hex(), salt

def verify_password(password: str, hashed: str, salt: str) -> bool:
    """验证密码"""
    new_hash, _ = hash_password(password, salt)
    return hmac.compare_digest(new_hash, hashed)
```

## 任务4：创建测试基础设施

### 4.1 创建测试配置
```python
# tests/conftest.py
"""测试配置"""
import pytest
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

@pytest.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """测试数据库会话"""
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async_session = sessionmaker(engine, class_=AsyncSession)
    
    async with async_session() as session:
        yield session

@pytest.fixture
def mock_logger():
    """模拟日志器"""
    from unittest.mock import MagicMock
    return MagicMock()

@pytest.fixture
def mock_config():
    """模拟配置"""
    from src.infra.config.base import BaseConfig
    return BaseConfig(
        service_name="test_service",
        database_url="sqlite:///:memory:",
    )
```

## 验证步骤

### 1. 验证目录结构
```bash
# 检查新结构是否完整
find src -type d | sort | head -20
```

### 2. 验证导入路径
```python
# scripts/verify_imports.py
import sys
sys.path.insert(0, 'src')

try:
    from infra.config.base import BaseConfig
    from infra.logger.interface import ILogger
    from infra.exceptions.base import BaseError
    from infra.middleware.base import BaseMiddleware
    print("✅ 所有基础模块导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
```

### 3. 运行基础测试
```bash
# 创建简单的冒烟测试
poetry run pytest tests/unit/infra -v
```

## 完成标准

- [ ] 新目录结构创建完成
- [ ] 配置系统迁移完成
- [ ] 日志系统迁移完成
- [ ] 中间件系统迁移完成
- [ ] 异常处理系统创建完成
- [ ] 依赖注入容器迁移完成
- [ ] 基础工具类创建完成
- [ ] 测试基础设施准备完成
- [ ] 所有模块可正常导入