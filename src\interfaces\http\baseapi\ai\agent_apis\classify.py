from ..utils.base import LLMClient

CATEGORIES = [
    "美食",
    "中式菜系",
    "甜品",
    "烘焙",
    "奶茶|果汁",
    "咖啡",
    "便当|简餐",
    "米粉|捞烫",
    "面",
    "滋补|汤炖",
    "粥",
    "小吃",
    "烧烤",
    "火锅",
    "香锅",
    "西餐",
    "日料",
]


def classify(user_input: str) -> str:
    """
    将用户输入的文本分类到预定义的餐饮类别中

    Args:
        user_input: 用户输入的文本

    Returns:
        最匹配的类别
    """
    prompt = f"""请将以下输入文本匹配到最相近的餐饮分类中。
可选分类有：{', '.join(CATEGORIES)}

规则：
1. 只返回一个最匹配的分类名称
2. 不要返回任何解释
3. 必须从给定的分类列表中选择
4. 如果实在无法判断，返回"美食"

用户输入：{user_input}

分类："""

    llm_client = LLMClient("doubao", "default")
    # 由于我们没有使用 stream=True，所以这里一定返回 str
    result = llm_client.complete(prompt, stream=False)

    # 确保返回的分类在预定义列表中
    if result not in CATEGORIES:
        return "小吃"

    return result  # type: ignore
