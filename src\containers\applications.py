# encoding: utf-8
# src/containers/applications.py
# created: 2025-08-10 11:30:00

from dependency_injector import containers, providers

from src.applications.common.commands.benefits import (
    OrderNoticeCommandService,
    ProductOrderExportCommandService,
    SkuChargeCheckCommandService,
    SkuChargeCommandService,
    UnionPurchaseSyncCommandService,
    UnionSkuSyncCommandService,
)
from src.applications.common.commands.delivery import OrderSyncCommandService

# Common应用服务
from src.applications.common.queries import ActivityQueryService, ShopsQueryService
from src.applications.common.queries.benefits import UnionPurchaseQueryService
from src.applications.common.queries.delivery import UnionOrderQueryService

# Growth Hacker应用服务
from src.applications.growth_hacker.services.build_task import BuildTaskService
from src.applications.growth_hacker.services.task_service import TaskService

# OpenAPI应用服务
from src.applications.openapi.authenticator import OpenAPIAKSKAuthenticator, OpenAPITokenAuthenticator
from src.applications.openapi.commands.delivery import OrderNotifyCommandService
from src.applications.openapi.queries import (
    ActivitiesQueryService,
    BenefitsQueryService,
    CustomerQueryService,
)
from src.applications.openapi.services import BenefitsChargeService, CustomerService, ElemeAixincanService

# Passport应用服务
from src.applications.passport.queries import UserQueryService
from src.applications.passport.services import AppService, AuthService, LoginService, UserService


class Applications(containers.DeclarativeContainer):
    """应用层服务容器，集中管理所有应用层服务"""

    config = providers.Configuration()
    domains = providers.DependenciesContainer()
    infrastructures = providers.DependenciesContainer()
    repositories = providers.DependenciesContainer()

    # ========================
    # Passport 应用服务
    # ========================
    passport_user_query_service = providers.Factory(
        UserQueryService,
        user_repository=repositories.passport_user_repository,
        tenant_repository=repositories.passport_tenant_repository,
        app_repository=repositories.passport_app_repository,
    )

    passport_user_service = providers.Factory(
        UserService,
        user_repository=repositories.passport_user_repository,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
    )

    passport_app_service = providers.Factory(
        AppService,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
    )

    passport_auth_service = providers.Factory(
        AuthService,
        app_repository=repositories.passport_app_repository,
        user_domain_service=domains.user_service,
    )

    passport_login_service = providers.Factory(
        LoginService,
        user_repository=repositories.passport_user_repository,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
        redis=infrastructures.redis_manager,
        user_domain_service=domains.user_service,
        sms_gateway=infrastructures.aliyun_sms_gateway,
    )

    # ========================
    # OpenAPI 应用服务
    # ========================
    openapi_bearer_authenticator = providers.Factory(
        OpenAPITokenAuthenticator, customer_repo=repositories.customer_repository
    )

    openapi_aksk_authenticator = providers.Factory(
        OpenAPIAKSKAuthenticator, customer_repo=repositories.customer_repository
    )

    openapi_activities_query_service = providers.Factory(
        ActivitiesQueryService,
        delivery_page_service=domains.delivery_page_service,
        delivery_page_repo=repositories.delivery_page_repository,
        bifrost_gateway=infrastructures.bifrost_gateway,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
        eleme_activity_service=domains.eleme_activity_service,
        wifi_master_gateway=infrastructures.wifimaster_gateway,
        redis=infrastructures.redis_manager,
        config=config,
        eleme_aixincan_gateway=infrastructures.eleme_aixincan_gateway,
    )

    openapi_customer_query_service = providers.Factory(
        CustomerQueryService,
        customer_repo=repositories.customer_repository,
        customer_subscribe_repo=repositories.customer_subscribe_repository,
    )

    openapi_benefits_query_service = providers.Factory(
        BenefitsQueryService,
        product_service=domains.benefits_product_service,
        customer_benefits_repo=repositories.customer_benefits_repository,
    )

    openapi_benefits_charge_service = providers.Factory(
        BenefitsChargeService,
        customer_benefit_repo=repositories.customer_benefits_repository,
        benefits_product_service=domains.benefits_product_service,
        benefits_product_order_repo=repositories.benefits_pdorder_repository,
        customer_repo=repositories.customer_repository,
        config=config,
    )

    openapi_aixincan_service = providers.Factory(
        ElemeAixincanService, aixincan_gateway=infrastructures.eleme_aixincan_gateway
    )

    openapi_customer_service = providers.Factory(
        CustomerService,
        customer_repo=repositories.customer_repository,
        app_repo=repositories.passport_app_repository,
        tenant_repo=repositories.passport_tenant_repository,
    )

    openapi_order_notify_command_service = providers.Factory(
        OrderNotifyCommandService,
        config=config,
        customer_query_service=openapi_customer_query_service,  # 现在是同层调用，合理
        customer_benefit_service=domains.customer_benefit_service,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
    )

    # ========================
    # Common 应用服务
    # ========================
    common_activity_query_service = providers.Factory(
        ActivityQueryService,
        delivery_page_service=domains.delivery_page_service,
        delivery_page_repo=repositories.delivery_page_repository,
        bifrost_gateway=infrastructures.bifrost_gateway,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
        eleme_activity_service=domains.eleme_activity_service,
    )

    common_shops_query_service = providers.Factory(
        ShopsQueryService,
        shop_repo=repositories.shop_repository,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
        redis_manager=infrastructures.redis_manager,
    )

    common_union_order_query_service = providers.Factory(
        UnionOrderQueryService,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
    )

    common_union_purchase_query_service = providers.Factory(
        UnionPurchaseQueryService,
        eleme_union_benefits_gateway=infrastructures.eleme_union_benefits_gateway,
    )

    # Delivery命令服务
    common_order_sync_command_service = providers.Factory(
        OrderSyncCommandService,
        delivery_order_repository=repositories.delivery_order_repository,
        eleme_order_repository=repositories.delivery_eleme_order_repository,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
        bifrost_gateway=infrastructures.bifrost_gateway,
    )

    # Benefits命令服务
    common_order_notice_command_service = providers.Factory(
        OrderNoticeCommandService,
        order_repo=repositories.benefits_pdorder_repository,
    )

    common_product_order_export_command_service = providers.Factory(
        ProductOrderExportCommandService,
        order_repo=repositories.benefits_pdorder_repository,
        redis_client=infrastructures.redis_manager.provided.client,
    )

    common_sku_charge_command_service = providers.Factory(
        SkuChargeCommandService,
        sku_charge_record_repo=repositories.benefits_sku_charge_record_repository,
    )

    common_sku_charge_check_command_service = providers.Factory(
        SkuChargeCheckCommandService,
        product_service=domains.benefits_product_service,
        sku_charge_record_repo=repositories.benefits_sku_charge_record_repository,
    )

    common_union_purchase_sync_command_service = providers.Factory(
        UnionPurchaseSyncCommandService,
        supplier_repo=repositories.benefits_supplier_repository,
        benefits_supplier_service=domains.benefits_supplier_service,
        sku_service=domains.benefits_sku_service,
        benefits_product_service=domains.benefits_product_service,
    )

    common_union_sku_sync_command_service = providers.Factory(
        UnionSkuSyncCommandService,
    )

    # ========================
    # Growth Hacker 应用服务
    # ========================
    growth_hacker_task_service = providers.Factory(
        TaskService,
        browser_manager=None,  # 保留参数但设为 None，兼容现有构造函数
        user_profile_service=domains.user_profile_domain_service,
        task_service=domains.task_domain_service,
        proxy_manager=infrastructures.ip_proxy_manager,
    )

    growth_hacker_build_task_service = providers.Factory(
        BuildTaskService,
        eleme_user_profile_repository=repositories.eleme_user_profile_repository,
        delivery_page_repo=repositories.delivery_page_repository,
        eleme_activity_service=domains.eleme_activity_service,
        bifrost_gateway=infrastructures.bifrost_gateway,
        eleme_union_delivery_gateway=infrastructures.eleme_union_delivery_gateway,
    )
