# encoding: utf-8
# src/interfaces/consumers/delivery/sync_eleme_order.py
# created: 2025-06-08 02:07:14

from typing import TYPE_CHECKING

from loguru import logger
from pydantic import ValidationError

from src.domains.delivery.messages import UnionOrderSyncMessage
from src.infrastructures.rabbitmq import BaseConsumer

if TYPE_CHECKING:
    from aio_pika.abc import AbstractIncomingMessage

    from src.applications.common.commands.delivery import OrderSyncCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class SyncElemeOrderConsumer(BaseConsumer):
    """饿了么订单详情同步"""

    exchange_name = "delivery.topic"
    queue_name = "delivery_eleme_order_sync"
    routing_key = "delivery.union_order"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        order_sync_service: "OrderSyncCommandService",
    ):
        self.order_sync_service = order_sync_service
        super().__init__(conn_pool, producer)

    async def process(self, message: "AbstractIncomingMessage") -> None:
        """处理消息"""
        msg = UnionOrderSyncMessage.model_validate_json(message.body)
        logger.info(f"SyncElemeOrderConsumer 收到消息: {msg.payload.parent_order_id}")
        await self.order_sync_service.sync_eleme_order_detail(msg.payload)

    async def rollback(self, message: "AbstractIncomingMessage", exp: Exception) -> None:
        """回滚 - 订单同步失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "SyncElemeOrderConsumer 回滚: 消息[{message_id}] 处理失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
