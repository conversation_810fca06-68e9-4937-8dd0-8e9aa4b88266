# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/benefits/products.py
# created: 2025-01-11 20:07:50
# updated: 2025-05-26 11:04:06

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

from src.domains.benefits.dto import (
    BenefitsProductCreateDTO,
    BenefitsProductDetailDTO,
    BenefitsProductDTO,
    BenefitsProductOrderDTO,
    BenefitsProductSkuDTO,
    BenefitsProductUpdateDTO,
    SupplierItemDTO,
)
from src.infrastructures import errors
from src.interfaces.http.baseapi import Container
from src.repositories.benefits.product import ProductFilters, ProductRepository
from src.repositories.benefits.product_order import ProductOrderFilters, ProductOrderRepository
from src.repositories.benefits.sku import SkuRepository

from ...schemas import (
    BaseResponse,
    CreateProductSkuPayload,
    ListData,
    ProductListResponse,
    ProductOrderListResponse,
    ProductResponse,
)

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsProduct

router = APIRouter(tags=["benefits", "products"])


async def product_to_detail_dto(product: "BenefitsProduct") -> BenefitsProductDetailDTO:
    product_skus = await ProductRepository.get_relations_by_product_code(product.code)
    skus = []

    for count, sku in product_skus:
        await sku.fetch_related("supplier")
        skus.append(
            BenefitsProductSkuDTO(
                name=sku.name,
                code=sku.code,
                cost=sku.cost,
                stock=sku.stock,
                detail=sku.detail,
                count=count,
                supplier=SupplierItemDTO(
                    id=sku.supplier.id,
                    name=sku.supplier.name,
                    identify=sku.supplier.identify,
                    contact=sku.supplier.contact,
                    phone=sku.supplier.phone,
                ),
            )
        )

    return BenefitsProductDetailDTO(
        name=product.name,
        code=product.code,
        price=product.price,
        sale_price=product.sale_price,
        description=product.description,
        detail=product.detail,
        skus=skus,
    )


@router.post("/products", response_model=ProductResponse)
@inject
async def create_product(
    product: BenefitsProductCreateDTO,  # type: ignore
    product_repository: "ProductRepository" = Depends(Provide[Container.repositories.benefits_product_repository]),
):  # type: ignore
    product = await product_repository.create_product(product)
    product_detail = await product_to_detail_dto(product)
    return ProductResponse(data=product_detail)


@router.put("/products/{product_code}", response_model=ProductResponse)
async def update_product(product_code: str, product: BenefitsProductUpdateDTO):  # type: ignore
    product = await ProductRepository.update_product(product_code, product)
    if not product:
        raise errors.BenefitsProductNotFoundError
    product_detail = await product_to_detail_dto(product)
    return ProductResponse(data=product_detail)


@router.delete("/products/{product_code}", response_model=ProductResponse)
async def delete_product(product_code: str):
    product = await ProductRepository.get_by_code(product_code)
    if not product:
        raise errors.BenefitsProductNotFoundError
    await product.delete()
    return BaseResponse(message="产品删除成功")


@router.get("/products", response_model=ProductListResponse)
async def get_products(params: ProductFilters = Query(...)):
    count, products = await ProductRepository.filter_products(params)
    products = [await BenefitsProductDTO.from_tortoise_orm(product) for product in products]  # type: ignore
    return ProductListResponse(data=ListData(total=count, data=products))


@router.get("/products/{product_code}", response_model=ProductResponse)
async def get_product(product_code: str):
    product = await ProductRepository.get_by_code(product_code)
    if not product:
        raise errors.BenefitsProductNotFoundError
    product_detail = await product_to_detail_dto(product)
    return ProductResponse(data=product_detail)


@router.get("/products/{product_code}/orders", response_model=ProductOrderListResponse)
async def get_product_orders_by_product_code(product_code: str, params: ProductOrderFilters = Query(...)):
    params.product_code = product_code
    count, orders, customers = await ProductOrderRepository.get_product_orders(params)
    orders = [await BenefitsProductOrderDTO.from_tortoise_orm(order) for order in orders]  # type: ignore
    return ProductOrderListResponse(data=ListData(total=count, data=orders))


@router.post("/products/{product_code}/skus/{sku_code}", response_model=BaseResponse)
async def create_product_sku(product_code: str, sku_code: str, payload: CreateProductSkuPayload):
    product = await ProductRepository.get_by_code(product_code)
    if not product:
        raise errors.BenefitsProductNotFoundError
    sku = await SkuRepository.get_by_code(sku_code)
    if not sku:
        raise errors.SkuNotFoundError
    await ProductRepository.create_product_sku_relations(product, sku, payload.count)
    return BaseResponse(message="商品SKU创建成功")


@router.delete("/products/{product_code}/skus/{sku_code}", response_model=BaseResponse)
async def delete_product_sku(product_code: str, sku_code: str):
    product = await ProductRepository.get_by_code(product_code)
    if not product:
        raise errors.BenefitsProductNotFoundError
    sku = await SkuRepository.get_by_code(sku_code)
    if not sku:
        raise errors.SkuNotFoundError
    result = await ProductRepository.delete_product_sku_relations(product, sku)
    if result is None:
        raise errors.BenefitsProductSkuNotFoundError
    return BaseResponse(message="商品SKU删除成功")
