## Container 层重构方案（DDD + dependency_injector）

### 背景与问题
- 现状：`applications/*/container.py` 基本按“每个小应用一个容器”维护，接口层（HTTP/Consumer/Scheduler）需要重复装配多个容器，装配路径分散，维护成本高。
- 问题：
  - 容器碎片化，装配重复，入口层 wiring 与配置注入不统一。
  - 生命周期管理（连接初始化/关闭）分散；部分使用 `Callable` 充当初始化逻辑，缺少统一 `Resource` 管理。
  - 少量“跨层巨无霸容器”（如将仓储/领域/应用混搭）易造成边界模糊与依赖歧义。

### 目标
- 统一容器分层与装配路径，降低接口层复杂度。
- 用“基础三层 + 应用上下文 + 进程装配”的结构治理依赖关系，保持清晰的层边界与职责划分。
- 统一生命周期与 wiring 管理：资源集中初始化/释放；HTTP/Consumer/Scheduler 入口一致。
- 提升测试可控性：容器覆写更简单；单测直连仓储/领域实现，避免穿透 Query 层。

### 目标架构与分层拓扑
- 基础容器（三件套）：
  - `Repositories`：数据访问实现（`providers.Singleton(...)`）
  - `Infrastructures`：连接池、客户端、队列、网关（优先 `Singleton` + 需要时用 `Resource` 管理生命周期）
  - `Domains`：领域服务聚合，依赖 Repositories/Infrastructures，通常 `Singleton`，对需上下文态的使用 `Factory`
- 应用容器（按限界上下文）：
  - 每个上下文一个 `Applications` 容器（例如 `passport`、`openapi`、`growth_hacker`、`common`）
  - 仅依赖 `Domains / Infrastructures / Repositories / config`
  - 内部暴露 UseCase/Command/Query 服务，建议统一使用 `Factory`
- 聚合应用容器（统一对外装配点）：
  - `src/applications/container.py` 定义总 `Applications` 容器，聚合所有上下文容器
  - 接口进程容器只接入这一个聚合容器，避免多处重复依赖传递
- 进程装配容器（接口层）：
  - HTTP（BaseAPI/OpenAPI）、Consumer、Scheduler 各自一个装配容器
  - 职责：注入 `config` → 装配 `Repositories/Infrastructures/Domains` → 装配聚合 `Applications` → 统一 wiring 与生命周期

### 生命周期与 wiring 规范
- 生命周期：
  - 连接/客户端/驱动类使用 `providers.Resource(init=..., shutdown=...)`
  - 入口在启动时调用 `await container.init_resources()`，退出时 `await container.shutdown_resources()`
  - FastAPI 使用 `lifespan` 管理；Consumer/Scheduler 在进程主函数管理
- wiring：
  - 入口统一调用 `container.wire(packages=[...])`
  - Handler/Router/Task 内使用 `@inject` + `Provide[...]` 获取依赖

### Provider 粒度约定
- 基础设施客户端/连接池/消息队列/网关：`Singleton`
- 仓储：`Singleton`
- 领域服务：无状态优先 `Singleton`；涉及上下文（如短期缓存/会话）的使用 `Factory`
- 应用服务（UseCase/Command/Query）：统一 `Factory`，避免跨请求共享隐式状态
- 资源管理：需显式 init/shutdown 的使用 `Resource`

### 配置注入规范
- 进程装配容器定义：`config = providers.Configuration()`
- 入口通过 `container.config.from_dict(settings)` 注入
- 子容器通过 `providers.Container(Sub, config=config, ...)` 级联接收

### 层边界约束
- 接口层仅依赖应用层 `Applications`，禁止直接调用 `Domains/Repositories`
- 领域层不依赖任何具体基础设施协议实现（坚持端口-适配器）
- 避免“巨无霸容器”：不得在一个容器内混合仓储/领域/应用三层提供者

### 目录与命名规范
- 基类：`src/applications/base.py` 提供统一依赖声明，供各上下文容器继承
- 上下文容器：`src/applications/{context}/container.py`，类名统一 `Applications`
- 聚合容器：`src/applications/container.py`，类名 `Applications`（持有 `{context}.Applications` 子容器）
- Provider 命名：
  - 应用服务：`xxx_service`
  - 查询服务：`xxx_query_service`

### 代码骨架（示例）
以下示例为最小必要骨架，用于统一风格与依赖接口；可在现有代码上逐步替换。

1) 应用容器基类（`src/applications/base.py`）
```python
from dependency_injector import containers, providers

class BaseApplications(containers.DeclarativeContainer):
    config = providers.Configuration()
    domains = providers.DependenciesContainer()
    infrastructures = providers.DependenciesContainer()
    repositories = providers.DependenciesContainer()
```

2) 上下文容器（以 `passport` 为例，`src/applications/passport/container.py`）
```python
from dependency_injector import providers
from src.applications.base import BaseApplications
from .queries import UserQueryService
from .services import UserService, AppService, AuthService, LoginService

class Applications(BaseApplications):
    user_query_service = providers.Factory(
        UserQueryService,
        user_repository=repositories.passport_user_repository,  # type: ignore[attr-defined]
        tenant_repository=repositories.passport_tenant_repository,
        app_repository=repositories.passport_app_repository,
    )

    user_service = providers.Factory(
        UserService,
        user_repository=repositories.passport_user_repository,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
    )

    app_service = providers.Factory(
        AppService,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
    )

    auth_service = providers.Factory(
        AuthService,
        app_repository=repositories.passport_app_repository,
        user_domain_service=domains.user_service,
    )

    login_service = providers.Factory(LoginService, auth_service=auth_service)
```

3) 聚合应用容器（`src/applications/container.py`）
```python
from dependency_injector import containers, providers
from src.applications.passport.container import Applications as PassportApps
from src.applications.openapi.container import Applications as OpenapiApps
from src.applications.common.container import Applications as CommonApps
from src.applications.growth_hacker.container import Applications as GrowthHackerApps

class Applications(containers.DeclarativeContainer):
    config = providers.Configuration()
    domains = providers.DependenciesContainer()
    infrastructures = providers.DependenciesContainer()
    repositories = providers.DependenciesContainer()

    passport = providers.Container(
        PassportApps, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
    openapi = providers.Container(
        OpenapiApps, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
    common = providers.Container(
        CommonApps, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
    growth_hacker = providers.Container(
        GrowthHackerApps, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
```

4) 接口进程容器装配（以 BaseAPI 为例）
```python
from dependency_injector import containers, providers
from src.containers.repositories import Repositories
from src.containers.infrastructures import Infrastructures
from src.containers.domains import Domains
from src.applications.container import Applications as AllApplications

class Container(containers.DeclarativeContainer):
    config = providers.Configuration()
    repositories = providers.Container(Repositories)
    infrastructures = providers.Container(Infrastructures, config=config)
    domains = providers.Container(Domains, repositories=repositories, infrastructures=infrastructures)

    applications = providers.Container(
        AllApplications, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
```

5) FastAPI 生命周期（统一资源管理与 wiring）
```python
from contextlib import asynccontextmanager
from dependency_injector.wiring import Provide

@asynccontextmanager
async def lifespan(app):
    container = Provide[Container]
    await container.init_resources()
    try:
        yield
    finally:
        await container.shutdown_resources()
```

### 迁移步骤（可逐步落地）
1. 新增 `src/applications/base.py` 与 `src/applications/container.py`（聚合容器），不修改现有行为。
2. 按上下文逐个将 `XxxApplications` 重命名/合并为统一 `Applications(BaseApplications)`，仅调整导入与类名，功能不变。
3. 在接口层容器（BaseAPI/OpenAPI/Consumer/Scheduler）中，仅保留一个 `applications`（聚合），替换原来多个 `providers.Container(...)`。
4. 将连接/客户端初始化从 `Callable` 迁移到 `Resource`，在入口统一 `init_resources / shutdown_resources`。
5. 统一 wiring：在各入口调用 `container.wire(packages=[...])`。
6. 移除或标注 `src/infrastructures/containers.py` 等跨层混合容器为 legacy，逐步下线。
7. 跑 `pytest` 与静态检查，确认无回归；增补缺失的覆写/替身测试。

### 测试策略
- 单元测试优先直连仓储/领域实现，避免经 Query/应用装配层启动复杂依赖。
- 通过 `provider.override(fake_impl)` 注入替身，或在 `conftest.py` 中提供 `build_test_container` 统一构建测试容器。
- 集成测试（接口/消息）在进程容器维度装配，确保生命周期完整。

覆写示例：
```python
def test_user_query_service(container):
    container.repositories.passport_user_repository.override(FakeUserRepo())
    svc = container.applications.passport.user_query_service()
    assert svc.get_user("u1").id == "u1"
```

### 风险与回滚
- 风险：
  - 路由/任务中的 `Provide[...]` 路径变更导致注入失败
  - 资源未迁移到 `Resource` 导致连接泄露或重复初始化
- 防范：
  - 先引入聚合容器，保持旧容器并行一段时间；通过灰度替换路由引用
  - 在入口强制 `init_resources / shutdown_resources`，并在 CI 增加连接泄露检测
- 回滚：
  - 保留旧容器定义与导入别名；如遇生产问题，可在入口将 `applications` 回指旧容器装配实现

### 验收清单（Checklist）
- [ ] 入口装配仅通过一个聚合 `applications` 完成
- [ ] 所有连接/客户端迁移为 `Resource` 并由入口统一管理
- [ ] 路由/任务 `Provide[...]` 引用路径已替换并通过测试
- [ ] `src/infrastructures/containers.py` 不再被入口引用（或已标记为 legacy）
- [ ] 单测直连仓储/领域，必要处通过 `override` 注入替身

### 参考
- 当前装配参考：`src/interfaces/http/baseapi/__init__.py`、`src/interfaces/http/openapi/container.py`、`src/interfaces/consumers/container.py`、`src/interfaces/schedulers/__init__.py`
- 基础容器参考：`src/containers/repositories.py`、`src/containers/infrastructures.py`、`src/containers/domains.py`


