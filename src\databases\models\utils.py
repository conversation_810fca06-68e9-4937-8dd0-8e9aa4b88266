# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/utils.py
# created: 2025-04-06 06:10:53
# updated: 2025-04-06 06:18:25

from datetime import datetime
from typing import Any, ClassVar, Optional, Type, TypeVar

from tortoise import fields
from tortoise.models import Model
from tortoise.queryset import QuerySet

T = TypeVar("T", bound="SoftDeleteMixin")


class SoftDeleteMixin:
    """软删除Mixin, 可与Model类一起混合使用"""

    # 添加软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    async def soft_delete(self) -> None:
        """软删除实例"""
        self.is_deleted = True  # type: ignore
        self.deleted_at = datetime.now()  # type: ignore
        await self.save(update_fields=["is_deleted", "deleted_at"])  # type: ignore

    @classmethod
    def get_queryset(cls) -> QuerySet:
        """重写获取查询集方法, 过滤已删除记录"""
        return super().get_queryset().filter(is_deleted=False)  # type: ignore

    @classmethod
    async def get_or_none(cls: Type[T], *args: Any, **kwargs: Any) -> Optional[Model]:
        """重写get_or_none方法, 自动过滤已删除记录"""
        kwargs["is_deleted"] = False
        return await super().get_or_none(*args, **kwargs)  # type: ignore

    @classmethod
    def filter(cls: Type[T], *args: Any, **kwargs: Any) -> QuerySet:
        """重写filter方法, 自动过滤已删除记录"""
        if not any(k in kwargs for k in ["is_deleted", "is_deleted__in"]):
            kwargs["is_deleted"] = False
        return super().filter(*args, **kwargs)  # type: ignore

    @classmethod
    def all(cls) -> QuerySet:
        """重写all方法, 自动过滤已删除记录"""
        return super().all().filter(is_deleted=False)  # type: ignore

    @classmethod
    def all_with_deleted(cls) -> QuerySet:
        """获取所有记录, 包括已删除的"""
        return super().all()  # type: ignore

    @classmethod
    async def restore(cls: Type[T], **kwargs: Any) -> int:
        """恢复已删除记录"""
        return await cls.all_with_deleted().filter(**kwargs, is_deleted=True).update(is_deleted=False, deleted_at=None)
