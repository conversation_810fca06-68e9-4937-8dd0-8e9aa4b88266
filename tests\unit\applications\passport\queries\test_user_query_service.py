# encoding: utf-8
# tests/unit/applications/passport/queries/test_user_query_service.py
# created: 2025-08-02 11:30:00

"""UserQueryService 单元测试"""

from unittest.mock import MagicMock

import pytest

from src.applications.passport.dto import TenantInfoDTO
from src.applications.passport.queries.user import UserQueryService
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from tests.unit.applications.passport.base import BasePassportUnitTest
from tests.unit.applications.passport.factories import (
    AppFactory,
    TenantFactory,
    UserFactory,
)

# Rebuild models to handle forward references
UserEntity.model_rebuild()
AppEntity.model_rebuild()
TenantEntity.model_rebuild()


class TestUserQueryService(BasePassportUnitTest):
    """测试 UserQueryService 的查询功能"""

    @pytest.fixture
    def user_query_service(
        self,
        mock_user_repository,
        mock_tenant_repository,
        mock_app_repository,
    ):
        """创建 UserQueryService 实例"""
        return UserQueryService(
            user_repository=mock_user_repository,
            tenant_repository=mock_tenant_repository,
            app_repository=mock_app_repository,
        )

    @pytest.fixture
    def sample_user_entity(self):
        """创建示例用户实体"""
        return UserFactory.create_user_entity(
            uid="test_uid_123",
            phone="13800138000",
            nickname="测试用户",
        )

    @pytest.fixture
    def sample_app_entity(self):
        """创建示例应用实体"""
        return AppFactory.create_app_entity(
            app_id="test_app_001",
            app_name="测试应用",
        )

    # 获取用户租户列表测试
    @pytest.mark.asyncio
    async def test_get_user_tenants_success(
        self, user_query_service, mock_user_repository, sample_user_entity, sample_app_entity
    ):
        """测试成功获取用户租户列表"""
        # Mock 用户关系
        tenant_model_1 = TenantFactory.create_tenant_model(
            tenant_id="tenant_001",
            name="租户一",
        )
        tenant_model_2 = TenantFactory.create_tenant_model(
            tenant_id="tenant_002",
            name="租户二",
        )

        relation_1 = MagicMock()
        relation_1.tenant = tenant_model_1
        relation_2 = MagicMock()
        relation_2.tenant = tenant_model_2

        mock_user_repository.get_user_relations.return_value = [relation_1, relation_2]

        result = await user_query_service.get_user_tenants(sample_user_entity, sample_app_entity)

        assert isinstance(result, list)
        assert len(result) == 2

        # 检查第一个租户
        assert isinstance(result[0], TenantInfoDTO)
        assert result[0].tenant_id == "tenant_001"
        assert result[0].tenant_name == "租户一"
        assert result[0].app_id == sample_app_entity.app_id
        assert result[0].app_name == sample_app_entity.app_name

        # 检查第二个租户
        assert isinstance(result[1], TenantInfoDTO)
        assert result[1].tenant_id == "tenant_002"
        assert result[1].tenant_name == "租户二"

        mock_user_repository.get_user_relations.assert_called_once_with(sample_user_entity, sample_app_entity)

    @pytest.mark.asyncio
    async def test_get_user_tenants_empty(
        self, user_query_service, mock_user_repository, sample_user_entity, sample_app_entity
    ):
        """测试获取用户租户列表为空"""
        mock_user_repository.get_user_relations.return_value = []

        result = await user_query_service.get_user_tenants(sample_user_entity, sample_app_entity)

        assert isinstance(result, list)
        assert len(result) == 0
        mock_user_repository.get_user_relations.assert_called_once_with(sample_user_entity, sample_app_entity)

    @pytest.mark.asyncio
    async def test_get_user_tenants_with_none_tenant(
        self, user_query_service, mock_user_repository, sample_user_entity, sample_app_entity
    ):
        """测试获取用户租户列表包含空租户的关系"""
        # Mock 一个有效关系和一个无效关系
        tenant_model = TenantFactory.create_tenant_model()

        valid_relation = MagicMock()
        valid_relation.tenant = tenant_model

        invalid_relation = MagicMock()
        invalid_relation.tenant = None  # 无效关系

        mock_user_repository.get_user_relations.return_value = [valid_relation, invalid_relation]

        result = await user_query_service.get_user_tenants(sample_user_entity, sample_app_entity)

        assert isinstance(result, list)
        assert len(result) == 1  # 只有一个有效租户
        assert result[0].tenant_id == tenant_model.tenant_id
