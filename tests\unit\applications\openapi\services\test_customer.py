# encoding: utf-8
# tests/unit/applications/openapi/services/test_customer.py
# created: 2025-08-06 21:00:00

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from src.applications.openapi.services.customer import CustomerService
from src.domains.customer.dto import CustomerCreateFields, CustomerDTO
from src.infrastructures import errors


@pytest.mark.unit
class TestCustomerService:
    
    @pytest.fixture
    def customer_repo_mock(self):
        return AsyncMock()
    
    @pytest.fixture
    def app_repo_mock(self):
        return AsyncMock()
    
    @pytest.fixture
    def tenant_repo_mock(self):
        return AsyncMock()
    
    @pytest.fixture
    def customer_service(self, customer_repo_mock, app_repo_mock, tenant_repo_mock):
        return CustomerService(
            customer_repo=customer_repo_mock,
            app_repo=app_repo_mock,
            tenant_repo=tenant_repo_mock
        )
    
    @pytest.mark.asyncio
    @patch('src.applications.openapi.services.customer.atomic')
    async def test_create_customer_success(self, mock_atomic, customer_service, app_repo_mock, tenant_repo_mock, customer_repo_mock):
        """测试创建客户成功"""
        # 准备测试数据
        create_fields = CustomerCreateFields(
            name="测试客户",
            description="测试客户描述",
            type=1
        )
        
        # Mock 应用模型
        app_model = MagicMock()
        app_model.id = 1
        app_model.app_id = "OPEN_PLATFORM"
        app_model.name = "开放平台"
        app_model.app_secret = "secret"
        app_model.wechat_app_id = ""
        app_model.wechat_app_secret = ""
        app_model.dingtalk_app_id = ""
        app_model.dingtalk_app_secret = ""
        
        # Mock 客户模型
        customer_model = MagicMock()
        customer_model.id = 1
        customer_model.name = "测试客户"
        customer_model.code = "ABC123"
        customer_model.balance = 0
        
        # 设置 mock 行为
        app_repo_mock.get_by_appid.return_value = app_model
        tenant_repo_mock.save.return_value = None
        customer_repo_mock.create_customer.return_value = customer_model
        
        # Mock CustomerDTO.from_tortoise_orm
        now = datetime.now()
        expected_dto = CustomerDTO(
            id=1,
            name="测试客户",
            code="ABC123",
            description="测试客户描述",
            type=1,
            balance=0,
            created_at=now,
            updated_at=now,
            subscribes=[]
        )
        
        with pytest.mock.patch('src.domains.customer.dto.CustomerDTO.from_tortoise_orm', return_value=expected_dto):
            # 执行测试
            result = await customer_service.create_customer(create_fields, "OPEN_PLATFORM")
            
            # 验证结果
            assert result == expected_dto
            app_repo_mock.get_by_appid.assert_called_once_with("OPEN_PLATFORM")
            tenant_repo_mock.save.assert_called_once()
            customer_repo_mock.create_customer.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_customer_app_not_found(self, customer_service, app_repo_mock):
        """测试创建客户时应用不存在"""
        create_fields = CustomerCreateFields(
            name="测试客户",
            description="测试客户描述",
            type=1
        )
        
        # 设置应用不存在
        app_repo_mock.get_by_appid.return_value = None
        
        # 执行测试并验证异常
        with patch.object(customer_service, 'create_customer', wraps=customer_service.create_customer) as mock_create:
            # 去掉装饰器
            mock_create.__func__ = customer_service.__class__.create_customer.__func__
            with pytest.raises(errors.PassportAppNotFoundError):
                await mock_create(create_fields, "NON_EXISTENT_APP")
    
    @pytest.mark.asyncio
    @patch('src.applications.openapi.services.customer.atomic')
    async def test_recharge_success(self, mock_atomic, customer_service, customer_repo_mock):
        """测试充值成功"""
        # 准备测试数据
        customer_id = 1
        amount = 10000  # 100元，单位分
        description = "测试充值"
        operator_id = "op123"
        operator_name = "操作员"
        
        # Mock 客户模型
        customer_model = MagicMock()
        customer_model.id = 1
        customer_model.code = "ABC123"
        customer_model.name = "测试客户"
        customer_model.balance = 5000
        customer_model.save = AsyncMock()
        
        # 设置 mock 行为
        customer_repo_mock.get_by_id.return_value = customer_model
        
        # Mock CustomerDTO
        now = datetime.now()
        expected_dto = CustomerDTO(
            id=1,
            name="测试客户",
            code="ABC123",
            description="",
            type=1,
            balance=15000,  # 原余额5000 + 充值10000
            created_at=now,
            updated_at=now,
            subscribes=[]
        )
        
        with pytest.mock.patch('src.domains.customer.dto.CustomerDTO.from_tortoise_orm', return_value=expected_dto):
            with pytest.mock.patch('src.databases.models.customer.CustomerRechargeRecord.create', new_callable=AsyncMock):
                # 执行测试
                result = await customer_service.recharge(customer_id, amount, description, operator_id, operator_name)
                
                # 验证结果
                assert result == expected_dto
                assert customer_model.balance == 15000
                customer_repo_mock.get_by_id.assert_called_once_with(customer_id)
                customer_model.save.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.applications.openapi.services.customer.atomic')
    async def test_recharge_customer_not_found(self, mock_atomic, customer_service, customer_repo_mock):
        """测试充值时客户不存在"""
        customer_repo_mock.get_by_id.return_value = None
        
        with pytest.raises(errors.CustomerNotFoundError):
            await customer_service.recharge(1, 10000, "测试", "op123", "操作员")