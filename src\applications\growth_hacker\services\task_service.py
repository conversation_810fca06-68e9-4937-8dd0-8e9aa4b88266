# encoding: utf-8
# src/applications/growth_hacker/services/task_service.py
# created: 2025-07-25 17:20:03

import random
import time
from typing import TYPE_CHECKING, Optional

from loguru import logger
from playwright.async_api import Geolocation, ProxySettings, StorageState

from src.databases.models.growth_hacker import TaskStatus
from src.domains.growth_tasks.interactors.eleme10883 import Eleme10083Interactor
from src.infrastructures.browsers import create_session
from src.infrastructures.browsers.types import (
    DEVICE_INFO,
    WEBVIEW_CONFIGS,
    DeviceInfo,
    MobileModel,
    WebViewInfo,
    WebViewType,
)
from src.infrastructures.exceptions.growth_hacker import (
    AlreadyClaimedError,
    NoIpProxyError,
    ProxyConnectionError,
    ProxySSLError,
    ProxyTunnelError,
    RiskDetectedError,
)

if TYPE_CHECKING:
    from src.databases.models.growth_hacker import UserDeviceCache
    from src.domains.growth_tasks.services import TaskDomainService, UserProfileDomainService
    from src.infrastructures.browsers import PageSession
    from src.infrastructures.ip_proxy import Ip<PERSON><PERSON><PERSON>, ProxyManager

    from ..schemas import TaskMessageContent


class TaskService:

    def __init__(
        self,
        browser_manager: Optional[object],  # 保留参数以兼容 DI 容器，但不使用
        proxy_manager: "ProxyManager",
        user_profile_service: "UserProfileDomainService",
        task_service: "TaskDomainService",
    ) -> None:
        # 不再使用 browser_manager
        self.proxy_manager = proxy_manager
        self.user_profile_service = user_profile_service
        self.task_service = task_service

    async def check_build_task(self, task: "TaskMessageContent") -> bool:
        """检查任务合法性&创建任务"""
        task_dict = task.model_dump()
        valid = await self.task_service.validate_task(**task_dict)
        if valid:
            await self.task_service.create_task(**task_dict)
        return valid

    async def execute_task(self, task: "TaskMessageContent", enable_none_proxy: bool = False) -> None:
        """执行任务"""

        # 初始化环境参数
        user_profile = await self.user_profile_service.get_user_profile(task.phone)
        ip_proxy = await self.proxy_manager.alloc(task.city)
        if not enable_none_proxy and not ip_proxy:
            raise NoIpProxyError()

        # 准备参数
        device = self._convert_to_device_info(user_profile.device_config if user_profile else {})
        webview = self._convert_to_webview_info(user_profile.webview_config if user_profile else {})
        location = Geolocation(latitude=float(task.lat), longitude=float(task.lng))

        # 直接使用数据库中的 cookies 和 local_storage
        # 转换 localStorage 为 Playwright 期望的格式
        local_storage_items = []
        if user_profile and user_profile.local_storage:
            local_storage_items = [{"name": key, "value": value} for key, value in user_profile.local_storage.items()]

        state = StorageState(
            cookies=user_profile.cookies if user_profile and user_profile.cookies else [],
            origins=(  # type: ignore
                [
                    {
                        "origin": "https://h5.ele.me",
                        "localStorage": local_storage_items,  # type: ignore
                    }
                ]
                if local_storage_items
                else []
            ),
        )

        proxy = (
            ProxySettings(server=ip_proxy.server, username=ip_proxy.username, password=ip_proxy.password)
            if ip_proxy
            else None
        )

        success = False
        stime = time.time()
        status = TaskStatus.RUNNING
        error_message = ""  # 用于记录错误消息，默认为空字符串
        await self.task_service.update_task_status(task.task_id, status)

        # 使用新的 create_session 替代 BrowserManager
        async with create_session(device, webview, state, location, proxy) as session:
            try:
                # 执行具体的页面交互
                interactor = Eleme10083Interactor(session)

                await interactor.load_page(task.access_url)
                await interactor.detect_page()
                await interactor.execute_main_action()

                status = TaskStatus.SUCCESS
                success = True
            except AlreadyClaimedError as e:
                success = True
                status = TaskStatus.ALREADY_CLAIMED
                error_message = str(e)
            except RiskDetectedError as e:
                status = TaskStatus.RISK_DETECTED
                error_message = str(e)
            except (ProxyTunnelError, ProxySSLError) as e:
                # 代理连接错误，记录详细信息
                logger.error(f"🔴 代理连接错误 [{e.error_code}]: {e.message}")
                if e.context.get("proxy_server"):
                    logger.error(f"   代理服务器: {e.context['proxy_server']}")
                logger.error(f"   错误类型: {e.context.get('error_type', 'UNKNOWN')}")

                # 标记为代理问题，便于后续分析
                status = TaskStatus.FAILED
                error_message = f"[{e.error_code}] {e.message} (代理: {e.context.get('proxy_server', 'N/A')})"
                # 可以在这里添加逻辑将该代理标记为失败

            except ProxyConnectionError as e:
                # 其他代理连接错误
                logger.error(f"🔴 代理连接问题: {e.message}")
                status = TaskStatus.FAILED
                error_message = f"[{e.error_code}] {e.message}"
            except Exception as e:
                logger.error(f"执行任务失败: {str(e)}")
                status = TaskStatus.FAILED
                error_message = f"执行任务失败: {str(e)}"
            finally:
                # 在上下文关闭前收集用户数据（只在成功时收集）
                if success:
                    await self._update_user_profile_from_session(task.phone, session)

        # 在 session 上下文外处理后续逻辑
        try:
            # 释放ip代理
            if ip_proxy:
                await self.proxy_manager.release(ip_proxy.identify, task.city, success)

            execution_time = time.time() - stime
            if status == TaskStatus.SUCCESS:
                logger.success(f"✅ 执行任务成功, 耗时: {execution_time:.2f}s")
            elif status == TaskStatus.ALREADY_CLAIMED:
                logger.warning(f"🧧 任务已领取, 耗时: {execution_time:.2f}s")
            elif status == TaskStatus.RISK_DETECTED:
                logger.warning(f"🚧 任务风险检测, 耗时: {execution_time:.2f}s")
            elif status == TaskStatus.FAILED:
                logger.error(f"❌ 执行任务失败, 耗时: {execution_time:.2f}s")

            await self.task_service.update_task_status(
                task.task_id, status, message=error_message, execution_time=execution_time
            )
        except Exception as e:
            logger.error(f"任务后处理失败: {str(e)}")

    def _convert_to_device_info(self, device_config: dict) -> DeviceInfo:
        """将数据库中的设备配置转换为 DeviceInfo 对象"""
        if not device_config:
            # 随机选择一个设备型号
            random_model = random.choice(list(MobileModel))
            logger.debug(f"随机选择设备: {random_model.value}")
            return DEVICE_INFO[random_model]

        try:
            return DeviceInfo.model_validate(device_config)
        except Exception as e:
            logger.warning(f"设备配置验证失败: {e}，使用随机设备")
            random_model = random.choice(list(MobileModel))
            return DEVICE_INFO[random_model]

    def _convert_to_webview_info(self, webview_config: dict) -> WebViewInfo:
        """将数据库中的 WebView 配置转换为 WebViewInfo 对象"""
        if not webview_config:
            return WEBVIEW_CONFIGS[WebViewType.SAFARI]

        try:
            return WebViewInfo.model_validate(webview_config)
        except Exception as e:
            logger.warning(f"WebView 配置验证失败: {e}，使用默认配置")
            return WEBVIEW_CONFIGS[WebViewType.SAFARI]

    async def _update_user_profile_from_session(self, phone: str, session: "PageSession") -> None:
        """从 PageSession 更新用户缓存信息"""
        try:
            # 直接调用封装好的方法收集所有浏览器数据
            browser_data = await session.collect_browser_data()

            await self.user_profile_service.update_user_profile(phone, browser_data)
            logger.info("🔄 用户浏览器数据保存完成")
        except Exception as e:
            logger.error(f"更新用户缓存信息失败: {str(e)}")
