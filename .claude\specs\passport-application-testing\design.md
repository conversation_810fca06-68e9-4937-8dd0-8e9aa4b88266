# Design Document

## Overview

This document describes the design for implementing comprehensive test coverage for the passport application module. The testing strategy follows a layered approach, with unit tests for individual components and integration tests for API endpoints. The design leverages pytest as the testing framework, uses dependency injection for mocking, and follows the project's established testing patterns.

## Architecture

### Testing Structure

```
tests/
├── unit/
│   └── applications/
│       └── passport/
│           ├── __init__.py
│           ├── test_login_service.py
│           ├── test_user_service.py
│           ├── test_app_service.py
│           ├── test_authorization_service.py
│           └── queries/
│               ├── test_user_query_service.py
│               ├── test_app_query_service.py
│               └── test_tenant_query_service.py
└── integration/
    └── applications/
        └── passport/
            ├── __init__.py
            ├── test_login_endpoints.py
            ├── test_user_endpoints.py
            └── test_application_endpoints.py
```

### Testing Layers

1. **Unit Tests**: Test individual service methods in isolation
   - Mock all dependencies (repositories, gateways, Redis)
   - Focus on business logic validation
   - Test error handling and edge cases

2. **Integration Tests**: Test complete request-response flows
   - Use test database and Redis instances
   - Verify API contracts
   - Test authentication and authorization

### Key Testing Patterns

1. **Dependency Injection**: Use dependency-injector for clean mocking
2. **Fixtures**: Pytest fixtures for reusable test data and mocks
3. **Async Testing**: Use pytest-asyncio for async service testing
4. **Test Isolation**: Each test runs in isolation with clean state

## Components and Interfaces

### Test Base Classes

```python
# tests/unit/applications/passport/base.py
class BasePassportUnitTest:
    """Base class for passport unit tests with common fixtures"""
    
    @pytest.fixture
    def mock_user_repository(self):
        """Mock user repository fixture"""
        
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client fixture"""
```

### Mock Factories

```python
# tests/unit/applications/passport/factories.py
class UserFactory:
    """Factory for creating test user entities"""
    
    @staticmethod
    def create_user(uid: str = None, phone: str = None) -> UserEntity:
        """Create a test user entity"""

class AppFactory:
    """Factory for creating test app entities"""
    
    @staticmethod
    def create_app(app_id: str = None) -> AppEntity:
        """Create a test app entity"""
```

### Test Fixtures

```python
# tests/conftest.py
@pytest.fixture
async def test_db():
    """Provide test database connection"""
    
@pytest.fixture
async def test_redis():
    """Provide test Redis connection"""
    
@pytest.fixture
async def authenticated_client():
    """Provide authenticated test client"""
```

## Data Models

### Test Data Structures

```python
# Test user data
test_user = {
    "uid": "test_user_123",
    "phone": "13800138000",
    "nickname": "Test User",
    "avatar_url": "https://example.com/avatar.jpg",
    "email": "<EMAIL>"
}

# Test app data
test_app = {
    "app_id": "test_app_001",
    "app_name": "Test Application",
    "app_secret": "test_secret_key",
    "wechat_app_id": "wx_test_123",
    "dingtalk_app_id": "dt_test_123"
}

# Test tenant data
test_tenant = {
    "tenant_id": "test_tenant_001",
    "tenant_name": "Test Tenant",
    "is_active": True
}
```

### Mock Response Patterns

```python
# Successful login response
login_success_response = {
    "code": 0,
    "msg": "success",
    "data": {
        "token": "jwt_token_here",
        "user": test_user
    }
}

# Error response
error_response = {
    "code": 400001,
    "msg": "验证码错误",
    "data": None
}
```

## Error Handling

### Test Error Scenarios

1. **Authentication Errors**
   - Invalid credentials
   - Expired tokens
   - Missing authentication headers

2. **Validation Errors**
   - Invalid phone numbers
   - Missing required fields
   - Invalid verification codes

3. **Business Logic Errors**
   - User not found
   - App not found
   - Tenant not found
   - Rate limiting

### Error Testing Strategy

```python
@pytest.mark.parametrize("error_case", [
    ("invalid_phone", "手机号格式错误"),
    ("invalid_code", "验证码错误"),
    ("user_not_found", "用户不存在"),
])
async def test_login_errors(error_case, expected_message):
    """Test various login error scenarios"""
```

## Testing Strategy

### Unit Test Coverage Goals

1. **Service Layer Coverage**
   - LoginService: 100% method coverage
   - UserService: 100% method coverage
   - AppService: 100% method coverage
   - AuthorizationService: 100% method coverage
   - Query Services: 100% method coverage

2. **Edge Case Testing**
   - Null/empty inputs
   - Boundary conditions
   - Concurrent operations
   - Transaction rollbacks

### Integration Test Coverage Goals

1. **API Endpoint Coverage**
   - All HTTP methods (GET, POST, PUT, DELETE)
   - All response codes (2xx, 4xx, 5xx)
   - Request validation
   - Response serialization

2. **Authentication Flow Testing**
   - SMS login flow
   - WeChat login flow
   - DingTalk login flow
   - Token refresh flow

### Test Execution Strategy

1. **Local Development**
   ```bash
   # Run all passport tests
   poetry run pytest tests/unit/applications/passport -v
   
   # Run with coverage
   poetry run pytest tests/unit/applications/passport --cov=src/applications/passport
   ```

2. **CI/CD Pipeline**
   ```yaml
   - name: Run Passport Tests
     run: |
       poetry run pytest tests/unit/applications/passport
       poetry run pytest tests/integration/applications/passport
   ```

### Mock Strategy

1. **Repository Mocks**
   - Return predefined entities
   - Simulate database errors
   - Track method calls

2. **Gateway Mocks**
   - Mock external API responses
   - Simulate network failures
   - Test rate limiting

3. **Redis Mocks**
   - Mock cache operations
   - Simulate cache misses
   - Test TTL behavior

### Test Documentation

Each test file includes:
- Module docstring explaining what is tested
- Method docstrings for complex test scenarios
- Inline comments for non-obvious test logic
- Clear test method names following `test_<method>_<scenario>_<expected_result>` pattern