# encoding: utf-8
# docs/growth-hacker-architecture.md
# created: 2025-08-08 10:30:00

# Growth Hacker 并发架构图

## 架构关系图

```mermaid
graph TB
    subgraph "任务层 Task Layer"
        T1[Task 1<br/>城市: 北京]
        T2[Task 2<br/>城市: 上海]
        T3[Task 3<br/>城市: 深圳]
        TN[Task N<br/>城市: ...]
    end

    subgraph "任务调度器 Task Scheduler"
        TS[TaskService<br/>并发控制器]
        TQ[任务队列<br/>asyncio.Semaphore]
    end

    subgraph "会话管理层 Session Manager"
        S1[Session 1]
        S2[Session 2]
        S3[Session 3]
        SN[Session N]
    end

    subgraph "浏览器池 Browser Pool"
        BP[BrowserPool<br/>轮询分配器]
        subgraph "浏览器实例"
            B1[Browser 1<br/>usage: 15/100<br/>current: 2]
            B2[Browser 2<br/>usage: 32/100<br/>current: 3]
            B3[Browser 3<br/>usage: 8/100<br/>current: 1]
        end
        BC1[Context 1.1]
        BC2[Context 1.2]
        BC3[Context 2.1]
        BC4[Context 2.2]
        BC5[Context 2.3]
        BC6[Context 3.1]
    end

    subgraph "代理管理器 Proxy Manager"
        PM[ProxyManager<br/>分配控制器]
        subgraph "代理池"
            P1[Proxy 1<br/>城市: 北京<br/>锁定: Task1]
            P2[Proxy 2<br/>城市: 上海<br/>锁定: Task2]
            P3[Proxy 3<br/>城市: 深圳<br/>锁定: Task3]
            P4[Proxy 4<br/>城市: 北京<br/>空闲]
            PN[Proxy N<br/>城市: ...<br/>状态: ...]
        end
    end

    %% 任务流程
    T1 --> TS
    T2 --> TS
    T3 --> TS
    TN --> TS
    
    TS --> TQ
    TQ -->|并发限制| S1
    TQ -->|并发限制| S2
    TQ -->|并发限制| S3
    TQ -->|并发限制| SN

    %% Session 获取资源
    S1 -->|1. 获取代理| PM
    S2 -->|1. 获取代理| PM
    S3 -->|1. 获取代理| PM
    
    PM -->|分配&锁定| P1
    PM -->|分配&锁定| P2
    PM -->|分配&锁定| P3

    S1 -->|2. 获取浏览器| BP
    S2 -->|2. 获取浏览器| BP
    S3 -->|2. 获取浏览器| BP

    %% 浏览器分配
    BP -->|轮询分配| B1
    BP -->|轮询分配| B2
    BP -->|轮询分配| B3

    B1 --> BC1
    B1 --> BC2
    B2 --> BC3
    B2 --> BC4
    B2 --> BC5
    B3 --> BC6

    %% Context 绑定
    BC1 -.->|绑定| S1
    BC3 -.->|绑定| S2
    BC6 -.->|绑定| S3

    style T1 fill:#e1f5fe
    style T2 fill:#e1f5fe
    style T3 fill:#e1f5fe
    style TN fill:#e1f5fe
    
    style TS fill:#fff3e0
    style TQ fill:#fff3e0
    
    style BP fill:#f3e5f5
    style PM fill:#f3e5f5
    
    style B1 fill:#c8e6c9
    style B2 fill:#c8e6c9
    style B3 fill:#c8e6c9
    
    style P1 fill:#ffccbc
    style P2 fill:#ffccbc
    style P3 fill:#ffccbc
    style P4 fill:#d7ccc8
```

## 核心组件说明

### 1. 任务层 (Task Layer)
- **并发任务**: 多个任务同时执行，每个任务对应一个城市
- **任务属性**: 城市、用户信息、执行策略等

### 2. 任务调度器 (Task Scheduler)
- **TaskService**: 主控制器，管理任务生命周期
- **并发控制**: 使用 `asyncio.Semaphore` 限制同时执行的任务数
- **任务队列**: 管理待执行任务

### 3. 会话管理层 (Session Manager)
- **PageSession**: 每个任务创建独立的会话
- **资源绑定**: 一个 Session 绑定一个 Proxy + 一个 Context
- **生命周期**: 任务完成后释放所有资源

### 4. 浏览器池 (Browser Pool)
- **轮询分配**: 使用 Round-Robin 算法分配浏览器实例
- **实例管理**: 
  - `usage_count`: 总使用次数（达到上限后重建）
  - `current_usage`: 当前活跃的 Context 数量
- **自动重建**: 当 `current_usage = 0` 且达到重建条件时自动重建
- **Context 隔离**: 每个 Context 独立，互不影响

### 5. 代理管理器 (Proxy Manager)
- **城市匹配**: 根据任务城市分配对应城市的代理
- **锁定机制**: 任务执行期间锁定代理，避免冲突
- **自动释放**: 任务完成后自动释放代理供其他任务使用
- **故障转移**: 代理失败时自动切换到备用代理

## 并发流程

```mermaid
sequenceDiagram
    participant Task
    participant TaskService
    participant Session
    participant ProxyManager
    participant BrowserPool
    participant Context
    participant Proxy

    Task->>TaskService: 提交任务
    TaskService->>TaskService: 检查并发限制
    
    TaskService->>Session: 创建会话
    
    Session->>ProxyManager: 请求代理(城市)
    ProxyManager->>Proxy: 分配&锁定
    Proxy-->>Session: 返回代理配置
    
    Session->>BrowserPool: 请求浏览器
    BrowserPool->>BrowserPool: 轮询选择Browser
    BrowserPool->>Context: 创建新Context
    Context-->>Session: 返回Context
    
    Session->>Session: 执行任务逻辑
    
    Session->>Context: 关闭Context
    Context->>BrowserPool: 更新current_usage(-1)
    
    Session->>ProxyManager: 释放代理
    ProxyManager->>Proxy: 解锁
    
    Session-->>TaskService: 任务完成
    TaskService-->>Task: 返回结果
```

## 资源管理策略

### 浏览器资源
1. **池化管理**: 预创建固定数量的浏览器实例
2. **轮询分配**: 均匀分配负载到各个浏览器
3. **使用计数**: 跟踪每个浏览器的使用情况
4. **自动重建**: 
   - 条件1: `usage_count >= max_usage` (默认100次)
   - 条件2: `lifetime >= max_lifetime` (默认3600秒)
   - 前提: `current_usage == 0` (无活跃Context)

### 代理资源
1. **城市池**: 按城市组织代理池
2. **独占锁定**: 任务执行期间独占代理
3. **超时释放**: 设置锁超时防止死锁
4. **健康检查**: 定期检查代理可用性

### 并发控制
1. **任务级**: Semaphore 限制并发任务数
2. **浏览器级**: 每个浏览器限制最大Context数
3. **代理级**: 每个代理同时只服务一个任务

## 关键配置参数

```python
# 浏览器池配置
BROWSER_COUNT = 3           # 浏览器实例数
BROWSER_MAX_USAGE = 100     # 最大使用次数
BROWSER_MAX_LIFETIME = 3600 # 最大存活时间(秒)

# 代理配置
PROXY_LOCK_TIMEOUT = 300    # 代理锁超时(秒)
PROXY_RETRY_COUNT = 3       # 代理重试次数

# 并发配置
MAX_CONCURRENT_TASKS = 10   # 最大并发任务数
TASK_TIMEOUT = 600          # 任务超时(秒)
```

## 故障处理

1. **浏览器崩溃**: 自动检测并重建浏览器实例
2. **代理失效**: 自动切换到备用代理
3. **任务失败**: 支持重试机制，记录失败原因
4. **资源泄漏**: 强制超时释放，防止资源占用

## 监控指标

- 任务成功率
- 浏览器使用率
- 代理可用率
- 平均任务耗时
- 资源等待时间
- 并发任务数