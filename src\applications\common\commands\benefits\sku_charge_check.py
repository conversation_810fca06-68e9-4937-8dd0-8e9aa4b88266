# encoding: utf-8
# src/applications/common/commands/benefits/sku_charge_check.py
# created: 2025-08-19 10:00:00

from typing import TYPE_CHECKING

from loguru import logger

from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.domains.benefits.services import ChargeStrategyFactory

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsSkuChargeRecord
    from src.domains.benefits.services import BenefitsProductService
    from src.repositories.benefits import SkuChargeRecordRepository


class SkuChargeCheckCommandService:
    """SKU充值检查命令服务"""

    def __init__(self, product_service: "BenefitsProductService", sku_charge_record_repo: "SkuChargeRecordRepository"):
        self.product_service = product_service
        self.sku_charge_record_repo = sku_charge_record_repo

    async def check_charge_status(self, record_id: int, retry_count: int = 0) -> tuple[RecordStatus, bool, int]:
        """
        检查充值状态

        Args:
            record_id: 充值记录ID
            retry_count: 重试次数

        Returns:
            tuple[RecordStatus, bool, int]: (状态, 是否需要检查订单, 下次延迟毫秒数)
        """
        from src.utils.schemas import fib_binet

        logger.info(f"开始检查充值状态: record_id={record_id}, retry_count={retry_count}")

        # 从注入的仓库获取充值记录
        charge_record = await self.sku_charge_record_repo.get_by_id(record_id)
        if not charge_record:
            logger.error(f"充值记录未找到: {record_id}")
            return RecordStatus.FAILED, False, 0

        logger.info(f"获取充值记录[{charge_record.id}]，" f"详情: {charge_record.detail}")

        # 检查充值状态
        charge_status = await self._check_status(charge_record)

        # 根据充值状态进行不同处理
        if charge_status.status == RecordStatus.PROCESSING:
            # 处理中状态：检查重试次数
            if retry_count > 20:
                logger.error(f"充值检查重试次数超过20次，" f"记录: {charge_status.id}，设置状态为失败")
                charge_status.status = RecordStatus.FAILED
                await charge_status.save()
                return RecordStatus.FAILED, True, 0

            # 继续重试
            delay = fib_binet(retry_count + 1) * 1000
            logger.info(
                f"充值状态仍在处理中，准备重试 - "
                f"记录: {charge_status.id}，"
                f"重试次数: {retry_count + 1}，"
                f"延迟: {delay}ms"
            )
            return RecordStatus.PROCESSING, False, delay

        elif charge_status.status in [
            RecordStatus.SUCCESS,
            RecordStatus.FAILED,
            RecordStatus.REFUNDED,
            RecordStatus.USED,
        ]:
            # 终态状态：需要检查订单
            logger.info(f"充值记录已达到终态 - " f"记录: {charge_status.id}，状态: {charge_status.status}")
            # 检查订单状态
            await self.product_service.check_charge_result(charge_record.charge_order_id)
            return charge_status.status, True, 0

        else:
            # 其他状态
            logger.warning(f"充值记录状态异常 - " f"记录: {charge_status.id}，状态: {charge_status.status}")
            # 确保返回的是 RecordStatus 枚举类型
            status = charge_status.status
            if isinstance(status, str):
                status = RecordStatus(status)
            return status, True, 0

    async def _check_status(self, charge_record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """
        调用充值策略检查状态

        Args:
            charge_record: 充值记录

        Returns:
            BenefitsSkuChargeRecord: 更新后的充值记录
        """
        charge_strategy = ChargeStrategyFactory.get_strategy(charge_record.sku.charge_func)
        return await charge_strategy.check_charge_status(charge_record)
