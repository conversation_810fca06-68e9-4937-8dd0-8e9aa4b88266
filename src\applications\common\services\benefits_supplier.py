# encoding: utf-8
# src/applications/common/services/benefits_supplier.py
# created: 2025-08-03 16:02:03

from typing import TYPE_CHECKING, Optional

from loguru import logger

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsPurchase, BenefitsSku, BenefitsSupplier
    from src.repositories.benefits import PurchaseRepository, SkuRepository, SupplierRepository


class BenefitsSupplierService:
    """权益供应商服务

    提供供应商、采购单和SKU的通用查询和管理功能
    """

    def __init__(
        self, supplier_repo: "SupplierRepository", purchase_repo: "PurchaseRepository", sku_repo: "SkuRepository"
    ):
        self.supplier_repo = supplier_repo
        self.purchase_repo = purchase_repo
        self.sku_repo = sku_repo

    async def get_supplier_by_identify(self, identify: str) -> Optional["BenefitsSupplier"]:
        """
        根据标识获取供应商

        Args:
            identify: 供应商标识

        Returns:
            BenefitsSupplier: 供应商对象，不存在时返回None
        """
        supplier = await self.supplier_repo.get_by_identify(identify)
        if not supplier:
            logger.warning(f"供应商不存在: identify={identify}")
        return supplier

    async def get_purchase_by_id(self, purchase_id: str) -> Optional["BenefitsPurchase"]:
        """
        根据采购单ID获取采购单

        Args:
            purchase_id: 采购单ID

        Returns:
            BenefitsPurchase: 采购单对象，不存在时返回None
        """
        purchase = await self.purchase_repo.get_by_purchase_id(purchase_id)
        if not purchase:
            logger.warning(f"采购单不存在: purchase_id={purchase_id}")
        return purchase

    async def get_sku_by_code(self, sku_code: str) -> Optional["BenefitsSku"]:
        """
        根据SKU编码获取SKU

        Args:
            sku_code: SKU编码

        Returns:
            BenefitsSku: SKU对象，不存在时返回None
        """
        sku = await self.sku_repo.get_by_code(sku_code)
        if not sku:
            logger.warning(f"SKU不存在: sku_code={sku_code}")
        return sku

    async def get_available_stock(self, sku_code: str) -> int:
        """
        获取SKU的可用库存

        Args:
            sku_code: SKU编码

        Returns:
            int: 可用库存数量
        """
        sku = await self.sku_repo.get_by_code(sku_code)
        if not sku:
            return 0

        # 计算可用库存：总库存 - 已使用 - 已锁定
        available = sku.stock - sku.used - sku.locked
        return max(0, available)

    async def check_supplier_status(self, identify: str) -> bool:
        """
        检查供应商状态是否正常

        Args:
            identify: 供应商标识

        Returns:
            bool: 供应商是否可用
        """
        supplier = await self.supplier_repo.get_by_identify(identify)
        if not supplier:
            return False

        # 检查供应商是否启用
        return supplier.enabled

    async def get_supplier_purchases(self, supplier_id: int, limit: int = 10) -> list["BenefitsPurchase"]:
        """
        获取供应商的采购单列表

        Args:
            supplier_id: 供应商ID
            limit: 返回数量限制

        Returns:
            list[BenefitsPurchase]: 采购单列表
        """
        purchases = await self.purchase_repo.get_by_supplier_id(supplier_id, limit=limit)
        return purchases
