#!/usr/bin/env python3
# encoding: utf-8
# scripts/check_settings.py
# created: 2025-08-18 16:10:00

"""
配置检查脚本
用于验证各服务的配置是否正确加载
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent))


def check_service_config(service_name: str):
    """检查指定服务的配置"""
    print(f"\n{'='*50}")
    print(f"检查 {service_name} 服务配置")
    print('='*50)
    
    try:
        if service_name == "baseapi":
            from deploys.baseapi.settings import config
        elif service_name == "consumer":
            from deploys.consumer.settings import config
        elif service_name == "openapi":
            from deploys.openapi.settings import config
        elif service_name == "scheduler":
            from deploys.scheduler.settings import config
        elif service_name == "growth_hacker":
            from deploys.growth_hacker.settings import config
        else:
            print(f"❌ 未知的服务: {service_name}")
            return False
            
        # 检查基础配置
        checks = []
        
        # 数据库配置
        if hasattr(config, 'database'):
            mysql_ok = bool(config.database.mysql_uri)
            redis_ok = bool(config.database.redis_uri)
            checks.append(("MySQL URI", mysql_ok))
            checks.append(("Redis URI", redis_ok))
        
        # RabbitMQ 配置
        if hasattr(config, 'rabbitmq'):
            rabbitmq_ok = bool(config.rabbitmq.url)
            checks.append(("RabbitMQ URL", rabbitmq_ok))
        
        # FastAPI 配置（部分服务有）
        if hasattr(config, 'fastapi'):
            port_ok = config.fastapi.port > 0
            checks.append(("FastAPI Port", port_ok))
        
        # 第三方服务配置
        if hasattr(config, 'bifrost'):
            bifrost_ok = bool(config.bifrost.account)
            checks.append(("Bifrost Account", bifrost_ok))
        
        if hasattr(config, 'eleme_union'):
            eleme_ok = bool(config.eleme_union.app_key)
            checks.append(("Eleme Union Key", eleme_ok))
        
        # 打印检查结果
        all_ok = True
        for name, status in checks:
            icon = "✅" if status else "⚠️"
            status_text = "已配置" if status else "未配置"
            print(f"{icon} {name}: {status_text}")
            if not status:
                all_ok = False
        
        # 服务特有配置
        print("\n服务特有配置:")
        if service_name == "growth_hacker":
            if hasattr(config, 'browser'):
                print(f"  浏览器配置: Headless={config.browser.launch.headless}")
            if hasattr(config, 'ip_proxy'):
                print(f"  代理配置: 已加载")
            if hasattr(config, 'logger'):
                print(f"  日志配置: App={config.logger.app_name if hasattr(config.logger, 'app_name') else 'N/A'}")
        elif service_name in ["baseapi", "consumer"]:
            if hasattr(config, 'sms'):
                sms_ok = bool(config.sms.access_key_id)
                print(f"  短信服务: {'已配置' if sms_ok else '未配置'}")
        
        return all_ok
        
    except ImportError as e:
        print(f"❌ 导入配置失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查配置时出错: {e}")
        return False


def main():
    """主函数"""
    print("配置系统检查工具")
    print("=" * 50)
    
    # 检查环境变量
    print("\n环境变量检查:")
    env_vars = [
        "DATABASE__MYSQL_URI",
        "DATABASE__REDIS_URI", 
        "RABBITMQ__URL",
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: 已设置")
        else:
            print(f"⚠️ {var}: 未设置")
    
    # 检查配置文件
    print("\n配置文件检查:")
    config_files = [
        ".env",
        ".env.toml",
    ]
    
    for file in config_files:
        path = Path(file)
        if path.exists():
            print(f"✅ {file}: 存在")
        else:
            print(f"⚠️ {file}: 不存在")
    
    # 检查各服务配置
    services = ["baseapi", "consumer", "openapi", "scheduler", "growth_hacker"]
    
    all_services_ok = True
    for service in services:
        service_ok = check_service_config(service)
        if not service_ok:
            all_services_ok = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_services_ok:
        print("✅ 所有服务配置检查通过")
    else:
        print("⚠️ 部分配置未设置，请检查：")
        print("1. 是否已创建 .env 或 .env.toml 文件")
        print("2. 是否已填入必要的配置值")
        print("3. 环境变量是否正确设置")
        print("\n参考 .env.example 和 .env.toml.example 文件")
    
    return 0 if all_services_ok else 1


if __name__ == "__main__":
    sys.exit(main())