# encoding: utf-8
# src/domains/growth_tasks/interactors/eleme10883/interactor.py
# created: 2025-08-08 01:06:11

import asyncio
import random
import re
import time
from typing import TYPE_CHECKING

from loguru import logger
from playwright.async_api import Error as PlaywrightError

from src.infrastructures.exceptions.growth_hacker import (
    AlreadyClaimedError,
    InteractionError,
    NavigationError,
    ProxyConnectionError,
    ProxySSLError,
    ProxyTunnelError,
)

from .page_detector import InteractionResult, PageDetector
from .user_simulator import UserSimulator

if TYPE_CHECKING:
    from src.infrastructures.browsers import PageSession


class Eleme10083Interactor:
    """饿了么10083 - 天天领红包会场交互器"""

    def __init__(self, session: "PageSession"):
        self.session = session
        self.page_detector = PageDetector(session)
        self.user_simulator = UserSimulator(session)

    async def load_page(self, url: str) -> None:
        """改进的页面加载方法，能识别并处理代理错误"""
        if not self.session.page:
            raise ValueError("PageSession must have a valid page")

        try:
            # 首先加载页面
            await self.session.page.goto(url, wait_until="domcontentloaded", timeout=45000)
            logger.debug(f"🔄 页面基本加载完成: {url}")

        except PlaywrightError as e:
            error_msg = str(e)

            # 提取具体的网络错误类型
            error_patterns = {
                r"ERR_TUNNEL_CONNECTION_FAILED": ProxyTunnelError,
                r"ERR_SSL_PROTOCOL_ERROR": ProxySSLError,
                r"ERR_PROXY_CONNECTION_FAILED": ProxyConnectionError,
                r"ERR_CONNECTION_REFUSED": ProxyConnectionError,
                r"ERR_CONNECTION_RESET": ProxyConnectionError,
                r"ERR_CONNECTION_TIMED_OUT": ProxyConnectionError,
            }

            # 尝试从session获取代理服务器信息
            proxy_server = None
            if hasattr(self.session, "proxy") and self.session.proxy:
                proxy_server = self.session.proxy.get("server")  # 使用 get 方法安全访问

            # 匹配具体错误类型
            for pattern, error_class in error_patterns.items():
                if re.search(pattern, error_msg):
                    logger.error(f"🚫 检测到代理错误 [{pattern}]: {error_msg}")

                    if error_class == ProxyTunnelError:
                        raise ProxyTunnelError(proxy_server=proxy_server, url=url) from e
                    elif error_class == ProxySSLError:
                        raise ProxySSLError(proxy_server=proxy_server, url=url) from e
                    else:
                        # 提取错误类型
                        match = re.search(pattern, error_msg)
                        error_type = match.group(0) if match else "UNKNOWN"
                        raise ProxyConnectionError(
                            message=error_msg, error_type=error_type, proxy_server=proxy_server, url=url
                        ) from e

            # 如果不是代理相关错误，抛出通用导航错误
            logger.error(f"❌ 页面加载失败: {error_msg}")
            raise NavigationError(
                message=f"页面加载失败: {error_msg}",
                target_url=url,
                current_url=self.session.page.url if self.session.page else None,
            ) from e

    async def detect_page(self) -> InteractionResult:
        """检测页面内容"""
        return await self.page_detector.detect_page()

    async def execute_main_action(self) -> InteractionResult:
        """执行主要操作"""
        try:
            await self.user_simulator.simulate_browsing()  # 模拟用户浏览页面
            await self.user_simulator.scroll_to_top()  # 滚动到页面顶部
            await self._click_hongbao_button()  # 点击红包按钮
            await self._wait_for_result()  # 等待结果

            if random.random() < 0.1:
                await self.user_simulator.visit_shop_detail()  # 访问店铺详情

            return InteractionResult(True, "执行主要操作成功")
        except Exception as e:
            raise InteractionError(f"执行主要操作失败: {e}") from e

    async def _click_hongbao_button(self) -> InteractionResult:
        """触摸红包按钮 - H5触摸事件"""
        if not self.session.page:
            return InteractionResult(success=False, message="页面未初始化")

        # 检查按钮是否存在
        if not self.page_detector.hongbao_button:
            return InteractionResult(success=False, message="红包按钮未找到")

        try:
            # 确保按钮仍然可见和可点击
            if not await self.page_detector.hongbao_button.is_visible():
                return InteractionResult(success=False, message="红包按钮不可见")

            if not await self.page_detector.hongbao_button.is_enabled():
                return InteractionResult(success=False, message="红包按钮不可点击")

            # 滚动到按钮位置（确保完全可见）
            await self.page_detector.hongbao_button.scroll_into_view_if_needed()
            wait_time = random.uniform(0.3, 0.8)
            await asyncio.sleep(wait_time)

            # 执行真实的触摸操作
            result = await self.user_simulator.perform_realistic_touch(self.page_detector.hongbao_button)
            if not result:
                return InteractionResult(success=False, message="领取红包过程-触摸操作失败")

            # 等待触摸响应并检查页面变化
            response_wait = random.uniform(0.8, 1.5)
            await asyncio.sleep(response_wait)

            # 简单检查页面是否有响应
            try:
                # 检查按钮状态是否改变
                if self.page_detector.hongbao_button and await self.page_detector.hongbao_button.is_visible():
                    button_text = await self.page_detector.hongbao_button.inner_text()

                    # 如果按钮文本变化，说明点击生效了
                    if any(keyword in button_text for keyword in ["已领取", "已完成", "明天再来", "今日已领"]):
                        return InteractionResult(success=True, message=f"红包领取成功，按钮状态: {button_text}")

                # 检查页面是否有弹窗或提示
                success_messages = await self.session.page.evaluate(
                    """
                    () => {
                        const messages = [];
                        const selectors = [
                            '.toast', '.message', '.alert', '.popup', '.dialog',
                            '[class*="toast"]', '[class*="message"]', '[class*="alert"]'
                        ];

                        selectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                const text = el.textContent?.trim();
                                if (text && text.length > 0) {
                                    messages.push(text);
                                }
                            });
                        });

                        return messages;
                    }
                    """
                )

                if success_messages:
                    for msg in success_messages:
                        if any(keyword in msg for keyword in ["成功", "领取", "红包", "恭喜"]):
                            return InteractionResult(success=True, message=f"红包领取成功: {msg}")

            except Exception:
                pass

            return InteractionResult(success=True, message="触摸操作已执行，等待结果确认")

        except Exception as e:
            return InteractionResult(success=False, message=f"尝试领取红包失败: {e}")

    async def _wait_for_result(self) -> InteractionResult:
        """等待操作结果"""
        if not self.session.page:
            return InteractionResult(success=False, message="页面未初始化")

        start_time = time.time()
        timeout = 12  # 减少超时时间从15秒到12秒
        initial_url = self.session.page.url
        check_interval = 1.2  # 增加检查间隔从0.8秒到1.2秒，减少页面查询频率

        while time.time() - start_time < timeout:
            try:
                # 检查URL变化
                current_url = self.session.page.url
                if current_url != initial_url:
                    await asyncio.sleep(1.0)

                await self.page_detector._check_already_received()
            except AlreadyClaimedError:
                return InteractionResult(success=True, message="红包领取成功（最终确认）")
            except Exception as e:
                logger.debug(f"结果等待检查异常: {e}")
            finally:
                await asyncio.sleep(check_interval)

        return InteractionResult(success=True, message="操作已完成，等待超时")
