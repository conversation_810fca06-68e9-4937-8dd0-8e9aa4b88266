# encoding: utf-8
# scripts/jintie_shop_sync.py
# created: 2025-08-04 13:48:30

import asyncio
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject

from src.databases.models.shops import Area, AreaShops, Shop
from src.infrastructures.databases import RedisManager
from src.interfaces.schedulers import Container
from src.repositories.shops.shops import ShopRepository

if TYPE_CHECKING:
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway


@inject
async def sync_jintie_shop(
    eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway" = Provide[
        Container.infrastructures.eleme_union_delivery_gateway
    ],
):
    shop_repo = ShopRepository()

    session_id = None
    while True:
        session_id, records = await eleme_union_delivery_gateway.get_shop_list(
            pid="alsc_28806810_9518007_30890010",
            lat=31.2,
            lng=121.38,
            session_id=session_id,
            search_content="近铁",
        )
        if len(records) < 20:
            break

        for union_shop in records:
            try:
                # 直接使用UnionShopDto对象创建或更新店铺
                shop = await shop_repo.create_or_update_shop_from_union_data(union_shop)
                print(f"已同步店铺: {shop.title} ({shop.shop_id})")

            except Exception as e:
                print(f"同步店铺失败: {e}")
                continue
        await asyncio.sleep(0.5)


@inject
async def get_shop_info(
    shop_id: str,
    eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway" = Provide[
        Container.infrastructures.eleme_union_delivery_gateway
    ],
):
    pid = "alsc_28806810_9518007_30890010"
    response = await eleme_union_delivery_gateway.get_shop_detail(shop_id, pid)
    from pprint import pprint

    pprint(response)


@inject
async def clear_cache_key(redis_manager: "RedisManager" = Provide[Container.infrastructures.redis_manager]):
    await redis_manager.client.delete("hot_shops_by_area:21wwzRzLxYRx3Pfu")
    await redis_manager.client.delete("shops_by_area:21wwzRzLxYRx3Pfu")


async def main():
    from src.interfaces.schedulers.main import lifespan

    async with lifespan() as container:
        container.wire(modules=[__name__])

        # await sync_jintie_shop()
        # area = Area(title="近铁城市广场内部商圈")
        # await area.save()
        # area = await Area.get_or_none(title="近铁城市广场内部商圈")
        # if not area:
        #     area = Area(title="近铁城市广场内部商圈")
        #     await area.save()

        # shop_list = await Shop.filter(title__contains="近铁").all()
        # for shop in shop_list:
        #     area_shops = AreaShops(area=area, shop=shop)
        #     await area_shops.save()

        # await get_shop_info("F4A698A4C55EB778173E2F554D80B74C")
        # await sync_jintie_shop()

        await clear_cache_key()


if __name__ == "__main__":
    asyncio.run(main())
