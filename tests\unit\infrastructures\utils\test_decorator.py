# encoding: utf-8
# tests/unit/infrastructures/utils/test_decorator.py
# created: 2025-08-04 16:30:00

import json
import pickle
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from redis.asyncio import Redis

from src.infrastructures.utils.decorator import cache, redis_context, SerializationType


# 测试用的类（pickle 需要全局级别的类）
class ComplexObject:
    def __init__(self, id: str, created_at: datetime):
        self.id = id
        self.created_at = created_at
        
    def __eq__(self, other):
        return self.id == other.id and self.created_at == other.created_at


class SimpleTestObject:
    def __init__(self, value: str):
        self.value = value


class TestCacheDecorator:
    """测试缓存装饰器"""
    
    @pytest.fixture
    def mock_redis(self):
        """创建 mock Redis 客户端"""
        redis = AsyncMock(spec=Redis)
        redis.get = AsyncMock(return_value=None)  # 默认缓存未命中
        redis.set = AsyncMock(return_value=True)
        return redis
    
    @pytest.mark.asyncio
    async def test_cache_with_self_redis(self, mock_redis):
        """测试从 self.redis 获取 Redis 实例"""
        
        class TestService:
            def __init__(self):
                self.redis = mock_redis
                
            @cache(key_prefix="test", ttl=60, serialization="json")
            async def get_data(self, item_id: str):
                return {"id": item_id, "data": "test"}
        
        service = TestService()
        result = await service.get_data("123")
        
        # 验证结果
        assert result == {"id": "123", "data": "test"}
        
        # 验证 Redis 调用
        mock_redis.get.assert_called_once_with("test:123")
        mock_redis.set.assert_called_once()
        
        # 验证缓存的内容
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == "test:123"
        assert json.loads(call_args[0][1].decode()) == {"id": "123", "data": "test"}
        assert call_args[1]["ex"] == 60
    
    @pytest.mark.asyncio
    async def test_cache_with_redis_getter(self, mock_redis):
        """测试通过 redis_getter 获取 Redis 实例"""
        
        def get_redis():
            return mock_redis
        
        @cache(key_prefix="user", ttl=300, redis_getter=get_redis)
        async def get_user(user_id: str):
            return {"user_id": user_id, "name": "John"}
            
        result = await get_user("456")
        
        assert result == {"user_id": "456", "name": "John"}
        mock_redis.get.assert_called_once_with("user:456")
        mock_redis.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_with_context_redis(self, mock_redis):
        """测试从上下文获取 Redis 实例"""
        
        # 设置上下文 Redis
        redis_context.set(mock_redis)
        
        @cache(key_prefix="config", ttl=600)
        async def get_config(key: str):
            return {"key": key, "value": "test_value"}
            
        result = await get_config("app_name")
        
        assert result == {"key": "app_name", "value": "test_value"}
        mock_redis.get.assert_called_once_with("config:app_name")
        mock_redis.set.assert_called_once()
        
        # 清理上下文
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_hit(self, mock_redis):
        """测试缓存命中的情况"""
        
        # 模拟缓存命中
        cached_data = {"id": "123", "cached": True}
        mock_redis.get = AsyncMock(return_value=json.dumps(cached_data))
        
        class TestService:
            def __init__(self):
                self.redis = mock_redis
                self.call_count = 0
                
            @cache(key_prefix="hit_test", serialization="json")
            async def get_data(self, item_id: str):
                self.call_count += 1
                return {"id": item_id, "cached": False}
        
        service = TestService()
        result = await service.get_data("123")
        
        # 验证返回缓存的数据
        assert result == cached_data
        assert service.call_count == 0  # 函数没有被执行
        mock_redis.get.assert_called_once_with("hit_test:123")
        mock_redis.set.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_cache_with_multiple_args(self, mock_redis):
        """测试多参数的缓存键生成"""
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="multi")
        async def process_data(category: str, item_id: int, active: bool = True):
            return {"category": category, "item_id": item_id, "active": active}
            
        await process_data("books", 123, active=False)
        
        # 验证生成的缓存键
        expected_key = "multi:books:123:active:False"
        mock_redis.get.assert_called_once_with(expected_key)
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_with_custom_key_func(self, mock_redis):
        """测试自定义键生成函数"""
        
        redis_context.set(mock_redis)
        
        def custom_key(*args, **kwargs):
            # 只使用第一个参数作为键
            return f"custom_{args[0]}"
        
        @cache(key_prefix="custom", key_func=custom_key)
        async def get_item(item_id: str, extra_data: dict = None):
            return {"id": item_id}
            
        await get_item("abc123", extra_data={"foo": "bar"})
        
        mock_redis.get.assert_called_once_with("custom:custom_abc123")
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_without_redis(self):
        """测试没有 Redis 实例时的行为"""
        
        @cache(key_prefix="no_redis")
        async def get_data():
            return {"data": "no_cache"}
            
        # 不应该抛出异常，应该正常执行函数
        result = await get_data()
        assert result == {"data": "no_cache"}
    
    @pytest.mark.asyncio
    async def test_cache_redis_error_handling(self, mock_redis):
        """测试 Redis 操作失败时的错误处理"""
        
        # 模拟 Redis get 操作失败
        mock_redis.get.side_effect = Exception("Redis connection error")
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="error_test")
        async def get_data():
            return {"data": "test"}
            
        # 应该忽略缓存错误，正常执行函数
        result = await get_data()
        assert result == {"data": "test"}
        
        # 即使 get 失败，仍应尝试 set
        mock_redis.set.assert_called_once()
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_set_error_handling(self, mock_redis):
        """测试缓存设置失败时的错误处理"""
        
        # 模拟 Redis set 操作失败
        mock_redis.set.side_effect = Exception("Redis set error")
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="set_error")
        async def get_data():
            return {"data": "test"}
            
        # 应该忽略缓存设置错误，正常返回结果
        result = await get_data()
        assert result == {"data": "test"}
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_with_pickle_serialization(self, mock_redis):
        """测试使用 Pickle 序列化的缓存（默认）"""
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="complex", serialization=SerializationType.PICKLE)
        async def get_complex_object(obj_id: str):
            return ComplexObject(obj_id, datetime(2025, 1, 1, 12, 0, 0))
            
        result = await get_complex_object("test123")
        
        # 验证结果
        assert isinstance(result, ComplexObject)
        assert result.id == "test123"
        
        # 验证 pickle 序列化被调用
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == "complex:test123"
        # 验证是 bytes（pickle 的输出）
        assert isinstance(call_args[0][1], bytes)
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_with_json_serialization(self, mock_redis):
        """测试使用 JSON 序列化的缓存"""
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="simple", ttl=60, serialization="json")
        async def get_simple_data(data_id: str):
            return {"id": data_id, "name": "Test Item", "count": 42}
            
        result = await get_simple_data("item123")
        
        # 验证结果
        assert result == {"id": "item123", "name": "Test Item", "count": 42}
        
        # 验证 JSON 序列化被调用
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == "simple:item123"
        # 验证是编码后的 JSON 字符串
        assert isinstance(call_args[0][1], bytes)
        assert json.loads(call_args[0][1].decode()) == result
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_pickle_hit(self, mock_redis):
        """测试 Pickle 缓存命中"""
        
        # 模拟缓存命中
        cached_obj = SimpleTestObject("cached_value")
        mock_redis.get = AsyncMock(return_value=pickle.dumps(cached_obj))
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="pickle_test", serialization="pickle")
        async def get_object():
            return SimpleTestObject("new_value")
            
        result = await get_object()
        
        # 应该返回缓存的对象
        assert result.value == "cached_value"
        
        redis_context.set(None)
    
    @pytest.mark.asyncio
    async def test_cache_json_with_datetime(self, mock_redis):
        """测试 JSON 序列化处理特殊类型（如 datetime）"""
        
        redis_context.set(mock_redis)
        
        @cache(key_prefix="datetime", serialization="json")
        async def get_data_with_datetime():
            return {
                "id": "123",
                "created_at": datetime(2025, 1, 1, 12, 0, 0),
                "updated_at": None
            }
            
        result = await get_data_with_datetime()
        
        # 验证 default=str 被正确使用
        call_args = mock_redis.set.call_args
        cached_data = json.loads(call_args[0][1].decode())
        assert cached_data["created_at"] == "2025-01-01 12:00:00"
        
        redis_context.set(None)