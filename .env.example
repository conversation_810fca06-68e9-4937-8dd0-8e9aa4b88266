# encoding: utf-8
# .env.example
# created: 2025-08-18 15:40:00
#
# 环境变量配置示例文件
# 复制此文件为 .env 并填入实际的配置值
# 注意：不要将包含敏感信息的 .env 文件提交到版本控制

# ==================== 数据库配置 ====================
# MySQL 数据库连接字符串
DATABASE__MYSQL_URI=mysql://username:password@host:port/database

# Redis 连接字符串
DATABASE__REDIS_URI=redis://username:password@host:port

# ==================== RabbitMQ 配置 ====================
# RabbitMQ 连接 URL
RABBITMQ__URL=amqp://username:password@host:port/vhost

# 连接池大小
RABBITMQ__POOL_SIZE=2

# 每个连接的最大通道数
RABBITMQ__MAX_CHANNELS_PER_CONNECTION=50

# 心跳间隔（秒）
RABBITMQ__HEARTBEAT=60

# 连接超时（秒）
RABBITMQ__CONNECTION_TIMEOUT=10

# 预取数量
RABBITMQ__PREFETCH_COUNT=2

# 消费者实例数
RABBITMQ__CONSUMER_INSTANCES=1

# ==================== FastAPI 配置 ====================
# 服务端口
FASTAPI__PORT=8000

# 工作进程数
FASTAPI__WORKERS=2

# 主机名配置
FASTAPI__HOSTNAME__HOSTNAME=
FASTAPI__HOSTNAME__BASE_FE=

# ==================== 第三方服务配置 ====================

# Bifrost 配置
BIFROST__PUBLIC_KEY=
BIFROST__PRIVATE_KEY=
BIFROST__ACCOUNT=
BIFROST__SECRET_KEY=
BIFROST__BASE_URL=
BIFROST__PREFIX=

# 饿了么联盟配置
ELEME_UNION__APP_KEY=
ELEME_UNION__APP_SECRET=
ELEME_UNION__TOP_GATEWAY_URL=
ELEME_UNION__VERIFY_SSL=true

# WiFi Master 配置
WIFIMASTER__APP_ID=
WIFIMASTER__SSO_URL=

# 阿里云短信服务配置
SMS__ACCESS_KEY_ID=
SMS__ACCESS_KEY_SECRET=
SMS__ENDPOINT=dysmsapi.aliyuncs.com
SMS__SIGN_NAME=
SMS__TEMPLATE_CODE=
SMS__REPEAT_INTERVAL=60

# ==================== Growth Hacker 特有配置 ====================
# （仅在 growth_hacker 服务中使用）

# 调试模式
DEBUG=true

# 浏览器配置
BROWSER__POOL__BROWSER_COUNT=2
BROWSER__POOL__MIN_BROWSERS=1
BROWSER__POOL__MAX_BROWSERS=3
BROWSER__POOL__BROWSER_MAX_USAGE=20
BROWSER__POOL__BROWSER_MAX_LIFETIME=1200
BROWSER__POOL__BROWSER_TIMEOUT=30000
BROWSER__POOL__ENABLE_HEALTH_CHECK=true
BROWSER__POOL__HEALTH_CHECK_INTERVAL=60

BROWSER__LAUNCH__HEADLESS=true

BROWSER__CONTEXT__LOCALE=zh-CN
BROWSER__CONTEXT__TIMEZONE_ID=Asia/Shanghai
BROWSER__CONTEXT__IS_MOBILE=true
BROWSER__CONTEXT__HAS_TOUCH=true
BROWSER__CONTEXT__VIEWPORT_WIDTH=375
BROWSER__CONTEXT__VIEWPORT_HEIGHT=812

# IP 代理配置
IP_PROXY__QG_SHORT_SETTINGS__ENDPOINT=https://share.proxy.qg.net
IP_PROXY__QG_SHORT_SETTINGS__KEY=
IP_PROXY__QG_SHORT_SETTINGS__PASSWORD=

IP_PROXY__QG_LONG_SETTINGS__ENDPOINT=https://longterm.proxy.qg.net
IP_PROXY__QG_LONG_SETTINGS__KEY=
IP_PROXY__QG_LONG_SETTINGS__PASSWORD=

# 日志配置
LOGGER__APP_NAME=growth_hacker

# ==================== 注意事项 ====================
# 1. 使用双下划线 (__) 来表示嵌套字段
# 2. 布尔值使用 true/false
# 3. 数字类型不需要引号
# 4. 字符串类型建议使用引号（特别是包含特殊字符时）
# 5. 生产环境必须设置所有必需的配置项
# 6. 不要在代码中硬编码任何敏感信息