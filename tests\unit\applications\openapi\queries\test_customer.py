# encoding: utf-8
# tests/unit/applications/openapi/queries/test_customer.py
# created: 2025-08-02 17:00:00

"""OpenAPI 客户查询服务测试"""

import pytest

from src.applications.openapi.dto import AccountInfoDTO
from src.applications.openapi.queries.customer import CustomerQueryService
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.unit
class TestCustomerQueryService(BaseOpenapiUnitTest):
    """测试客户查询服务"""

    @pytest.fixture
    def customer_service(self):
        """客户查询服务实例"""
        return CustomerQueryService()

    @pytest.fixture
    def sample_customer(self):
        """示例客户"""
        return CustomerEntity(
            id=1,
            code="customer_001",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=50000,  # 500元
        )

    @pytest.mark.asyncio
    async def test_get_account_info_success(
        self,
        customer_service,
        sample_customer,
    ):
        """测试获取账户信息成功"""
        # 执行查询
        result = await customer_service.get_account_info(sample_customer)
        
        # 验证结果
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == sample_customer.balance
        assert result.code == sample_customer.code

    @pytest.mark.asyncio
    async def test_get_account_info_zero_balance(
        self,
        customer_service,
    ):
        """测试获取账户信息 - 余额为零"""
        # 创建余额为零的客户
        zero_balance_customer = CustomerEntity(
            id=2,
            code="customer_002",
            name="零余额客户",
            description="余额为零的测试客户",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=0,
        )
        
        # 执行查询
        result = await customer_service.get_account_info(zero_balance_customer)
        
        # 验证结果
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == 0
        assert result.code == zero_balance_customer.code

    @pytest.mark.asyncio
    async def test_get_account_info_high_balance(
        self,
        customer_service,
    ):
        """测试获取账户信息 - 高余额"""
        # 创建高余额的客户
        high_balance_customer = CustomerEntity(
            id=3,
            code="customer_003",
            name="高余额客户",
            description="高余额的测试客户",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=********9,  # 很高的余额
        )
        
        # 执行查询
        result = await customer_service.get_account_info(high_balance_customer)
        
        # 验证结果
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == ********9
        assert result.code == high_balance_customer.code

    @pytest.mark.asyncio
    async def test_get_account_info_special_characters_in_code(
        self,
        customer_service,
    ):
        """测试获取账户信息 - 客户编码包含特殊字符"""
        # 创建包含特殊字符的客户编码
        special_customer = CustomerEntity(
            id=4,
            code="customer_测试_001_@#$",
            name="特殊字符客户",
            description="包含特殊字符的客户编码",
            app_id="test_app_001", 
            tenant_id="test_tenant_001",
            balance=12345,
        )
        
        # 执行查询
        result = await customer_service.get_account_info(special_customer)
        
        # 验证结果
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == 12345
        assert result.code == "customer_测试_001_@#$"

    @pytest.mark.asyncio
    async def test_get_account_info_data_consistency(
        self,
        customer_service,
    ):
        """测试获取账户信息的数据一致性"""
        # 使用工厂创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data(
            customer_id="consistency_test_001",
            balance=77777
        )
        
        customer = CustomerEntity(**customer_data)
        
        # 执行查询
        result = await customer_service.get_account_info(customer)
        
        # 验证结果与输入数据的一致性
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == customer_data["balance"]
        assert result.code == customer_data["code"]

    @pytest.mark.asyncio 
    async def test_get_account_info_multiple_calls_consistent(
        self,
        customer_service,
        sample_customer,
    ):
        """测试多次调用获取账户信息的一致性"""
        # 执行多次查询
        result1 = await customer_service.get_account_info(sample_customer)
        result2 = await customer_service.get_account_info(sample_customer)
        result3 = await customer_service.get_account_info(sample_customer)
        
        # 验证结果的一致性
        assert result1.balance == result2.balance == result3.balance
        assert result1.code == result2.code == result3.code
        
        # 验证对象不是同一个实例（返回新的DTO对象）
        assert result1 is not result2
        assert result2 is not result3
        assert result1 is not result3


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.integration
class TestCustomerQueryServiceIntegration:
    """客户查询服务集成测试"""

    @pytest.mark.asyncio
    async def test_complete_customer_query_flow(self):
        """测试完整的客户查询流程"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data(
            customer_id="integration_test_001",
            balance=123456
        )
        
        # 创建服务
        service = CustomerQueryService()
        
        # 创建客户实体
        customer = CustomerEntity(**customer_data)
        
        # 执行查询
        result = await service.get_account_info(customer)
        
        # 验证结果
        assert isinstance(result, AccountInfoDTO)
        assert result.balance == customer_data["balance"]
        assert result.code == customer_data["code"]

    @pytest.mark.asyncio
    async def test_service_with_various_customer_types(self):
        """测试服务处理各种客户类型"""
        service = CustomerQueryService()
        
        # 测试数据集
        test_cases = [
            {
                "name": "普通客户",
                "data": OpenAPITestDataFactory.create_customer_data(balance=10000),
            },
            {
                "name": "零余额客户", 
                "data": OpenAPITestDataFactory.create_customer_data(balance=0),
            },
            {
                "name": "高余额客户",
                "data": OpenAPITestDataFactory.create_customer_data(balance=********),
            },
        ]
        
        # 执行测试
        for test_case in test_cases:
            customer = CustomerEntity(**test_case["data"])
            result = await service.get_account_info(customer)
            
            # 验证结果
            assert isinstance(result, AccountInfoDTO)
            assert result.balance == test_case["data"]["balance"]
            assert result.code == test_case["data"]["code"]

    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求处理"""
        import asyncio
        
        service = CustomerQueryService()
        
        # 创建多个客户
        customers = []
        for i in range(10):
            customer_data = OpenAPITestDataFactory.create_customer_data(
                customer_id=f"concurrent_test_{i:03d}",
                balance=1000 * (i + 1)
            )
            customers.append(CustomerEntity(**customer_data))
        
        # 并发执行查询
        tasks = [
            service.get_account_info(customer)
            for customer in customers
        ]
        results = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(results) == 10
        for i, result in enumerate(results):
            assert isinstance(result, AccountInfoDTO)
            assert result.balance == 1000 * (i + 1)
            assert result.code == f"concurrent_test_{i:03d}"

    @pytest.mark.asyncio
    async def test_performance_test(self):
        """测试性能 - 大量查询"""
        import time
        
        service = CustomerQueryService()
        customer_data = OpenAPITestDataFactory.create_customer_data(balance=50000)
        customer = CustomerEntity(**customer_data)
        
        # 执行大量查询并测量时间
        start_time = time.time()
        
        for _ in range(1000):
            result = await service.get_account_info(customer)
            assert isinstance(result, AccountInfoDTO)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能要求（1000次查询应该在1秒内完成）
        assert execution_time < 1.0, f"Performance test failed: {execution_time:.3f}s for 1000 queries"