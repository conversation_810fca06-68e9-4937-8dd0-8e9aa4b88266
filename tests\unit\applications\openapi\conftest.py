# encoding: utf-8
# tests/unit/applications/openapi/conftest.py
# created: 2025-08-02 14:55:00

"""OpenAPI 应用层测试配置"""

import pytest

# 注册 pytest 标记
def pytest_configure(config):
    """配置 pytest 标记"""
    config.addinivalue_line("markers", "openapi: mark test as openapi application layer test")
    config.addinivalue_line("markers", "authenticator: mark test as authenticator test")
    config.addinivalue_line("markers", "query_service: mark test as query service test")
    config.addinivalue_line("markers", "business_service: mark test as business service test")
    config.addinivalue_line("markers", "container: mark test as container test")
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "performance: mark test as performance test")
    config.addinivalue_line("markers", "security: mark test as security test")


@pytest.fixture(autouse=True)
def openapi_test_setup():
    """OpenAPI 测试自动设置"""
    # 可以在这里添加测试前的通用设置
    yield
    # 可以在这里添加测试后的清理工作