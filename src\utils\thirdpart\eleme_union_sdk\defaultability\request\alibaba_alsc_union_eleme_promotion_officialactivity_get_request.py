from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionOfficialactivityGetRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.officialactivity.get"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest:
    def __init__(
        self,
        pid: str = None,
        activity_id: str = None,
        sid: str = None,
        include_wx_img: bool = None,
        include_qr_code: bool = None,
        include_image: bool = None,
        include_watchword: bool = None,
    ):
        """
        渠道PID
        """
        self.pid = pid
        """
            活动ID
        """
        self.activity_id = activity_id
        """
            三方会员id。长度限制50
        """
        self.sid = sid
        """
            【废弃】
        """
        self.include_wx_img = include_wx_img
        """
            是否返回二维码
        """
        self.include_qr_code = include_qr_code
        """
            是否返回图片
        """
        self.include_image = include_image
        """
            是否返回密令
        """
        self.include_watchword = include_watchword
