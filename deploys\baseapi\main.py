# encoding: utf-8
# deploys/baseapi/main.py
# created: 2025-07-23 16:30:00

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from src.interfaces.http.baseapi import container

from deploys.baseapi.settings import config


def setup_container():
    """设置依赖注入容器"""
    # 配置容器
    container.config.from_pydantic(config)

    # 进行依赖注入的线路连接
    container.wire(
        packages=[
            __name__,
            "src.interfaces.http.baseapi",
            "src.domains.benefits.services.charge_strategy",
            "src.applications.common.commands.benefits",
        ]
    )

    return container


def create_app():
    """创建并配置 FastAPI 应用（用于多进程模式）"""
    setup_container()
    from src.interfaces.http.baseapi.main import create_app as _create_app

    return _create_app(config)


if __name__ == "__main__":
    # 设置容器
    setup_container()

    import uvicorn

    from src.interfaces.http.baseapi.main import create_app as _create_app

    app = _create_app(config)
    uvicorn.run(app, host="0.0.0.0", port=8000)
