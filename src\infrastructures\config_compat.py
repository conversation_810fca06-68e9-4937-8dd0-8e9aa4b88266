# encoding: utf-8
# src/infrastructures/config_compat.py
# created: 2025-08-18 17:10:00

"""
配置系统兼容层
用于向后兼容旧的 config.py，让迁移更平滑
"""

import os
from functools import lru_cache
from typing import Any, Optional

import toml
from pydantic import BaseModel


# 从旧 config.py 复制的必要类定义
class ThirdpartElemeChannelConfig(BaseModel):
    eleme_app_id: Optional[str] = None
    eleme_channle_no: str = ""
    eleme_channel_source: str = ""
    eleme_channel_app_id: str = ""
    eleme_channel_secret: str = ""
    eleme_welfare3pp: Optional[str] = None
    eleme_channel_url: Optional[str] = None
    eleme_channel_pay_type: Optional[str] = None


class ThirdpartElemeUnionConfig(BaseModel):
    app_key: str = ""
    app_secret: str = ""
    top_gateway_url: str = "https://gw.api.taobao.com/router/rest"


class ThirdpartElemeConfig(BaseModel):
    channels: dict[str, ThirdpartElemeChannelConfig] = {}
    union: ThirdpartElemeUnionConfig = ThirdpartElemeUnionConfig()


class ThirdpartAliyunOSSConfig(BaseModel):
    access_key_id: str = ""
    access_key_secret: str = ""
    role_arn: str = ""
    role_session_name: str = ""


class ThirdpartAliyunConfig(BaseModel):
    oss: ThirdpartAliyunOSSConfig = ThirdpartAliyunOSSConfig()


class ThirdpartDingtalkConfig(BaseModel):
    order_notify_url: str = ""


class ThirdpartBifrostConfig(BaseModel):
    public_key: str = ""
    private_key: str = ""
    account: str = ""
    secret_key: str = ""
    base_url: str = ""
    prefix: str = ""


class ThirdpartConfig(BaseModel):
    eleme: ThirdpartElemeConfig = ThirdpartElemeConfig()
    aliyun: ThirdpartAliyunConfig = ThirdpartAliyunConfig()
    dingtalk: ThirdpartDingtalkConfig = ThirdpartDingtalkConfig()
    bifrost: ThirdpartBifrostConfig = ThirdpartBifrostConfig()


class WebappConfig(BaseModel):
    secret: str = "default-secret-key-for-jwt"  # JWT密钥


class Config(BaseModel):
    """兼容旧的 Config 类"""

    # 简化的配置结构，只包含还在使用的部分
    thirdpart: ThirdpartConfig = ThirdpartConfig()
    webapp: WebappConfig = WebappConfig()
    hostname: Any = None  # 根据需要添加


@lru_cache(maxsize=1)
def load_config(path: str = ".env.toml") -> Config:
    """兼容旧的 load_config 函数"""
    try:
        # 尝试从文件加载
        if os.path.exists(path):
            data = toml.load(open(path))
            return Config(**data)
    except Exception:
        pass

    # 返回默认配置
    return Config()


# 全局 config 实例
config = load_config()


# 导出兼容接口
__all__ = ["Config", "config", "load_config"]
