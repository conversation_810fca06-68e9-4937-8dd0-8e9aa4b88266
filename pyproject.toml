[tool.poetry]
name = "service-backend"
version = "0.1.0"
description = ""
authors = ["yaofeng <<EMAIL>>"]
readme = "README.md"
package-mode = false

[[tool.poetry.source]]
name = "mirrors"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

[tool.poetry.dependencies]
python = "^3.12"
certifi = "2021.10.8"
charset-normalizer = "2.0.7"
idna = "3.3"
requests = "^2.31.0"
urllib3 = "1.26.7"
pyjwt = "^2.9.0"
nanoid = "^2.0.0"
rsa = "^4.9"
cryptography = "^43.0.1"
loguru = "^0.7.2"
pymysql = "^1.1.1"
black = "^24.10.0"
alipay-sdk-python = "^3.7.752"
redis = "^5.1.1"
gunicorn = "^23.0.0"
uvicorn = "^0.32.0"
pypinyin = "^0.53.0"
pytest-html = "^4.1.1"
pytest-asyncio = "^0.24.0"
python-dotenv = "^1.0.1"
aliyun-log-python-sdk = "^0.9.12"
fastapi = "^0.115.12"
types-toml = "^0.10.8.20240310"
motor = "^3.6.0"
tortoise-orm = { extras = ["asyncmy"], version = "^0.24.2" }
types-nanoid = "^2.0.0.20240601"
aerich = "^0.8.2"
types-pymysql = "^1.1.0.20241103"
fastapi-cache2 = "^0.2.2"
pandas = "^2.2.3"
alibabacloud-dysmsapi20170525 = "3.1.0"
aio-pika = "^9.5.4"
apscheduler = "^3.11.0"
lark-oapi = "^1.4.8"
openpyxl = "^3.1.5"
asgi-lifespan = "^2.1.0"
pytest = "^8.3.5"
alibabacloud-dingtalk = "^2.1.93"
dependency-injector = "^4.46.0"
pytest-mock = "^3.14.0"
ruff = "^0.11.8"
aliyun-python-sdk-core = "^2.16.0"
aliyun-python-sdk-sts = "^3.1.2"
aiohttp = "^3.10.0"
pydantic = "^2.0.0"
pycryptodome = "^3.20.0"
httpx = "^0.28.1"
pytest-cov = "^6.1.1"
faker = "^37.3.0"
psutil = "^7.0.0"
pydantic-settings = "^2.10.1"
playwright = "^1.54.0"
toml = "^0.10.2"
factory-boy = "^3.3.3"
types-pytz = "^2025.2.0.20250516"
rich = "^14.1.0"


[tool.poetry.group.dev.dependencies]
freezegun = "^1.5.3"
mypy = "^1.17.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.aerich]
tortoise_orm = "core.database.tortoise_config"
location = "./migrations"
src_folder = "./."

[tool.black]
line-length = 120
target-version = ['py312']

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = ["--strict-markers", "--tb=short", "-v", "--asyncio-mode=strict"]
markers = [
    "unit: 单元测试",
    "integration: 集成测试",
    "e2e: 端到端测试",
    "slow: 标记为慢速测试",
    "auth: 认证相关测试",
    "benefits: 权益相关测试",
    "passport: 用户相关测试",
    "baseapi: BaseAPI接口相关测试",
    "baseapi_base: BaseAPI Base模块测试",
    "baseapi_ai: BaseAPI AI模块测试",
    "baseapi_mis: BaseAPI MIS模块测试",
    "baseapi_internal: BaseAPI Internal模块测试",
]
pythonpath = ["."]

# 测试目录配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "apps/*",
    "common/*",
    "src/domains/delivery/utils/union_sdk/*",
    "utils/thirdpart/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.mypy]
exclude = ["domains/delivery/utils/union_sdk", "apps", "utils/thirdpart"]
ignore_missing_imports = true


[tool.ruff]
line-length = 120
target-version = "py312"
exclude = [
    "migrations",
    ".venv",
    "tests/_data",
    "packages",
    "utils/thirdpart",
    "tests",
    "apps",
    "common",
    "apis/ai",
    "scripts/schedules/dingtalk_auto_benefits.py",
    "apis/base/routers/dingtalk_blind.py",
    "src/utils/thirdpart/eleme_union_sdk",
]

[tool.ruff.lint]
select = ["E", "F", "I", "B", "N"]
ignore = ["E501", "B008", "F403", "F405", "F401"]
