# encoding: utf-8
# tests/unit/applications/openapi/authenticator/test_token_authenticator.py
# created: 2025-08-02 15:20:00

"""OpenAPI Token 认证器测试"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from src.applications.openapi.authenticator.openapi_token_authenticator import OpenAPITokenAuthenticator
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory, OpenAPIMockFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.authenticator
@pytest.mark.unit
class TestOpenAPITokenAuthenticator(BaseOpenapiUnitTest):
    """测试 OpenAPI Token 认证器"""

    @pytest.fixture
    def auth_fixtures(self):
        """认证固件"""
        return OpenAPIFixtureFactory.create_authentication_fixtures()

    @pytest.fixture
    def mock_customer_secret(self):
        """Mock 客户密钥"""
        mock_secret = MagicMock()
        mock_secret.customer = MagicMock()
        mock_secret.customer.id = 1
        mock_secret.customer.code = "test_customer"
        mock_secret.customer.name = "测试客户"
        mock_secret.customer.description = "测试客户描述"
        mock_secret.customer.app_id = "test_app"
        mock_secret.customer.tenant_id = "test_tenant"
        mock_secret.customer.balance = 10000
        mock_secret.customer.fetch_related = AsyncMock()
        return mock_secret

    @pytest.fixture
    def token_authenticator(self, mock_customer_repository):
        """Token 认证器实例"""
        return OpenAPITokenAuthenticator(customer_repo=mock_customer_repository)

    @pytest.mark.asyncio
    async def test_init(self, mock_customer_repository):
        """测试认证器初始化"""
        authenticator = OpenAPITokenAuthenticator(customer_repo=mock_customer_repository)
        assert authenticator.customer_repo == mock_customer_repository

    @pytest.mark.asyncio
    async def test_authenticate_success(self, token_authenticator, mock_customer_repository, auth_fixtures, mock_customer_secret):
        """测试成功认证"""
        # 准备测试数据
        valid_token = auth_fixtures["valid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {valid_token}"}
        )
        
        # 设置 mock 返回值
        mock_customer_repository.get_secret_by_token.return_value = mock_customer_secret
        
        # Mock CustomerEntity.from_secret
        expected_customer = CustomerEntity(
            id=1,
            code="test_customer",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app",
            tenant_id="test_tenant",
            balance=10000,
        )
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 执行认证
            success, customer, message = await token_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is True
        assert customer is not None
        assert customer.id == 1
        assert customer.code == "test_customer"
        assert message == ""
        
        # 验证方法调用
        mock_customer_repository.get_secret_by_token.assert_called_once_with(valid_token)

    @pytest.mark.asyncio
    async def test_authenticate_missing_authorization_header(self, token_authenticator):
        """测试缺少 Authorization 头"""
        mock_request = OpenAPIMockFactory.create_mock_request(headers={})
        
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Invalid or missing Bearer token"

    @pytest.mark.asyncio
    async def test_authenticate_invalid_authorization_format(self, token_authenticator):
        """测试无效的 Authorization 格式"""
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": "Basic invalid_token"}
        )
        
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Invalid or missing Bearer token"

    @pytest.mark.asyncio
    async def test_authenticate_empty_bearer_token(self, token_authenticator, mock_customer_repository):
        """测试空的 Bearer token"""
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": "Bearer "}
        )
        
        # 设置 mock 返回值 - 空token应该查询不到
        mock_customer_repository.get_secret_by_token.return_value = None
        
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Invalid token"

    @pytest.mark.asyncio
    async def test_authenticate_invalid_token(self, token_authenticator, mock_customer_repository, auth_fixtures):
        """测试无效 token"""
        # 准备测试数据
        invalid_token = auth_fixtures["invalid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {invalid_token}"}
        )
        
        # 设置 mock 返回值 - token 不存在
        mock_customer_repository.get_secret_by_token.return_value = None
        
        # 执行认证
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is False
        assert customer is None
        assert message == "Invalid token"
        
        # 验证方法调用
        mock_customer_repository.get_secret_by_token.assert_called_once_with(invalid_token)

    @pytest.mark.asyncio
    async def test_authenticate_repository_exception(self, token_authenticator, mock_customer_repository, auth_fixtures):
        """测试仓储异常"""
        # 准备测试数据
        valid_token = auth_fixtures["valid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {valid_token}"}
        )
        
        # 设置 mock 抛出异常
        exception_message = "Database connection failed"
        mock_customer_repository.get_secret_by_token.side_effect = Exception(exception_message)
        
        # 执行认证
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is False
        assert customer is None
        assert f"Bearer token authentication failed: {exception_message}" in message
        
        # 验证方法调用
        mock_customer_repository.get_secret_by_token.assert_called_once_with(valid_token)

    @pytest.mark.asyncio
    async def test_authenticate_customer_entity_creation_exception(self, token_authenticator, mock_customer_repository, auth_fixtures, mock_customer_secret):
        """测试客户实体创建异常"""
        # 准备测试数据
        valid_token = auth_fixtures["valid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {valid_token}"}
        )
        
        # 设置 mock 返回值
        mock_customer_repository.get_secret_by_token.return_value = mock_customer_secret
        
        # Mock CustomerEntity.from_secret 抛出异常
        exception_message = "Entity creation failed"
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', side_effect=Exception(exception_message)):
            # 执行认证
            success, customer, message = await token_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is False
        assert customer is None
        assert f"Bearer token authentication failed: {exception_message}" in message

    @pytest.mark.asyncio
    async def test_authenticate_with_expired_token(self, token_authenticator, mock_customer_repository, auth_fixtures):
        """测试过期 token"""
        # 准备测试数据
        expired_token = auth_fixtures["expired_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        
        # 设置 mock 返回值 - token 过期，返回 None
        mock_customer_repository.get_secret_by_token.return_value = None
        
        # 执行认证
        success, customer, message = await token_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is False
        assert customer is None
        assert message == "Invalid token"

    @pytest.mark.asyncio
    async def test_token_extraction(self, token_authenticator, mock_customer_repository):
        """测试 token 提取逻辑"""
        test_cases = [
            ("Bearer abc123", "abc123"),
            ("Bearer token_with_underscores", "token_with_underscores"),
            ("Bearer token-with-dashes", "token-with-dashes"),
            ("Bearer token.with.dots", "token.with.dots"),
        ]
        
        for auth_header, expected_token in test_cases:
            mock_request = OpenAPIMockFactory.create_mock_request(
                headers={"Authorization": auth_header}
            )
            
            # 设置 mock 返回值
            mock_customer_repository.get_secret_by_token.return_value = None
            
            # 执行认证
            await token_authenticator.authenticate(mock_request)
            
            # 验证提取的 token
            mock_customer_repository.get_secret_by_token.assert_called_with(expected_token)
            
            # 重置 mock
            mock_customer_repository.reset_mock()

    @pytest.mark.asyncio
    async def test_multiple_authentication_calls(self, token_authenticator, mock_customer_repository, auth_fixtures, mock_customer_secret):
        """测试多次认证调用"""
        valid_token = auth_fixtures["valid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {valid_token}"}
        )
        
        # 设置 mock 返回值
        mock_customer_repository.get_secret_by_token.return_value = mock_customer_secret
        
        expected_customer = CustomerEntity(
            id=1,
            code="test_customer",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app",
            tenant_id="test_tenant",
            balance=10000,
        )
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 第一次认证
            success1, customer1, message1 = await token_authenticator.authenticate(mock_request)
            
            # 第二次认证
            success2, customer2, message2 = await token_authenticator.authenticate(mock_request)
        
        # 验证两次认证都成功
        assert success1 is True
        assert success2 is True
        assert customer1.id == customer2.id
        assert message1 == message2 == ""
        
        # 验证方法被调用两次
        assert mock_customer_repository.get_secret_by_token.call_count == 2

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_authenticate_performance(self, token_authenticator, mock_customer_repository, auth_fixtures, mock_customer_secret):
        """测试认证性能"""
        import time
        
        valid_token = auth_fixtures["valid_token"]
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {valid_token}"}
        )
        
        # 设置 mock 返回值
        mock_customer_repository.get_secret_by_token.return_value = mock_customer_secret
        
        expected_customer = CustomerEntity(
            id=1,
            code="test_customer",
            name="测试客户", 
            description="测试客户描述",
            app_id="test_app",
            tenant_id="test_tenant",
            balance=10000,
        )
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            start_time = time.time()
            
            # 执行多次认证
            for _ in range(10):
                success, customer, message = await token_authenticator.authenticate(mock_request)
                assert success is True
            
            end_time = time.time()
            
            # 验证性能 - 10次认证应该在1秒内完成
            assert (end_time - start_time) < 1.0


@pytest.mark.openapi
@pytest.mark.authenticator
@pytest.mark.integration
class TestOpenAPITokenAuthenticatorIntegration:
    """OpenAPI Token 认证器集成测试"""

    @pytest.mark.asyncio
    async def test_full_authentication_flow(self):
        """测试完整的认证流程"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        secret_data = OpenAPITestDataFactory.create_customer_secret_data(customer_id=customer_data["id"])
        auth_fixtures = OpenAPIFixtureFactory.create_authentication_fixtures()
        
        # 创建 mock 对象
        mock_customer_repo = OpenAPIMockFactory.create_mock_customer_repository()
        
        # 创建 mock secret
        mock_secret = MagicMock()
        mock_secret.customer = MagicMock(**customer_data)
        mock_secret.customer.fetch_related = AsyncMock()
        
        # 设置返回值
        mock_customer_repo.get_secret_by_token.return_value = mock_secret
        
        # 创建认证器
        authenticator = OpenAPITokenAuthenticator(customer_repo=mock_customer_repo)
        
        # 创建请求
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {auth_fixtures['valid_token']}"}
        )
        
        # Mock CustomerEntity.from_secret
        expected_customer = CustomerEntity(**customer_data)
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 执行认证
            success, customer, message = await authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is True
        assert customer is not None
        assert customer.id == customer_data["id"]
        assert customer.code == customer_data["code"]
        assert message == ""
        
        # 验证仓储方法被正确调用
        mock_customer_repo.get_secret_by_token.assert_called_once_with(auth_fixtures['valid_token'])

    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 创建错误场景
        error_scenarios = OpenAPIFixtureFactory.create_error_scenarios()
        auth_fixtures = OpenAPIFixtureFactory.create_authentication_fixtures()
        
        # 创建 mock 仓储
        mock_customer_repo = OpenAPIMockFactory.create_mock_customer_repository()
        
        # 创建认证器
        authenticator = OpenAPITokenAuthenticator(customer_repo=mock_customer_repo)
        
        # 测试不同的错误场景
        for scenario_name, scenario_data in error_scenarios.items():
            mock_request = OpenAPIMockFactory.create_mock_request(
                headers={"Authorization": f"Bearer {auth_fixtures['valid_token']}"}
            )
            
            # 设置对应的异常
            if scenario_name == "database_error":
                mock_customer_repo.get_secret_by_token.side_effect = Exception(scenario_data["message"])
            elif scenario_name == "authentication_error":
                mock_customer_repo.get_secret_by_token.return_value = None
            
            # 执行认证
            success, customer, message = await authenticator.authenticate(mock_request)
            
            # 验证错误结果
            assert success is False
            assert customer is None
            assert message != ""
            
            # 重置 mock
            mock_customer_repo.reset_mock()
            mock_customer_repo.get_secret_by_token.side_effect = None