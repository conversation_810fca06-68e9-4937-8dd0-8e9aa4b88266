# encoding: utf-8
# src/infrastructures/databases/tortoise.py
# created: 2025-07-27 17:06:51

from typing import TYPE_CHECKING, Union

from tortoise import Tortoise

if TYPE_CHECKING:
    from .settings import DatabaseSettings


async def init_tortoise(config: Union["DatabaseSettings", dict]):

    # 兼容处理两种配置格式
    if isinstance(config, dict):
        mysql_uri = config.get("mysql_uri", config.get("mysql_uri"))
    else:
        mysql_uri = config.mysql_uri

    await Tortoise.init(
        db_url=mysql_uri,
        modules={
            "models": [
                "src.databases.models.growth_hacker",
                "src.databases.models.passport",
                "src.databases.models.customer",
                "src.databases.models.delivery",
                "src.databases.models.benefits",
                "src.databases.models.shops",
            ]
        },
        timezone="Asia/Shanghai",
    )
