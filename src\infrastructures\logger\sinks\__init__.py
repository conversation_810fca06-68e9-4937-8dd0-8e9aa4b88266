# encoding: utf-8
# src/infrastructures/logger/sinks/__init__.py
# created: 2025-08-14 14:52:00

import sys
from typing import Union

from ..settings import ConsoleSinkConfig, FileSinkConfig, SLSSinkConfig
from ..types import SinkType


def create_sink(config: Union[ConsoleSinkConfig, FileSinkConfig, SLSSinkConfig], app_name: str) -> SinkType:
    """创建输出目标实例

    根据配置类型创建对应的输出目标

    Args:
        config: 输出目标配置
        app_name: 应用名称

    Returns:
        输出目标实例或原生对象
    """
    if isinstance(config, ConsoleSinkConfig) or config.type == "console":
        # 对于控制台，直接返回 sys.stdout
        # loguru 会自动处理彩色和格式化
        return sys.stdout

    elif isinstance(config, FileSinkConfig) or config.type == "file":
        # 对于文件，返回处理后的文件路径
        # loguru 会自动处理轮转、压缩等
        from .file import FileSink

        sink = FileSink(config, app_name)

        # 直接返回文件路径字符串
        # 文件相关的配置（rotation、retention等）需要在 core.py 中处理
        return str(sink.file_path)

    elif isinstance(config, SLSSinkConfig) or config.type == "sls":
        # 对于SLS，返回自定义处理器
        from .sls import SLSSink

        return SLSSink(config, app_name)

    else:
        raise ValueError(f"Unknown sink type: {config.type}")


__all__ = ["create_sink"]
