# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/repositories/third_user.py
# created: 2025-02-11 14:59:37
# updated: 2025-04-01 15:26:55

from src.databases.models.passport import PassportThirdUser, PassportUser, ThirdPlatformEnum


class ThirdUserRepository:

    @classmethod
    async def get_third_user(cls, user: PassportUser, platform: ThirdPlatformEnum) -> PassportThirdUser | None:
        return await PassportThirdUser.filter(user=user, platform=platform).first()

    @classmethod
    async def create_third_user(
        cls, user: PassportUser, platform: ThirdPlatformEnum, open_id: str, union_id: str, third_user_info: dict
    ) -> PassportThirdUser:
        return await PassportThirdUser.create(
            user=user, platform=platform, open_id=open_id, union_id=union_id, profile=third_user_info
        )

    @classmethod
    async def get_third_user_by_platform(cls, platform: ThirdPlatformEnum, uid: str) -> PassportThirdUser | None:
        return await PassportThirdUser.filter(platform=platform, user__uid=uid).first()
