# encoding: utf-8
# src/repositories/eleme/eleme_user_profile.py
# created: 2025-08-07 10:47:49

from typing import Optional

from src.databases.models.delivery import Eleme<PERSON>dd<PERSON>, ElemeUserProfile


class ElemeUserProfileRepository:

    async def get_count_by_city_lifecycle(self, city: str, lifecycle: str) -> int:
        """根据城市和标签获取饿了么用户数量"""
        count = await ElemeUserProfile.filter(main_city__contains=city, lifecycle_stage=lifecycle).count()
        return count

    async def gets_by_city_lifecycle(
        self,
        city: str,
        lifecycle: str,
        offset: int = 0,
        limit: int = 100,
    ) -> list[ElemeUserProfile]:
        """根据城市和标签获取饿了么用户"""
        users = (
            await ElemeUserProfile.filter(main_city__contains=city, lifecycle_stage=lifecycle)
            .order_by("created_at")
            .offset(offset)
            .limit(limit)
            .all()
        )
        return users

    async def get_last_address(self, phone: str) -> Optional[ElemeAddress]:
        """获取用户最后一次地址"""
        address = await ElemeAddress.filter(alsc_phone=phone).order_by("-created_at").first()
        return address
