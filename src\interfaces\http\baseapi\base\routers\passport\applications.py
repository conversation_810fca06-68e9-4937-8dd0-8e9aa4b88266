# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/routers/passport/applications.py
# created: 2025-05-27 01:09:51
# updated: 2025-05-27 01:17:01

import base64
from typing import TYPE_CHECKING

from fastapi import APIRouter, Depends

from src.infrastructures.gateways.wechat import WechatMpGateway

from ...authorization import base_api_authentication, get_env
from ...schemas import Env
from .schemas import GetWechatMpUnlimitedQrcodeParams, Qrcode, QrcodeResponse

if TYPE_CHECKING:
    from src.domains.passport.entities import UserEntity


router = APIRouter(tags=["Passport", "Applications"])


@router.post("/applications/wechat_mp/unlimited_qrcode", response_model=QrcodeResponse)
async def get_wechat_mp_qrcode(
    params: GetWechatMpUnlimitedQrcodeParams,
    current_user: "UserEntity" = Depends(base_api_authentication),
    env: Env = Depends(get_env),
):
    wechat_mp_gateway = WechatMpGateway(app_id=env.app.wechat_app_id, app_secret=env.app.wechat_app_secret)
    buffer = await wechat_mp_gateway.get_unlimited_qrcode(**params.model_dump())
    return QrcodeResponse(data=Qrcode(base64=base64.b64encode(buffer).decode()))
