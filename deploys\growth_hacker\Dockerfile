# Growth Hacker Service Dockerfile
# 多阶段构建，优化镜像大小

FROM hicaspian-registry.cn-shanghai.cr.aliyuncs.com/backend/python:3.12-poetry

# 设置时区和环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装基础系统依赖
RUN rm -f /etc/apt/sources.list.d/* && \
    echo "deb http://mirrors.aliyun.com/debian bookworm main contrib non-free non-free-firmware\n\
deb http://mirrors.aliyun.com/debian bookworm-updates main contrib non-free non-free-firmware\n\
deb http://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free non-free-firmware" \
> /etc/apt/sources.list && \
    apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    tzdata && \
    rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 安装Poetry并配置镜像源
# RUN pip install --no-cache-dir poetry==1.8.2 -i https://mirrors.aliyun.com/pypi/simple/
RUN poetry config virtualenvs.create false
# RUN poetry config repositories.pypi https://mirrors.aliyun.com/pypi/simple/

# 复制依赖文件并安装Python依赖
COPY pyproject.toml poetry.lock ./
RUN poetry install --no-interaction --no-ansi --no-root --without dev --only main

# 安装playwright - 注意playwright已经包含在pyproject.toml中，所以不需要单独pip install
# 安装浏览器所需的系统依赖
RUN playwright install-deps chromium
# 安装 Chromium 浏览器
RUN playwright install chromium

# 复制应用代码
COPY . /app

# 创建必要的目录
RUN mkdir -p logs screenshots datas/input

# 保持root用户运行（移除创建和切换用户的步骤）

# 暴露端口（SAE默认使用8080）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD python -c "import sys; sys.exit(0)"

# 启动命令
CMD ["python", "-m", "deploys.growth_hacker.main"]