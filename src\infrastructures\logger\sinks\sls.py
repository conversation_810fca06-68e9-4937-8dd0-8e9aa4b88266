# encoding: utf-8
# src/infrastructures/logger/sinks/sls.py
# created: 2025-08-14 14:48:00

import time
from collections import deque
from pathlib import Path
from threading import Lock, Thread
from typing import Deque, List, Tuple

from aliyun.log import LogClient, LogItem, PutLogsRequest

from ..settings import SLSSinkConfig
from ..types import LogMessage
from .base import BaseSink


class SLSSink(BaseSink):
    """简化的阿里云SLS输出实现

    批量发送日志到阿里云SLS
    支持异步和同步模式，自动批处理和定时刷新
    """

    def __init__(self, config: SLSSinkConfig, app_name: str):
        """初始化SLS输出

        Args:
            config: SLSSinkConfig 配置
            app_name: 应用名称
        """
        super().__init__(config, app_name)

        # 初始化SLS客户端
        self.client = LogClient(config.endpoint, config.access_key_id, config.access_key_secret)

        self.project = config.project
        self.logstore = config.logstore.replace(".", "-")  # 替换不合法字符
        self.batch_size = config.batch_size
        self.flush_interval = config.flush_interval

        # 缓冲区和锁
        self.buffer: Deque[LogItem] = deque(maxlen=config.batch_size * 10)
        self.lock = Lock()
        self.last_flush = time.time()

        # 启动后台刷新线程
        self._start_flush_thread()

    def write(self, message: LogMessage) -> None:
        """写入消息到缓冲区

        Args:
            message: loguru Message 对象
        """
        log_item = self._create_log_item(message)

        with self.lock:
            self.buffer.append(log_item)

            # 如果缓冲区满了，立即刷新
            if len(self.buffer) >= self.batch_size:
                self._flush()

    def _create_log_item(self, message: LogMessage) -> LogItem:
        """创建SLS日志项

        Args:
            message: loguru Message 对象

        Returns:
            LogItem 实例
        """
        record = message.record

        # 获取相对路径
        file_path = record["file"].path
        try:
            base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
            rel_path = str(Path(file_path).relative_to(base_dir))
        except ValueError:
            rel_path = file_path

        # 构建日志内容
        contents: List[Tuple[str, str]] = [
            ("time", record["time"].strftime(self.config.formatter.time_format)),
            ("level", record["level"].name),
            ("file", rel_path),
            ("line", str(record["line"])),
            ("function", record["function"]),
            ("message", record["message"]),
        ]

        # 添加异常信息
        if record.get("exception"):
            exception = record["exception"]
            if exception:
                contents.append(("exception", str(exception)))

        # 添加额外字段
        for key, value in record.get("extra", {}).items():
            if isinstance(value, (str, int, float, bool)):
                contents.append((key, str(value)))

        return LogItem(int(time.time()), contents=contents)

    def _flush(self) -> None:
        """刷新缓冲区到SLS

        线程安全的同步刷新
        """
        if not self.buffer:
            return

        # 获取当前批次
        batch = []
        for _ in range(min(self.batch_size, len(self.buffer))):
            if self.buffer:
                batch.append(self.buffer.popleft())

        if batch:
            try:
                request = PutLogsRequest(self.project, self.logstore, logitems=batch)
                self.client.put_logs(request)
            except Exception as e:
                # 避免日志循环，使用print输出错误
                import sys

                print(f"SLS flush error: {e}", file=sys.stderr)

        self.last_flush = time.time()

    def _flush_thread(self) -> None:
        """后台刷新线程"""
        while True:
            time.sleep(self.flush_interval)

            with self.lock:
                # 如果超过刷新间隔，强制刷新
                if time.time() - self.last_flush >= self.flush_interval:
                    self._flush()

    def _start_flush_thread(self) -> None:
        """启动后台刷新线程"""
        thread = Thread(target=self._flush_thread, daemon=True)
        thread.start()

    def close(self) -> None:
        """关闭时刷新所有缓冲区"""
        with self.lock:
            while self.buffer:
                self._flush()
