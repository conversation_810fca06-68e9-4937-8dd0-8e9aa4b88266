# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/messages.py
# created: 2025-02-12 13:38:04
# updated: 2025-04-13 12:58:08


from datetime import datetime

from pydantic import BaseModel, Field

from src.infrastructures.rabbitmq import message_creator

from .dto import UnionOrderInfoDTO

# 饿了么联盟订单同步消息
UnionOrderSyncMessage = message_creator(
    "UnionOrderSyncMessage", UnionOrderInfoDTO, "delivery.union_order", exchange_name="delivery.topic"
)


class DeliveryWashOrderExportMessageContent(BaseModel):
    """洗单任务导出消息内容"""

    export_id: str = Field(..., description="导出任务ID")
    filters: dict = Field(..., description="导出过滤条件")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class DeliveryWashOrderRunMessageContent(BaseModel):
    """洗单任务运行消息内容"""

    export_id: str = Field(..., description="导出任务ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


DeliveryWashOrderExportMessage = message_creator(
    "DeliveryWashOrderExportMessage",
    DeliveryWashOrderExportMessageContent,
    "delivery.wash_order_export",
    exchange_name="delivery.topic",
)

DeliveryWashOrderRunMessage = message_creator(
    "DeliveryWashOrderRunMessage",
    DeliveryWashOrderRunMessageContent,
    "delivery.wash_order_run",
    exchange_name="delivery.topic",
)


class WashOrderTaskMessageContent(BaseModel):
    phone: str = Field(..., description="用户手机号")
    city: str = Field(default="", description="城市")
    lat: str | float = Field(..., description="纬度")
    lng: str | float = Field(..., description="经度")
    access_url: str = Field(..., description="访问URL")
    task_id: str = Field(..., description="任务ID")
    batch_name: str = Field(default="", description="批次名称")


WashOrderTaskMessage = message_creator(
    "WashOrderTaskMessage", WashOrderTaskMessageContent, "delivery.wash_order_task", exchange_name="delivery.topic"
)
