from typing import Dict, Optional

from loguru import logger
from pydantic import BaseModel, Field
from tortoise.functions import Count

from src.databases.models.delivery import ElemeAdd<PERSON>, ElemeUserProfile


class GenerateWashOrderTasksPayload(BaseModel):
    batch_name_prefix: Optional[str] = Field(default=None, description="批次名称前缀")
    city: str = Field(..., description="城市")
    city_py: str = Field(..., description="城市拼音")
    lifecycle_stage: str = Field(..., description="生命周期阶段")
    page: Optional[int] = Field(default=1, description="页码")
    page_size: Optional[int] = Field(default=10, description="每页条数")
    page_code: Optional[str] = Field(default=None, description="页面代码")


class DeliveryUserRepository:
    @classmethod
    async def get_users_list_with_address(cls, payload: GenerateWashOrderTasksPayload):
        payload.page = payload.page or 1
        payload.page_size = payload.page_size or 500

        query = ElemeUserProfile.filter(
            main_city__contains=payload.city, order_count__gt=0, lifecycle_stage=payload.lifecycle_stage
        ).order_by("-created_at")

        if payload.page > 0:
            query = query.offset((payload.page - 1) * payload.page_size)

        users = await query.limit(payload.page_size).all()

        user_list = []
        for user in users:
            address = await ElemeAddress.filter(alsc_phone=user.buyer_phone, city_name__contains=payload.city).first()

            if address:
                user_list.append(
                    {
                        "phone": user.buyer_phone,
                        "lat": str(address.receive_lat),
                        "lng": str(address.receive_lng),
                        "city": payload.city_py,
                        "lifecycle_stage": payload.lifecycle_stage,
                        "hourly_order_distribution": user.hourly_order_distribution,
                    }
                )

        logger.info(f"获取到 {len(user_list)} 个有效用户")
        return user_list

    @classmethod
    async def get_users_by_city(cls, city: str) -> Dict[str, int]:
        """
        根据城市获取各个生命周期阶段的用户数量

        Args:
            city: 城市名称

        Returns:
            Dict[str, int]: 各个lifecycle_stage对应的人数字典
        """
        result = (
            await ElemeUserProfile.filter(main_city__contains=city)
            .group_by("lifecycle_stage")
            .annotate(count=Count("id"))
            .values("lifecycle_stage", "count")
        )

        # 转换为字典格式
        lifecycle_count_dict = {item["lifecycle_stage"]: item["count"] for item in result}

        logger.info(f"城市 {city} 的生命周期用户统计: {lifecycle_count_dict}")
        return lifecycle_count_dict

    @classmethod
    async def get_user_by_phone_with_address(cls, phone: str) -> Optional[tuple[ElemeUserProfile, ElemeAddress]]:
        user = await ElemeUserProfile.filter(buyer_phone=phone).first()
        if not user:
            return None

        address = await ElemeAddress.filter(alsc_phone=phone, city_name__contains=user.main_city).first()
        if not address:
            return None

        return user, address
