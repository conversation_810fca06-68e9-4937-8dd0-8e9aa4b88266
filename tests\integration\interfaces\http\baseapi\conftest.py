# encoding: utf-8
# tests/integration/interfaces/http/baseapi/conftest.py
# created: 2025-08-02 10:30:00

from typing import AsyncGenerator
import pytest
import httpx
from fastapi import FastAPI

from src.interfaces.http.baseapi.main import app as baseapi_app
from src.interfaces.http.baseapi import Container as ApplicationContainer


@pytest.fixture(scope="session")
async def integration_container() -> ApplicationContainer:
    """创建集成测试的依赖注入容器"""
    container = ApplicationContainer()
    container.config.from_dict({
        "database": {
            "url": "sqlite:///integration_test.db", 
            "echo": False,
        },
        "redis": {
            "url": "redis://localhost:6379/2",  # 使用不同的Redis数据库
        }
    })
    container.wire(packages=["src"])
    return container


@pytest.fixture(scope="session")
def integration_app(integration_container: ApplicationContainer) -> FastAPI:
    """创建集成测试用的FastAPI应用实例"""
    # baseapi_app.dependency_overrides[ApplicationContainer] = lambda: integration_container
    return baseapi_app


@pytest.fixture
async def integration_client(integration_app: FastAPI) -> AsyncGenerator[httpx.AsyncClient, None]:
    """创建集成测试异步客户端"""
    async with httpx.AsyncClient(app=integration_app, base_url="http://integration-test") as ac:
        yield ac


@pytest.fixture
def integration_auth_headers() -> dict[str, str]:
    """创建集成测试认证头部"""
    return {
        "Authorization": "Bearer integration_test_token",
        "X-Tenant-ID": "integration_tenant", 
        "X-App-ID": "integration_app"
    }


@pytest.fixture(autouse=True, scope="session")
async def setup_integration_environment():
    """设置集成测试环境"""
    # 测试前设置数据库表和初始数据
    yield
    # 测试后清理
    pass