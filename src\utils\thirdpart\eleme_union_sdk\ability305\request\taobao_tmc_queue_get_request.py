from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class TaobaoTmcQueueGetRequest(BaseRequest):

    def __init__(self, group_name: str = None):
        """
        TMC组名
        """
        self._group_name = group_name

    @property
    def group_name(self):
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        if isinstance(group_name, str):
            self._group_name = group_name
        else:
            raise TypeError("group_name must be str")

    def get_api_name(self):
        return "taobao.tmc.queue.get"

    def to_dict(self):
        request_dict = {}
        if self._group_name is not None:
            request_dict["group_name"] = convert_basic(self._group_name)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
