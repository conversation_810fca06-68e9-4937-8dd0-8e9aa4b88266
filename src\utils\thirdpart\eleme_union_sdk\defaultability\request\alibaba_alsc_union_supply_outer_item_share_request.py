from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionSupplyOuterItemShareRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.supply.outer.item.share"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionSupplyOuterItemShareOuterItemShareRequest:
    def __init__(
        self,
        scene: str = None,
        object_id: str = None,
        object_type: int = None,
        pid: str = None,
        tk_promotion_url: str = None,
        sid: str = None,
    ):
        """
        业务场景
        """
        self.scene = scene
        """
            商品ID或会场ID
        """
        self.object_id = object_id
        """
            16-商品，17-活动
        """
        self.object_type = object_type
        """
            pid
        """
        self.pid = pid
        """
            淘宝联盟推广地址（淘宝联盟的单品二合一领券链接），需要使用短链
        """
        self.tk_promotion_url = tk_promotion_url
        """
            sid
        """
        self.sid = sid
