# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/sku.py
# created: 2025-04-06 10:28:07
# updated: 2025-07-01 23:47:09

from typing import TYPE_CHECKING, List, Optional

from loguru import logger
from tortoise.transactions import atomic

from src.databases.models.benefits import BenefitsSkuChargeRecord
from src.domains.benefits.dto import BenefitsProductOrderDTO
from src.domains.benefits.messages import SkuChargeMessage, SkuChargeMessageContent
from src.domains.benefits.services.charge_strategy import ChargeStrategyFactory
from src.infrastructures.rabbitmq import RabbitMQProducer

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsProductService
    from src.repositories.benefits import PurchaseRepository, SkuChargeRecordRepository, SkuRepository


class BenefitsSkuService:

    def __init__(
        self,
        sku_repo: "SkuRepository",
        sku_charge_record_repo: "SkuChargeRecordRepository",
        purchase_repo: "PurchaseRepository",
        producer: RabbitMQProducer,
    ):
        self.sku_repo = sku_repo
        self.sku_charge_record_repo = sku_charge_record_repo
        self.purchase_repo = purchase_repo
        self.producer = producer

    async def get_sku_record_by_supplier_order_id(self, supplier_order_id: str) -> Optional["BenefitsSkuChargeRecord"]:
        """根据供应商订单号获取充值记录"""
        return await self.sku_charge_record_repo.get_by_supplier_order_id(supplier_order_id)

    @atomic()
    async def charge_skus(self, sku_codes: List[str], order_dto: BenefitsProductOrderDTO):
        skus = await self.sku_repo.gets_by_codes(sku_codes)
        sku_map = {sku.code: sku for sku in skus}  # 创建代码到SKU的映射，以便快速查找
        charge_skus = [
            sku_map.get(code) for code in sku_codes if code in sku_map
        ]  # 按照sku_codes的顺序获取对应的SKU对象

        records = [
            BenefitsSkuChargeRecord(
                sku=sku,
                account=order_dto.account,
                supplier_sku_code=sku.third_part_code,  # type: ignore
                source_identify=order_dto.source_identify,
                charge_order_id=order_dto.order_id,
                app_id=order_dto.app_id,
                tenant_id=order_dto.tenant_id,
            )
            for sku in charge_skus
        ]
        await self.sku_charge_record_repo.create_multi_records(records)

        records = await self.sku_charge_record_repo.gets_by_order_id(order_dto.order_id)
        logger.info(f"批量创建充值记录, records: {[record.id for record in records]}")

        # 开始处理异步实际发放sku的consumer逻辑
        messages = [SkuChargeMessage(payload=SkuChargeMessageContent(record_id=record.id)) for record in records]
        for message in messages:
            await self.producer.publish_message(message, exchange_name="benefits.topic", delay=500)

    async def update_charge_record_status(self, charge_record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """更新充值记录状态，类似 sku_charge_check 的逻辑"""
        charge_strategy = ChargeStrategyFactory.get_strategy(charge_record.sku.charge_func)
        return await charge_strategy.check_charge_status(charge_record)

    @atomic()
    async def sync_stock_from_purchase(
        self, ticket_id: str, purchase_id: str, op_type: str, sub_type: str, product_service: "BenefitsProductService"
    ) -> None:
        """
        处理联盟库存变化回调

        Args:
            ticket_id: 凭证ID, 对应 benefits_sku_charge_record.supplier_order_id
            purchase_id: 采购单ID
            op_type: 操作类型 (ticket_deliver, ticket_refund)
            sub_type: 子类型 (deliver_success, deliver_fail, refund_success, refund_fail)
            product_service: 产品服务实例，用于检查订单充值结果
        """
        logger.info(
            f"处理联盟库存变化回调: ticket_id={ticket_id}, purchase_id={purchase_id}, "
            f"op_type={op_type}, sub_type={sub_type}"
        )

        # 1. 根据 ticket_id 找到对应的充值记录
        charge_record = await self.sku_charge_record_repo.get_by_supplier_order_id(ticket_id)
        if not charge_record:
            logger.warning(f"未找到对应的充值记录, ticket_id: {ticket_id}")
            return
        logger.info(f"找到充值记录: {charge_record.id}, SKU: {charge_record.sku.code}")

        try:
            # 2. 更新充值记录状态
            updated_record = await self.update_charge_record_status(charge_record)
            logger.info(f"更新充值记录状态: {charge_record.id}, 状态: {updated_record.status}")

            # 3. 检查订单充值结果
            await product_service.check_charge_result(charge_record.charge_order_id)

        except Exception as e:
            logger.error(f"更新充值记录状态失败: {charge_record.id}, 错误: {str(e)}")

        # 4. 同步SKU库存
        try:
            await self._sync_sku_stock_from_purchase(purchase_id, charge_record.sku.id)
        except Exception as e:
            logger.error(f"同步SKU库存失败: purchase_id={purchase_id}, sku_id={charge_record.sku.id}, 错误: {str(e)}")

    async def _sync_sku_stock_from_purchase(self, purchase_id: str, sku_id: int) -> None:
        """
        从采购单同步SKU库存

        Args:
            purchase_id: 采购单ID
            sku_id: SKU ID
        """
        logger.info(f"开始同步SKU库存: purchase_id={purchase_id}, sku_id={sku_id}")

        # 1. 获取并锁定采购单记录
        purchase_record = await self.purchase_repo.select_for_update_by_purchase_id(purchase_id)
        if not purchase_record:
            logger.warning(f"未找到采购单记录: {purchase_id}")
            return

        # 2. 这里可以调用联盟API更新采购单信息 ---- 暂时不需要
        # updated_purchase_detail = ElemeUnionBenefitsUtils.get_purchase_detail(purchase_id)
        # if updated_purchase_detail:
        #     purchase_record.remain_stock = updated_purchase_detail.remain_stock
        #     await purchase_record.save()

        # 3. 计算SKU的总库存
        total_stock = await self.purchase_repo.calculate_total_remain_stock_by_sku(sku_id)
        logger.info(f"计算SKU总库存: sku_id={sku_id}, total_stock={total_stock}")

        # 4. 更新SKU库存
        updated_sku = await self.sku_repo.update_stock_by_id(sku_id, total_stock)
        if updated_sku:
            logger.info(f"更新SKU库存成功: sku_code={updated_sku.code}, stock={total_stock}")
        else:
            logger.error(f"更新SKU库存失败: sku_id={sku_id}")
