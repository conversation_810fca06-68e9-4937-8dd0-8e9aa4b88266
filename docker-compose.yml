version: "3.9"

services:
  openapi:
    image: hicaspian-registry.cn-shanghai.cr.aliyuncs.com/backend/openapi:${tag}
    container_name: openapi
    command: poetry run python -m deploys.openapi.main
    working_dir: /app
    ports:
      - "5012:8000"
    volumes:
      - ./logs:/app/logs
      - ./.openapi.toml:/app/deploys/openapi/.env.toml:ro
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    labels:
      aliyun.logs.collect: "true"
      aliyun.logs.project: "hicaspian-backend"
      aliyun.logs.logstore: "openapi"
      aliyun.logs.service: "openapi"
      aliyun.logs.env: "production"
    environment:
      - APP_NAME=openapi
      - SERVICE_TYPE=openapi
      - PYTHONPATH=/app
    restart: unless-stopped
    networks:
      - backend

  baseapi:
    image: hicaspian-registry.cn-shanghai.cr.aliyuncs.com/backend/baseapi:${tag}
    container_name: baseapi
    command: poetry run python -m deploys.baseapi.main
    working_dir: /app
    ports:
      - "5007:8000"
    volumes:
      - ./logs:/app/logs
      - ./.baseapi.toml:/app/deploys/baseapi/.env.toml:ro
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    labels:
      aliyun.logs.collect: "true"
      aliyun.logs.project: "hicaspian-backend"
      aliyun.logs.logstore: "baseapi"
      aliyun.logs.service: "baseapi"
      aliyun.logs.env: "production"
    environment:
      - APP_NAME=baseapi
      - SERVICE_TYPE=baseapi
      - PYTHONPATH=/app
    restart: unless-stopped
    networks:
      - backend

  scheduler:
    image: hicaspian-registry.cn-shanghai.cr.aliyuncs.com/backend/scheduler:${tag}
    container_name: schedule
    command: poetry run python -m deploys.scheduler.main
    working_dir: /app
    volumes:
      - ./logs:/app/logs
      - ./.scheduler.toml:/app/deploys/scheduler/.env.toml:ro
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    labels:
      aliyun.logs.collect: "true"
      aliyun.logs.project: "hicaspian-backend"
      aliyun.logs.logstore: "scheduler"
      aliyun.logs.service: "scheduler"
      aliyun.logs.env: "production"
    environment:
      - APP_NAME=scheduler
      - SERVICE_TYPE=scheduler
      - PYTHONPATH=/app
    restart: unless-stopped
    networks:
      - backend

  consumer:
    image: hicaspian-registry.cn-shanghai.cr.aliyuncs.com/backend/consumer:${tag}
    container_name: consumer
    command: poetry run python -m deploys.consumer.main
    working_dir: /app
    volumes:
      - ./logs:/app/logs
      - ./.consumer.toml:/app/deploys/consumer/.env.toml:ro
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    labels:
      aliyun.logs.collect: "true"
      aliyun.logs.project: "hicaspian-backend"
      aliyun.logs.logstore: "consumer"
      aliyun.logs.service: "consumer"
      aliyun.logs.env: "production"
    environment:
      - APP_NAME=consumer
      - SERVICE_TYPE=consumer
      - PYTHONPATH=/app
    restart: unless-stopped
    networks:
      - backend
    
networks:
  backend:
    driver: bridge

volumes:
  logs:
    driver: local
