# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/17 23:14

import base64
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Tuple

import requests
from loguru import logger

from src.utils.thirdpart.openapi_client import OpenApiClientBase
from src.utils.thirdpart.xingxiang_sdk.schemas import (
    CinemaList,
    City,
    ConfirmOrderInfo,
    District,
    LockSeat,
    MovieList,
    OrderInfo,
    ShowList,
)

logger = logger.bind(module_name="xx_sdk")


class XXPlusSDKError(Exception):
    def __init__(self, message: str):
        self.message = message


class XXPlusClient(OpenApiClientBase):

    def __init__(self, auth_key: str, secret: str, base_url: str):
        self.auth_key = auth_key
        self.secret = secret
        super().__init__(base_url)

    async def _async_prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        return params, payload  # type: ignore

    @staticmethod
    def _flatten_value(value: str):
        """递归处理value，展开嵌套的列表和字典，并将其转为字符串"""
        if isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False, separators=(",", ":"))
        elif isinstance(value, list):
            return json.dumps(value, ensure_ascii=False, separators=(",", ":"))
        elif isinstance(value, float):
            return f"{int(value)}" if value.is_integer() else str(value)
        else:
            return str(value)

    def _generate_sign(self, params):
        """根据参数生成签名"""
        sorted_params = sorted(params.items())
        params_sign_str = "".join(self._flatten_value(value) for _, value in sorted_params) + self.secret
        logger.debug(f"params to sign str: {params_sign_str}")
        sign = base64.b64encode(hashlib.md5(params_sign_str.encode("utf-8")).digest().hex().encode("utf-8")).decode(
            "utf-8"
        )
        logger.debug(f"Generated sign: {sign} with params: {params} and params_to_str: {params_sign_str}")
        return sign

    def _prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        new_params = (params if method.upper() == "GET" else payload) or {}
        new_params.update({"auth_key": self.auth_key, "timestamp": str(int(time.time()))})
        new_params["sign"] = self._generate_sign(new_params)
        return (new_params, payload) if method.upper() == "GET" else (params, new_params)

    def _request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        payload: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        try:
            response = super()._request(method, endpoint, params, payload)
            code = response.get("code", -1)
            if code != 0:
                logger.warning(f"Request to {endpoint} failed with response: {response}")
                raise XXPlusSDKError(response.get("msg", "Unknown error"))
            logger.debug(f"Request {method} {endpoint} success with response: {response}")
            return response
        except requests.RequestException as exp:
            logger.error(f"Request {method} {endpoint} failed with error: {exp}")
            raise XXPlusSDKError(f"Request {method} {endpoint} failed with error: {exp}") from exp

    async def _async_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        payload: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        try:
            response = await super()._async_request(method, endpoint, params, payload)
            code = response.get("code", -1)
            if code != 0:
                logger.warning(f"Request to {endpoint} failed with response: {response}")
                raise XXPlusSDKError(response.get("msg", "Unknown error"))
            logger.debug(f"Request {method} {endpoint} success with response: {response}")
            return response
        except requests.RequestException as exp:
            logger.error(f"Request {method} {endpoint} failed with error: {exp}")
            raise XXPlusSDKError(f"Request {method} {endpoint} failed with error: {exp}") from exp

    def check_sign(self, params: dict, sign: str) -> bool:
        """根据参数check签名"""
        gen_sign = self._generate_sign(params)
        logger.info(f"[xx_sdk] check sign: {sign} == {gen_sign}")
        return sign == gen_sign

    def cities(self):
        """获取城市列表"""
        endpoint = "/openapi/v1/movie/city"
        response = self._request("GET", endpoint)
        return [City.model_validate(city) for city in response.get("data", [])]

    async def acities(self):
        """获取城市列表"""
        endpoint = "/openapi/v1/movie/city"
        response = await self._async_request("GET", endpoint)
        return [City.model_validate(city) for city in response.get("data", [])]

    def regions(self, city_id: int):
        """获取区域列表"""
        endpoint = "/openapi/v1/movie/region"
        response = self._request("GET", endpoint, params={"city_id": city_id})
        return [District.model_validate(district) for district in response.get("data", [])]

    async def aregions(self, city_id: int):
        """获取区域列表"""
        endpoint = "/openapi/v1/movie/region"
        response = await self._async_request("GET", endpoint, params={"city_id": city_id})
        return [District.model_validate(district) for district in response.get("data", [])]

    def movies(self, page_index: int, page_size: int, movie_type: str = "hot"):
        """获取电影列表"""
        endpoint = "/openapi/v1/movie/list"
        response = self._request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "type": movie_type}
        )
        return MovieList.model_validate(response.get("data"))

    async def amovies(self, page_index: int, page_size: int, movie_type: str = "hot"):
        """获取电影列表"""
        endpoint = "/openapi/v1/movie/list"
        response = await self._async_request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "type": movie_type}
        )
        return MovieList.model_validate(response.get("data"))

    def show_seat(self, show_id: str) -> Dict[str, Any]:
        """获取座位图"""
        endpoint = "/openapi/v1/movie/seat"
        response = self._request("GET", endpoint, params={"show_id": show_id})
        return response.get("data", {})

    async def ashow_seat(self, show_id: str) -> Dict[str, Any]:
        """获取座位图"""
        endpoint = "/openapi/v1/movie/seat"
        response = await self._async_request("GET", endpoint, params={"show_id": show_id})
        return response.get("data", {})

    def lock_seat(self, partner_order_code: str, show_id: int, mobile: str, seats: List[LockSeat]) -> ConfirmOrderInfo:
        """锁座"""
        endpoint = "/openapi/v1/movie/seat/lock"
        data = {
            "partner_order_code": partner_order_code,
            "show_id": str(show_id),
            "mobile": mobile,
            "seats": seats,
        }
        response = self._request("POST", endpoint, payload=data)
        return ConfirmOrderInfo.model_validate(response.get("data", {}))

    async def alock_seat(
        self, partner_order_code: str, show_id: int, mobile: str, seats: List[LockSeat]
    ) -> ConfirmOrderInfo:
        """锁座"""
        endpoint = "/openapi/v1/movie/seat/lock"
        data = {
            "partner_order_code": partner_order_code,
            "show_id": str(show_id),
            "mobile": mobile,
            "seats": seats,
        }
        response = await self._async_request("POST", endpoint, payload=data)
        return ConfirmOrderInfo.model_validate(response.get("data", {}))

    def cancel_order(self, order_code: str) -> bool:
        """取消订单 & unlock seat"""
        endpoint = "/openapi/v1/movie/seat/unlock"
        data = {"order_code": order_code}
        response = self._request("POST", endpoint, payload=data)
        return response["code"] == 0

    async def acancel_order(self, order_code: str) -> bool:
        """取消订单 & unlock seat"""
        endpoint = "/openapi/v1/movie/seat/unlock"
        data = {"order_code": order_code}
        response = await self._async_request("POST", endpoint, payload=data)
        return response.get("code", -1) == 0

    def shows(self, page_index: int, page_size: int, cinema_id: int) -> ShowList:
        """获取电影院排片列表"""
        endpoint = "/openapi/v1/movie/show"
        response = self._request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "cinema_id": cinema_id}
        )
        return ShowList.model_validate(response.get("data", {}))

    async def ashows(self, page_index: int, page_size: int, cinema_id: int) -> ShowList:
        """获取电影院排片列表"""
        endpoint = "/openapi/v1/movie/show"
        response = await self._async_request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "cinema_id": cinema_id}
        )
        return ShowList.model_validate(response.get("data", {}))

    def cinemas(self, page_index: int, page_size: int, city_id: int) -> CinemaList:
        """获取电影院列表"""
        endpoint = "/openapi/v1/movie/cinema"
        response = self._request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "city_id": city_id}
        )
        return CinemaList.model_validate(response.get("data", []))

    async def acinemas(self, page_index: int, page_size: int, city_id: int) -> CinemaList:
        """获取电影院列表"""
        endpoint = "/openapi/v1/movie/cinema"
        response = await self._async_request(
            "GET", endpoint, params={"page_index": page_index, "page_size": page_size, "city_id": city_id}
        )
        return CinemaList.model_validate(response.get("data", []))

    def balance(self) -> str:
        """查询账户余额"""
        endpoint = "/openapi/v1/user"
        response = self._request("GET", endpoint)
        return response.get("data", {}).get("balance", "0")

    async def abalance(self) -> str:
        """查询账户余额"""
        endpoint = "/openapi/v1/user"
        response = await self._async_request("GET", endpoint)
        return response.get("data", {}).get("balance", "0")

    def confirm_pay(self, partner_order_code: str, order_code: str, total_clear_price: float) -> bool:
        """确认支付，出票"""
        endpoint = "/openapi/v1/order/confirm"
        data = {
            "partner_order_code": partner_order_code,
            "order_code": order_code,
            "total_clear_price": total_clear_price,
        }
        response = self._request("POST", endpoint, payload=data)
        return response["code"] == 0

    async def aconfirm_pay(self, partner_order_code: str, order_code: str, total_clear_price: float) -> bool:
        """确认支付，出票"""
        endpoint = "/openapi/v1/order/confirm"
        data = {
            "partner_order_code": partner_order_code,
            "order_code": order_code,
            "total_clear_price": total_clear_price,
        }
        response = await self._async_request("POST", endpoint, payload=data)
        return response["code"] == 0

    def query_order(self, partner_order_code: str, order_code: str) -> OrderInfo:
        """查询订单"""
        endpoint = "/openapi/v1/order"
        data = {"partner_order_code": partner_order_code, "order_code": order_code}
        response = self._request("POST", endpoint, payload=data)
        return OrderInfo.model_validate(response.get("data", {}))

    async def aquery_order(self, partner_order_code: str, order_code: str) -> OrderInfo:
        """查询订单"""
        endpoint = "/openapi/v1/order"
        data = {"partner_order_code": partner_order_code, "order_code": order_code}
        response = await self._async_request("POST", endpoint, payload=data)
        return OrderInfo.model_validate(response.get("data", {}))
