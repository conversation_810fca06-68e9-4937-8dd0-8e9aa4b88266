# encoding: utf-8
# src/domains/customer/services/benefits_charge.py
# created: 2024-12-08 13:40:15

from typing import TYPE_CHECKING, Optional

from loguru import logger
from tortoise.transactions import atomic

from src.domains.benefits.services import BenefitsChargeService
from src.domains.customer.dto import OpenPlatformCustomerBenefitsDTO
from src.infrastructures import errors
from src.repositories.customer import BenefitsRepository, CustomerRepository

if TYPE_CHECKING:
    from src.databases.models.customer import Customer
    from src.domains.benefits.dto import BenefitsProductOrderDTO
    from src.domains.benefits.services import BenefitsProductService, BenefitsSkuService


class CustomerBenefitService:

    def __init__(
        self,
        benefits_product_service: "BenefitsProductService",
        bsku_service: "BenefitsSkuService",
        benefits_repo: "BenefitsRepository",
        customer_repo: "CustomerRepository",
    ):
        self.bsku_service = bsku_service
        self.benefits_product_service = benefits_product_service
        self.benefits_repo = benefits_repo
        self.customer_repo = customer_repo

    async def _check_customer_info(self, customer: "Customer"):
        await customer.fetch_related("app", "tenant")
        if not customer.app or not customer.tenant:
            logger.warning(
                f"客户信息不完整[{customer.code}-{customer.name}], app: {customer.app}, tenant: {customer.tenant}"
            )
            raise errors.ChargeOrderNotFoundError

    async def _check_order_source(self, order: "BenefitsProductOrderDTO", customer: "Customer"):
        await customer.fetch_related("app", "tenant")
        if order.app_id != customer.app.id or order.tenant_id != customer.tenant.tenant_id:  # type: ignore
            logger.warning(
                f"订单来源不匹配[{customer.code}-{customer.name}], "
                f"订单app_id: {order.app_id}, 订单tenant_id: {order.tenant_id}, "
                f"客户app_id: {customer.app.app_id}, 客户tenant_id: {customer.tenant.tenant_id}"  # type: ignore
            )
            raise errors.ChargeOrderNotFoundError

    async def get_customer_by_supplier_order_id(self, supplier_order_id: str) -> Optional["Customer"]:
        """根据供应商订单号获取客户"""
        charge_record = await self.bsku_service.get_sku_record_by_supplier_order_id(supplier_order_id)

        # 检查charge_record是否存在
        if charge_record is None:
            return None
        await charge_record.fetch_related("app", "tenant")

        # 检查app和tenant是否存在
        if charge_record.app is None or charge_record.tenant is None:
            return None

        customer = await self.customer_repo.get_by_app_tenant(charge_record.app, charge_record.tenant)
        return customer

    async def get_order_by_order_id(self, order_id: str, customer: "Customer") -> "BenefitsProductOrderDTO":
        """根据订单id获取订单, 并检查订单来源是否匹配"""
        await self._check_customer_info(customer)  # 检查客户信息是否完整
        order = await self.benefits_product_service.get_order_by_order_id(order_id)
        await self._check_order_source(order, customer)  # 检查订单来源是否匹配
        return order

    async def get_order_by_out_order_id(self, out_order_id: str, customer: "Customer") -> "BenefitsProductOrderDTO":
        """根据第三方订单号获取订单, 并检查订单来源是否匹配"""
        await self._check_customer_info(customer)  # 检查客户信息是否完整
        order = await self.benefits_product_service.get_order_by_out_order_id(out_order_id)
        await self._check_order_source(order, customer)  # 检查订单来源是否匹配
        return order

    async def get_products(self, customer: "Customer") -> list[OpenPlatformCustomerBenefitsDTO]:
        """获取客户权益产品列表"""
        products = await self.benefits_repo.gets_all_by_customer(customer.id)
        return [
            OpenPlatformCustomerBenefitsDTO.model_validate(
                {
                    "id": product.id,
                    "code": product.product.code,
                    "name": product.product.name,
                    "price": product.sale_price,
                    "stock": product.stock,
                }
            )
            for product in products
        ]

    @atomic()
    async def charge_benefit(
        self,
        customer: "Customer",
        product_code: str,
        account: str,
        out_order_id: str,
        notify_url: str,
        sid: Optional[str] = None,
    ) -> "BenefitsProductOrderDTO":
        """充值客户权益产品"""
        customer = await self.customer_repo.get_by_id_for_update(customer.id)  # type: ignore
        benefit = await self.benefits_repo.get_by_customer_and_product_for_update(customer.id, product_code)

        await self._check_customer_info(customer)  # 检查客户信息是否完整

        # 检查产品是否存在
        if not benefit:
            raise errors.BenefitsProductNotFoundError

        await customer.fetch_related("app", "tenant")
        await benefit.fetch_related("product")

        if benefit.stock == 0:
            logger.error(
                "客户[{customer}]权益产品库存不足: {product_code}",
                customer=f"{customer.code}-{customer.name}",
                product_code=benefit.product.code,
            )
            raise errors.CustomerBenefitsProductStockEmptyError
        elif benefit.stock > 0:
            logger.info(
                "客户[{customer}]权益产品库存更新: {product_code}, 库存变化: {stock} --> {new_stock}",
                customer=f"{customer.code}-{customer.name}",
                product_code=benefit.product.code,
                stock=benefit.stock,
                new_stock=benefit.stock - 1,
            )
            benefit.stock -= 1
        else:
            # 库存不足，走余额扣减逻辑，先校验余额是否充足
            if customer.balance < benefit.sale_price:
                logger.error(
                    "客户[{customer}]余额不足: 产品代码: {product_code}, 余额: {balance}, 所需金额: {sale_price}",
                    customer=f"{customer.code}-{customer.name}",
                    product_code=benefit.product.code,
                    balance=customer.balance,
                    sale_price=benefit.sale_price,
                )
                raise errors.CustomerBalanceNotEnoughError
            logger.info(
                "客户[{customer}]余额更新: , 产品代码: {product_code}, 余额变化: {balance} --> {new_balance}",
                customer=f"{customer.code}-{customer.name}",
                product_code=benefit.product.code,
                balance=customer.balance,
                new_balance=customer.balance - benefit.sale_price,
            )
            customer.balance -= benefit.sale_price

        # 特殊处理海狸员工权益的数据格式
        if not sid:
            source_identify = f"{customer.app.app_id}.{customer.tenant.tenant_id}.benefits"  # type: ignore
        else:
            source_identify = f"HAILI_BENEFITS.{sid}.benefits"

        order = await self.benefits_product_service.charge_product(
            product_code=benefit.product.code,
            account=account,
            out_order_id=out_order_id,
            notify_url=notify_url,
            source_identify=source_identify,
            sale_price=benefit.sale_price,
            from_app=customer.app,
            from_tenant=customer.tenant,  # type: ignore
        )
        logger.info(
            "客户[{customer}]发起权益产品充值: {product_code}, 订单号: {order_id}, 金额: {amount}",
            customer=f"{customer.code}-{customer.name}",
            product_code=benefit.product.code,
            order_id=order.order_id,
            amount=benefit.sale_price,
        )

        await benefit.save()
        await customer.save()
        return order

    async def recharge_order(
        self, order_id: str, out_order_id: str, notify_url: str, customer: "Customer"
    ) -> "BenefitsProductOrderDTO":
        """重新充值权益订单"""
        # 检查客户信息是否完整
        await self._check_customer_info(customer)
        order = await self.benefits_product_service.get_order_by_order_id(order_id)

        # 检查订单号是否匹配
        if order.out_order_id != out_order_id:
            logger.error(f"发起重新充值的订单号不匹配: {order_id}, {out_order_id}")
            raise errors.ChargeOrderNotFoundError

        # 检查订单来源是否匹配
        await self._check_order_source(order, customer)

        order = await BenefitsChargeService.recharge_order(order.order_id, notify_url)
        return order
