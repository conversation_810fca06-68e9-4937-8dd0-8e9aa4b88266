# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/13 13:15
from typing import Optional

from pydantic import BaseModel, Field


class Product(BaseModel):
    mac_pid: str = Field(..., title="商品 ID", alias="resId")
    name: str = Field(..., title="商品名称", alias="name")
    denom: int = Field(..., title="商品面额", alias="denom")
    price: float = Field(..., title="商品售价", alias="price")
    enabled: bool = Field(..., title="是否启用", alias="enabled")
    type_name: str = Field(..., title="产品业务类型", alias="typeName")


class Account(BaseModel):
    account_id: str = Field(..., title="账户 ID", alias="id")
    balance: float = Field(..., title="账户余额", alias="balance")
    enabled: bool = Field(..., title="是否启用", alias="enabled")


class ShinesunChargePayload(BaseModel):
    account: str = Field(..., title="充值账号", serialization_alias="account")
    code: str = Field(..., title="商品 ID", serialization_alias="macpid")
    order_id: str = Field(..., title="合作商订单 ID", serialization_alias="orderid")
    notify_url: Optional[str] = Field(None, title="回调地址", serialization_alias="noticeurl")
    count: Optional[int] = Field(1, title="商品数量", serialization_alias="count")
    sale_price: Optional[float] = Field(None, title="商品售价", serialization_alias="saleprice")
    ip: Optional[str] = Field(None, title="IP 地址", serialization_alias="ip")
    game_zone: Optional[str] = Field(None, title="游戏区服", serialization_alias="gamezone")
    game_service: Optional[str] = Field(None, title="游戏服务器", serialization_alias="gameservice")


class ShinesunChargeResult(BaseModel):
    order_id: str = Field(..., title="合作商订单 ID", alias="id")
    platform_order_id: str = Field(..., title="平台订单 ID", alias="orderId")
    status: Optional[int] = Field(..., title="充值状态", alias="status")
    price: float = Field(..., title="订单金额", alias="price")
    count: int = Field(..., title="商品数量", alias="count")
    voucher: Optional[str] = Field(None, title="充值凭证", alias="voucher")
    card_info: Optional[str] = Field(None, title="卡密信息", alias="cardInfo")
    detail: Optional[str] = Field("", title="发放明细", alias="detail")
