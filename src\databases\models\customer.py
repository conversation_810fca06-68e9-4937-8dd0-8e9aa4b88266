# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/customer.py
# created: 2025-03-24 22:41:35
# updated: 2025-07-01 23:37:27

import enum
from typing import Any, List, Optional, Tuple

import nanoid
from tortoise import fields
from tortoise.models import Model

from src.databases.models.benefits import BenefitsProduct
from src.databases.models.passport import PassportApp, PassportTenant
from src.utils.idalloc import id_alloc_factor


class CustomerType(enum.IntEnum):
    UNKNOWN = 0
    ENTERPRISE = 1
    PERSONAL = 2


class CustomerSubscribeType(enum.StrEnum):
    """客户订阅类型"""

    DELIVERY_ORDER_COUPON = "delivery_order_coupon"
    DELIVERY_ORDER = "delivery_order"


def customer_code_generate():
    return nanoid.generate(alphabet="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", size=8)


def generate_code() -> str:
    return nanoid.generate(alphabet="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", size=8)


class Customer(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, description="客户名称", default="")
    code = fields.CharField(max_length=255, description="客户编码", default=customer_code_generate, unique=True)
    description = fields.TextField(description="客户描述", default="")
    type = fields.IntEnumField(CustomerType, description="客户类型", default=CustomerType.UNKNOWN)
    balance = fields.IntField(description="余额(单位分)", default=0)
    app: Optional[PassportApp] = fields.ForeignKeyField(
        "models.PassportApp", description="关联应用", db_constraint=False
    )  # type: ignore
    tenant: Optional[PassportTenant] = fields.ForeignKeyField(
        "models.PassportTenant", description="关联租户", db_constraint=False
    )  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    secrets: Any = fields.ReverseRelation["CustomerSecret"]  # type: ignore

    class Meta:
        table = "customer"
        table_description = "客户总表"
        unique_together = ("app_id", "tenant_id")

    class PydanticMeta:
        exclude = ["secrets", "products", "channels", "app_id", "tenant_id", "app", "tenant"]

    def __str__(self) -> str:
        return f"{self.name}[{self.code}]"


class CustomerSecret(Model):
    id = fields.IntField(pk=True)
    customer: Any = fields.ForeignKeyField("models.Customer", related_name="secrets", description="客户")
    name = fields.CharField(max_length=255, description="密钥名称", default="")
    app_key = fields.CharField(max_length=32, description="APP_KEY", default="")
    app_secret = fields.CharField(max_length=64, description="APP_SECRET", default="", unique=True, index=True)
    # 移除默认值函数，以防止迁移时的默认值冲突
    access_key = fields.CharField(max_length=32, description="ACCESS_KEY", default=id_alloc_factor(32), index=True)
    access_secret = fields.CharField(
        max_length=64, description="ACCESS_SECRET", default=id_alloc_factor(64), index=True
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "customer_secret"
        table_description = "客户密钥表"

    class PydanticMeta:
        exclude = ["customer"]


class CustomerChannels(Model):
    id = fields.IntField(pk=True, description="主键")
    name = fields.CharField(max_length=255, description="渠道名称", null=False, default="")
    code = fields.CharField(max_length=16, description="渠道编码", null=False, default=generate_code)
    identify = fields.CharField(max_length=255, description="渠道标识", null=False, default="")
    customer = fields.ForeignKeyField("models.Customer", related_name="channels", description="客户")  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "customer_channels"
        table_description = "客户渠道表"
        unique_together = [("customer", "identify")]


class CustomerBenefits(Model):
    id = fields.IntField(pk=True)
    customer: fields.ForeignKeyRelation[Customer] = fields.ForeignKeyField(
        "models.Customer", related_name="products", description="客户"
    )
    product: fields.ForeignKeyRelation[BenefitsProduct] = fields.ForeignKeyField(
        "models.BenefitsProduct", description="权益产品", on_delete=fields.CASCADE
    )

    sale_price = fields.IntField(description="售价(单位分)", default=0)
    stock = fields.IntField(description="库存", default=-1)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    def sale_mode(self) -> str:
        return "stock" if self.stock >= 0 else "finacial"

    def price(self) -> int:
        return self.sale_price

    class Meta:
        table = "customer_benefits"
        table_description = "客户权益表"

    class PydanticMeta:
        computed = ["sale_mode", "price"]


class CustomerRechargeRecord(Model):
    id = fields.IntField(pk=True)
    customer_id = fields.IntField(description="客户ID")
    customer_code = fields.CharField(max_length=255, description="客户编码")
    customer_name = fields.CharField(max_length=255, description="客户名称")
    amount = fields.IntField(description="充值金额(单位分)")
    balance_after = fields.IntField(description="充值后余额(单位分)")
    description = fields.CharField(max_length=255, null=True, description="充值说明", default="")
    operator_id = fields.CharField(max_length=64, description="操作人ID")
    operator_name = fields.CharField(max_length=255, description="操作人名称")
    created_at = fields.DatetimeField(auto_now_add=True, description="充值时间")

    class Meta:
        table = "customer_recharge_record"
        table_description = "客户充值记录表"


class CustomerSubscribe(Model):
    id = fields.IntField(pk=True)
    customer: fields.ForeignKeyRelation[Customer] = fields.ForeignKeyField(
        "models.Customer", related_name="subscribes", description="客户", db_constraint=False
    )
    type = fields.CharField(
        max_length=255,
        description="订阅类型",
        default=CustomerSubscribeType.DELIVERY_ORDER,
        choices=CustomerSubscribeType,
    )
    url = fields.CharField(max_length=255, description="订阅URL", null=False, default="")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "customer_subscribe"
        table_description = "客户订阅信息表"
        unique_together = [("customer", "type")]

    class PydanticMeta:
        exclude = ["customer"]
