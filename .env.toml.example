# encoding: utf-8
# .env.toml.example
# created: 2025-08-18 15:45:00
#
# TOML 格式配置示例文件
# 复制此文件为 .env.toml 并填入实际的配置值
# 优先级：环境变量 > .env > .env.toml

# ==================== 数据库配置 ====================
[database]
mysql_uri = "mysql://username:password@host:port/database"
redis_uri = "redis://username:password@host:port"

# ==================== RabbitMQ 配置 ====================
[rabbitmq]
url = "amqp://username:password@host:port/vhost"
pool_size = 2
max_channels_per_connection = 50
heartbeat = 60
connection_timeout = 10
prefetch_count = 2
consumer_instances = 1
mandatory = true

# ==================== FastAPI 配置 ====================
[fastapi]
port = 8000
workers = 2

[fastapi.hostname]
hostname = ""
base_fe = ""

# ==================== 第三方服务配置 ====================

# Bifrost 配置
[bifrost]
public_key = ""
private_key = ""
account = ""
secret_key = ""
base_url = ""
prefix = ""

# 饿了么联盟配置
[eleme_union]
app_key = ""
app_secret = ""
top_gateway_url = ""
verify_ssl = true

# WiFi Master 配置
[wifimaster]
app_id = ""
sso_url = ""

# 阿里云短信服务配置
[sms]
access_key_id = ""
access_key_secret = ""
endpoint = "dysmsapi.aliyuncs.com"
sign_name = ""
template_code = ""
repeat_interval = 60

# ==================== Growth Hacker 特有配置 ====================
# （仅在 growth_hacker 服务中使用）

debug = true

# 浏览器池配置
[browser.pool]
browser_count = 2
min_browsers = 1
max_browsers = 3
browser_max_usage = 20
browser_max_lifetime = 1200
browser_timeout = 30000
enable_health_check = true
health_check_interval = 60

# 浏览器启动配置
[browser.launch]
headless = true
browser_args = [
    "--no-sandbox",
    "--disable-gpu",
    "--disable-dev-shm-usage",
    "--disable-blink-features=AutomationControlled",
]

# 浏览器上下文配置
[browser.context]
locale = "zh-CN"
timezone_id = "Asia/Shanghai"
permissions = ["geolocation", "notifications"]
is_mobile = true
has_touch = true
viewport_width = 375
viewport_height = 812

# 资源缓存配置
[browser.cache]
enable_cache = true
cache_static_resources = true
cache_api_responses = false
max_cache_size_mb = 100
cache_ttl_seconds = 3600
cached_resource_types = ["image", "stylesheet", "font", "script"]

# IP 代理配置
[ip_proxy.qg_short_settings]
endpoint = "https://share.proxy.qg.net"
key = ""
password = ""

[ip_proxy.qg_long_settings]
endpoint = "https://longterm.proxy.qg.net"
key = ""
password = ""

# 本地代理映射
[ip_proxy.local]
# city = "proxy_url"

# SAE 代理映射
[ip_proxy.sae]
# city = "proxy_url"

# 日志配置
[logger]
app_name = "growth_hacker"

# 控制台输出配置
[[logger.sinks]]
type = "console"
level = "INFO"
colorize = true
serialize = false

# 文件输出配置
[[logger.sinks]]
type = "file"
level = "DEBUG"
path = "logs/{app_name}.log"
rotation = "10 MB"
retention = "7 days"
compression = "gz"

# ==================== 注意事项 ====================
# 1. TOML 格式使用 [section] 表示配置节
# 2. 嵌套配置使用 [parent.child] 格式
# 3. 数组使用 [[array]] 格式
# 4. 布尔值使用 true/false（小写）
# 5. 字符串使用双引号
# 6. 生产环境配置不要提交到版本控制