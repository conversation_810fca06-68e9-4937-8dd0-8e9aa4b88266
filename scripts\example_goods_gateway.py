# encoding: utf-8
# scripts/example_goods_gateway.py
# created: 2025-08-18 16:25:46

"""
饿了么联盟商品网关示例脚本

功能演示：
1. 获取新天特商品列表
2. 获取商品详情
3. 查询商品门店
4. 创建订单
5. 查询订单状态
6. 支付订单
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

from redis.asyncio import Redis
from rich.console import Console
from rich.table import Table

from src.infrastructures.gateways.eleme.union import ElemeUnionGoodsGateway
from src.infrastructures.gateways.eleme.union.settings import ElemeUnionSettings

console = Console()


async def main():
    """主函数"""

    # 配置信息（请根据实际情况修改）
    config = ElemeUnionSettings(
        app_key=os.getenv("ELEME_APP_KEY", "34813436"),
        app_secret=os.getenv("ELEME_APP_SECRET", "929eafdde0fb34c57ff0290e271b54c3"),
        top_gateway_url="https://eco.taobao.com/router/rest",
    )

    # 创建Redis连接
    redis = Redis.from_url("redis://localhost:6379/0", decode_responses=True)

    try:
        # 创建网关实例
        gateway = ElemeUnionGoodsGateway(config=config, redis=redis)

        console.print("\n[bold cyan]========== 饿了么联盟商品网关示例 ==========[/bold cyan]\n")

        # 1. 获取新天特商品列表
        console.print("[bold yellow]1. 获取新天特商品列表[/bold yellow]")
        try:
            goods_list = await gateway.get_goods_list(page_size=10, page_number=1, item_type=4)  # 4-新天特

            # 显示商品列表
            table = Table(title="新天特商品列表")
            table.add_column("商品ID", style="cyan", no_wrap=True)
            table.add_column("商品名称", style="magenta")
            table.add_column("商品类型", style="green")

            for item in goods_list.records[:5]:  # 只显示前5个
                table.add_row(item.item_id, item.item_name, str(item.item_type))

            console.print(table)
            console.print(f"[green]总计: {goods_list.total_count} 个商品[/green]\n")

            # 保存第一个商品ID用于后续测试
            if goods_list.records:
                first_item_id = goods_list.records[0].item_id
                session_id = goods_list.session_id
            else:
                console.print("[red]没有找到商品，请检查配置[/red]")
                return

        except Exception as e:
            console.print(f"[red]获取商品列表失败: {e}[/red]")
            return

        # 2. 获取商品详情
        console.print("[bold yellow]2. 获取商品详情[/bold yellow]")
        try:
            goods_detail = await gateway.get_goods_detail(item_id=first_item_id, item_type=4)  # 新天特

            # 输出所有字段信息
            console.print("\n[bold cyan]===== 商品基本信息 =====[/bold cyan]")
            console.print(f"商品ID: [cyan]{goods_detail.item_id}[/cyan]")
            console.print(f"商品名称: [magenta]{goods_detail.item_name}[/magenta]")
            console.print(f"商品类型(item_type): {goods_detail.item_type}")
            console.print(f"业务类型(biz_type): {goods_detail.biz_type}")
            console.print(
                f"图片URL: {goods_detail.picture[:50]}..."
                if len(goods_detail.picture) > 50
                else f"图片URL: {goods_detail.picture}"
            )

            console.print("\n[bold cyan]===== 价格信息 =====[/bold cyan]")
            console.print(
                f"原价: [red]{goods_detail.original_price_cent/100:.2f}[/red] 元 ({goods_detail.original_price_cent} 分)"
            )
            console.print(
                f"售价: [green]{goods_detail.sell_price_cent/100:.2f}[/green] 元 ({goods_detail.sell_price_cent} 分)"
            )
            if goods_detail.activity_price_cent:
                console.print(
                    f"活动价: [bold green]{goods_detail.activity_price_cent/100:.2f}[/bold green] 元 ({goods_detail.activity_price_cent} 分)"
                )
            console.print(f"折扣(discount): {goods_detail.discount}")
            console.print(f"活动价格类型(activity_price_type): {goods_detail.activity_price_type}")

            console.print("\n[bold cyan]===== 时间信息 =====[/bold cyan]")
            console.print(
                f"开始时间: {datetime.fromtimestamp(goods_detail.start_time).strftime('%Y-%m-%d %H:%M:%S')} (时间戳: {goods_detail.start_time})"
            )
            console.print(
                f"结束时间: {datetime.fromtimestamp(goods_detail.end_time).strftime('%Y-%m-%d %H:%M:%S')} (时间戳: {goods_detail.end_time})"
            )
            if goods_detail.promo_start_time:
                console.print(
                    f"优惠开始时间: {datetime.fromtimestamp(goods_detail.promo_start_time).strftime('%Y-%m-%d %H:%M:%S')} (时间戳: {goods_detail.promo_start_time})"
                )
            if goods_detail.promo_end_time:
                console.print(
                    f"优惠结束时间: {datetime.fromtimestamp(goods_detail.promo_end_time).strftime('%Y-%m-%d %H:%M:%S')} (时间戳: {goods_detail.promo_end_time})"
                )

            console.print("\n[bold cyan]===== 佣金信息 =====[/bold cyan]")
            console.print(f"佣金比例(commission_rate): {goods_detail.commission_rate}")
            console.print(
                f"预估佣金(commission): {goods_detail.commission/100:.2f} 元"
                if goods_detail.commission
                else "预估佣金(commission): None"
            )

            console.print("\n[bold cyan]===== 适用范围 =====[/bold cyan]")
            console.print(f"适用门店数(apply_shop_count): [blue]{goods_detail.apply_shop_count}[/blue]")
            console.print(f"适用城市数(apply_city_count): {goods_detail.apply_city_count}")
            console.print(f"城市可核销门店数汇总(suit_city): {goods_detail.suit_city}")
            console.print(f"省份直辖市可核销门店数汇总(suit_province): {goods_detail.suit_province}")

            # 限购信息
            if goods_detail.purchase_limit:
                console.print("\n[bold cyan]===== 限购信息 =====[/bold cyan]")
                limit = goods_detail.purchase_limit
                console.print(
                    f"每人每天限购(item_daily_limit_per_user): {limit.item_daily_limit_per_user if limit.item_daily_limit_per_user > 0 else '不限'}"
                )
                console.print(
                    f"每人终身限购(item_limit_per_user): {limit.item_limit_per_user if limit.item_limit_per_user > 0 else '不限'}"
                )
                if limit.item_limit_per_user_order:
                    console.print(
                        f"每人每订单限购(item_limit_per_user_order): {limit.item_limit_per_user_order if limit.item_limit_per_user_order > 0 else '不限'}"
                    )
                console.print(
                    f"活动每人每天限购(activity_daily_limit_per_user): {limit.activity_daily_limit_per_user if limit.activity_daily_limit_per_user > 0 else '不限'}"
                )
                console.print(
                    f"活动每人限购(activity_limit_per_user): {limit.activity_limit_per_user if limit.activity_limit_per_user > 0 else '不限'}"
                )

            # 凭证信息
            if goods_detail.ticket:
                console.print("\n[bold cyan]===== 凭证信息(ticket) =====[/bold cyan]")
                console.print(
                    f"价格(price): {goods_detail.ticket.price/100:.2f} 元"
                    if goods_detail.ticket.price
                    else "价格(price): None"
                )
                console.print(f"数量(quantity): {goods_detail.ticket.quantity}")
                console.print(
                    f"使用门槛(threshold): {goods_detail.ticket.threshold/100:.2f} 元"
                    if goods_detail.ticket.threshold
                    else "使用门槛(threshold): None"
                )

            # 服务信息
            if goods_detail.services:
                console.print("\n[bold cyan]===== 服务信息(services) =====[/bold cyan]")
                for i, service in enumerate(goods_detail.services[:3], 1):  # 只显示前3个
                    console.print(f"服务{i}:")
                    console.print(f"  标题: {service.title if service.title else 'None'}")
                    console.print(f"  类型: {service.type}")
                    console.print(f"  内容: {', '.join(service.contents[:2]) if service.contents else 'None'}...")
                if len(goods_detail.services) > 3:
                    console.print(f"  ... 还有 {len(goods_detail.services) - 3} 个服务")

            # 使用方法
            if goods_detail.usage_method:
                console.print("\n[bold cyan]===== 使用方法(usage_method) =====[/bold cyan]")
                for i, method in enumerate(goods_detail.usage_method[:2], 1):  # 只显示前2个
                    console.print(f"方法{i}:")
                    console.print(f"  标题: {method.title if method.title else 'None'}")
                    console.print(f"  类型: {method.type}")
                    console.print(f"  内容: {', '.join(method.contents[:2]) if method.contents else 'None'}...")

            # 商品规则
            if goods_detail.item_rules:
                console.print("\n[bold cyan]===== 商品规则(item_rules) =====[/bold cyan]")
                for i, rule in enumerate(goods_detail.item_rules[:2], 1):  # 只显示前2个
                    console.print(f"规则{i}:")
                    console.print(f"  标题: {rule.title if rule.title else 'None'}")
                    console.print(f"  类型: {rule.type}")
                    console.print(f"  内容: {', '.join(rule.contents[:2]) if rule.contents else 'None'}...")

            # 素材图片
            if goods_detail.item_material_pictures:
                console.print("\n[bold cyan]===== 素材图片(item_material_pictures) =====[/bold cyan]")
                for i, pic_url in enumerate(goods_detail.item_material_pictures[:3], 1):
                    console.print(f"图片{i}: {pic_url[:50]}..." if len(pic_url) > 50 else f"图片{i}: {pic_url}")
                if len(goods_detail.item_material_pictures) > 3:
                    console.print(f"  ... 还有 {len(goods_detail.item_material_pictures) - 3} 张图片")

            # 商品规格补充说明
            if goods_detail.specification:
                console.print("\n[bold cyan]===== 商品规格补充说明(specification) =====[/bold cyan]")
                spec = goods_detail.specification
                console.print(f"品牌名称(brand_name): {spec.brand_name}")
                console.print(f"面值说明(par_value): {spec.par_value}")
                console.print(f"兑换方式描述(exchange_desc): {spec.exchange_desc}")
                console.print(f"商品描述(item_desc): {spec.item_desc}")
                console.print(f"原料(ingredient): {spec.ingredient}")
                console.print(f"份量(item_weight): {spec.item_weight}")

            console.print()

        except Exception as e:
            console.print(f"[red]获取商品详情失败: {e}[/red]\n")

        # 3. 查询商品门店
        console.print("[bold yellow]3. 查询商品门店[/bold yellow]")
        try:
            stores = await gateway.get_goods_stores(item_id=first_item_id, page_size=5, page_number=1, item_type=4)

            # 显示门店列表
            table = Table(title="商品适用门店")
            table.add_column("门店ID", style="cyan", no_wrap=True)
            table.add_column("门店名称", style="magenta")
            table.add_column("城市", style="green")
            table.add_column("地址", style="yellow")

            for store in stores.records[:5]:  # 只显示前5个
                table.add_row(
                    store.store_id,
                    store.name,
                    store.city_code,
                    store.address[:30] + "..." if len(store.address) > 30 else store.address,
                )

            console.print(table)
            console.print(f"[green]总计: {stores.total_count} 个门店[/green]\n")

        except Exception as e:
            console.print(f"[red]查询门店失败: {e}[/red]\n")

        # 4. 创建订单（示例，实际使用需要真实数据）
        console.print("[bold yellow]4. 创建订单示例[/bold yellow]")
        console.print("[dim]注意: 这是一个示例，实际创建订单需要真实的商品和用户信息[/dim]")

        # 生成测试订单号
        test_order_id = f"TEST_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 这里只是演示API调用方式，实际使用需要真实数据
            console.print("[cyan]示例订单参数:[/cyan]")
            console.print(f"  外部订单号: {test_order_id}")
            console.print(f"  商品ID: {first_item_id}")
            console.print("  数量: 1")
            console.print("  金额: 1000 分 (10元)")
            console.print("  手机号: 13800138000")
            console.print("  商品类型: 4 (新天特)")

            # 注释掉实际创建订单的代码，避免产生真实订单
            # 如需测试实际订单创建，请取消以下注释：
            #
            # order_result = await gateway.create_order(
            #     outer_order_id=test_order_id,
            #     item_id=first_item_id,
            #     quantity=1,
            #     order_fee=1000,  # 10元
            #     title="测试商品",
            #     phone="13800138000",
            #     ext_info='{"test": true}',
            #     skip_pay=True,  # 预下单不支付
            #     item_type=4  # 新天特
            # )
            #
            # if order_result:
            #     console.print(f"[green]订单创建成功![/green]")
            #     console.print(f"  外部订单号: {order_result.get('outer_order_id')}")
            #     console.print(f"  饿了么订单号: {order_result.get('biz_order_id')}")
            #
            #     # 5. 查询订单状态
            #     console.print("\n[bold yellow]5. 查询订单状态[/bold yellow]")
            #     order_status = await gateway.query_order(outer_order_id=test_order_id)
            #     console.print(f"订单状态: {order_status}")
            #
            #     # 6. 支付订单（示例）
            #     console.print("\n[bold yellow]6. 支付订单示例[/bold yellow]")
            #     console.print("[dim]实际支付需要用户确认[/dim]")
            #     # pay_result = await gateway.pay_order(outer_order_id=test_order_id)
            #     # console.print(f"支付结果: {pay_result}")

            console.print("\n[dim]实际订单创建已注释，避免产生真实订单[/dim]")

        except Exception as e:
            console.print(f"[red]订单操作失败: {e}[/red]")

        # 7. 分页获取更多商品
        console.print("\n[bold yellow]7. 分页获取商品（第2页）[/bold yellow]")
        if session_id:
            try:
                page2_goods = await gateway.get_goods_list(
                    page_size=10, page_number=2, item_type=4, session_id=session_id  # 使用第一页返回的session_id
                )
                console.print(f"[green]第2页获取到 {len(page2_goods.records)} 个商品[/green]")

            except Exception as e:
                console.print(f"[red]获取第2页失败: {e}[/red]")

        console.print("\n[bold green]========== 示例执行完成 ==========[/bold green]")

    except Exception as e:
        console.print(f"[bold red]发生错误: {e}[/bold red]")
        import traceback

        traceback.print_exc()

    finally:
        # 关闭Redis连接
        await redis.aclose()


def run_example():
    """运行示例"""
    console.print("[bold]启动饿了么联盟商品网关示例...[/bold]")

    # 检查环境变量
    required_env_vars = ["ELEME_APP_KEY", "ELEME_APP_SECRET", "ELEME_PID", "ELEME_SID"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        console.print("[yellow]警告: 以下环境变量未设置:[/yellow]")
        for var in missing_vars:
            console.print(f"  - {var}")
        console.print("\n[dim]请设置环境变量或修改脚本中的配置信息[/dim]")
        console.print("[dim]示例: export ELEME_APP_KEY=your_app_key[/dim]\n")

    # 运行异步主函数
    asyncio.run(main())


if __name__ == "__main__":
    run_example()
