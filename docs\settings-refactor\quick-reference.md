# encoding: utf-8
# docs/settings-refactor/quick-reference.md
# created: 2025-08-18 18:30:00

# 配置系统快速参考

## 🚀 快速开始

```bash
# 1. 复制配置模板
cp .env.example .env

# 2. 设置必要配置
vim .env

# 3. 验证配置
python scripts/check_settings.py

# 4. 启动服务
python deploys/baseapi/main.py
```

## 📋 环境变量速查

### 必需配置
```bash
DATABASE__MYSQL_URI=mysql://user:pass@host/db
DATABASE__REDIS_URI=redis://user:pass@host
RABBITMQ__URL=amqp://user:pass@host/vhost
```

### 可选配置
```bash
FASTAPI__PORT=8000
FASTAPI__WORKERS=2
DEBUG=true
```

## 🏗️ 文件结构

```
project/
├── .env                    # 本地配置（不提交）
├── .env.example            # 配置示例
├── .env.toml              # TOML配置（可提交）
├── .env.toml.example      # TOML示例
├── scripts/
│   └── check_settings.py  # 配置检查工具
├── src/infrastructures/
│   ├── settings/          # 配置基类
│   │   └── base.py
│   └── config_compat.py   # 兼容层
└── deploys/
    └── */settings.py      # 各服务配置
```

## 💻 代码片段

### 使用配置
```python
from deploys.baseapi.settings import config

# 访问配置
db_url = config.database.mysql_uri
port = config.fastapi.port
```

### 创建新服务配置
```python
from src.infrastructures.settings import BaseServiceSettings

class NewServiceSettings(BaseServiceSettings):
    """新服务配置"""
    pass

config = NewServiceSettings()
```

### 添加配置字段
```python
from pydantic import Field

class MySettings(BaseServiceSettings):
    my_field: str = Field(default="", description="我的配置")
```

## 🔍 常用命令

```bash
# 检查配置
python scripts/check_settings.py

# 查看当前配置
python -c "from deploys.baseapi.settings import config; print(config.model_dump())"

# 验证环境变量
env | grep DATABASE

# 运行测试
pytest tests/unit/test_settings.py
```

## 📊 配置优先级

```
高 ┌─────────────────┐
   │   环境变量      │ (生产环境)
   ├─────────────────┤
   │   .env 文件     │ (开发环境)
   ├─────────────────┤
   │  .env.toml 文件 │ (默认配置)
低 └─────────────────┘
```

## 🔒 安全检查清单

- [ ] 无硬编码密码
- [ ] .env 在 .gitignore 中
- [ ] 敏感配置使用环境变量
- [ ] 生产配置已加密
- [ ] 定期轮换密钥

## 🛠️ 故障排查

| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 配置未加载 | 文件路径错误 | 检查 .env 位置 |
| 值被覆盖 | 优先级问题 | 检查环境变量 |
| 验证失败 | 类型错误 | 查看错误信息 |
| 服务启动失败 | 必需配置缺失 | 运行 check_settings.py |

## 📚 相关文档

- [完整指南](./README.md)
- [架构设计](./02-architecture.md)
- [迁移指南](./configuration-migration.md)
- [安全实践](./config-security-best-practices.md)

## 🏷️ 服务配置清单

| 服务 | 类名 | 特有配置 |
|------|------|----------|
| baseapi | BaseapiSettings | fastapi, sms |
| consumer | ConsumerSettings | fastapi, sms |
| openapi | OpenapiSettings | fastapi |
| scheduler | SchedulerSettings | - |
| growth_hacker | GrowthHackerSettings | browser, ip_proxy, logger |

## 📝 配置模板

### 最小配置
```env
DATABASE__MYSQL_URI=mysql://localhost/test
DATABASE__REDIS_URI=redis://localhost
RABBITMQ__URL=amqp://localhost
```

### 开发配置
```env
DATABASE__MYSQL_URI=mysql://dev:dev@localhost/dev_db
DATABASE__REDIS_URI=redis://localhost:6379/0
RABBITMQ__URL=amqp://guest:guest@localhost:5672/
DEBUG=true
FASTAPI__PORT=8000
```

### 生产配置
```env
DATABASE__MYSQL_URI=${SECRET_DB_URI}
DATABASE__REDIS_URI=${SECRET_REDIS_URI}
RABBITMQ__URL=${SECRET_RMQ_URL}
DEBUG=false
FASTAPI__WORKERS=4
```

---

**提示**: 使用 `Ctrl+F` 快速查找所需信息