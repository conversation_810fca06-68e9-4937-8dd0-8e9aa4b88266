# encoding: utf-8
# src/interfaces/schedulers/tasks/sync_union_purchase.py
# created: 2025-03-09 23:21:12

import asyncio
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.applications.common.queries.benefits import UnionPurchaseQueryService
from src.interfaces.schedulers import Container
from src.interfaces.schedulers.main import lifespan

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsSupplierService
    from src.repositories.benefits import SupplierRepository


@inject
async def sync_union_purchases(
    union_purchase_query_service: UnionPurchaseQueryService = Provide[
        Container.applications.common_union_purchase_query_service
    ],
    supplier_repo: "SupplierRepository" = Provide[Container.repositories.benefits_supplier_repository],
    supplier_service: "BenefitsSupplierService" = Provide[Container.domains.benefits_supplier_service],
):
    """同步饿了么联盟权益购买订单"""

    # 获取饿了么联盟供应商
    supplier = await supplier_repo.get_by_identify("eleme_union")
    if not supplier:
        logger.error("饿了么联盟供应商没有找到, 停止同步脚本运行. supplier identify: eleme_union")
        return

    # 使用应用层服务获取所有购买订单
    purchases = await union_purchase_query_service.get_all_purchases()

    logger.info(f"开始同步联盟权益购买订单，共{len(purchases)}条")

    # 处理每个购买订单
    for purchase in purchases:
        await supplier_service.sync_eleme_union_purchase(purchase, supplier.id)

    logger.info("联盟权益购买订单同步完成")


async def main():
    from src.containers import Container
    from src.infrastructures.settings import BaseServiceSettings

    # 创建容器实例
    container = Container()
    container.config.from_pydantic(BaseServiceSettings())

    async with lifespan(container) as active_container:
        active_container.wire(modules=[__name__])  # 注册依赖

        await sync_union_purchases()


if __name__ == "__main__":
    asyncio.run(main())
