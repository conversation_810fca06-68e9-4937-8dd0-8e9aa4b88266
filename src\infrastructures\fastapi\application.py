# encoding: utf-8
# src/infrastructures/fastapi/application.py
# created: 2025-07-28 16:15:39

from contextlib import asynccontextmanager
from typing import Any, Callable, List, Optional

from dependency_injector.containers import DeclarativeContainer
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
from tortoise import Tortoise

from src.infrastructures.databases import RedisManager, init_tortoise
from src.infrastructures.errors import BusinessError
from src.infrastructures.fastapi.response import BaseResponse
from src.infrastructures.logger import setup

from .middlewares import RequestLoggingMiddleware


async def health_check():
    """健康检查"""
    return {"status": "ok", "service": "healthy"}


def register_app(
    name: str,
    version: str = "1.0.0",
    description: str = "",
    container: Optional[DeclarativeContainer] = None,
    config: Optional[Any] = None,
    tags: Optional[List[str]] = None,
    custom_middleware: Optional[List[Callable]] = None,
    enable_middleware: bool = True,
) -> FastAPI:
    """统一的 FastAPI 应用注册方法

    Args:
        name: 应用名称
        version: 版本号，默认 "1.0.0"
        description: 应用描述
        container: 可选的依赖注入容器，如果不提供则创建新容器
        config: 可选的配置对象，如果不提供则加载默认配置
        tags: 可选的标签列表
        custom_middleware: 可选的自定义中间件列表
        enable_middleware: 是否启用默认中间件
    """

    # 如果没有提供配置，使用空配置占位
    if config is None:
        raise ValueError("config parameter is required")

    # 如果没有提供容器，创建新容器
    if container is None:
        from src.containers import Container

        container = Container()
        container.config.override(config)

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        """应用生命周期管理"""
        # 初始化日志系统
        log_config = {
            "app_name": app.title,
            "sinks": [
                {"type": "console", "level": "INFO"},
                {"type": "file", "level": "DEBUG", "path": f"logs/{app.title}.log"},
            ],
        }

        # 如果配置中有日志相关设置，可以根据需要调整
        if hasattr(config, "logger") or hasattr(config, "log"):
            # 可以从配置中提取更多信息来定制日志配置
            pass

        setup(log_config)

        # 初始化数据库
        await init_tortoise(config.database)
        redis_manager = RedisManager(config.database)

        logger.info(f"Starting {app.title} service...")

        yield

        # 清理资源
        await redis_manager.close()
        await Tortoise.close_connections()
        logger.info(f"Shutting down {app.title} service...")

    # 创建 FastAPI 应用
    app = FastAPI(title=name, version=version, description=description, tags=tags or [], lifespan=lifespan)

    # 将容器绑定到应用状态
    app.state.container = container

    # 添加健康检查端点
    app.head("/", tags=["system"])(health_check)
    app.get("/health", tags=["system"])(health_check)

    # 添加中间件
    if enable_middleware:
        app.add_middleware(RequestLoggingMiddleware)

    # CORS 中间件 - 兼容多种配置格式
    webapp_config = getattr(config, "webapp", None) or getattr(config, "fastapi", None)
    cors_origins = ["*"]
    allow_credentials = True
    allow_methods = ["*"]
    allow_headers = ["*"]

    if webapp_config:
        cors_origins = getattr(webapp_config, "cors_origins", cors_origins)
        allow_credentials = getattr(webapp_config, "allow_credentials", allow_credentials)
        allow_methods = getattr(webapp_config, "allow_methods", allow_methods)
        allow_headers = getattr(webapp_config, "allow_headers", allow_headers)

    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,
        allow_credentials=allow_credentials,
        allow_methods=allow_methods,
        allow_headers=allow_headers,
    )

    # 添加自定义中间件
    for middleware in custom_middleware or []:
        app.middleware("http")(middleware)

    # 添加异常处理器
    @app.exception_handler(BusinessError)
    async def business_error_handler(request: Request, exc: BusinessError) -> Response:
        logger.warning(f"business error: {exc.code} - {exc.message}", error_code=exc.code, error_message=exc.message)
        return JSONResponse(status_code=200, content=BaseResponse(code=exc.code, message=exc.message).model_dump())

    @app.exception_handler(Exception)
    async def exception_handler(request: Request, exc: Exception) -> Response:
        exc_message = str(exc).replace("{", "{{").replace("}", "}}")
        logger.exception(
            f"server internal error: {exc_message}", extra={"error_code": 500, "error_message": exc_message}
        )
        return JSONResponse(status_code=500, content={"detail": "Server Internal Error."})

    return app
