# encoding: utf-8
# tests/unit/applications/openapi/queries/test_activities.py
# created: 2025-08-02 16:00:00

"""OpenAPI 活动查询服务测试"""

import urllib.parse
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.applications.openapi.errors import ActivityNotFoundError
from src.applications.openapi.queries.activities import ActivitiesQueryService
from src.domains.activity.entities import ActivityEntity
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory, OpenAPIMockFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.unit
class TestActivitiesQueryService(BaseOpenapiUnitTest):
    """测试活动查询服务"""

    @pytest.fixture
    def mock_delivery_page_service(self):
        """Mock 投放页面服务"""
        return AsyncMock()

    @pytest.fixture
    def mock_delivery_page_repo(self):
        """Mock 投放页面仓储"""
        return AsyncMock()

    @pytest.fixture
    def mock_bifrost_gateway(self):
        """Mock Bifrost 网关"""
        return AsyncMock()

    @pytest.fixture
    def mock_eleme_union_delivery_gateway(self):
        """Mock 饿了么联盟网关"""
        return AsyncMock()

    @pytest.fixture
    def mock_eleme_activity_service(self):
        """Mock 饿了么活动服务"""
        mock = AsyncMock()
        mock.build_channel_params.return_value = {
            "channel": "test_channel",
            "mobile": "13800138000",
            "openid": "test_openid",
        }
        return mock

    @pytest.fixture
    def mock_wifi_master_gateway(self):
        """Mock WiFi Master 网关"""
        mock = AsyncMock()
        mock.parse_auth_code.return_value = "13800138000"
        return mock

    @pytest.fixture
    def mock_config(self):
        """Mock 配置"""
        config = MagicMock()
        config.hostname.base_fe = "https://test.example.com"
        return config

    @pytest.fixture
    def activities_service(
        self,
        mock_delivery_page_service,
        mock_delivery_page_repo,
        mock_bifrost_gateway,
        mock_eleme_union_delivery_gateway,
        mock_eleme_activity_service,
        mock_wifi_master_gateway,
        mock_redis,
        mock_config,
    ):
        """活动查询服务实例"""
        # 创建mock redis manager
        mock_redis_manager = MagicMock()
        mock_redis_manager.client = mock_redis

        return ActivitiesQueryService(
            delivery_page_service=mock_delivery_page_service,
            delivery_page_repo=mock_delivery_page_repo,
            bifrost_gateway=mock_bifrost_gateway,
            eleme_union_delivery_gateway=mock_eleme_union_delivery_gateway,
            eleme_activity_service=mock_eleme_activity_service,
            wifi_master_gateway=mock_wifi_master_gateway,
            redis=mock_redis_manager,
            config=mock_config,
        )

    @pytest.fixture
    def sample_customer(self):
        """示例客户"""
        return CustomerEntity(
            id=1,
            code="test_customer_001",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=10000,
        )

    @pytest.fixture
    def sample_page_info(self):
        """示例页面信息"""
        return MagicMock(
            id=1,
            code="test_page_001",
            name="测试投放页面",
            description="测试投放页面描述",
            url="https://activity.example.com/test",
            union_active_id="union_001",
            union_zone_pid="zone_001",
        )

    @pytest.fixture
    def sample_activity_links(self):
        """示例活动链接"""
        from src.domains.activity.schemas import ActivityLinks
        from src.utils.eleme.dto import TopH5PromotionDto, TopWxPromotionDto

        return ActivityLinks(
            sid="test_sid",
            h5_promotion=TopH5PromotionDto(h5_url="https://h5.eleme.cn/test"),
            wx_promotion=TopWxPromotionDto(
                wx_app_id="wx_test_123",
                wx_path="/test/path",
            ),
        )

    @pytest.mark.asyncio
    async def test_init(
        self,
        mock_delivery_page_service,
        mock_delivery_page_repo,
        mock_bifrost_gateway,
        mock_eleme_union_delivery_gateway,
        mock_eleme_activity_service,
        mock_wifi_master_gateway,
        mock_redis,
        mock_config,
    ):
        """测试服务初始化"""
        mock_redis_manager = MagicMock()
        mock_redis_manager.client = mock_redis

        service = ActivitiesQueryService(
            delivery_page_service=mock_delivery_page_service,
            delivery_page_repo=mock_delivery_page_repo,
            bifrost_gateway=mock_bifrost_gateway,
            eleme_union_delivery_gateway=mock_eleme_union_delivery_gateway,
            eleme_activity_service=mock_eleme_activity_service,
            wifi_master_gateway=mock_wifi_master_gateway,
            redis=mock_redis_manager,
            config=mock_config,
        )

        assert service.delivery_page_service == mock_delivery_page_service
        assert service.delivery_page_repo == mock_delivery_page_repo
        assert service.bifrost_gateway == mock_bifrost_gateway
        assert service.eleme_union_delivery_gateway == mock_eleme_union_delivery_gateway
        assert service.eleme_activity_service == mock_eleme_activity_service
        assert service.wifi_master_gateway == mock_wifi_master_gateway
        assert service.redis == mock_redis
        assert service.config == mock_config

    @pytest.mark.asyncio
    async def test_get_eleme_pure_url_success(
        self, activities_service, mock_delivery_page_repo, sample_customer, sample_page_info, sample_activity_links
    ):
        """测试获取饿了么纯净版链接成功"""
        page_code = "test_page_001"
        extra_info = {"utm_source": "test", "utm_medium": "api"}

        # 设置mock返回值
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock ActivityEntity.from_delivery_page
        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = sample_activity_links

        with patch(
            "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page", return_value=mock_activity
        ):
            # 执行查询
            result = await activities_service.get_eleme_pure_url(sample_customer, page_code, extra_info)

        # 验证结果
        assert result == sample_activity_links

        # 验证方法调用
        mock_delivery_page_repo.get_page_by_code.assert_called_once_with(page_code)

        # 验证extra_info被正确合并
        expected_extra_info = {
            **extra_info,
            "customer_code": sample_customer.code,
            "customer_name": sample_customer.name,
        }
        mock_activity.get_links_with_eleme_miniapp.assert_called_once_with(
            activities_service.bifrost_gateway, activities_service.eleme_union_delivery_gateway, expected_extra_info
        )

    @pytest.mark.asyncio
    async def test_get_eleme_pure_url_page_not_found(
        self, activities_service, mock_delivery_page_repo, sample_customer
    ):
        """测试页面不存在的情况"""
        page_code = "nonexistent_page"
        extra_info = {}

        # 设置mock返回值 - 页面不存在
        mock_delivery_page_repo.get_page_by_code.return_value = None

        # 验证抛出异常
        with pytest.raises(ActivityNotFoundError):
            await activities_service.get_eleme_pure_url(sample_customer, page_code, extra_info)

        # 验证方法调用
        mock_delivery_page_repo.get_page_by_code.assert_called_once_with(page_code)

    @pytest.mark.asyncio
    async def test_get_eleme_url_with_auth_success(
        self,
        activities_service,
        mock_delivery_page_repo,
        mock_eleme_activity_service,
        sample_customer,
        sample_page_info,
        sample_activity_links,
    ):
        """测试获取带认证的饿了么链接成功"""
        page_code = "test_page_001"
        extra_info = {"utm_source": "test"}
        jwt_token = "test_jwt_token"
        eleme_channel = "test_channel"
        latitude = 39.9042
        longitude = 116.4074

        # 设置mock返回值
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock channel params
        channel_params = {"channel": eleme_channel, "mobile": "13800138000"}
        mock_eleme_activity_service.build_channel_params.return_value = channel_params

        # Mock ActivityEntity.from_delivery_page
        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = sample_activity_links

        with (
            patch(
                "src.domains.customer.entities.CustomerEntity.parse_jwt", return_value=("test_openid", "13800138000")
            ) as mock_parse_jwt,
            patch(
                "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page",
                return_value=mock_activity,
            ),
            patch("src.utils.eleme.channels.ElemeChannelsUtils.url_with_params") as mock_url_with_params,
        ):

            mock_url_with_params.return_value = "https://h5.eleme.cn/test?channel=test_channel"

            # 执行查询
            result = await activities_service.get_eleme_url_with_auth(
                sample_customer, page_code, extra_info, jwt_token, eleme_channel, latitude, longitude
            )

            # 验证结果
            assert result.h5_promotion.h5_url == "https://h5.eleme.cn/test?channel=test_channel"

            # 验证方法调用
            mock_delivery_page_repo.get_page_by_code.assert_called_once_with(page_code)
            mock_parse_jwt.assert_called_once_with(jwt_token)
            mock_eleme_activity_service.build_channel_params.assert_called_once_with(
                eleme_channel=eleme_channel,
                mobile="13800138000",
                user_open_id="test_openid",
                latitude=latitude,
                longitude=longitude,
            )

    @pytest.mark.asyncio
    async def test_get_eleme_url_with_auth_page_not_found(
        self,
        activities_service,
        mock_delivery_page_repo,
        sample_customer,
    ):
        """测试获取带认证链接时页面不存在"""
        page_code = "nonexistent_page"
        extra_info = {}
        jwt_token = "test_jwt_token"
        eleme_channel = "test_channel"

        # 设置mock返回值 - 页面不存在
        mock_delivery_page_repo.get_page_by_code.return_value = None

        # 验证抛出异常
        with pytest.raises(ActivityNotFoundError):
            await activities_service.get_eleme_url_with_auth(
                sample_customer, page_code, extra_info, jwt_token, eleme_channel, None, None
            )

    @pytest.mark.asyncio
    async def test_get_eleme_wifimaster_url_user_exists(
        self,
        activities_service,
        mock_delivery_page_repo,
        mock_wifi_master_gateway,
        mock_eleme_activity_service,
        mock_redis,
        sample_customer,
        sample_page_info,
        sample_activity_links,
    ):
        """测试WiFi Master用户已存在的情况"""
        page_code = "test_page_001"
        auth_code = "test_auth_code"
        latitude = 39.9042
        longitude = 116.4074
        extra_info = {"utm_source": "wifimaster"}
        phone = "13800138000"

        # 设置mock返回值
        mock_wifi_master_gateway.parse_auth_code.return_value = phone
        mock_redis.exists.return_value = 1  # 用户已存在
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock channel params
        channel_params = {"channel": "wifi_master", "mobile": phone}
        mock_eleme_activity_service.build_channel_params.return_value = channel_params

        # Mock ActivityEntity和链接生成
        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = sample_activity_links

        with (
            patch(
                "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page",
                return_value=mock_activity,
            ),
            patch("src.utils.eleme.channels.ElemeChannelsUtils.url_with_params") as mock_url_with_params,
        ):

            expected_url = "https://h5.eleme.cn/test?channel=wifi_master"
            mock_url_with_params.return_value = expected_url

            # 执行查询
            result = await activities_service.get_eleme_wifimaster_url(
                sample_customer, page_code, auth_code, latitude, longitude, extra_info
            )

        # 验证结果 - 直接返回活动链接
        assert result == expected_url

        # 验证方法调用
        mock_wifi_master_gateway.parse_auth_code.assert_called_once_with(auth_code)
        mock_redis.exists.assert_called_once_with(f"wifi_master_user:{phone}")
        mock_eleme_activity_service.build_channel_params.assert_called_once_with(
            eleme_channel="wifi_master",
            mobile=phone,
            user_open_id=f"wifimaster_{phone}",
            latitude=latitude,
            longitude=longitude,
        )

    @pytest.mark.asyncio
    async def test_get_eleme_wifimaster_url_user_not_exists(
        self,
        activities_service,
        mock_delivery_page_repo,
        mock_wifi_master_gateway,
        mock_eleme_activity_service,
        mock_redis,
        mock_config,
        sample_customer,
        sample_page_info,
        sample_activity_links,
    ):
        """测试WiFi Master用户不存在的情况"""
        page_code = "test_page_001"
        auth_code = "test_auth_code"
        latitude = 39.9042
        longitude = 116.4074
        extra_info = {"utm_source": "wifimaster"}
        phone = "13800138000"

        # 设置mock返回值
        mock_wifi_master_gateway.parse_auth_code.return_value = phone
        mock_redis.exists.return_value = 0  # 用户不存在
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock channel params
        channel_params = {"channel": "wifi_master", "mobile": phone}
        mock_eleme_activity_service.build_channel_params.return_value = channel_params

        # Mock ActivityEntity和链接生成
        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = sample_activity_links

        with (
            patch(
                "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page",
                return_value=mock_activity,
            ),
            patch("src.utils.eleme.channels.ElemeChannelsUtils.url_with_params") as mock_url_with_params,
        ):

            activity_url = "https://h5.eleme.cn/test?channel=wifi_master"
            mock_url_with_params.return_value = activity_url

            # 执行查询
            result = await activities_service.get_eleme_wifimaster_url(
                sample_customer, page_code, auth_code, latitude, longitude, extra_info
            )

        # 验证结果 - 返回中转页面链接
        expected_redirect_url = (
            f"{mock_config.hostname.base_fe}/wifiMaster/landing?redir={urllib.parse.quote(activity_url)}"
        )
        assert result == expected_redirect_url

        # 验证方法调用
        mock_redis.exists.assert_called_once_with(f"wifi_master_user:{phone}")

    @pytest.mark.asyncio
    async def test_get_eleme_wifimaster_url_no_h5_promotion(
        self,
        activities_service,
        mock_delivery_page_repo,
        mock_wifi_master_gateway,
        mock_eleme_activity_service,
        mock_redis,
        sample_customer,
        sample_page_info,
    ):
        """测试WiFi Master链接没有H5推广链接的情况"""
        page_code = "test_page_001"
        auth_code = "test_auth_code"
        extra_info = {}
        phone = "13800138000"

        # 创建没有h5_promotion的活动链接
        from src.domains.activity.schemas import ActivityLinks

        empty_links = ActivityLinks(
            sid="test_sid",
            h5_promotion=None,
            wx_promotion=None,
        )

        # 设置mock返回值
        mock_wifi_master_gateway.parse_auth_code.return_value = phone
        mock_redis.exists.return_value = 1  # 用户存在
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock ActivityEntity
        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = empty_links

        with patch(
            "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page", return_value=mock_activity
        ):
            # 执行查询
            result = await activities_service.get_eleme_wifimaster_url(
                sample_customer, page_code, auth_code, None, None, extra_info
            )

        # 验证结果 - 返回空字符串
        assert result == ""

    @pytest.mark.asyncio
    async def test_error_handling_wifi_master_auth_failed(
        self,
        activities_service,
        mock_wifi_master_gateway,
    ):
        """测试WiFi Master认证失败的错误处理"""
        auth_code = "invalid_auth_code"

        # 设置mock抛出异常
        mock_wifi_master_gateway.parse_auth_code.side_effect = Exception("Auth code parse failed")

        # 验证异常传播
        with pytest.raises(Exception, match="Auth code parse failed"):
            await activities_service.get_eleme_wifimaster_url(MagicMock(), "test_page", auth_code, None, None, {})

    @pytest.mark.asyncio
    async def test_customer_jwt_parsing_error(
        self,
        activities_service,
        mock_delivery_page_repo,
        sample_customer,
        sample_page_info,
    ):
        """测试客户JWT解析错误"""
        page_code = "test_page_001"
        extra_info = {}
        jwt_token = "invalid_jwt_token"
        eleme_channel = "test_channel"

        # 设置mock返回值
        mock_delivery_page_repo.get_page_by_code.return_value = sample_page_info

        # Mock ActivityEntity
        mock_activity = AsyncMock()

        with (
            patch(
                "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page",
                return_value=mock_activity,
            ),
            patch("src.domains.customer.entities.CustomerEntity.parse_jwt", side_effect=Exception("Invalid JWT token")),
        ):
            # 验证异常传播
            with pytest.raises(Exception, match="Invalid JWT token"):
                await activities_service.get_eleme_url_with_auth(
                    sample_customer, page_code, extra_info, jwt_token, eleme_channel, None, None
                )


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.integration
class TestActivitiesQueryServiceIntegration:
    """活动查询服务集成测试"""

    @pytest.mark.asyncio
    async def test_complete_eleme_pure_url_flow(self):
        """测试完整的饿了么纯净版链接获取流程"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        activity_data = OpenAPITestDataFactory.create_activity_data()

        # 创建mock对象
        mocks = {
            "delivery_page_service": AsyncMock(),
            "delivery_page_repo": AsyncMock(),
            "bifrost_gateway": AsyncMock(),
            "eleme_union_delivery_gateway": AsyncMock(),
            "eleme_activity_service": AsyncMock(),
            "wifi_master_gateway": AsyncMock(),
            "config": MagicMock(),
        }

        # 设置config
        mocks["config"].hostname.base_fe = "https://test.example.com"

        # 创建Redis manager mock
        mock_redis_manager = MagicMock()
        mock_redis_manager.client = AsyncMock()

        # 创建服务
        service = ActivitiesQueryService(redis=mock_redis_manager, **mocks)

        # 创建客户实体
        customer = CustomerEntity(**customer_data)

        # 设置mock返回值
        mock_page_info = MagicMock(**activity_data)
        mocks["delivery_page_repo"].get_page_by_code.return_value = mock_page_info

        # Mock活动链接
        from src.domains.activity.schemas import ActivityLinks
        from src.utils.eleme.dto import TopH5PromotionDto

        expected_links = ActivityLinks(
            sid="integration_test",
            h5_promotion=TopH5PromotionDto(h5_url="https://h5.eleme.cn/integration"),
            wx_promotion=None,
        )

        mock_activity = AsyncMock()
        mock_activity.get_links_with_eleme_miniapp.return_value = expected_links

        with patch(
            "src.applications.openapi.queries.activities.ActivityEntity.from_delivery_page", return_value=mock_activity
        ):
            # 执行查询
            result = await service.get_eleme_pure_url(
                customer, activity_data["code"], {"utm_source": "integration_test"}
            )

        # 验证结果
        assert result == expected_links
        assert result.h5_promotion.h5_url == "https://h5.eleme.cn/integration"

        # 验证调用链
        mocks["delivery_page_repo"].get_page_by_code.assert_called_once_with(activity_data["code"])
        mock_activity.get_links_with_eleme_miniapp.assert_called_once()

    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """测试错误传播机制"""
        # 创建minimal服务实例
        mock_redis_manager = MagicMock()
        mock_redis_manager.client = AsyncMock()

        service = ActivitiesQueryService(
            delivery_page_service=AsyncMock(),
            delivery_page_repo=AsyncMock(),
            bifrost_gateway=AsyncMock(),
            eleme_union_delivery_gateway=AsyncMock(),
            eleme_activity_service=AsyncMock(),
            wifi_master_gateway=AsyncMock(),
            redis=mock_redis_manager,
            config=MagicMock(),
        )

        customer = CustomerEntity(
            id=1, code="test", name="test", description="test", app_id="test", tenant_id="test", balance=0
        )

        # 测试ActivityNotFoundError
        service.delivery_page_repo.get_page_by_code.return_value = None

        with pytest.raises(ActivityNotFoundError):
            await service.get_eleme_pure_url(customer, "nonexistent_page", {})

        # 测试其他异常的传播
        service.delivery_page_repo.get_page_by_code.side_effect = Exception("Database error")

        with pytest.raises(Exception, match="Database error"):
            await service.get_eleme_pure_url(customer, "test_page", {})
