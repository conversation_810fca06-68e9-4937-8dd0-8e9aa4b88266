version = "development"

[hostname]
openapi = "https://openapi.hicaspian.com"
business = "https://business.hicaspian.com"
base_fe = "https://alpha-base.hicaspian.com"

[webapp]
secret = "Nl2bJsNrdH#ctt7b5n9e6gt4&y)d04(mmo(y=e_cmg7mjwrnal7"
allow_hosts = ["test", "*"]
cors_origins = [
    "https://base.hicaspian.com",
    "https://alpha-base.hicaspian.com",
    "http://127.0.0.1:8083",
    "http://localhost:3000",
    "https://api-movie.hicaspian.com",
    "https://alpha-api-movie.hicaspian.com",
    "https://baseapi.hicaspian.com",
    "https://alpha-baseapi.hicaspian.com",
    "https://mis.hicaspian.com",
    "https://alpha-mis.hicaspian.com",
    "https://misweb.hicaspian.com",
    "https://alpha-misweb.hicaspian.com",
]
allow_methods = ["HEAD", "OPTIONS", "GET", "POST", "PUT", "DELETE"]
allow_headers = ["*"]
allow_credentials = true

[log]
dir = "logs"

[log.handlers.console]
level = "INFO"
enqueue = true
colorize = true
diagnose = true
backtrace = true

[log.handlers.file]
level = "INFO"
enqueue = true
diagnose = true
backtrace = true
rotation = "100 MB"
retention = "7 days"
compression = "zip"

[log.handlers.sls]
endpoint = "cn-shanghai.log.aliyuncs.com"
access_key_id = "LTAI5tGoNih4TGHe7sFRTfwX"
access_key_secret = "******************************"
project = "beaver-py"
logstore = "total"

[database.mongodb]
uri = "***************************************************************************************************"
database = "beaver"

[database.redis]
uri = "*******************************************************"

[database.tortoise]
timezone = "Asia/Shanghai"

[database.tortoise.connections]
default = "mysql://beaver_service:beaver_service123456@*************:63306/beaver_service_bak"

[database.tortoise.apps.models]
models = [
    "aerich.models",
    "src.databases.models.customer",
    "src.databases.models.benefits",
    "src.databases.models.datas",
    "src.databases.models.delivery",
    "src.databases.models.passport",
    # "src.databases.models.activities",
]
default_connection = "default"

[rabbitmq]
host = "rabbitmq-cn-em943gbtg04.cn-shanghai.amqp-22.net.mq.amqp.aliyuncs.com"
vhost = "hicaspian-base"
username = "MjpyYWJiaXRtcS1jbi1lbTk0M2didGcwNDpMVEFJNXQ2Zm5wOUJIcGdBbjU5RjNZQXY="
password = "OEQ5NTNGODAxNjE1MEQxMjg2MjJGRTU4RTZGNjYzNDZBNUMxMUFEMToxNzM3MzEwMjMxNDE5"
port = 5672
pool_size = 5

[thirdpart.eleme.union]
app_key = "34813436"
app_secret = "929eafdde0fb34c57ff0290e271b54c3"
top_gateway_url = "https://eco.taobao.com/router/rest"
verify_ssl = true

[thirdpart.eleme.channels.haili]
eleme_channle_no = "mobile.haili"
eleme_channel_source = "haili"
eleme_channel_app_id = "haili"
eleme_channel_secret = "a82d232c88594435317451bc0fe50fdda8977368"

[thirdpart.eleme.channels.wifi_master]
eleme_channle_no = "mobile.wifihaili"
eleme_channel_source = "wifihaili"
eleme_channel_app_id = "wifihaili"
eleme_channel_secret = "e6bcdfcfd49f04a26c859829d016701ccddd7925"

[thirdpart.eleme.channels.aikesi]
eleme_app_id = "56999682"
eleme_channle_no = "mobile.aikesinew"
eleme_channel_source = "aikesinew"
eleme_channel_app_id = "aikesinew"
eleme_welfare3pp = "AIKESINEW_OPENPAY"
eleme_channel_secret = "9f3e21fd42c415aaad06f802050215432bb6c7db"

[thirdpart.eleme.channels.dine_in]
eleme_channle_no = "mobile.beaveraoi"
eleme_channel_source = "beaveraoi"
eleme_channel_app_id = "beaver_dine_in"
eleme_channel_secret = "a28e582e5f1189abf4df52c72ee31bf2ccf66b4e"
eleme_channel_url = "https://tb.ele.me/app/info-web/co-welfare-login/home"
eleme_channel_pay_type = "alipay"

[thirdpart.eleme_union]
app_key = "34813436"
app_secret = "929eafdde0fb34c57ff0290e271b54c3"
top_gateway_url = "https://eco.taobao.com/router/rest"
verify_ssl = true

[thirdpart.bifrost]
public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtf7rj76PWJbb0x61WuBhKvY7F090ZWwYnww1Vs12LrJQazhhoxLQ8mwuPuIStaaXSbxJBxI2t6BwkiEx4a9UmGFEP6UilNJ3OY+lW6ChSlCQk/BZfkxughXwX3OzW+bF9YRwXKWjKZgHLDB/6YOBL+vfL30miMdIx6Ta8nMfgsGam8lQRByNl9YDcVF+Im4gCx6Q997kxBBElItQw+IwbDF54ZNkE5ZXbhsELBUqZI0RzHdz+wZU3q3ydWo8z92CCiRJW9BXeGbA8aOfhXtbzDOixk96SaxMG0vneDovoZ6fMnBVD1mIzlkSysCCCZPyscaQPVY6bQWoa2NTPTeLCQIDAQAB"
private_key = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDENxQErAlThyAdPbGU3tUy/y1EPvuGoH0Pt/Xf9aCeDnqVkGGHO0+V79J/I9GQVz02CK5D8YKhPmvHfxghtJUqbktH2OetLaSNtmLtO+kqNCMTE+OLYqYasBsZdgtRzZiILD7gB/vRoQ/WS39xtWtaQvfpJyn4t4UbvpuCm8b7aM4yRsexHq72nSjXYwsq9+axwHETXg3AEwOkdmii0WI5zGYH9GHWE3qOizFIJD2+VUu/Jn+m8AyygP4JVIlIMvZn6huiLlfwzYiXu2yRVaf8Z30uf1ovtqIOC3sO4ULFbk8jRyI9JA875IMhNWO5XoBS4eDFY/oiRRGv8zeKsq61AgMBAAECggEAKsDEhhsVIiPSccSgRauDAUxcCN9/Ty93aH4hHxYmU+IcQCv4MC7Scb1SXH0Ju71iezxLekbse2U+NzcAY1G1Ycwr74D5xABqty0LI51W+ejnzo+aGCQbxLtkADl6EG4vgtY/MfHTYvMI4BzNrVFGDaIDYgQ0TzrvkCLJQB1gryUeWU2yhDlX8CDlYbwCEmz8KOYOMDqZJaG09Luf2yLK4tzREmOTBp2nmKJzca2BV1u//6Cug2TuuKaYOlVmdQyaGi2c8lrcVv0Zt7G+J9JU6l6zXSjqw7eebq/0EuvJTPJjWCn37nVhKJ5nUWGe8rHbzLlnrksoL54SZ6BtH0NrMQKBgQDtfppTmRJGltdNugaVZO018OqNhwe+X34xKWEel9XW2hZ54clehsVTQZ8kw0TQbWDU9/z+ODisW0G/C/UMlJfmlE+ZaXvNEKOEyNC+fQTydGY2laKLS8d3XKcSYTk+N9+G+UwDnM5LFiJBIy5fprYRmSeqXV1iWTWcT0xHN8wgUQKBgQDTgQ8du/LSgVztGR1E5mU2t+4/C4OaAn78JTWFcOJdaNUDrgGlk7ac+KcwIlgedcx72D1Dfd2y4KDwiAkKQOCxpgKVnp05JHgKYeMPHFewB0UmRr/mxZgU0gW07kBlvh2NGLKdXIqInWWS5f4CqhX98TRuLNmb1iknFaxl0AwTJQKBgQCXJWS4YYnlDOjXmdXIzGO/WaUdD1vZR9L7HLenjcBVLZTyWsUaeLEqrG/JKNEpyQLAZ1dGv6cp4iY0nTqGmCcYYzlJjH1y4+z9fASFxvEYEQZgJNk+x6qZh4j4xJF2zH5g21YKUohj9yEzzV0dGO90wcEhxqvBBL9+zGTiSluKEQKBgQCN/T2Fq7DHi1s0PUD+CWJ3iqFiJ7uwv+46HkzBCdid9wvSTZYDb5gP54pt8RCRWmnt1mzCi5QzS0QgVmMjRAzUlmtzwKkyPH5uEesaMN/ZZ/gPSz33kj2X9KsqHSyUYT57g9IdfIvwTSJsrQSLC156Pd0B/******************************/hGKnLv0ngl1k9BqIV+/M3/rnacJPqn9f1cmbo76b0YpZZrF9oI1CjryXzPIr49bOqGjnF/GIUAHPu90KKZ2KVn9bkANApi2vr910pWjrJZaRzLLSS8Hp0EUv3RsLqZneuyNZ0htYAYK0W6IvcibVMR+p8/71U3hrig=="
account = "1234"
secret_key = "1234"
base_url = "https://alpha-bifrost.hicaspian.com/openapi"
prefix = "alpha"

[thirdpart.dingtalk]
order_notify_url = "https://edu.dingtalk.com/acceptOrder"

[thirdpart.wifimaster]
sso_url = "https://api.ttwifi.net/opensso/finclip/phone/get"
app_id = "**********"

[thirdpart.aliyun.sms]
access_key_id = "LTAI5tCX3VPTGfx5URDAvybo"
access_key_secret = "******************************"
endpoint = "dysmsapi.aliyuncs.com"
sign_name = "南京里海数据科技"
template_code = "SMS_302800024"
repeat_interval = 60

[thirdpart.henan_mobile]
partner_id = "**********"
secret_key = "1234"
base_url = "https://example.com/api"

[thirdpart.wechat.benefits_mp]
app_id = "wx5a9e650464e4584f"
app_secret = "3668f9e7fbb54228673efe74236dfcdb"

[thirdpart.aliyun.oss]
access_key_id = "LTAI5tCX3VPTGfx5URDAvybo"
access_key_secret = "******************************"
role_arn = "acs:ram::****************:role/oss-uploader"
role_session_name = "web-upload-session"

[thirdpart.alipay]
ad_biz_token = "608a66b8e6374dba92286a08e8b98290"
app_id = "2021004195613243"
server_url = "https://openapi.alipay.com/gateway.do"
alipay_public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9e4tZtI2P1wQQL/adWdJ/eNqdKaCyKsp2Of7XWJzWPHkURU0ETcVbBcLRZv8JHtTNba00jj5Z0JHn9C3SazFu1cew7Lgu9I/lghQcD1WE+DDPFrnfY+******************************+RK0ErnQsTmWoclRkpH79NT74ABPzcwd3B84mW2BJSZAgtngF2iT5sXff6oDSIR0BPTzOmyjIpupTrmM5KJFFU0W5rDwMjFDlXnIAeYFDXkoB/eFkRalQd1D4WkIijvx2g4Jnruo7RKDfMk1quqxM0M9bkJlUJsEgNn84txIX4Mq+rkH/PWJ8Ap1WVmKJx7fghJgQIDAQAB"
app_private_key = "MIIEogIBAAKCAQEArMpFjH7Tj/t81QpslgfgcpuiH53rtn6ca7i/7QqiIofDCoSGR4pAASijlT5mlmdO0FUos+pQyEv6Ucs4DqiRN+PXMvwv7hJ0gVBG3+BNrCoYxnZ5+4rmTwHJJrj/XWCTYrRWgFQhps79W5NiMIpcS5tNXGOmtEQfrucgCHuIczXsjFSbM9CIoFwunLdmmFiyl8qylhPnyaMvWKPT1u4yJ7iImJBb/E1H1MBk2uvyQ9tCAU8CR+5TJo69BxSHTGLyKJE0C19zVUlJtAVmTUAQqnH1jCFcXR0oyNPfWOvEJB0M3xCh0zV02GSwEWSou+zw8FN5jg3dMhsrCP0BIX0cpwIDAQABAoIBABiN0d5KM4Q1Z6LajV2wltuSdDJr/Y/8Y/wGz/c+WJXxluzKsk4+PiQsAzr1GBztZ0zBnTwb4wjjixnOeBPVLnWzwePz5Fe/daDeqIOt2zvI66ZgNatiLKIzjcMb7OX3EFqpZ9VqnzGWHnjB8+UT9FOmKRMk8g6R07LGE05BR72fQqfLdMuRJQUiT4WmKWuwbyig21CVCpOlPLXkyzARm9Gn347pHPQVyAQTQK8deDn6UBtefK/4suRqtppw/i6iASq1cOvRlkhq3iQ/LY+65LBcUqK86QywKwzCOgD9uEI5iBmE5veFBwTrUaGu52wsgYxUyVFiP7hLDYxALYCLC/kCgYEA84xHsYoRPUW4I3MhXOqBD/NeM3cueOpehidP2Q7lYFbYbgvc00WXrq398rf3KVpP7E9tLKUeGpYhtidzooU9/NZAV/2EoQGq4NdjxZIbKDFIvq9giUo+qi1d8MclijfKvVHN/tXP6L2LQkgEsbd6PtWygz0xo0cOezUxLd4RpJ0CgYEAtZ/dgaVn3WuJf8wNMsdF7FZNGzcvrM/vTKm2sDyJP64VXtY98TEfbRBZXTUtQ6LNOOVL90Tt/qSO62iIOCkitbGS+sBn47oJYRtg8+njuLD9Xs0m+jpyTqRU88lH9wSLbD+T7yYUaf5Ahd3Pwe9dyN5yhC3aFEyOryG7x15X6RMCgYBBY9yr8mIGjX2PJw5CIERev/z+3HUBygtvAYcSxEkZwLeDdHPp1bmQEO8qB/K4i6MB06GAZyTaBo0ulxEZBaVLCUtlVJATmpsCm1ISbdQUyoa5i+Tjd6ezkVKznwSZQ7mPczNXxJh56MpHFYcNIDIWRNIVIoz9RcrBkdqOch1SkQKBgEVp1sdQxvRZwBAmlTi97hxYE1n+amsagTOEfTy3tiCvUJ2RNdUzV/Zf1DeKNkVuOA6xm6niHy8+Bx0zMzR7jDdyqWDKHiprlGAXjaNK1WxlwD/2GhpyMfOaXJlbaPSgOQTi+4/ftteXg2NmFZvh9q7pTUz1FS7C35lRRCn7BZAfAoGAA/YSmYEallEcWT+y/lDSkF4JOl/yrhgmy6Hh6TsX8OA2inLarLUeQNLrT25cc/l0SZrtYEHPL1h8Ylh3FnR8S21VelLf/GNNe37P/quzuvMS9MZkMru+KdKm//se6sJiE8qJODOpdT5IrnbPKqiBbwVcxQoCG022AX+L08VL+i4="

[thirdpart.shinesun.no_tex]
base_url = "https://cg.shinesun.cn/userapi"
mac_id = "1729606885280663"
secret_key = "uvJ5xj6MTR0hs7f6TbBeebMnVIdFwxuc"

[thirdpart.shinesun.tex]
base_url = "https://cg.shinesun.cn/userapi"
mac_id = "1729012258700010"
secret_key = "ke7r65qDjFMN7wbRwjpsaa8wo1IFBirb"