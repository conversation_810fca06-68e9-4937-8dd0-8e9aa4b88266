# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/services/pages.py
# created: 2025-02-06 12:42:41
# updated: 2025-06-10 09:45:54

from typing import Optional

import toml
from dependency_injector.wiring import Provide, inject
from loguru import logger
from redis.asyncio import Redis

from src.databases.models.delivery import DeliveryPageTypeEnum
from src.domains.delivery.dto import DeliveryPageDTO
from src.infrastructures import errors
from src.repositories.delivery.pages import DeliveryPageRepository
from src.utils.eleme.channels import ElemeChannelsUtils
from src.utils.eleme.dto import UnionActivityInfoDto, UnionActivityUrlDTO, UnionPageLinkDTO

# from src.utils.eleme.union_delivery import UnionDeliveryUtils

SPEC_ACT_ID = "10883"
AUTO_GET_COUPONS_URL = "https://h5.ele.me/adsite/pages/home/<USER>"


class DeliveryPageService:

    @classmethod
    async def _get_page_by_code(cls, page_code: str) -> DeliveryPageDTO:
        """内部方法：根据页面编码获取页面，如果不存在则抛出异常"""
        page = await DeliveryPageRepository.get_page_by_code(page_code)
        if not page:
            logger.error(f"Page not found: {page_code}")
            raise errors.DeliveryPageNotFoundError
        return await DeliveryPageDTO.from_tortoise_orm(page)

    @classmethod
    def _get_page_url(cls, page: DeliveryPageDTO) -> str:
        """获取页面基础URL"""
        if page.code == "autoGetCoupons":
            return AUTO_GET_COUPONS_URL
        elif page.url:
            return page.url
        else:
            return ""  # 需要通过联盟API获取

    @classmethod
    def build_channel_params(
        cls,
        eleme_channel: str,
        mobile: str,
        user_open_id: str,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
    ) -> dict:
        """构建渠道参数"""
        channel_params = ElemeChannelsUtils.generate_channels_params(eleme_channel, mobile, user_open_id)
        if latitude and longitude:
            channel_params["latitude"] = latitude
            channel_params["longitude"] = longitude
        return channel_params

    # @classmethod
    # @inject
    # async def _get_union_activity_url(
    #     cls, page: DeliveryPageDTO, extra_info: dict, redis: Redis = Provide["redis"]
    # ) -> str:
    #     """获取联盟活动URL"""
    #     import hashlib

    #     extra_info_hash = hashlib.md5(str(extra_info).encode()).hexdigest()
    #     key = f"union_activity_url:{page.union_active_id}:{page.union_zone_pid}:{extra_info_hash}"
    #     activity_link = await redis.get(key)
    #     if activity_link:
    #         logger.info(f"hit redis cache: {key}")
    #         return activity_link

    #     activity_link = await UnionDeliveryUtils.get_union_activity_url(
    #         page.union_active_id, page.union_zone_pid, extra_info
    #     )

    #     if not activity_link.h5_promotion or not activity_link.h5_promotion.h5_url:
    #         raise errors.ActivityH5LinkNotFoundError

    #     await redis.set(key, activity_link.h5_promotion.h5_url, ex=60 * 60 * 24)
    #     return activity_link.h5_promotion.h5_url

    # @classmethod
    # def _extract_url_from_activity_info(cls, page: DeliveryPageDTO, activity_info: UnionActivityInfoDto) -> str:
    #     """从活动信息中提取URL"""
    #     if page.union_active_id == SPEC_ACT_ID:
    #         return activity_info.link.h5_url or ""
    #     elif page.code == "autoGetCoupons":
    #         return AUTO_GET_COUPONS_URL
    #     else:
    #         if activity_info.link.wx_promotion and activity_info.link.wx_promotion.wx_path:
    #             page_url = activity_info.link.wx_promotion.wx_path
    #             return page_url.replace("ad-bdlm-sub", "https://h5.ele.me/adminiappsub")
    #         return ""

    # 公共API方法
    @classmethod
    async def get_page_by_code(cls, page_code: str) -> DeliveryPageDTO:
        """根据页面编码获取页面信息"""
        return await cls._get_page_by_code(page_code)

    @classmethod
    async def get_pages(cls) -> list[DeliveryPageDTO]:
        """获取所有页面列表"""
        pages = await DeliveryPageRepository.get_pages()
        return [await DeliveryPageDTO.from_tortoise_orm(page) for page in pages]

    @classmethod
    async def create_page(
        cls,
        type: DeliveryPageTypeEnum,
        name: str,
        description: str,
        comment: str,
        union_active_id: str,
        union_zone_pid: str,
        code: Optional[str] = None,
        url: Optional[str] = None,
        custom_behavior: Optional[str] = None,
    ) -> DeliveryPageDTO:
        """创建新页面"""
        page = await DeliveryPageRepository.create_page(
            type, name, description, comment, union_active_id, union_zone_pid, code, url, custom_behavior
        )
        return await DeliveryPageDTO.from_tortoise_orm(page)

    @classmethod
    async def update_page(cls, page_id: int, update_data: dict) -> DeliveryPageDTO:
        """更新页面信息"""
        page = await DeliveryPageRepository.get_page_by_id(page_id)
        if not page:
            raise errors.DeliveryPageNotFoundError
        updated_page = await DeliveryPageRepository.update_page(page, update_data)
        return await DeliveryPageDTO.from_tortoise_orm(updated_page)

    @classmethod
    async def delete_page(cls, page_code: str) -> None:
        """删除页面"""
        page = await DeliveryPageRepository.get_page_by_code(page_code)
        if not page:
            raise errors.DeliveryPageNotFoundError
        await DeliveryPageRepository.delete_page(page)

    @classmethod
    async def get_access_url(
        cls,
        page_code: str,
        mobile: str,
        user_open_id: str,
        eleme_channel: str = "haili",
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
        extra_info: Optional[dict] = None,
    ) -> str:
        """获取饿了么活动页面访问链接+渠道版token联登"""
        page = await cls._get_page_by_code(page_code)
        extra_info = extra_info or {}

        # 获取页面URL
        page_url = cls._get_page_url(page)
        if not page_url:
            page_url = await cls._get_union_activity_url(page, extra_info)

        # 构建渠道版参数并生成最终访问链接
        channel_params = cls.build_channel_params(eleme_channel, mobile, user_open_id, latitude, longitude)
        access_url = ElemeChannelsUtils.url_with_params(page_url, channel_params)

        logger.info(f"获取饿了么活动页面访问链接+渠道版token联登, 最终访问链接: {access_url}")
        return access_url

    # @classmethod
    # async def get_pure_access_url(cls, page_code: str, extra_info: Optional[dict] = None) -> UnionPageLinkDTO:
    #     """获取饿了么活动页面访问链接(不包含渠道版token联登参数)"""
    #     page = await cls._get_page_by_code(page_code)
    #     extra_info = extra_info or {}

    #     # 获取页面URL
    #     page_url = cls._get_page_url(page)
    #     if page_url:
    #         activity_link = UnionPageLinkDTO.model_validate({"h5_promotion": {"h5_url": page_url}})
    #     else:
    #         activity_link = await UnionDeliveryUtils.get_union_activity_url(
    #             page.union_active_id, page.union_zone_pid, extra_info
    #         )

    #     logger.info(f"获取饿了么活动页面访问链接(无联登参数): {activity_link.model_dump()}")
    #     return activity_link

    # @classmethod
    # async def get_access_url_by_sid(cls, page_code: str, sid: Optional[str] = None) -> str:
    #     """获取饿了么活动页面访问链接, 不包含渠道版token联登,不过会包含特殊标识"""
    #     page = await cls._get_page_by_code(page_code)

    #     activity_info = await UnionDeliveryUtils.get_union_activity_info(
    #         page.union_active_id, page.union_zone_pid, sid or ""
    #     )
    #     logger.info(
    #         f"获取联盟活动信息: {page.union_active_id} {page.union_zone_pid} {sid}, 联盟活动信息 {activity_info}"
    #     )

    #     return cls._extract_url_from_activity_info(page, activity_info)

    # @classmethod
    # async def get_shop_url(cls, shop_id: str, mobile: str, user_open_id: str, eleme_channel: str = "haili") -> str:
    #     """获取饿了么店铺访问链接"""
    #     sid = f"aishop.{user_open_id}".strip()
    #     page_url = await UnionDeliveryUtils.get_shop_url(shop_id, sid)

    #     channel_params = cls.build_channel_params(eleme_channel, mobile, user_open_id)
    #     access_url = ElemeChannelsUtils.url_with_params(page_url, channel_params)
    #     return access_url
