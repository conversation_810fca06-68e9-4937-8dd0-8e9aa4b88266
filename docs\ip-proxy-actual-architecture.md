# encoding: utf-8
# docs/ip-proxy-actual-architecture.md
# created: 2025-08-08 11:00:00

# IP Proxy 实际架构图

## 整体架构

```mermaid
graph TB
    subgraph "Growth Hacker Tasks"
        T1[Task 1<br/>城市: 北京]
        T2[Task 2<br/>城市: 上海]
        T3[Task 3<br/>城市: 深圳]
    end
    
    subgraph "ProxyManager 核心组件"
        PM[ProxyManager<br/>主管理器]
        
        subgraph "城市代理池映射"
            CP[city_pools: Dict]
            P1[IpProxyPool<br/>北京]
            P2[IpProxyPool<br/>上海]
            P3[IpProxyPool<br/>深圳]
        end
        
        subgraph "代理提供者"
            QG[QingguoShortResources<br/>青果短效代理]
        end
    end
    
    subgraph "Redis 存储层"
        subgraph "数据结构"
            Z1[ip_proxy:pool:beijing<br/>ZSet 有序集合<br/>按过期时间排序]
            H1[ip_proxy:concurrent:beijing<br/>Hash 并发计数]
            F1[ip_proxy:failures:beijing<br/>Hash 失败计数]
            
            Z2[ip_proxy:pool:shanghai<br/>ZSet 有序集合]
            H2[ip_proxy:concurrent:shanghai<br/>Hash 并发计数]
            F2[ip_proxy:failures:shanghai<br/>Hash 失败计数]
        end
        
        subgraph "分布式锁"
            L1[ip_proxy:fetch_lock:beijing<br/>获取新代理锁]
            L2[ip_proxy:fetch_lock:shanghai<br/>获取新代理锁]
        end
    end
    
    subgraph "外部服务"
        RG[RegionGateway<br/>城市区划转换]
        API[青果API<br/>代理供应商]
    end
    
    %% 任务请求流程
    T1 -->|alloc(beijing)| PM
    T2 -->|alloc(shanghai)| PM
    T3 -->|alloc(shenzhen)| PM
    
    PM --> CP
    CP --> P1
    CP --> P2
    CP --> P3
    
    P1 --> Z1
    P1 --> H1
    P1 --> F1
    
    P2 --> Z2
    P2 --> H2
    P2 --> F2
    
    PM --> QG
    QG --> RG
    QG --> API
    
    PM -.->|redis_lock| L1
    PM -.->|redis_lock| L2
    
    style PM fill:#e3f2fd
    style QG fill:#f3e5f5
    style Z1 fill:#fff3e0
    style H1 fill:#e8f5e9
    style F1 fill:#ffebee
```

## 代理分配流程

```mermaid
sequenceDiagram
    participant Task
    participant ProxyManager
    participant IpProxyPool
    participant Redis
    participant QingguoShort
    participant RegionGateway
    
    Task->>ProxyManager: alloc(city="beijing", max_concurrent=5)
    
    ProxyManager->>ProxyManager: 检查city_pools
    alt 池不存在
        ProxyManager->>ProxyManager: 创建 IpProxyPool(beijing)
    end
    
    ProxyManager->>IpProxyPool: clear_expired()
    IpProxyPool->>Redis: zrangebyscore(过期代理)
    IpProxyPool->>Redis: zremrangebyscore(清理)
    
    ProxyManager->>IpProxyPool: alloc(max_concurrent=5)
    
    loop 遍历未过期代理
        IpProxyPool->>Redis: zrangebyscore(获取有效代理)
        IpProxyPool->>Redis: hincrby(concurrent, +1)
        alt 并发未满
            IpProxyPool-->>ProxyManager: 返回代理
            ProxyManager-->>Task: 返回 IpProxy
        else 并发已满
            IpProxyPool->>Redis: hincrby(concurrent, -1)
        end
    end
    
    alt 池中无可用代理
        ProxyManager->>Redis: 获取分布式锁
        alt 获取锁成功
            ProxyManager->>QingguoShort: get_proxy(city)
            QingguoShort->>RegionGateway: get_district_code_by_pinyin(city)
            RegionGateway-->>QingguoShort: 返回区划代码
            QingguoShort->>外部API: 请求代理
            外部API-->>QingguoShort: 返回代理数据
            QingguoShort-->>ProxyManager: 返回 IpProxy
            ProxyManager->>IpProxyPool: add(new_proxy)
            IpProxyPool->>Redis: zadd(代理池)
            IpProxyPool->>Redis: hset(concurrent, 0)
            IpProxyPool->>Redis: hset(failures, 0)
            ProxyManager->>ProxyManager: 递归 alloc(retry+1)
        else 获取锁失败
            ProxyManager-->>Task: 返回 None
        end
    end
```

## Redis 数据结构详解

```mermaid
graph LR
    subgraph "ZSet: ip_proxy:pool:城市"
        Z[有序集合]
        Z --> M1[Member: {proxy_json}<br/>Score: expired_timestamp]
        Z --> M2[Member: {proxy_json}<br/>Score: expired_timestamp]
        Z --> M3[Member: {proxy_json}<br/>Score: expired_timestamp]
    end
    
    subgraph "Hash: ip_proxy:concurrent:城市"
        H[哈希表]
        H --> C1[Key: proxy_id_1<br/>Value: 3]
        H --> C2[Key: proxy_id_2<br/>Value: 0]
        H --> C3[Key: proxy_id_3<br/>Value: 5]
    end
    
    subgraph "Hash: ip_proxy:failures:城市"
        F[哈希表]
        F --> F1[Key: proxy_id_1<br/>Value: 0]
        F --> F2[Key: proxy_id_2<br/>Value: 2]
        F --> F3[Key: proxy_id_3<br/>Value: 4]
    end
```

## 代理释放流程

```mermaid
flowchart TD
    Start[开始释放]
    
    Start --> Release[release(proxy_id, city, success)]
    
    Release --> CheckPool{城市池存在?}
    CheckPool -->|是| DecConcurrent[减少并发计数]
    CheckPool -->|否| LogWarning[记录警告]
    
    DecConcurrent --> CheckSuccess{使用成功?}
    
    CheckSuccess -->|是| LogSuccess[记录成功]
    CheckSuccess -->|否| RecordFailure[记录失败]
    
    RecordFailure --> IncFailure[失败计数+1]
    IncFailure --> CheckThreshold{失败>=5次?}
    
    CheckThreshold -->|是| RemoveProxy[移除代理]
    CheckThreshold -->|否| End[结束]
    
    RemoveProxy --> DeleteFromZSet[从ZSet删除]
    DeleteFromZSet --> DeleteFromHash[从Hash删除]
    DeleteFromHash --> End
    
    LogSuccess --> End
    LogWarning --> End
    
    style Start fill:#e1f5fe
    style End fill:#f5f5f5
    style RemoveProxy fill:#ffcdd2
```

## 分布式锁机制

```mermaid
graph TB
    subgraph "redis_lock 上下文管理器"
        Lock[redis_lock]
        
        subgraph "锁获取流程"
            TryLock[尝试 SET NX EX]
            Wait[等待 10ms]
            CheckTimeout{超时?}
        end
        
        subgraph "锁释放流程"
            Delete[DELETE lock_key]
        end
    end
    
    subgraph "锁参数"
        P1[lock_key: ip_proxy:fetch_lock:城市]
        P2[timeout: 10秒 锁有效期]
        P3[wait_timeout: 3秒 等待超时]
    end
    
    Lock --> TryLock
    TryLock -->|失败| Wait
    Wait --> CheckTimeout
    CheckTimeout -->|否| TryLock
    CheckTimeout -->|是| ReturnFalse[返回 acquired=False]
    TryLock -->|成功| ReturnTrue[返回 acquired=True]
    
    ReturnTrue --> YieldControl[yield 控制权]
    YieldControl --> Delete
    
    style Lock fill:#e3f2fd
    style ReturnTrue fill:#c8e6c9
    style ReturnFalse fill:#ffcdd2
```

## 类关系图

```mermaid
classDiagram
    class ProxyManager {
        -RedisManager redis_manager
        -Dict[str, IpProxyPool] city_pools
        -QingguoShortResources qg_short_provider
        +alloc(city, max_concurrent) IpProxy
        +release(proxy_id, city, success) None
    }
    
    class IpProxyPool {
        -Redis redis
        -str city
        -str _key
        -str _concurrent_key
        -str _failure_key
        +add(ip: IpProxy) None
        +alloc(max_concurrent) IpProxy
        +release(proxy_id) None
        +record_failure(proxy_id, max_failures) bool
        +remove(proxy_id) bool
        +clear_expired(now) None
    }
    
    class IpProxy {
        +str identify
        +str server
        +str username
        +str password
        +str city
        +datetime expired_at
    }
    
    class QingguoShortResources {
        -QGShortConfig config
        +get_proxy(city) IpProxy
        +refresh_proxies(city) List[IpProxy]
        +get_proxy_type() ProxyType
    }
    
    class RedisManager {
        +Redis client
    }
    
    class RegionGateway {
        +get_district_code_by_pinyin(city) str
    }
    
    ProxyManager "1" --> "*" IpProxyPool : manages
    ProxyManager "1" --> "1" QingguoShortResources : uses
    ProxyManager "1" --> "1" RedisManager : uses
    IpProxyPool "1" --> "*" IpProxy : stores
    IpProxyPool "1" --> "1" Redis : uses
    QingguoShortResources "1" --> "1" RegionGateway : uses
    QingguoShortResources "1" --> "*" IpProxy : creates
```

## 关键特性

### 1. 并发复用机制
- 每个代理支持 `max_concurrent` 个并发连接（默认5个）
- 使用 Redis HINCRBY 原子操作管理并发计数
- 超过并发限制自动回滚计数

### 2. 自动过期清理
- 使用 Redis ZSet 按过期时间排序存储
- 每次分配前清理过期代理（30秒缓冲）
- 清理时同步删除并发和失败计数

### 3. 失败处理策略
- 记录每个代理的失败次数
- 失败5次自动移除代理
- 成功使用不影响失败计数

### 4. 分布式锁保护
- 获取新代理时使用分布式锁
- 防止并发请求重复获取代理
- 锁超时自动释放（10秒）

### 5. 动态城市池
- 按需创建城市代理池
- 每个城市独立管理
- 支持不同城市不同配置

## 配置参数

```python
# 并发控制
MAX_CONCURRENT = 5          # 每个代理最大并发数

# 失败处理
MAX_FAILURES = 5            # 最大失败次数

# 锁配置
LOCK_TIMEOUT = 10           # 分布式锁超时（秒）
LOCK_WAIT_TIMEOUT = 3       # 等待锁超时（秒）

# 过期缓冲
EXPIRE_BUFFER = 30          # 过期时间缓冲（秒）
```

## 监控点

1. **代理池状态**
   - 各城市可用代理数
   - 代理并发使用率
   - 代理失败率

2. **性能指标**
   - 代理分配耗时
   - 锁等待时间
   - API请求成功率

3. **异常监控**
   - 获取新代理失败
   - 锁获取超时
   - Redis操作异常