# Project Structure Steering Document

## Directory Organization

### `/src` - Main Source Code
Follow the modular monolithic architecture with clear layer separation:

```
src/
├── interfaces/        # Entry points (HTTP APIs, Consumers, Schedulers)
├── applications/      # Application services layer (business orchestration)
├── domains/          # Domain layer (business logic and entities)
├── repositories/     # Data access layer (interfaces + implementations)
├── databases/        # Database models and migrations
└── infrastructures/  # Technical infrastructure and utilities
```

### Layer Responsibilities

#### `interfaces/` - Application Entry Points
- **http/**: REST APIs (baseapi, openapi, growth_hacker)
- **consumers/**: Message queue consumers
- **schedulers/**: Scheduled tasks
- Never directly access domains or databases

#### `applications/` - Business Orchestration
- Coordinate between multiple domain services
- Handle transaction boundaries
- Implement use case logic
- Examples: `passport/`, `benefits/`, `openapi/`

#### `domains/` - Core Business Logic
- Pure business logic without infrastructure dependencies
- Entity definitions and business rules
- Domain services for complex operations
- Examples: `passport/entities/`, `benefits/services/`

#### `repositories/` - Data Access
- Abstract data persistence
- Implement repository pattern
- Handle database queries and updates
- Examples: `passport/users.py`, `benefits/product.py`

#### `infrastructures/` - Technical Components
- Framework configurations
- External service integrations
- Cross-cutting concerns (logging, error handling)
- Examples: `gateways/`, `browsers/`, `fastapi/`

## File Naming Patterns

### Python Files
- Use snake_case: `user_service.py`, `product_order.py`
- Service files: `*_service.py`
- Repository files: match domain names
- Entity files: singular names in `entities/`

### API Routers
- Group by feature: `passport/`, `benefits/`, `delivery/`
- Router files: feature-specific names
- Schema files: `schemas.py` in each module

## Key File Locations

### Configuration
- `/env.toml` - Environment configuration
- `/pyproject.toml` - Project dependencies and tools
- `/docker-compose.yml` - Service orchestration

### Entry Points
- `/deploys/*/main.py` - Service entry points
- `/src/interfaces/http/*/main.py` - API applications

### Shared Components
- `/src/infrastructures/containers.py` - DI container setup
- `/src/infrastructures/errors.py` - Custom exceptions
- `/src/infrastructures/fastapi/application.py` - FastAPI setup

### Database
- `/src/databases/models/` - ORM models
- `/src/databases/migrations/` - SQL migrations

## Import Patterns

Always use absolute imports from `src`:
```python
from src.domains.passport.entities import UserEntity
from src.repositories.passport import UserRepository
from src.infrastructures.errors import BusinessError
```

Never use relative imports across modules.