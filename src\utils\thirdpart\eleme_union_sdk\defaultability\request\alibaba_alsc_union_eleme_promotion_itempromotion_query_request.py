from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionItempromotionQueryRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.itempromotion.query"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionItempromotionQueryItemPromotionQueryRequest:
    def __init__(
        self,
        session_id: str = None,
        biz_type: str = None,
        pid: str = None,
        city_code: str = None,
        sort_type: str = None,
        page_number: int = None,
        page_size: int = None,
        sid: str = None,
        search_content: str = None,
        item_type: str = None,
    ):
        """
        会话ID（查询第一页为空，从第二页开始赋值，取值来自第一页返回结果）
        """
        self.session_id = session_id
        """
            商品类型（hoard_coupon-囤券券）
        """
        self.biz_type = biz_type
        """
            推广位
        """
        self.pid = pid
        """
            城市编码（国标）
        """
        self.city_code = city_code
        """
            排序（normal-佣金倒序，commission_desc-佣金倒序，commission_rate_desc-佣金比例倒序，sell_price_asc-价格正序，sell_price_desc-价格倒序）
        """
        self.sort_type = sort_type
        """
            请求页（从1开始）
        """
        self.page_number = page_number
        """
            每页数（1~20）
        """
        self.page_size = page_size
        """
            会员ID（需要联系运营申请）
        """
        self.sid = sid
        """
            品牌搜索
        """
        self.search_content = search_content
        """
            商品类型，多值'|'分隔，默认全部（1-商品券；2-代金券）
        """
        self.item_type = item_type
