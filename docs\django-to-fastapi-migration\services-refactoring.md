# Services层重构指南

## 🎯 重构策略

**基于新架构完全重写，充分利用现有DDD架构优势，不兼容老逻辑**

## 📋 重构范围

### 需要完善的Services

| 服务 | 位置 | 当前状态 | 需要补充的方法 |
|------|------|----------|----------------|
| `BenefitsProductService` | `domains/benefits/services/product.py` | ✅ 基础架构已存在 | 3个API专用方法 |

## 🔧 核心方法实现

### 1. 权益充值方法

```python
async def charge_product(self, product_code: str, payload: dict, channel: str) -> dict:
    """
    权益充值 - 基于现有架构重写
    集成策略工厂、消息队列、Repository模式
    """
    # Step 1: 获取产品信息
    product = await self.product_repo.get_by_code(product_code)
    if not product:
        raise ProductNotFoundError(f"Product {product_code} not found")
    
    # Step 2: Channel转Customer (新老系统映射)
    customer = await self._get_customer_by_channel(channel)
    
    # Step 3: 创建充值订单
    order_data = {
        "product_id": product.id,
        "customer_id": customer.id,
        "out_order_id": payload["out_order_id"],
        "account": payload["account"],
        "amount": product.price,
        "status": "pending"
    }
    order = await self.product_order_repo.create(order_data)
    
    # Step 4: 调用充值策略 (复用现有策略工厂)
    strategy = self._get_charge_strategy(product.supplier_type)
    charge_result = await strategy.charge(order)
    
    # Step 5: 更新订单状态
    await self.product_order_repo.update(order.id, {
        "status": charge_result.status,
        "third_order_id": charge_result.third_order_id
    })
    
    # Step 6: 发送消息队列 (复用现有消息机制)
    await self.producer.publish("sku_charge", {
        "order_id": order.id,
        "product_code": product_code
    })
    
    return {
        "order_id": str(order.id),
        "status": charge_result.status,
        "amount": order.amount,
        "message": charge_result.message
    }
```

### 2. 产品详情方法

```python
async def get_product_by_code(self, product_code: str, channel: str) -> dict:
    """
    获取产品详情 - 新实现
    包含权限控制和数据格式转换
    """
    # Step 1: 获取产品
    product = await self.product_repo.get_by_code(product_code)
    if not product:
        raise ProductNotFoundError(f"Product {product_code} not found")
    
    # Step 2: 检查渠道权限
    customer = await self._get_customer_by_channel(channel)
    has_permission = await self._check_product_permission(customer, product)
    if not has_permission:
        raise PermissionDeniedError("Channel has no permission for this product")
    
    # Step 3: 返回兼容格式
    return {
        "product_code": product.code,
        "product_name": product.name,
        "price": product.price,
        "description": product.description,
        "status": product.status,
        "supplier": product.supplier_type
    }
```

### 3. 回调处理方法

```python
async def handle_shinesun_callback(self, params: dict):
    """
    处理向上网络回调 - 新实现
    基于新的消息队列和异步处理机制
    """
    order_id = params["order_id"]
    status = params["status"]
    
    # Step 1: 获取订单
    order = await self.product_order_repo.get_by_id(order_id)
    if not order:
        raise OrderNotFoundError(f"Order {order_id} not found")
    
    # Step 2: 更新订单状态
    new_status = "success" if status == 1 else "failed"
    await self.product_order_repo.update(order.id, {
        "status": new_status,
        "callback_time": datetime.now(),
        "callback_params": params
    })
    
    # Step 3: 发送后续处理消息
    if new_status == "success":
        await self.producer.publish("order_success", {
            "order_id": order.id,
            "customer_id": order.customer_id
        })
    else:
        await self.producer.publish("order_failed", {
            "order_id": order.id,
            "reason": params.get("message", "Unknown error")
        })
```

## 🔧 辅助方法实现

### Channel到Customer映射

```python
async def _get_customer_by_channel(self, channel: str):
    """将老的Channel概念转换为新的Customer"""
    customer_repo = container.customer_repo()
    customer = await customer_repo.get_by_channel_code(channel)
    
    if not customer:
        # 如果没有对应的customer，创建一个默认的
        customer = await customer_repo.create({
            "channel_code": channel,
            "name": f"Channel_{channel}",
            "status": "active",
            "created_from": "legacy_migration"
        })
    return customer
```

### 权限检查

```python
async def _check_product_permission(self, customer, product) -> bool:
    """检查客户是否有权限访问该产品"""
    benefits_repo = container.benefits_repo()
    permission = await benefits_repo.check_customer_product_permission(
        customer.id, product.id
    )
    return permission is not None
```

### 策略工厂集成

```python
def _get_charge_strategy(self, supplier_type: str):
    """复用现有的策略工厂"""
    from domains.benefits.services.charge_strategy import StrategyFactory
    return StrategyFactory.get_strategy(supplier_type)
```

## 📦 SDK整合计划

### 当前SDK状况
```
✅ domains/benefits/utils/biforst_sdk/     # 饿了么权益SDK
✅ domains/benefits/utils/shinesun_sdk/    # 向上网络SDK  
❌ apps/benefits/utils/taopiaopiao_sdk/    # 需要迁移
❌ utils/thirdpart/eleme_union_sdk/        # 需要迁移
```

### SDK迁移步骤

#### 1. 迁移TaoPiaoPiao SDK
```bash
# 从 apps/benefits/utils/taopiaopiao_sdk/ 
# 迁移到 domains/benefits/utils/taopiaopiao_sdk/
cp -r apps/benefits/utils/taopiaopiao_sdk/ domains/benefits/utils/
```

#### 2. 迁移ElemeUnion SDK
```bash
# 从 utils/thirdpart/eleme_union_sdk/
# 迁移到 domains/benefits/utils/eleme_union_sdk/
cp -r utils/thirdpart/eleme_union_sdk/ domains/benefits/utils/
```

#### 3. 统一SDK工厂
```python
# domains/benefits/utils/sdk_factory.py
class SDKFactory:
    @staticmethod
    def get_sdk(supplier_type: str):
        if supplier_type == "biforst":
            return BiforstBenefitsClient()
        elif supplier_type == "shinesun":
            return ShinesunClient()
        elif supplier_type == "taopiaopiao":
            return TaoPiaoPiaoClient()
        elif supplier_type == "eleme_union":
            return ElemeUnionClient()
        else:
            raise UnsupportedSupplierError(f"Unsupported supplier: {supplier_type}")
```

## 🎯 充值策略扩展

### 新增TaoPiaoPiao策略

```python
# domains/benefits/services/charge_strategy/implements/taopiaopiao_strategy.py

class TaoPiaoPiaoStrategy(ChargeStrategy):
    """淘票票充值策略 - 基于新架构实现"""
    
    def __init__(self):
        self.sdk = SDKFactory.get_sdk("taopiaopiao")
    
    async def charge(self, order: ProductOrder) -> ChargeResult:
        """淘票票充值逻辑 - 完全重写"""
        try:
            result = await self.sdk.charge_account(
                account=order.account,
                amount=order.amount,
                out_order_id=order.out_order_id
            )
            
            return ChargeResult(
                status="success" if result.success else "failed",
                third_order_id=result.order_id,
                message=result.message
            )
        except Exception as e:
            return ChargeResult(
                status="failed",
                message=str(e)
            )
```

### 策略工厂更新

```python
# domains/benefits/services/charge_strategy/strategy_factor.py

class StrategyFactory:
    @staticmethod
    def get_strategy(supplier_type: str) -> ChargeStrategy:
        strategies = {
            "eleme": ElemeStrategy(),
            "eleme_city": ElemeCityStrategy(),
            "eleme_union": ElemeUnionStrategy(),
            "shinesun": ShinesunStrategy(),
            "taopiaopiao": TaoPiaoPiaoStrategy(),  # 新增
        }
        
        strategy = strategies.get(supplier_type)
        if not strategy:
            raise UnsupportedSupplierError(f"Unsupported supplier: {supplier_type}")
        
        return strategy
```

## 🛠️ 数据兼容处理

### 价格单位转换

```python
class PriceConverter:
    """处理新老系统的价格单位差异"""
    
    @staticmethod
    def convert_from_old_system(old_price: int) -> int:
        """老系统价格转新系统价格"""
        return old_price  # 根据实际业务调整
    
    @staticmethod
    def convert_to_old_format(new_price: int) -> int:
        """新系统价格转老系统格式（用于API响应）"""
        return new_price
```

### 状态码映射

```python
class StatusMapper:
    """状态码新老系统映射"""
    
    OLD_TO_NEW = {
        "pending": "pending",
        "success": "success", 
        "failed": "failed",
        "processing": "pending"
    }
    
    NEW_TO_OLD = {v: k for k, v in OLD_TO_NEW.items()}
```

## ⏱️ 实施时间表

### Day 1-2: 核心方法实现
- [x] `charge_product()` 方法完整实现
- [x] 集成现有策略工厂和消息队列
- [x] 基础单元测试

### Day 3: 查询和权限
- [x] `get_product_by_code()` 方法实现
- [x] Channel到Customer映射逻辑
- [x] 权限检查机制

### Day 4: 回调和SDK
- [x] `handle_shinesun_callback()` 方法实现
- [x] SDK迁移整合
- [x] 统一SDK工厂

### Day 5: 完善和测试
- [x] 数据兼容处理
- [x] 异常处理完善
- [x] 完整单元测试覆盖

## ✅ 质量保证

### 单元测试覆盖

```python
# tests/unit/domains/benefits/services/test_product_service.py

class TestBenefitsProductService:
    async def test_charge_product_success(self):
        """测试充值成功场景"""
        pass
        
    async def test_charge_product_product_not_found(self):
        """测试产品不存在场景"""
        pass
        
    async def test_get_product_by_code_success(self):
        """测试获取产品详情成功"""
        pass
        
    async def test_handle_shinesun_callback_success(self):
        """测试回调处理成功"""
        pass
```

### 代码质量标准

- **类型注解**: 100%覆盖所有方法参数和返回值
- **文档字符串**: 所有public方法必须有完整docstring
- **异常处理**: 统一的异常类型和错误消息
- **日志记录**: 关键操作步骤的结构化日志

### 性能优化

- **数据库查询**: 使用索引，避免N+1查询
- **缓存策略**: 产品信息缓存，减少数据库访问
- **异步处理**: 充分利用异步IO提升性能