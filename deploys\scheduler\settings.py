# encoding: utf-8
# deploys/scheduler/settings.py
# created: 2025-08-03 16:51:04

from pathlib import Path

from pydantic_settings import SettingsConfigDict

from src.infrastructures.settings import BaseServiceSettings


class SchedulerSettings(BaseServiceSettings):
    """Scheduler 服务配置"""
    
    # Scheduler 服务不需要 FastAPI 和 SMS 配置
    # 所有需要的配置已经在 BaseServiceSettings 中定义
    
    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",  # 方便嵌套字段注入
    )


config = SchedulerSettings()
