# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/infrastructures/openapi.py
# created: 2024-12-11 03:21:10
# updated: 2025-08-31 12:00:00

from abc import ABC, abstractmethod
from typing import Optional

import aiohttp
from loguru import logger


class OpenApiClientBase:
    def __init__(self, base_url: str):
        self.base_url = base_url

    async def _async_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[dict] = None,
        payload: Optional[dict | list] = None,
        headers: Optional[dict] = None,
    ):
        url = f"{self.base_url}/{endpoint}"
        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
                logger.debug(f"Requesting {method} {url} with payload: {payload} and headers: {headers}")
                async with session.request(method, url, params=params, json=payload, headers=headers) as response:
                    response_json = await response.json()
                    logger.info(f"Response status: {response.status}, response: {response_json}")
                    return response_json
        except aiohttp.ClientError as e:
            logger.error(f"Request {method} {url} failed with error: {e}")
            raise
