# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/internal/authorization.py
# created: 2025-05-26 14:31:44
# updated: 2025-05-26 14:42:35

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from src.domains.passport.entities import AppEntity
from src.interfaces.http.baseapi import Container

if TYPE_CHECKING:
    from src.repositories.passport.apps import AppRepository

bearer_scheme = HTTPBearer()  # HTTP Bearer 安全方案


@inject
async def verify_internal_app(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme),
    passport_app_repo: "AppRepository" = Depends(Provide[Container.repositories.passport_app_repository]),
) -> AppEntity:
    """
    内部应用验证器（必需验证）
    使用HTTP Bearer验证方式, token为passport app的app_secret
    验证成功后返回app对象, 并自动验证路径中的app_id
    """
    if not credentials or not credentials.credentials:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Bearer token is required")

    app_secret = credentials.credentials
    app = await passport_app_repo.get_by_app_secret(app_secret)
    if not app:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid app secret")

    app_entity = await AppEntity.from_model(app)

    # 自动验证路径中的app_id（如果存在）
    path_params = request.path_params
    if "app_id" in path_params and app_entity.app_id != path_params["app_id"]:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid app id")

    return app_entity
