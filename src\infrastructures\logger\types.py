# encoding: utf-8
# src/infrastructures/logger/types.py
# created: 2025-08-15 11:00:00

"""日志模块类型定义"""

from datetime import datetime
from typing import Any, Callable, Dict, Optional, Protocol, Union

# Loguru 实际使用的是 dict-like 对象，不是严格的类
LogRecord = Dict[str, Any]


class LogMessage(Protocol):
    """Loguru Message 对象协议"""

    record: LogRecord
    formatted: str


# 输出目标类型
SinkType = Union[str, Callable[[LogMessage], None]]

# 过滤器类型
FilterType = Optional[Union[str, Callable[[LogRecord], bool], Dict[str, Any]]]

# 格式化器类型
FormatterType = Union[str, Callable[[LogRecord], str]]
