# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/charge_strategy/implements/eleme_city_strategy.py
# created: 2025-04-06 20:02:33
# updated: 2025-05-05 00:15:55

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from dependency_injector.wiring import Provide, inject
from loguru import logger
from tortoise.transactions import atomic

from src.containers import Container
from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as ChargeRecordStatus
from src.databases.models.benefits import ChargeFuncEnum
from src.domains.benefits.dto import ChargeRecordDetailSchema
from src.infrastructures.errors import SKUNotSupportRefundError

from ..strategy import ChargeStrategy
from ..strategy_factor import register_strategy

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsSkuChargeRecord
    from src.infrastructures.gateways.bifrost import BifrostGateway


@register_strategy(ChargeFuncEnum.ELEME_CITY)
class ElemeCityChargeStrategy(ChargeStrategy):
    """饿了么城市卡券充值策略"""

    @atomic()
    @inject
    async def charge(
        self,
        record: "BenefitsSkuChargeRecord",
        bifrost_client: "BifrostGateway" = Provide[Container.infrastructures.bifrost_gateway],
    ) -> Optional["BenefitsSkuChargeRecord"]:
        """充值"""
        show_name = "福利红包" if record.sku.detail is None else record.sku.detail.get("show_name", "福利红包")
        ori_status = record.status
        detail = ChargeRecordDetailSchema.model_validate(record.detail)
        request = {
            "name": show_name,
            "mobile": record.account,
            "welfare_case_no": record.sku.third_part_code,
        }
        detail.deliver_request = {
            **request,
            "timestamp": datetime.now().isoformat(),
        }

        # 执行充值操作
        try:
            result = await bifrost_client.apply_city_welfare(**request)
            logger.info(f"申请饿了么城市红包成功[{record.id}], 饿了么城市红包结果: {result.model_dump()}")
            detail.deliver_response = {
                **result.model_dump(),
                "timestamp": datetime.now().isoformat(),
            }
            record.charged_at = datetime.now()  # type: ignore

            # complete record supplier_order_id from result,
            # rightActivityId? rightInstanceId? coupon.eleCouponId?
            # 423076966448103862, 423119940995711414
            record.supplier_order_id = (
                result.response.get("data", {}).get("result", {}).get("extDetails", {}).get("coupon.eleCouponId")
            )
            record.status = ChargeRecordStatus.SUCCESS if result.success else ChargeRecordStatus.FAILED
        except Exception as e:
            logger.error(f"申请饿了么城市红包失败[{record.id}], 异常错误: {e}")
            detail.deliver_response = {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }
            record.status = ChargeRecordStatus.FAILED
        finally:
            logger.info(
                "请求饿了么城市红包完成, 变更 charge_record[{record_id}] 的 status: {ori_status} --> {to_status}",
                record_id=record.id,
                ori_status=ori_status,
                to_status=record.status,
            )
            record.detail = detail.model_dump()
            await record.save()

        return record

    async def check_charge_status(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """检查充值状态"""
        return record

    async def refund(self, record: "BenefitsSkuChargeRecord") -> Optional["BenefitsSkuChargeRecord"]:
        """退款"""
        raise SKUNotSupportRefundError
