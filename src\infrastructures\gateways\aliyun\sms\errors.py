# encoding: utf-8
# src/infrastructures/gateways/aliyun/sms/errors.py
# created: 2025-08-01 08:04:27

from src.infrastructures.errors import BusinessError


class AliyunSmsError(BusinessError):
    """阿里云短信服务基础错误"""

    code = 5001
    message = "阿里云短信服务错误"


class AliyunSmsSendError(AliyunSmsError):
    """发送短信失败"""

    code = 5002
    message = "发送短信失败"

    def __init__(self, extra_info: str = ""):
        super().__init__(message=f"发送短信失败 {extra_info}", code=self.code)


class AliyunSmsInvalidPhoneError(AliyunSmsError):
    """无效的手机号"""

    code = 5003
    message = "无效的手机号"

    def __init__(self, extra_info: str = ""):
        super().__init__(message=f"无效的手机号 {extra_info}", code=self.code)


class AliyunSmsRateLimitError(AliyunSmsError):
    """短信发送频率限制"""

    code = 5004
    message = "短信发送过于频繁，请稍后再试"

    def __init__(self, extra_info: str = ""):
        super().__init__(message=f"短信发送过于频繁，请稍后再试 {extra_info}", code=self.code)
