# encoding: utf-8
# src/infrastructures/logger/core.py
# created: 2025-08-14 14:35:00

from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union

from loguru import logger as _logger

from .context import LogContext
from .settings import LoggerSettings

if TYPE_CHECKING:
    from loguru import Logger as LoguruLogger


class Logger:
    """核心日志器封装

    基于 loguru 提供统一的日志接口
    支持多输出目标、上下文管理和标准库拦截
    """

    def __init__(self, config: Union[LoggerSettings, dict]):
        """初始化日志器

        Args:
            config: 日志配置
        """
        if isinstance(config, dict):
            config = LoggerSettings(**config)

        self.config = config
        self._logger = _logger
        self._sink_ids: List[int] = []
        self._setup()

    def _setup(self) -> None:
        """配置日志器"""
        # 清理默认处理器
        self._logger.remove()

        # 设置应用名称为默认上下文
        LogContext.set("app", self.config.app_name, default=True)

        # 设置其他默认上下文字段
        for key, value in self.config.context_fields.items():
            LogContext.set(key, value, default=True)

        # 配置日志补丁函数
        self._logger.configure(patcher=self._patch_record)  # type: ignore

        # 添加输出目标
        self._setup_sinks()

        # 拦截标准库日志
        if self.config.intercept_stdlib:
            self._intercept_stdlib()

    def _patch_record(self, record: Dict[str, Any]) -> None:
        """为日志记录添加上下文信息

        Args:
            record: loguru 日志记录
        """
        if "extra" not in record:
            record["extra"] = {}

        # 合并上下文信息
        context = LogContext.get_all()
        record["extra"].update(context)

    def _setup_sinks(self) -> None:
        """配置输出目标"""
        from .settings import FileSinkConfig
        from .sinks import create_sink

        for sink_config in self.config.sinks:
            # 创建输出目标实例
            sink = create_sink(sink_config, self.config.app_name)

            # 构建 loguru.add() 的参数
            add_kwargs = {
                "sink": sink,
                "level": sink_config.level,
                "filter": sink_config.filter,
                "backtrace": sink_config.backtrace,
                "diagnose": sink_config.diagnose,
                "enqueue": sink_config.enqueue,
            }

            # 对于文件输出，添加额外的配置参数
            if isinstance(sink_config, FileSinkConfig):
                add_kwargs.update(
                    {
                        "rotation": sink_config.rotation,  # type: ignore
                        "retention": sink_config.retention,  # type: ignore
                        "compression": sink_config.compression,
                        "encoding": sink_config.encoding,
                        "mode": sink_config.mode,
                    }
                )

            # 添加到 loguru
            handler_id = self._logger.add(**add_kwargs)  # type: ignore

            self._sink_ids.append(handler_id)

    def _intercept_stdlib(self) -> None:
        """拦截标准库日志"""
        import logging

        from .utils import StdlibInterceptor

        # 配置标准库日志处理器
        logging.basicConfig(handlers=[StdlibInterceptor()], level=0, force=True)

        # 特定模块的日志拦截
        for logger_name in ["uvicorn", "uvicorn.error", "uvicorn.access"]:
            logger = logging.getLogger(logger_name)
            logger.handlers = [StdlibInterceptor()]
            logger.propagate = False

    def bind(self, **kwargs) -> "LoguruLogger":
        """绑定额外的上下文信息

        Args:
            **kwargs: 要绑定的上下文键值对

        Returns:
            绑定了上下文的日志器
        """
        return self._logger.bind(**kwargs)

    def unbind(self, *keys) -> "LoguruLogger":
        """解绑上下文信息

        Args:
            *keys: 要解绑的键

        Returns:
            解绑了上下文的日志器
        """
        return self._logger.unbind(*keys)  # type: ignore

    def remove(self, handler_id: Optional[int] = None) -> None:
        """移除输出目标

        Args:
            handler_id: 处理器ID，如果为None则移除所有
        """
        if handler_id is None:
            # 移除所有处理器
            for sink_id in self._sink_ids:
                self._logger.remove(sink_id)
            self._sink_ids.clear()
        else:
            # 移除指定处理器
            self._logger.remove(handler_id)
            if handler_id in self._sink_ids:
                self._sink_ids.remove(handler_id)

    def complete(self) -> None:
        """完成所有待处理的日志"""
        self._logger.complete()

    # 代理 loguru 的日志方法
    def trace(self, message: str, *args, **kwargs) -> None:
        """TRACE 级别日志"""
        self._logger.trace(message, *args, **kwargs)

    def debug(self, message: str, *args, **kwargs) -> None:
        """DEBUG 级别日志"""
        self._logger.debug(message, *args, **kwargs)

    def info(self, message: str, *args, **kwargs) -> None:
        """INFO 级别日志"""
        self._logger.info(message, *args, **kwargs)

    def success(self, message: str, *args, **kwargs) -> None:
        """SUCCESS 级别日志"""
        self._logger.success(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs) -> None:
        """WARNING 级别日志"""
        self._logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs) -> None:
        """ERROR 级别日志"""
        self._logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs) -> None:
        """CRITICAL 级别日志"""
        self._logger.critical(message, *args, **kwargs)

    def exception(self, message: str, *args, **kwargs) -> None:
        """异常日志"""
        self._logger.exception(message, *args, **kwargs)

    def opt(self, **options) -> "LoguruLogger":
        """配置日志选项

        Args:
            **options: 日志选项

        Returns:
            配置了选项的日志器
        """
        return self._logger.opt(**options)

    def __getattr__(self, name: str) -> object:
        """代理其他 loguru 方法"""
        return getattr(self._logger, name)
