#!/usr/bin/env python3
# encoding: utf-8
# scripts/backfill_address_gb_info.py
# created: 2025-01-14

"""
历史数据清洗脚本：批量更新 ElemeAddress 表中的地址信息为国标标准

设计思路：
1. 先读取所有国标信息，建立 alsc_district_id -> 国标信息的映射
2. 根据国标信息中的id，去匹配数据库中 alsc_district_id 的数据
3. 批量更新，减少数据库操作次数
4. 使用项目中的 Container 管理依赖

优化策略：
1. 建立内存映射，避免重复查询
2. 批量更新，减少数据库操作
3. 记录处理进度，支持断点续传
4. 使用事务保证数据一致性
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dependency_injector.wiring import Provide, inject
from tortoise.transactions import atomic

from src.databases.models.delivery import ElemeAddress
from src.infrastructures.gateways.regions.gateway import region_gateway
from src.infrastructures.gateways.regions.schemas import RegionInfo
from src.interfaces.schedulers import Container

# 配置日志（控制台输出，禁用文件写入；仅警告级别以上）
logging.basicConfig(
    level=logging.WARNING,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class AddressBackfillProcessor:
    """地址信息回填处理器"""

    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.processed_count = 0
        self.updated_count = 0
        self.error_count = 0
        self.start_time = None

        # 国标信息映射缓存
        self.gb_info_map: Dict[int, RegionInfo] = {}

        # 错误统计
        self.error_details: Dict[str, int] = {}

    def build_gb_info_map(self):
        """构建国标信息映射"""
        logger.info("开始构建国标信息映射...")

        # 从 RegionGateway 获取所有区县信息
        district_count = 0
        for (province_id, city_id, district_id), district_data in region_gateway._district_map.items():
            alsc_district_id = district_data.get("id")
            if alsc_district_id:
                # 获取对应的城市和省份信息
                city_key = (province_id, city_id)
                city_data = region_gateway._city_map.get(city_key)
                province_data = region_gateway._province_map.get(province_id, {})

                # 构建 RegionInfo 对象
                gb_info = RegionInfo(
                    province_name=province_data.get("name", ""),
                    city_name=city_data.get("name", "") if city_data else "",
                    district_name=district_data.get("name", ""),
                    district_code=str(district_data.get("districtCode", "")),
                    province_full_name=province_data.get("fullName", ""),
                    city_full_name=city_data.get("fullName", "") if city_data else "",
                    district_full_name=district_data.get("fullName", ""),
                    latitude=district_data.get("latitude"),
                    longitude=district_data.get("longitude"),
                    found=True,
                )

                self.gb_info_map[alsc_district_id] = gb_info
                district_count += 1

        logger.info(f"国标信息映射构建完成，共 {district_count} 个区县")
        logger.info(f"映射示例: {list(self.gb_info_map.items())[:3]}")

    async def get_addresses_by_alsc_district_ids(self, alsc_district_ids: List[int]) -> List[ElemeAddress]:
        """根据 alsc_district_id 列表获取地址记录"""
        addresses = await ElemeAddress.filter(alsc_district_id__in=alsc_district_ids).all()
        return addresses

    async def get_all_alsc_district_ids(self) -> List[int]:
        """获取数据库中所有有效的 alsc_district_id（使用DISTINCT优化）"""
        # 使用DISTINCT查询获取所有唯一的 alsc_district_id
        result = await ElemeAddress.filter(alsc_district_id__gt=0).distinct().values("alsc_district_id")
        unique_ids = [row["alsc_district_id"] for row in result]

        logger.info(f"数据库中有效的 alsc_district_id 总数: {len(unique_ids)}")
        return unique_ids

    async def process_batch(self, alsc_district_ids: List[int]) -> Tuple[int, int, int]:
        """处理一批 alsc_district_id（优化版本）"""
        batch_processed = 0
        batch_updated = 0
        batch_errors = 0

        try:
            # 获取这批地址记录
            addresses = await self.get_addresses_by_alsc_district_ids(alsc_district_ids)
            batch_processed = len(addresses)

            if not addresses:
                return batch_processed, batch_updated, batch_errors

            # 按 alsc_district_id 分组地址
            address_groups: Dict[int, List[ElemeAddress]] = {}
            for address in addresses:
                district_key = address.alsc_district_id
                if district_key not in address_groups:
                    address_groups[district_key] = []
                address_groups[district_key].append(address)

            # 批量更新每个 district_id 对应的地址（优化：只处理有国标信息的ID）
            for district_id, address_list in address_groups.items():
                gb_info = self.gb_info_map.get(district_id)
                if not gb_info:
                    logger.warning(f"未找到 alsc_district_id={district_id} 的国标信息，跳过 {len(address_list)} 条记录")
                    batch_errors += len(address_list)
                    continue

                # 检查是否需要更新
                needs_update_addresses = []
                for address in address_list:
                    if (
                        address.province_name != gb_info.province_name
                        or address.city_name != gb_info.city_name
                        or address.district_name != gb_info.district_name
                    ):
                        needs_update_addresses.append(address)

                if not needs_update_addresses:
                    logger.debug(f"alsc_district_id={district_id} 的 {len(address_list)} 条记录无需更新")
                    continue

                # 批量更新
                try:
                    await self.bulk_update_addresses(needs_update_addresses, gb_info)
                    batch_updated += len(needs_update_addresses)

                    logger.info(
                        f"✓ 批量更新 alsc_district_id={district_id}: "
                        f"{len(needs_update_addresses)} 条记录, "
                        f"{gb_info.province_name}{gb_info.city_name}{gb_info.district_name}"
                    )

                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    error_key = f"{error_type}: {error_msg[:100]}"

                    logger.error(f"✗ 批量更新 alsc_district_id={district_id} 时发生错误: {e}")
                    logger.error(f"   错误详情: {error_type}: {error_msg}")
                    logger.error(f"   影响记录数: {len(needs_update_addresses)}")

                    # 统计错误类型
                    self.error_details[error_key] = self.error_details.get(error_key, 0) + len(needs_update_addresses)

                    batch_errors += len(needs_update_addresses)

        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            error_key = f"{error_type}: {error_msg[:100]}"

            logger.error(f"✗ 处理批次时发生错误: {e}")
            logger.error(f"   错误详情: {error_type}: {error_msg}")
            logger.error(f"   影响记录数: {batch_processed}")

            # 统计错误类型
            self.error_details[error_key] = self.error_details.get(error_key, 0) + batch_processed

            batch_errors += batch_processed

        return batch_processed, batch_updated, batch_errors

    @atomic()
    async def bulk_update_addresses(self, addresses: List[ElemeAddress], gb_info):
        """批量更新地址信息"""
        for address in addresses:
            address.province_name = gb_info.province_name
            address.city_name = gb_info.city_name
            address.district_name = gb_info.district_name

        # 批量保存
        await ElemeAddress.bulk_update(addresses, fields=["province_name", "city_name", "district_name"])

    async def run(self):
        """运行数据清洗"""
        self.start_time = time.time()
        logger.info("开始数据清洗...")

        try:
            # 1. 构建国标信息映射
            self.build_gb_info_map()

            # 2. 获取数据库中所有有效的 alsc_district_id
            all_alsc_district_ids = await self.get_all_alsc_district_ids()
            if not all_alsc_district_ids:
                logger.info("没有需要处理的记录")
                return

            # 3. 过滤出有对应国标信息的 alsc_district_id
            valid_alsc_district_ids = [alsc_id for alsc_id in all_alsc_district_ids if alsc_id in self.gb_info_map]
            invalid_alsc_district_ids = [
                alsc_id for alsc_id in all_alsc_district_ids if alsc_id not in self.gb_info_map
            ]

            logger.info(f"有对应国标信息的 alsc_district_id 数量: {len(valid_alsc_district_ids)}")
            logger.info(f"无对应国标信息的 alsc_district_id 数量: {len(invalid_alsc_district_ids)}")

            if invalid_alsc_district_ids:
                logger.warning(f"无对应国标信息的 alsc_district_id 示例: {invalid_alsc_district_ids[:10]}")

            # 4. 分批处理（优化：只处理有国标信息的ID）
            total_batches = (len(valid_alsc_district_ids) + self.batch_size - 1) // self.batch_size

            for i in range(0, len(valid_alsc_district_ids), self.batch_size):
                batch_ids = valid_alsc_district_ids[i : i + self.batch_size]
                batch_num = i // self.batch_size + 1

                logger.info(
                    f"处理批次 {batch_num}/{total_batches}: " f"alsc_district_id 范围 {batch_ids[0]}-{batch_ids[-1]}"
                )

                # 处理这批数据
                processed, updated, errors = await self.process_batch(batch_ids)

                # 更新统计
                self.processed_count += processed
                self.updated_count += updated
                self.error_count += errors

                # 显示进度
                elapsed_time = time.time() - self.start_time
                avg_time_per_batch = elapsed_time / batch_num if batch_num > 0 else 0
                remaining_batches = total_batches - batch_num
                estimated_remaining_time = remaining_batches * avg_time_per_batch

                logger.info(
                    f"📊 进度: {batch_num}/{total_batches} "
                    f"({batch_num/total_batches*100:.1f}%), "
                    f"已处理: {self.processed_count}, 已更新: {self.updated_count}, 错误: {self.error_count}, "
                    f"预计剩余时间: {estimated_remaining_time/60:.1f}分钟"
                )

                # 如果错误率过高，暂停一下
                if errors > 0 and errors / processed > 0.5:
                    logger.warning(
                        f"⚠️  批次 {batch_num} 错误率过高 ({errors}/{processed} = {errors/processed*100:.1f}%)，请检查数据"
                    )

                # 短暂休息，避免过度占用资源
                await asyncio.sleep(0.1)

            # 完成统计（仅打印简要汇总）
            total_time = time.time() - self.start_time
            print("=== 地址国标信息回填 - 汇总 ===")
            print(f"总处理时间: {total_time/60:.1f} 分钟")
            print(f"总记录数: {self.processed_count}")
            print(f"成功更新: {self.updated_count}")
            print(f"失败数量: {self.error_count}")

        except Exception as e:
            logger.error(f"数据清洗过程中发生错误: {e}")
            raise


@inject
async def backfill_address_gb_info():
    """数据清洗主函数"""
    try:
        # 创建处理器并运行
        processor = AddressBackfillProcessor(batch_size=100)  # 每批处理100个 alsc_district_id
        await processor.run()

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


async def main():
    """主函数"""
    from src.interfaces.growth_hacker.container import lifespan

    async with lifespan() as container:
        container.wire(modules=[__name__])
        await backfill_address_gb_info()


if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)

    # 运行主函数
    asyncio.run(main())
