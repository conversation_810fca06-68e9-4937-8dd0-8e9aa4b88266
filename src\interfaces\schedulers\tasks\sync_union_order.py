# encoding: utf-8
# src/interfaces/schedulers/tasks/sync_union_order.py
# created: 2025-01-22 14:25:08

from datetime import datetime, timedelta
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.applications.common.queries.delivery import UnionOrderQueryService
from src.domains.delivery.messages import UnionOrderSyncMessage
from src.interfaces.schedulers import Container

if TYPE_CHECKING:
    from src.infrastructures.rabbitmq import RabbitMQProducer


@inject
async def sync_union_order_by_1d(
    union_order_query_service: UnionOrderQueryService = Provide[
        Container.applications.common_union_order_query_service
    ],
    producer: "RabbitMQProducer" = Provide[Container.infrastructures.rabbitmq_producer],
) -> None:
    """同步昨日饿了么联盟订单, 全局同步"""
    date = datetime.now().date() - timedelta(days=1)  # 昨日
    start_time = datetime.combine(date, datetime.min.time())
    end_time = datetime.combine(date, datetime.max.time())

    # 使用应用层的查询服务
    orders = await union_order_query_service.get_orders_by_time_range(start_time, end_time)

    logger.info(
        "同步昨日联盟订单 {total} orders, {stime} - {etime}",
        total=len(orders),
        stime=start_time.strftime("%Y-%m-%d %H:%M:%S"),
        etime=end_time.strftime("%Y-%m-%d %H:%M:%S"),
    )

    for order in orders:
        message = UnionOrderSyncMessage(payload=order)
        await producer.publish_message(message, exchange_name="delivery.topic")


@inject
async def sync_union_order_by_3m(
    union_order_query_service: UnionOrderQueryService = Provide[
        Container.applications.common_union_order_query_service
    ],
    producer: "RabbitMQProducer" = Provide[Container.infrastructures.rabbitmq_producer],
) -> None:
    """同步3分钟前的饿了么联盟订单, 全局同步"""
    start_time = datetime.now() - timedelta(minutes=3)
    end_time = datetime.now()

    logger.info(f"启动同步3分钟前的联盟订单任务, {start_time} - {end_time}")

    # 使用应用层的查询服务
    orders = await union_order_query_service.get_orders_by_time_range(start_time, end_time)

    logger.info(
        "同步3分钟前的联盟订单 {total} orders, {stime} - {etime}",
        total=len(orders),
        stime=start_time.strftime("%Y-%m-%d %H:%M:%S"),
        etime=end_time.strftime("%Y-%m-%d %H:%M:%S"),
    )

    for order in orders:
        message = UnionOrderSyncMessage(payload=order)
        await producer.publish_message(message, exchange_name="delivery.topic")


async def main():
    from src.containers import Container
    from src.infrastructures.settings import BaseServiceSettings
    from src.interfaces.schedulers.main import lifespan

    # 创建容器实例
    container = Container()
    container.config.from_pydantic(BaseServiceSettings())

    async with lifespan(container) as active_container:
        active_container.wire(modules=[__name__])  # 注册依赖

        # await sync_union_order_by_1d()
        await sync_union_order_by_3m()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
