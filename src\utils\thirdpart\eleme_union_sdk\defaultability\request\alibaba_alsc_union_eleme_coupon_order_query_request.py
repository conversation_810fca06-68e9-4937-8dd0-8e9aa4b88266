from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponOrderQueryRequest(BaseRequest):

    def __init__(self, order_query_dto: object = None):
        """
        查询对象
        """
        self._order_query_dto = order_query_dto

    @property
    def order_query_dto(self):
        return self._order_query_dto

    @order_query_dto.setter
    def order_query_dto(self, order_query_dto):
        if isinstance(order_query_dto, object):
            self._order_query_dto = order_query_dto
        else:
            raise TypeError("order_query_dto must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.coupon.order.query"

    def to_dict(self):
        request_dict = {}
        if self._order_query_dto is not None:
            request_dict["order_query_dto"] = convert_struct(self._order_query_dto)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponOrderQueryCouponOrderQueryDto:
    def __init__(self, biz_order_id: str = None):
        """
        本地生活订单号
        """
        self.biz_order_id = biz_order_id
