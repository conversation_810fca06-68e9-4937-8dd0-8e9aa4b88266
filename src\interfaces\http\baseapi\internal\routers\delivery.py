# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/internal/routers/delivery.py
# created: 2025-05-19 12:16:17
# updated: 2025-05-29 14:03:44

from typing import TYPE_CHECKING, Optional

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from loguru import logger

from src.domains.delivery.services import DeliveryPageService
from src.interfaces.http.baseapi import Container
from src.utils.eleme.dto import UnionPageLinkDTO

from ..authorization import verify_internal_app
from ..schemas import DeliveryLinkExtPayload, DeliveryLinkResponse

if TYPE_CHECKING:
    from src.applications.common.queries import ActivityQueryService
    from src.domains.passport.entities import AppEntity

router = APIRouter(prefix="", tags=["delivery"])


@router.post(
    "/{app_id}/tenants/{tenant_id}/users/{user_id}/pages/{page_code}/link",
    summary="外卖取链接口",
    description="外卖取链接口, 传入租户id, 用户id, 页面编码, 额外参数, 返回外卖取链信息",
    response_model=DeliveryLinkResponse,
)
@inject
async def get_user_delivery_page(
    app_id: str,
    tenant_id: str,
    user_id: str,
    page_code: str,
    payload: Optional[DeliveryLinkExtPayload] = None,
    current_app: "AppEntity" = Depends(verify_internal_app),
    activity_query_service: "ActivityQueryService" = Depends(
        Provide[Container.applications.common_activity_query_service]
    ),
):
    # 获取外卖页面访问链接
    # 从额外参数中提取必要的信息
    links = await activity_query_service.get_eleme_pure_url(
        app=app_id,
        tenant=tenant_id,
        user=user_id,
        page_code=page_code,
        extra_info=payload.extra_params if payload and payload.extra_params else {},
    )

    logger.info(f"获取外卖链接成功: {links.model_dump()}")
    return DeliveryLinkResponse(data=links)
