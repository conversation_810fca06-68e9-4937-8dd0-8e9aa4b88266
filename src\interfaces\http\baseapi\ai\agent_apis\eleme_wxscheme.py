# encoding: utf-8
# src/interfaces/http/baseapi/ai/agent_apis/eleme_wxscheme.py
# created: 2025-07-23 00:00:00

from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient, TopException
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_officialactivity_wxscheme_request import (
    AlibabaAlscUnionElemePromotionOfficialactivityWxschemeActivityRequest,
    AlibabaAlscUnionElemePromotionOfficialactivityWxschemeRequest,
)


class ElemeWxSchemeClient:
    def __init__(self, appkey: str, app_secret: str, gateway_url: str):
        """
        初始化饿了么微信小程序链接生成客户端

        Args:
            appkey: 应用的 appkey
            app_secret: 应用的密钥
            gateway_url: API 网关地址
        """
        self.client = TopApiClient(appkey=appkey, app_sercet=app_secret, top_gateway_url=gateway_url, verify_ssl=True)
        self.ability = Defaultability(client=self.client)

    def get_wx_scheme_url(self, pid: str, activity_id: str, sid: str = "") -> str:
        """
        获取饿了么微信小程序 scheme URL

        Args:
            pid: 推广位 ID
            activity_id: 活动 ID
            sid: 会话 ID（可选）

        Returns:
            str: 生成的微信小程序 scheme URL

        Raises:
            TopException: 当 API 调用失败时抛出异常
        """
        # 创建活动请求对象
        activity_request = AlibabaAlscUnionElemePromotionOfficialactivityWxschemeActivityRequest()
        activity_request.pid = pid
        activity_request.activity_id = activity_id
        activity_request.sid = sid

        # 创建总请求对象
        request = AlibabaAlscUnionElemePromotionOfficialactivityWxschemeRequest()
        request.query_request = activity_request

        try:
            response = self.ability.alibaba_alsc_union_eleme_promotion_officialactivity_wxscheme(request)
            # {'data': {'url_scheme': 'weixin://dl/business/?t=VkX6zzzzVJv', 'wx_app_id': 'wxece3a9a4c82f58c9', 'wx_path': 'commercialize/pages/taoke-guide/index?scene=a216278b81774cedb41320231f11f819'}, 'message': 'success', 'result_code': 0, 'request_id': '15r62kfemzo3o'}
            return response.get("data", {}).get("url_scheme", "")
        except TopException as e:
            print(f"获取微信 scheme URL 失败: {str(e)}")
            raise


APP_KEY = "34813436"
APP_SECRET = "929eafdde0fb34c57ff0290e271b54c3"
TOP_GATEWAY_URL = "https://eco.taobao.com/router/rest"
PID = "alsc_28806810_9518007_26738131"

client = ElemeWxSchemeClient(APP_KEY, APP_SECRET, TOP_GATEWAY_URL)


def get_wx_scheme_url(activity_id: str, sid: str = "") -> str:
    return client.get_wx_scheme_url(PID, activity_id, sid)


if __name__ == "__main__":
    # print(get_wx_scheme_url("10145"))
    pass
