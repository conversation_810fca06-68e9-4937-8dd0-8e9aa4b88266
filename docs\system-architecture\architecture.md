# 架构设计文档

版本: 2.0
最后更新: 2024-07-25
作者: <PERSON> with Gemini AI Assistant


## 概述

### 1.1. 文档目的
本文档旨在定义 Hicaspian Service Backend 项目的下一代软件架构。该架构旨在解决当前单体应用在扩展性、可维护性和团队协作方面的挑战，通过引入“模块化单体 (Modular Monolith)”的设计思想，为未来的业务增长和技术演进奠定坚实基础。

### 1.2. 核心思想
新架构的核心是“关注点分离”与“独立部署”。我们将应用划分为两大块：

1. 共享核心 (Shared Core): 包含所有业务逻辑 (domains)、应用服务 (applications)和数据访问 (repositories)。这部分代码是跨服务复用的。
2. 独立服务 (Independent Services): 作为应用的入口点，每个服务都拥有独立的配置、部署单元和生命周期。这些服务包括HTTP API、消息消费者、定时任务等。

这种模式既能享受单体仓库带来的代码复用便利，又能获得类似微服务的独立部署和扩展能力。

### 1.3. 设计目标
- 提升开发效率: 清晰的模块边界和统一的编码模式，降低认知负担。
- 增强可扩展性: 每个服务都可以根据自身负载独立扩缩容。
- 明确团队权责: 不同团队可以负责不同的服务，实现并行开发。
- 简化部署流程: 每个服务拥有独立的CI/CD流水线，变更影响范围可控。
- 平滑演进路径: 为未来向微服务架构的演进提供清晰的、低风险的路径。

## 架构设计

### 2.1. 逻辑分层视图

graph TD
    subgraph "独立服务 (Independent Services)"
        HTTP_API[HTTP API]
        CONSUMERS[消息消费者]
        SCHEDULERS[定时任务]
        CLI[命令行工具]
    end

    subgraph "共享核心 (Shared Core)"
        APPLICATIONS[applications/]
        DOMAINS[domains/]
        REPOSITORIES[repositories/]
        DATABASES[databases/]
    end

    HTTP_API --> APPLICATIONS
    CONSUMERS --> APPLICATIONS
    SCHEDULERS --> APPLICATIONS
    CLI --> APPLICATIONS

    APPLICATIONS --> DOMAINS
    APPLICATIONS --> REPOSITORIES
    DOMAINS --> REPOSITORIES

    REPOSITORIES --> DATABASES

    style SHARED_CORE fill:#f9f,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
    style INDEPENDENT_SERVICES fill:#ccf,stroke:#333,stroke-width:2px

依赖规则: 任何上层都可以依赖下层，但反向依赖是严格禁止的。

### 2.2 服务部署视图

graph LR
    subgraph "Monorepo (GitLab)"
        direction LR
        subgraph "共享代码"
            applications
            domains
            repositories
        end
        subgraph "独立服务定义"
            services_benefits_api[services/benefits-api]
            services_delivery_api[services/delivery-api]
            services_consumers[services/consumers]
        end
    end

    subgraph "CI/CD Pipeline (GitLab CI)"
        services_benefits_api --> BUILD1[Build benefits-api]
        services_delivery_api --> BUILD2[Build delivery-api]
        services_consumers --> BUILD3[Build consumers]
    end

    subgraph "部署环境 (Kubernetes)"
        BUILD1 --> DEPLOY1[Deployment: benefits-api]
        BUILD2 --> DEPLOY2[Deployment: delivery-api]
        BUILD3 --> DEPLOY3[Deployment: consumers]
    end

    style Monorepo fill:#eee,stroke:#333
    style DEPLOY1 fill:#cde,stroke:#333
    style DEPLOY2 fill:#cde,stroke:#333
    style DEPLOY3 fill:#cde,stroke:#333

部署单元: interfaces/ 目录下的每个子目录都是一个独立的部署单元。

## 目录结构设计

service-backend/
├── src/                            → 【源代码根目录】
│   ├──interfaces/              → 【接口层】所有外部交互的入口点
│   │   ├── http/                   → HTTP API 服务
│   │   │   ├── benefits_api/       →   - 权益对外API服务
│   │   │   ├── delivery_api/       →   - 外卖API服务
│   │   │   └── mis_api/            →   - 管理后台API服务
│   │   ├── consumers/              → 消息消费服务
│   │   └── schedulers/             → 定时任务服务
│   │
│   ├──applications/            → 【应用服务层】编排业务用例
│   │   ├── benefits/
│   │   │   ├── commands/
│   │   │   ├── queries/
│   │   │   └── handlers/
│   │   └── ...
│   │
│   ├──domains/                 → 【领域层】核心业务逻辑与规则
│   │   ├── benefits/
│   │   │   ├── services/
│   │   │   └── dto/
│   │   └── ...
│   │
│   ├──repositories/             → 【数据访问层】数据访问接口及实现
│   │   ├── benefits/
│   │   └── ...
│   │
│   ├──databases/               → 【数据模型层】ORM/ODM模型
│   │   ├── models/
│   │   └── migrations/
│   │
│   └──infra/                  → 【共享基础设施】核心工具、基类等
│
├──tests/                       → 【测试目录】与src结构镜像
│   ├── integration/
│   │   ├── interfaces/
│   │   └── applications/
│   ├── unit/
│   │   ├── domains/
│   │   └── applications/
│   └── conftest.py
│
├──deploys/                    → 【服务部署定义】
│   ├── benefits_api/               → 权益API服务部署配置
│   │   ├── main.py                 → 服务启动入口
│   │   ├── config.py
│   │   ├── Dockerfile
│   │   └── .gitlab-ci.yml
│   ├── delivery_api/               → 外卖API服务部署配置 (...)
│   └── ...
│
├── .gitignore
├── .gitlab-ci.yml                  → 主CI/CD配置文件
├── poetry.lock
├── pyproject.toml                  → 项目依赖与配置 (PEP 621)
└── README.md


## 各层职责定义

- interfaces/ (服务层/接口层):
职责: 作为应用的入口，处理外部交互协议（如HTTP, AMQP）。
内容: 协议转换（HTTP请求 -> Command）、鉴权、参数验证、响应格式化、错误码转换。
禁止: 包含任何业务逻辑、直接操作数据库。
- applications/ (应用服务层):
职责: 编排和协调领域服务和基础设施来完成一个完整的业务用例。
内容: 事务管理、业务权限检查、调用多个领域服务、与外部服务（如支付）集成。
禁止: 包含核心业务规则的实现。
- domains/ (领域层):
职责: 实现核心业务逻辑和规则，是系统的业务核心。
内容: 业务实体、值对象、领域服务、业务规约和自定义业务异常。
禁止: 依赖任何外部协议和基础设施（如HTTP, Redis）。
- repositories/ (数据访问层):
职责: 解耦业务逻辑与数据存储，提供统一的数据访问接口。
内容: 定义数据访问接口 (interfaces.py) 和具体的ORM/ODM实现 (implementations.py)。
- databases/ (数据模型层):
职责: 定义数据在数据库中的存储结构。
内容: tortoise-orm 或 beanie 模型定义、数据库迁移脚本。

## 数据管理

### 5.1. 共享数据库
在当前阶段，所有服务共享同一个数据库。这简化了开发和事务管理，但也意味着服务间存在数据耦合。

### 5.2. 演进方向
随着业务发展，未来的演进方向是数据隔离：

1. 逻辑隔离: 在共享数据库中，按服务或领域划分不同的 schema。
2. 物理隔离: 为关键服务提供独立的数据库实例，实现完全的数据解耦。

## CI/CD 流程设计

6.1. 核心原则
采用“基于路径变更的独立流水线”策略。只有当某个服务自身的代码或其依赖的共享代码发生变更时，才会触发该服务的CI/CD流水线。
