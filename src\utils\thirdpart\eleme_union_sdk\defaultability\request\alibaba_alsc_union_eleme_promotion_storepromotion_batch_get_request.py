from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionStorepromotionBatchGetRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.storepromotion.batch.get"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionStorepromotionBatchGetBatchQueryStorePromotionRequest:
    def __init__(
        self,
        pid: str = None,
        shop_id: str = None,
        activity_id: str = None,
        media_activity_id: str = None,
        sid: str = None,
    ):
        """
        渠道PID
        """
        self.pid = pid
        """
            门店ID，支持多值，'|'分隔
        """
        self.shop_id = shop_id
        """
            活动ID
        """
        self.activity_id = activity_id
        """
            媒体出资活动ID
        """
        self.media_activity_id = media_activity_id
        """
            三方扩展id
        """
        self.sid = sid
