# encoding: utf-8
# src/applications/openapi/services/aixincan.py
# created: 2025-07-30 15:23:50

from typing import Optional

from src.infrastructures.gateways.eleme import ElemeAixincanGateway


class ElemeAixincanService:

    def __init__(self, aixincan_gateway: ElemeAixincanGateway):
        self.aixincan_gateway = aixincan_gateway

    async def add_aixincan_user(
        self, phone: str, province: str, city: str, district: Optional[str] = None, industry: Optional[str] = None
    ) -> bool:
        """
        添加饿了么爱心餐白名单用户

        Args:
            phone: 用户手机号
            province: 省份
            city: 城市
            district: 区县, 可选
            industry: 行业, 可选
        """
        result = await self.aixincan_gateway.add_aixincan_user(
            phone, province_name=province, city_name=city, district_name=district, industry=industry
        )
        return result.get("success", False)
