# encoding: utf-8

from playwright.async_api import Page

from src.infrastructures.exceptions.growth_hacker import RiskDetectedError

RISK_KEYWORDS = [
    "风险",
    "验证",
    "人机验证",
    "滑块",
    "验证码",
    "captcha",
    "robot",
    "forbidden",
    "安全验证",
    "请稍后再试",
    "访问异常",
    "系统繁忙",
]

RISK_SELECTORS = [
    "iframe[src*='captcha']",
    "iframe[id*='baxia-dialog-content']",
    "iframe[src*='waimai-guide.ele.me:443/h5/mtop.alibaba.o2o.ele.guide.local.tk']",
    "iframe[src*='https://ipassport.ele.me/mini_login']",
    "div[class*='captcha']",
    "div[class*='verify']",
    "div[class*='robot']",
    "canvas[id*='captcha']",
    "img[src*='captcha']",
    ".slider-verify",
    ".verify-code",
    "[data-testid*='captcha']",
]


class RiskDetector:
    """风控检测器"""

    def __init__(self, page: Page):
        self.page = page

    async def detect(self) -> bool:
        """检测风控"""

        # 检查页面标题
        title = await self.page.title()
        title_lower = title.lower()

        # 检查页面标题
        for keyword in RISK_KEYWORDS:
            if keyword in title_lower:
                raise RiskDetectedError(
                    f"页面标题包含风控关键词 '{keyword}': {title}，页面地址: {self.page.url}",
                    risk_type="title_keyword",
                    page_url=self.page.url,
                )

        # 检查页面内容
        body = await self.page.query_selector("body")
        if body:
            body_text = await body.inner_text()
            body_text_lower = body_text.lower()

            # 检查页面内容是否包含风控关键词
            for keyword in RISK_KEYWORDS:
                if keyword in body_text_lower:
                    raise RiskDetectedError(
                        f"页面内容包含风控关键词 '{keyword}': {body_text}，页面地址: {self.page.url}",
                        risk_type="content_keyword",
                        page_url=self.page.url,
                    )

        # 检查常见的风控元素

        for selector in RISK_SELECTORS:
            element = await self.page.query_selector(selector)
            if element:
                raise RiskDetectedError(
                    f"页面包含风控元素: {selector}，页面地址: {self.page.url}",
                    risk_type="element_selector",
                    page_url=self.page.url,
                )

        return True
