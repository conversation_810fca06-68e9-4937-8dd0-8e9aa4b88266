# encoding: utf-8
# src/infrastructures/browsers/pools/pool.py
# created: 2025-08-07 22:54:22

import asyncio
import time
from dataclasses import asdict, dataclass, field
from typing import TYPE_CHECKING, Optional

from loguru import logger
from playwright.async_api import StorageState, ViewportSize, async_playwright

from ..settings import BrowserConfig
from ..types import DeviceInfo, WebViewInfo

if TYPE_CHECKING:
    from playwright.async_api import (
        B<PERSON>er,
        BrowserContext,
        Geolocation,
        Playwright,
        ProxySettings,
    )


@dataclass
class DefaultContextSettings:
    locale: str = "zh-CN"
    timezone_id: str = "Asia/Shanghai"
    permissions: list[str] = field(default_factory=lambda: ["geolocation"])
    is_mobile: bool = True
    has_touch: bool = True


@dataclass
class BrowserStats:
    """浏览器统计"""

    usage_count: int = 0
    created_at: float = field(default_factory=time.time)
    current_usage: int = 0


class BrowserPool:
    """简化的浏览器池 - 支持自动重建"""

    def __init__(self, config: BrowserConfig):
        self.config = config
        self.browsers: dict[str, "Browser"] = {}
        self.browser_stats: dict[str, BrowserStats] = {}
        self.playwright: "Playwright" | None = None
        self._current = 0  # 轮询索引
        self._lock = asyncio.Lock()

    async def init(self):
        """初始化浏览器池"""
        if not self.playwright:
            self.playwright = await async_playwright().start()

        # 创建初始浏览器
        for i in range(self.config.pool.browser_count):
            await self._create_browser(f"browser_{i}")

        logger.info(f"✅ BrowserPool初始化完成, 成功创建 {self.config.pool.browser_count} 个实例")

    async def acquire(
        self,
        device: DeviceInfo,
        webview: WebViewInfo,
        state: StorageState,
        location: "Geolocation",
        proxy: Optional["ProxySettings"] = None,
    ) -> "BrowserContext":
        """获取一个全新的 Context - 轮询分配"""
        async with self._lock:
            browser_id = f"browser_{self._current}"
            self._current = (self._current + 1) % self.config.pool.browser_count

            # 检查是否需要重建
            if await self._should_rebuild(browser_id):
                await self._rebuild_browser(browser_id)

            browser = self.browsers[browser_id]

        # 构建参数
        user_agent = device.user_agent + webview.user_agent_suffix

        # 创建context
        context = await browser.new_context(
            **asdict(DefaultContextSettings()),
            proxy=proxy,
            geolocation=location,
            user_agent=user_agent,
            viewport=ViewportSize(width=device.viewport["width"], height=device.viewport["height"]),
            device_scale_factor=device.device_scale_factor,
            extra_http_headers=webview.extra_headers,
            storage_state=state,
        )

        # 更新使用计数
        self.browser_stats[browser_id].usage_count += 1
        self.browser_stats[browser_id].current_usage += 1
        return context

    async def release(self, context: "BrowserContext"):
        """释放 Context - 直接关闭"""
        try:
            # 获取browser_id以更新统计
            browser_id = None
            for bid, browser in self.browsers.items():
                if context.browser == browser:
                    browser_id = bid
                    break

            # 关闭context
            await context.close()

            # 更新当前使用数
            if browser_id and browser_id in self.browser_stats:
                self.browser_stats[browser_id].current_usage = max(0, self.browser_stats[browser_id].current_usage - 1)
                logger.debug(
                    f"Released context from {browser_id}, "
                    f"current usage: {self.browser_stats[browser_id].current_usage}"
                )
        except Exception as e:
            logger.debug(f"Context close error: {e}")

    async def close(self):
        """关闭池"""
        for _, browser in self.browsers.items():
            await browser.close()
        if self.playwright:
            await self.playwright.stop()
        self.browsers.clear()
        self.browser_stats.clear()
        logger.info("✅ 浏览器池已关闭")

    async def _create_browser(self, browser_id: str):
        """创建新浏览器实例"""
        if not self.playwright:
            raise RuntimeError("Playwright not initialized")

        browser = await self.playwright.chromium.launch(
            headless=self.config.launch.headless,
            args=self.config.launch.browser_args,
            timeout=self.config.pool.browser_timeout,
        )
        self.browsers[browser_id] = browser
        self.browser_stats[browser_id] = BrowserStats()
        logger.info(f"✅ Created {browser_id}")

    async def _should_rebuild(self, browser_id: str) -> bool:
        """检查是否需要重建浏览器"""
        stats = self.browser_stats.get(browser_id)
        if not stats:
            return False

        # 必须没有正在使用的context才能重建
        if stats.current_usage > 0:
            logger.debug(f"⏸️ {browser_id} 有 {stats.current_usage} 个context正在使用，暂不重建")
            return False

        # 检查使用次数
        if stats.usage_count >= self.config.pool.browser_max_usage:
            logger.info(f"🔄 {browser_id} 达到最大使用次数 {stats.usage_count}，" f"当前使用数: {stats.current_usage}")
            return True

        # 检查存活时间
        age = time.time() - stats.created_at
        if age >= self.config.pool.browser_max_lifetime:
            logger.info(f"🔄 {browser_id} 达到最大存活时间 {age:.0f}s，" f"当前使用数: {stats.current_usage}")
            return True

        return False

    async def _rebuild_browser(self, browser_id: str):
        """重建浏览器实例"""
        # 关闭旧浏览器
        old_browser = self.browsers.get(browser_id)
        if old_browser:
            try:
                await old_browser.close()
            except Exception as e:
                logger.debug(f"Close old browser error: {e}")

        # 创建新浏览器
        await self._create_browser(browser_id)
        logger.info(f"♻️ Rebuilt {browser_id}")


# 全局浏览器池实例 - 需要在初始化时传入配置
browser_pool: Optional[BrowserPool] = None
