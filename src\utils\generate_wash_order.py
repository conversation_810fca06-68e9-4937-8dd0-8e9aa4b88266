import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional

from dependency_injector.wiring import Provide, inject

from src.containers import Container
from src.domains.delivery.services.pages import DeliveryPageService


class GenerateWashOrder:
    def __init__(
        self,
        city: str,
        city_py: str,
        lifecycle_stage: str,
        batch_name_prefix: Optional[str] = None,
        page_code: Optional[str] = None,
    ):
        self.MAX_QPS = 10
        self.BATCH_SIZE = 10
        self.BATCH_INTERVAL = 0.2
        self.PAGE_CODE = page_code or "alipay_hv_514"
        self.ELEME_CHANNEL = "haili"
        self.TAG = "growth_hacker"
        self.RUN_DATE = datetime.now().strftime("%Y-%m-%d")
        self.TARGET_CITY = city.lower()
        self.TARGET_CITY_PY = city_py
        self.LIFECYCLE_STAGE = lifecycle_stage
        self.BATCH_NAME = f"{batch_name_prefix + '_' if batch_name_prefix else ''}{self.TARGET_CITY}_{self.LIFECYCLE_STAGE}_{self.RUN_DATE}"

    async def generate_wash_order_tasks(self, user_list: List[Dict[str, str]]):
        results = await self.process_users(user_list)
        return {
            "config": {
                "target_city": self.TARGET_CITY,
                "lifecycle_stage": self.LIFECYCLE_STAGE,
                "batch_name": self.BATCH_NAME,
            },
            "total_count": len(results),
            "results": results,
        }

    async def process_users(self, user_list: List[Dict[str, str]]):
        results = []
        total_users = len(user_list)

        # 按批次处理，每批BATCH_SIZE个
        for batch_start in range(0, total_users, self.BATCH_SIZE):
            batch_end = min(batch_start + self.BATCH_SIZE, total_users)
            batch_users = user_list[batch_start:batch_end]

            # 并发处理当前批次
            tasks = [
                self.process_single_user(user, batch_start + i + 1, total_users) for i, user in enumerate(batch_users)
            ]

            # 等待当前批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，过滤异常
            for result in batch_results:
                if not isinstance(result, Exception):
                    results.append(result)

            # 如果不是最后一批，等待间隔时间
            if batch_end < total_users:
                await asyncio.sleep(self.BATCH_INTERVAL)

        return results

    async def process_single_user(self, user: Dict[str, str], index: int, total: int) -> Dict:
        """处理单个用户，生成访问链接"""

        access_url = await self.generate_access_url(user["phone"], user["lat"], user["lng"])

        return {
            **user,
            "access_url": access_url,
            "success": bool(access_url),
            "processed_at": datetime.now().isoformat(),
            "task_id": uuid.uuid4().hex[:8],
            "batch_name": self.BATCH_NAME,
        }

    @inject
    async def generate_access_url(
        self,
        phone: str,
        lat: str,
        lng: str,
        delivery_page_service: DeliveryPageService = Provide[Container.domains.delivery_page_service],
    ) -> str:
        """生成饿了么活动页面访问链接"""
        try:
            access_url = await delivery_page_service.get_access_url(
                page_code=self.PAGE_CODE,
                mobile=phone,
                user_open_id=phone,
                eleme_channel=self.ELEME_CHANNEL,
                latitude=float(lat),
                longitude=float(lng),
                extra_info={
                    "tag": self.TAG,
                    "city": self.TARGET_CITY,
                    "batch": self.BATCH_NAME,
                    "date": self.RUN_DATE,
                    "phone": phone,
                },
            )
            return access_url
        except Exception:
            return ""
