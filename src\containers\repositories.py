# encoding: utf-8
# src/containers/repositories.py
# created: 2025-07-27 16:10:29

from dependency_injector import containers, providers


class Repositories(containers.DeclarativeContainer):

    # growth hacker repositories
    from src.repositories.growth_hacker import TaskRepository, UserRepository

    gh_user_repository = providers.Singleton(UserRepository)
    gh_task_repository = providers.Singleton(TaskRepository)

    # customer repositories
    from src.repositories.customer import BenefitsRepository as CustomerBenefitsRepository
    from src.repositories.customer import CustomerRepository, CustomerSubscribeRepository

    customer_repository = providers.Singleton(CustomerRepository)
    customer_benefits_repository = providers.Singleton(CustomerBenefitsRepository)
    customer_subscribe_repository = providers.Singleton(CustomerSubscribeRepository)

    # delivery repositories
    from src.repositories.delivery import (
        DeliveryElemeOrderRepository,
        DeliveryOrderRepository,
        DeliveryPageRepository,
        DeliveryUserRepository,
    )

    delivery_page_repository = providers.Singleton(DeliveryPageRepository)
    delivery_order_repository = providers.Singleton(DeliveryOrderRepository)
    delivery_eleme_order_repository = providers.Singleton(DeliveryElemeOrderRepository)
    delivery_user_repository = providers.Singleton(DeliveryUserRepository)

    # benefits repositories
    from src.repositories.benefits import (
        ProductOrderRepository,
        ProductRepository,
        PurchaseRepository,
        SkuChargeRecordRepository,
        SkuRepository,
        SupplierRepository,
    )

    benefits_product_repository = providers.Singleton(ProductRepository)
    benefits_pdorder_repository = providers.Singleton(ProductOrderRepository)
    benefits_sku_charge_record_repository = providers.Singleton(SkuChargeRecordRepository)
    benefits_sku_repository = providers.Singleton(SkuRepository)
    benefits_purchase_repository = providers.Singleton(PurchaseRepository)
    benefits_supplier_repository = providers.Singleton(SupplierRepository)

    # passport repositories
    from src.repositories.passport.apps import AppRepository
    from src.repositories.passport.tenants import TenantRepository
    from src.repositories.passport.users import UserRepository as PassportUserRepository

    passport_app_repository = providers.Singleton(AppRepository)
    passport_tenant_repository = providers.Singleton(TenantRepository)
    passport_user_repository = providers.Singleton(PassportUserRepository)

    # shops repositories
    from src.repositories.shops.shops import ShopRepository

    shop_repository = providers.Singleton(ShopRepository)

    # eleme repositories
    from src.repositories.eleme import ElemeUserProfileRepository

    eleme_user_profile_repository = providers.Singleton(ElemeUserProfileRepository)
