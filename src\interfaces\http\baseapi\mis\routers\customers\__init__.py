# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/customers/__init__.py
# created: 2025-02-06 15:03:51
# updated: 2025-02-06 15:09:13

from fastapi import APIRouter

from .benefits import router as benefits_router
from .customers import router as customers_router
from .secrets import router as secrets_router

router = APIRouter(tags=["customer"])

router.include_router(customers_router)
router.include_router(benefits_router)
router.include_router(secrets_router)
