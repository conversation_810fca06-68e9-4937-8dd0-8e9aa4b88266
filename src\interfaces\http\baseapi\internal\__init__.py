# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/internal/__init__.py
# created: 2024-12-08 17:20:53
# updated: 2025-05-19 13:22:01

from fastapi import FastAPI

from src.infrastructures.fastapi.application import register_app

from .routers.delivery import router as delivery_router
from .routers.passport import router as passport_router
from .routers.shops import router as shops_router


def get_internal_app(config=None) -> FastAPI:
    internal_app = register_app(
        name="internal",
        description="内部服务API",
        version="0.1.0",
        config=config,
    )

    internal_app.include_router(passport_router, prefix="/passport", tags=["passport"])
    internal_app.include_router(delivery_router, prefix="/delivery", tags=["delivery"])
    internal_app.include_router(shops_router, prefix="/shops", tags=["shops"])
    return internal_app
