# encoding: utf-8
# src/infrastructures/gateways/dingtalk/gateways.py
# created: 2025-08-01 17:01:57

from typing import Optional

from alibabacloud_dingtalk.contact_1_0 import models as dingtalk_contact_models
from alibabacloud_dingtalk.contact_1_0.client import Client as DingtalkContactClient
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalk_oauth_models
from alibabacloud_dingtalk.oauth2_1_0.client import Client as DingtalkOAuthClient
from alibabacloud_tea_openapi import models as dingtalk_models
from alibabacloud_tea_util import models as util_models
from loguru import logger

from src.infrastructures import errors

dintalk_config = dingtalk_models.Config(protocol="https", region_id="central")


class DingtalkGateway:

    def __init__(self, app_id: str, app_secret: str):
        self.app_id = app_id
        self.app_secret = app_secret

    async def get_user_token(self, auth_code: str) -> Optional[str]:
        """
        获取钉钉用户令牌。

        :param auth_code: 授权码，用于获取用户令牌。
        :param dingtalk_app_id: 钉钉应用的App ID。
        :param dingtalk_app_secret: 钉钉应用的App Secret。
        :return: 返回获取的用户访问令牌, 如果失败则返回None。
        :raises DingtalkLoginError: 如果获取用户令牌失败，则抛出此异常。
        """
        oauth_client = DingtalkOAuthClient(dintalk_config)
        try:
            request = dingtalk_oauth_models.GetUserTokenRequest(
                client_id=self.app_id,
                client_secret=self.app_secret,
                code=auth_code,
                refresh_token="",
                grant_type="authorization_code",
            )
            response = await oauth_client.get_user_token_async(request)
            access_token = response.body.access_token
            return access_token
        except Exception as e:
            logger.error(
                f"Get dingtalk user token failed: {e}, auth_code: {auth_code}, "
                f"dingtalk_app_id: {self.app_id}, dingtalk_app_secret: {self.app_secret}"
            )
            raise errors.DingtalkLoginError from e

    async def get_userinfo(self, access_token: str) -> dingtalk_contact_models.GetUserResponseBody:
        """
        获取钉钉用户信息。

        :param access_token: 用户访问令牌，用于获取用户信息。
        :return: 返回获取的用户信息, 如果失败则返回None。
        :raises DingtalkLoginError: 如果获取用户信息失败，则抛出此异常。
        """
        contact_client = DingtalkContactClient(dintalk_config)
        get_user_headers = dingtalk_contact_models.GetUserHeaders()
        get_user_headers.x_acs_dingtalk_access_token = access_token
        try:
            get_user_response = contact_client.get_user_with_options(
                union_id="me", headers=get_user_headers, runtime=util_models.RuntimeOptions()
            )
            return get_user_response.body
        except Exception as e:
            logger.error(f"Get dingtalk user info failed: {e}, access_token: {access_token}")
            raise errors.DingtalkLoginError from e
