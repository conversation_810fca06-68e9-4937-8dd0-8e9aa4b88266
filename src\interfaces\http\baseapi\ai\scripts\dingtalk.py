import json

import requests

# clientid: dingpadaxolo4tb9mzw4

# clientsecret: Ztmc4E1WQW03HEYMDOHm1u02BsoeXfRwtfsMuDBn_zkjlw0T3K54nmZgzhiv0gUQ


def get_access_token(app_key, app_secret):
    """
    封装钉钉/v1.0/oauth2/accessToken API接口，获取访问令牌

    参数:
        app_key (str): 应用的AppKey
        app_secret (str): 应用的AppSecret

    返回:
        dict: 包含access_token的API响应结果
    """
    url = "https://api.dingtalk.com/v1.0/oauth2/accessToken"

    headers = {"Content-Type": "application/json"}

    payload = {"appKey": app_key, "appSecret": app_secret}

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


def prepare_ai_interaction(access_token, open_conversation_id=None, union_id=None, content_type=None, content=None):
    """
    封装钉钉/v1.0/aiInteraction/prepare API接口

    参数:
        access_token (str): 钉钉访问令牌
        open_conversation_id (str, optional): 会话ID
        union_id (str, optional): 用户unionId
        content_type (str, optional): 内容类型
        content (str, optional): 内容

    返回:
        dict: API响应结果
    """
    url = "https://api.dingtalk.com/v1.0/aiInteraction/prepare"

    headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": access_token}

    payload = {}
    if open_conversation_id:
        payload["openConversationId"] = open_conversation_id
    if union_id:
        payload["unionId"] = union_id
    if content_type:
        payload["contentType"] = content_type
    if content:
        payload["content"] = content

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


# 单聊：{
#   "unionId": "sFxxxx",
#   "contentType": "ai_card",
#   "content": "{\"templateId\": \"xxxx-xxxxx-xxxx-xxxx.schema\",\"cardData\": \"{\"title\":\"我是标题\",\"desc\":\"我是描述。\"}"
# }


def update_ai_interaction(access_token, conversation_token, content_type=None, content=None):
    """
    封装钉钉/v1.0/aiInteraction/update API接口，更新AI交互内容

    参数:
        access_token (str): 钉钉访问令牌
        conversation_token (str): 会话令牌
        content_type (str, optional): 内容类型
        content (str, optional): 内容

    返回:
        dict: API响应结果
    """
    url = "https://api.dingtalk.com/v1.0/aiInteraction/update"

    headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": access_token}

    payload = {"conversationToken": conversation_token}

    if content_type:
        payload["contentType"] = content_type
    if content:
        payload["content"] = content

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


def finish_ai_interaction(access_token, conversation_token):
    """
    封装钉钉/v1.0/aiInteraction/finish API接口，完成AI交互

    参数:
        access_token (str): 钉钉访问令牌
        conversation_token (str): 会话令牌

    返回:
        dict: API响应结果
    """
    url = "https://api.dingtalk.com/v1.0/aiInteraction/finish"

    headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": access_token}

    payload = {"conversationToken": conversation_token}

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


def get_department_user_details(access_token, dept_id, cursor=0, size=10):
    """
    获取部门用户详细信息列表

    参数:
        access_token (str): 钉钉访问令牌
        dept_id (int): 部门ID，如果是根部门传1
        cursor (int, optional): 分页查询的游标，最开始传0，后续传返回参数中的next_cursor值
        size (int, optional): 分页大小，默认10

    返回:
        dict: API响应结果，包含用户详细信息列表
    """
    url = "https://oapi.dingtalk.com/topapi/v2/user/list"

    headers = {"Content-Type": "application/json"}

    params = {"access_token": access_token}

    payload = {"dept_id": dept_id, "cursor": cursor, "size": size}

    response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
    return response.json()


def get_department_user_ids(access_token, dept_id):
    """
    获取部门用户ID列表

    参数:
        access_token (str): 钉钉访问令牌
        dept_id (int): 部门ID

    返回:
        dict: API响应结果，包含用户ID列表
    """
    url = "https://oapi.dingtalk.com/topapi/user/listid"

    headers = {"Content-Type": "application/json"}

    params = {"access_token": access_token}

    payload = {"dept_id": dept_id}

    response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
    return response.json()


def send_ai_interaction(access_token, open_conversation_id=None, union_id=None, content_type=None, content=None):
    """
    封装钉钉/v1.0/aiInteraction/send API接口，直接发送AI交互内容

    参数:
        access_token (str): 钉钉访问令牌
        open_conversation_id (str, optional): 会话ID
        union_id (str, optional): 用户unionId
        content_type (str, optional): 内容类型
        content (str, optional): 内容

    返回:
        dict: API响应结果
    """
    url = "https://api.dingtalk.com/v1.0/aiInteraction/send"

    headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": access_token}

    payload = {}
    if open_conversation_id:
        payload["openConversationId"] = open_conversation_id
    if union_id:
        payload["unionId"] = union_id
    if content_type:
        payload["contentType"] = content_type
    if content:
        payload["content"] = content

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


def get_all_department_user_details(access_token, dept_id, size=100):
    """
    获取部门所有用户详细信息列表（自动处理分页）

    参数:
        access_token (str): 钉钉访问令牌
        dept_id (int): 部门ID，如果是根部门传1
        size (int, optional): 每页大小，默认100

    返回:
        list: 所有用户详细信息列表
    """
    all_users = []
    cursor = 0
    has_more = True

    while has_more:
        result = get_department_user_details(access_token, dept_id, cursor, size)
        if result.get("errcode") != 0:
            print(f"获取部门用户详情失败: {result}")
            break

        response_data = result.get("result", {})
        users = response_data.get("list", [])
        all_users.extend(users)

        next_cursor = response_data.get("next_cursor", 0)
        has_more = next_cursor != 0
        cursor = next_cursor

    return all_users
