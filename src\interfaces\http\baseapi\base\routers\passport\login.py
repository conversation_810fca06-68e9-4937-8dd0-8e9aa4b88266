# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/routers/passport/login.py
# created: 2025-05-27 00:41:50
# updated: 2025-05-28 10:31:37

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from loguru import logger

from src.interfaces.http.baseapi import Container

from ...authorization import base_api_authentication, get_env
from ...schemas import Env
from .schemas import (
    AlipayMpLoginPayload,
    LoginDingtalkRequestPayload,
    LoginResponse,
    LoginSmsRequestPayload,
    SendSmsRequestPayload,
    SendSmsResponse,
    SendSmsResponseData,
    WechatMpLoginPayload,
)

if TYPE_CHECKING:
    from src.applications.passport.services import LoginService
    from src.domains.passport.entities import UserEntity

router = APIRouter(tags=["Passport", "Login"])


@router.post("/login/sms/send_sms", name="发送短信验证码", response_model=SendSmsResponse)
@inject
async def send_sms(
    payload: SendSmsRequestPayload,
    env: Env = Depends(get_env),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    result = await login_service.send_sms_code(env.app, payload.phone_number)
    return SendSmsResponse(message="验证码已发送", data=SendSmsResponseData(expired_at=result))


@router.post("/login/sms", name="短信验证码登录", response_model=LoginResponse)
@inject
async def login_sms(
    payload: LoginSmsRequestPayload,
    env: Env = Depends(get_env),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    user_info = await login_service.sms_login(
        app=env.app, tenant=env.tenant, phone=payload.phone_number, verify_code=payload.auth_code
    )
    return LoginResponse(data=user_info)


@router.post("/login/dingtalk", name="钉钉登录", response_model=LoginResponse)
@inject
async def login_dingtalk(
    payload: LoginDingtalkRequestPayload,
    env: Env = Depends(get_env),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    user_info = await login_service.dingtalk_login(app=env.app, tenant=env.tenant, auth_code=payload.auth_code)
    return LoginResponse(data=user_info)


@router.post("/check_login", name="检查登录状态", response_model=LoginResponse)
@inject
async def check_login(
    env: Env = Depends(get_env),
    current_user: "UserEntity" = Depends(base_api_authentication),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    user_info = await login_service.check_login(current_user=current_user, app=env.app, tenant=env.tenant)
    return LoginResponse(data=user_info)


@router.post(
    "/login/wechat_mp",
    name="微信小程序登陆",
    response_model=LoginResponse,
    description="微信小程序登陆, 如果手机号未绑定, 则需要传入phone_code",
)
@inject
async def login_wechat_mp(
    payload: WechatMpLoginPayload,
    env: Env = Depends(get_env),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    user_info = await login_service.wechat_mp_login(
        app=env.app,
        code=payload.login_code,
        tenant=env.tenant,
        phone_code=payload.phone_code,
    )
    return LoginResponse(data=user_info)


@router.post(
    "/login/alipay_mp",
    name="支付宝小程序登陆",
    response_model=LoginResponse,
    description="支付宝小程序登陆, 如果手机号未绑定, 则需要传入phone_code",
)
@inject
async def login_alipay_mp(
    payload: AlipayMpLoginPayload,
    env: Env = Depends(get_env),
    login_service: "LoginService" = Depends(Provide[Container.applications.passport_login_service]),
):
    user_info = await login_service.alipay_mp_login(
        app=env.app,
        encrypted_data=payload.encrypted_data,
        tenant=env.tenant,
    )
    return LoginResponse(data=user_info)
