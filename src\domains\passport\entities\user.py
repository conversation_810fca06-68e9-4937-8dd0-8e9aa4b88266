# encoding: utf-8
# src/domains/passport/entities/user.py
# created: 2025-07-31 22:08:26

from typing import TYPE_CHECKING, Optional

import jwt
from loguru import logger
from pydantic import BaseModel

if TYPE_CHECKING:
    from src.databases.models.passport import PassportUser
    from src.domains.passport.entities import AppEntity, TenantEntity


class UserEntity(BaseModel):

    uid: str
    phone: str
    nickname: str
    avatar_url: str
    email: str

    current_app: Optional["AppEntity"] = None
    current_tenant: Optional["TenantEntity"] = None

    async def switch_tenant(self, tenant: "TenantEntity") -> None:
        self.current_tenant = tenant

    @property
    def jwt(self) -> str:
        if not self.current_app:
            # 需要生成jwt token的用户，必须需要有当前所在应用属性
            logger.warning("generate user jwt token error: current_app is None")
            return ""
        payload = dict(
            uid=self.uid,
            app_id=self.current_app.app_id,
            app_name=self.current_app.app_name,
            tenant_id=self.current_tenant.tenant_id if self.current_tenant else "",
            tenant_name=self.current_tenant.tenant_name if self.current_tenant else "",
            phone=self.phone,
            nickname=self.nickname,
            avatar=self.avatar_url,
        )
        token = jwt.encode(payload, key=self.current_app.app_secret, algorithm="HS256")
        return token

    @classmethod
    def create(
        cls,
        phone: str,
        app: "AppEntity",
        nickname: Optional[str] = None,
        email: Optional[str] = None,
        avatar_url: Optional[str] = None,
        tenant: Optional["TenantEntity"] = None,
    ) -> "UserEntity":
        return cls(
            uid=phone,
            phone=phone,
            nickname=nickname or f"手机用户{phone[-4:]}",
            avatar_url=avatar_url or "",
            email=email or "",
            current_app=app,
            current_tenant=tenant,
        )

    @classmethod
    async def from_model(cls, model: "PassportUser") -> "UserEntity":
        return cls(
            uid=model.uid,
            phone=model.phone,
            nickname=model.nickname if model.nickname else "",
            avatar_url=model.avatar_url if model.avatar_url else "",
            email=model.email if model.email else "",
            current_app=None,
            current_tenant=None,
        )
