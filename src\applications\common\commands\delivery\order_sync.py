# encoding: utf-8
# src/applications/common/commands/delivery/order_sync.py
# created: 2025-08-18 16:45:00

import json
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.databases.models.delivery import OrderStateEnum, SettleStateEnum
from src.domains.delivery.dto import ElemeAddressDTO, ElemeOrderDTO, SubjectDTO

from src.infrastructures.gateways.regions.gateway import region_gateway

if TYPE_CHECKING:
    from src.domains.delivery.messages import UnionOrderInfoDTO
    from src.infrastructures.gateways.bifrost import BifrostGateway
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway
    from src.repositories.delivery import DeliveryElemeOrderRepository, DeliveryOrderRepository


class OrderSyncCommandService:
    """订单同步命令服务"""

    def __init__(
        self,
        delivery_order_repository: "DeliveryOrderRepository",
        eleme_order_repository: "DeliveryElemeOrderRepository",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        bifrost_gateway: "BifrostGateway",
    ):
        self.delivery_order_repo = delivery_order_repository
        self.eleme_order_repo = eleme_order_repository
        self.eleme_gateway = eleme_union_delivery_gateway
        self.bifrost_gateway = bifrost_gateway

    async def sync_union_order(self, order_info: "UnionOrderInfoDTO") -> None:
        """
        同步联盟订单

        Args:
            order_info: 联盟订单信息
        """
        logger.info(f"开始同步联盟订单: {order_info.parent_order_id}")

        # 检查订单是否已存在且状态相同
        existing_order = await self.delivery_order_repo.get_order_by_id_state(
            str(order_info.parent_order_id),
            OrderStateEnum(order_info.order_state),
            SettleStateEnum(order_info.settle_state),
        )

        if existing_order:
            logger.info(f"订单状态未变化，跳过处理: {order_info.parent_order_id}")
            return

        # 解析 SID 信息
        if order_info.sid and order_info.sid.startswith("SID:"):
            source_info = await self.eleme_gateway.decode_sid(order_info.sid)
            order_info.source_info = source_info

        # 创建或更新订单
        await self.delivery_order_repo.create_or_update(order_info)
        logger.info(f"订单同步成功: {order_info.parent_order_id}")

    async def sync_eleme_order_detail(self, order_info: "UnionOrderInfoDTO") -> None:
        """
        同步饿了么订单详情

        Args:
            order_info: 联盟订单信息
        """
        order_id = str(order_info.parent_order_id)
        logger.info(f"开始同步饿了么订单详情: {order_id}")

        # 检查订单是否已存在且状态相同
        existing_order = await self.delivery_order_repo.get_order_by_id_state(
            order_id,
            OrderStateEnum(order_info.order_state),
            SettleStateEnum(order_info.settle_state),
        )

        if existing_order:
            logger.info(f"订单状态未变化，跳过处理: {order_id}")
            return

        # 获取订单详情
        try:
            data = await self.bifrost_gateway.query_orders([order_id])
        except Exception as e:
            logger.error(f"查询订单详情失败: order_id={order_id}, error={e}")
            # 将错误向上传递，让消费者处理重试逻辑
            raise

        # 安全地获取数据
        if not data or not isinstance(data, dict):
            logger.warning(f"订单详情响应格式异常: order_id={order_id}, response={data}")
            return

        # 检查是否有数据字段
        data_field = data.get("data")
        if not data_field or not isinstance(data_field, dict):
            logger.warning(f"订单详情数据为空: order_id={order_id}")
            return

        value_list = data_field.get("valueList", [])
        if not value_list:
            logger.warning(f"未获取到订单详情: {order_id}")
            return

        # 解析订单详情
        eleme_order = self._parse_eleme_order(order_id, value_list[0])

        # 创建或更新订单
        await self.eleme_order_repo.create_or_update(eleme_order)
        logger.info(f"饿了么订单详情同步成功: {order_id}")

    def _parse_eleme_order(self, order_id: str, order: dict) -> ElemeOrderDTO:
        """
        解析饿了么订单数据

        Args:
            order_id: 订单ID
            order: 订单原始数据

        Returns:
            ElemeOrderDTO: 解析后的订单数据
        """
        order_info = order.get("alscOrderModelInfo", {}).get("orderInfo", {})
        ext_info = order_info.get("extInfo", {})
        payment_order_info_list = order.get("alscOrderModelInfo", {}).get("paymentOrderInfoList", [])
        payment_order_info = payment_order_info_list[0] if payment_order_info_list else {}
        sub_order_info_list = order.get("alscOrderModelInfo", {}).get("subOrderInfoList", [])

        # 解析商品信息
        subjects = []
        for item in sub_order_info_list:
            subject = {
                "subject_name": item.get("subject"),
                "real_amount": item.get("realAmount"),
                "price": item.get("price"),
                "amount": item.get("amount"),
                "quantity": item.get("quantity"),
                "unit": item.get("unit"),
                "subsidy_amount": item.get("subsidyAmount"),
                "merchant_subsidy_amount": item.get("merchantSubsidyAmount"),
                "agent_subsidy_amount": item.get("agentSubsidyAmount"),
            }
            subjects.append(subject)

        # 解析支付信息
        payment_amount = payment_order_info.get("paymentAmount")
        trade_type = payment_order_info.get("extInfo", {}).get("tradeType")
        pay_time_str = payment_order_info.get("extInfo", {}).get("payTime")
        try:
            pay_time = datetime.strptime(pay_time_str, "%Y-%m-%d %H:%M:%S") if pay_time_str else None
        except ValueError:
            logger.warning(f"无法解析支付时间: {pay_time_str}")
            pay_time = None

        # 解析支付账号信息
        payee_account_no = ""
        payer_account_no = ""
        if trade_type == "ALIPAY":
            pp_pay_detail = json.loads(payment_order_info.get("extInfo", {}).get("ppPayDetail", "[]"))
            if pp_pay_detail:
                pp_pay_detail = pp_pay_detail[0]
                payee_account_no = pp_pay_detail.get("payeeAccountNo", "")
                payer_account_no = pp_pay_detail.get("payerAccountNo", "")

        # 解析其他信息
        buyer_uid = str(order_info.get("buyerUserId"))
        total_amount = order_info.get("totalAmount")
        merchant_subsidy_amount = order_info.get("merchantSubsidyAmount")
        agent_subsidy_amount = order_info.get("agentSubsidyAmount")
        real_amount = order_info.get("realAmount")
        biz_scene = order_info.get("bizScene")
        buyer_phone = ext_info.get("buyerBindPhone")
        user_tags = ext_info.get("userTags", "")
        address_info = ElemeAddressDTO.model_validate_json(ext_info.get("alscAddressInfo"))

        if address_info.alsc_district_id:
            gb_info = region_gateway.get_gb_info_by_alsc_district_id(address_info.alsc_district_id)
            if gb_info:
                # 更新地址数据，添加国标信息
                address_info.province_name = gb_info.province_name
                address_info.city_name = gb_info.city_name
                address_info.district_name = gb_info.district_name
            else:
                print(f"未找到alsc_district_id={address_info.alsc_district_id}对应的国标信息")

        return ElemeOrderDTO.model_validate(
            {
                "order_id": order_id,
                "biz_scene": biz_scene,
                "buyer_uid": buyer_uid,
                "buyer_phone": buyer_phone,
                "total_amount": total_amount,
                "merchant_subsidy_amount": merchant_subsidy_amount,
                "agent_subsidy_amount": agent_subsidy_amount,
                "real_amount": real_amount,
                "payment_amount": payment_amount,
                "trade_type": trade_type,
                "pay_time": pay_time,
                "payee_account_no": payee_account_no,
                "payer_account_no": payer_account_no,
                "address_info": address_info,
                "user_tag": user_tags,
                "subjects": [SubjectDTO.model_validate(s) for s in subjects],
            }
        )
