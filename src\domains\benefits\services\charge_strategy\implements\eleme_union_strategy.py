# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/charge_strategy/implements/eleme_union_strategy.py
# created: 2025-04-06 16:49:11
# updated: 2025-05-05 00:13:12

from datetime import datetime
from random import randint
from typing import TYPE_CHECKING

from loguru import logger
from tortoise.transactions import atomic

from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.databases.models.benefits import ChargeFuncEnum
from src.domains.benefits.dto import ChargeRecordDetailSchema
from src.infrastructures.errors import UnionBenefitsChargeRecordInvalidError, UnionBenefitsPurchaseNotFoundError
from src.repositories.benefits import PurchaseRepository
from src.utils.eleme.union_benefits import ElemeUnionBenefitsUtils

from ..strategy import ChargeStrategy
from ..strategy_factor import register_strategy

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsSkuChargeRecord

# INIT-发放中; EFFECTIVE:发放成功; USED:已核销/过期; CANCELED:发放失败/已撤销
TICKET_STATUS_MAP = {
    "INIT": RecordStatus.PROCESSING,
    "EFFECTIVE": RecordStatus.SUCCESS,
    "USED": RecordStatus.USED,
    "CANCELED": RecordStatus.FAILED,
}


@register_strategy(ChargeFuncEnum.ELEME_UNION)
class ElemeUnionChargeStrategy(ChargeStrategy):
    """饿了么联盟卡券充值策略"""

    async def _get_purchase_id(self, record: "BenefitsSkuChargeRecord") -> str:
        """获取联盟权益的 purchase_id"""
        logger.info(f"获取联盟权益, record_id: {record.id}, sku_id: {record.sku.id}")
        purchase_list = await PurchaseRepository.gets_available_by_sku(record.sku.id)
        if not purchase_list:
            logger.error(f"获取联盟权益失败, record_id: {record.id}")
            raise UnionBenefitsPurchaseNotFoundError

        logger.info(
            "获取联盟权益成功, record_id: {record_id}, purchase_list: {purchase_list}",
            record_id=record.id,
            purchase_list=[purchase.purchase_id for purchase in purchase_list],
        )

        # 随机负载
        idx = randint(0, len(purchase_list) - 1)
        return purchase_list[idx].purchase_id

    def _get_purchase_id_by_detail(self, record: "BenefitsSkuChargeRecord") -> str | None:
        """从 record.detail 中获取联盟权益的 purchase_id"""
        logger.info(f"获取联盟权益, record_id: {record.id}, detail: {record.detail}")
        if "deliver_request" in record.detail:
            return record.detail.get("deliver_request", {}).get("purchase_id")
        else:
            return None

    @atomic()
    async def charge(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """饿了么联盟卡券充值

        1. 调用饿了么联盟 API 进行充值;
        2. 变更 charge_record 的 status 成为 PROCESSING;
        """
        # step 1. 调用饿了么联盟 API 进行充值
        purchase_id = await self._get_purchase_id(record)
        ori_status = record.status
        request = dict(
            purchase_id=purchase_id,
            item_id=record.supplier_sku_code,
            mobile=record.account,
            outer_order_id=f"{record.charge_order_id}_{record.id}",
            outer_item_id=record.sku.code,
        )

        # 初始化 record detail
        request_time = datetime.now().isoformat()
        detail = ChargeRecordDetailSchema.model_validate({})
        detail.deliver_request = {**request, "timestamp": request_time}

        try:
            result = ElemeUnionBenefitsUtils.create_ticket(**request)
            deliver_response = result.model_dump()
            detail.deliver_response = {**deliver_response, "timestamp": datetime.now().isoformat()}

            logger.info(f"饿了么联盟卡券充值成功[{record.id}], 联盟充值结果: {result.model_dump()}")
            record.supplier_order_id = result.ticket_id
            record.detail = detail.model_dump()
            record.status = RecordStatus.PROCESSING
        except Exception as e:
            detail.deliver_response = {"error": str(e), "timestamp": datetime.now().isoformat()}

            logger.error(f"饿了么联盟卡券充值失败[{record.id}], 异常错误: {str(e)}")
            record.detail = detail.model_dump()
            record.status = RecordStatus.FAILED
        finally:
            # step 2. 变更 charge_record 的 status
            logger.info(
                "请求饿了么联盟充值完成, 变更 charge_record[{record_id}] 的 status: {ori_status} --> {to_status}",
                record_id=record.id,
                ori_status=ori_status,
                to_status=record.status,
            )
            record.detail = detail.model_dump()
            await record.save()

        return record

    @atomic()
    async def check_charge_status(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """检查联盟权益的充值状态

        1. 获取 record 的联盟权益的 purchase_id;
        2. 调用饿了么联盟 API 检查充值状态;
        3. 变更 charge_record 的 status 成为 SUCCESS 或 FAILED;
        """
        purchase_id = self._get_purchase_id_by_detail(record)
        if purchase_id is None:
            logger.error(f"饿了么联盟卡券状态查询失败[{record.id}], 没有找到 purchase_id, detail: {record.detail}")
            return record

        request = dict(
            ticket_id=record.supplier_order_id,
            purchase_id=purchase_id,
            item_id=record.sku.third_part_code,
            outer_order_id=f"{record.charge_order_id}_{record.id}",
        )
        request_time = datetime.now().isoformat()

        try:
            result = ElemeUnionBenefitsUtils.get_ticket_detail(**request)
            logger.info(f"饿了么联盟卡券状态查询结果[{record.id}]: {result.model_dump()}")
            detail = ChargeRecordDetailSchema.model_validate(record.detail)

            detail.check_list.append(
                {
                    "check_request": {**request, "timestamp": request_time},
                    "check_response": {**result.model_dump(), "timestamp": datetime.now().isoformat()},
                }
            )
            record.detail = detail.model_dump()
            if record.status == RecordStatus.PROCESSING and result.ticket_status == "EFFECTIVE":
                record.charged_at = datetime.now()
            record.status = TICKET_STATUS_MAP[result.ticket_status]
        except Exception as e:
            detail.check_list.append(
                {
                    "check_request": {**request, "timestamp": request_time},
                    "check_response": {"error": str(e), "timestamp": datetime.now().isoformat()},
                }
            )
            logger.error(f"饿了么联盟卡券状态查询失败[{record.id}], 异常错误: {str(e)}")
        finally:
            await record.save()

        return record

    @atomic()
    async def refund(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """饿了么联盟卡券退款

        1. 获取 record 的联盟权益的 purchase_id;
        2. 调用饿了么联盟 API 进行退款;
        3. 变更 charge_record 的 status 成为 REFUNDED;
        """

        purchase_id = self._get_purchase_id_by_detail(record)
        if purchase_id is None:
            logger.error(f"饿了么联盟卡券退款失败[{record.id}], 没有找到 purchase_id")
            raise UnionBenefitsChargeRecordInvalidError

        request = dict(
            ticket_id=record.supplier_order_id,
            item_id=record.sku.third_part_code,
            outer_order_id=f"{record.charge_order_id}_{record.id}",
            mobile=record.account,
            purchase_id=purchase_id,
        )
        request_time = datetime.now().isoformat()
        detail = ChargeRecordDetailSchema.model_validate(record.detail)
        detail.refund_request = {**request, "timestamp": request_time}

        try:
            result = ElemeUnionBenefitsUtils.refund_ticket(**request)
            detail.refund_response = {"result": result, "timestamp": datetime.now().isoformat()}
            record.status = RecordStatus.REFUNDED
        except Exception as e:
            detail.refund_response = {"error": str(e), "timestamp": datetime.now().isoformat()}
            logger.error(f"饿了么联盟卡券退款失败[{record.id}], 异常错误: {str(e)}")
        finally:
            record.detail = detail.model_dump()
            await record.save()

        return record
