import asyncio
import json
import re
import time
from functools import wraps
from typing import Dict, Generator, List, Optional, TypedDict, Union

import aiohttp
import requests


def retry_with_exponential_backoff(retries=3, initial_delay=1):
    """
    指数退避重试装饰器
    :param retries: 重试次数
    :param initial_delay: 初始延迟时间（秒）
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            delay = initial_delay
            last_exception = None

            for attempt in range(retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt == retries:
                        raise last_exception from e

                    time.sleep(delay)
                    delay *= 2

            raise last_exception

        return wrapper

    return decorator


class ModelConfig(TypedDict):
    url: str
    api_key: str
    models: Dict[str, str]


# 模型配置
MODEL_CONFIGS: Dict[str, ModelConfig] = {
    "chatglm": {
        "url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        "api_key": "ed3c51ffad1a03c3d33d8670d4c3f1be.Kz6lin2Ns9hsGfFF",
        "models": {
            "glm-4-plus": "glm-4-plus",
            "glm-4-air": "glm-4-air",
            "glm-4-flash": "glm-4-flash",
        },
    },
    "doubao": {
        "url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        "api_key": "f96fa388-1bd0-4c65-9e1b-060bc42b0009",
        "models": {"default": "doubao-1-5-lite-32k-250115", "deepseek-v3": "deepseek-v3-250324"},
    },
}


class LLMClient:
    def __init__(self, provider: str, model: str, temperature: float = 0.2, top_p: float = 0.3, max_tokens: int = 4000):
        """
        初始化LLM客户端
        :param provider: 提供商名称 (chatglm, deepseek等)
        :param model: 模型名称 (在对应提供商的models中定义的key)
        :param temperature: 控制输出的随机性，范围0-1，默认0.7
        :param top_p: 控制输出的多样性，范围0-1，默认0.95
        :param max_tokens: 控制输出的最大长度，默认2000
        """
        if provider not in MODEL_CONFIGS:
            raise ValueError(f"不支持的提供商: {provider}")

        config = MODEL_CONFIGS[provider]
        if model not in config["models"]:
            raise ValueError(f"该提供商不支持的模型: {model}")

        self.url = config["url"]
        self.api_key = config["api_key"]
        self.model = config["models"][model]
        self.provider = provider
        self.temperature = temperature
        self.top_p = top_p
        self.max_tokens = max_tokens

    def set_config(
        self, provider: str, model: str, temperature: float = 0.2, top_p: float = 0.3, max_tokens: int = 4000
    ):
        if provider not in MODEL_CONFIGS:
            raise ValueError(f"不支持的提供商: {provider}")

        config = MODEL_CONFIGS[provider]
        if model not in config["models"]:
            raise ValueError(f"该提供商不支持的模型: {model}")

        self.url = config["url"]
        self.api_key = config["api_key"]
        self.model = config["models"][model]
        self.provider = provider
        self.temperature = temperature
        self.top_p = top_p
        self.max_tokens = max_tokens

    def _make_request(self, url: str, headers: Dict, data: Dict, stream: bool = False):
        """实际发送请求的方法"""
        response = requests.post(url, headers=headers, json=data, stream=stream)
        if response.status_code != 200:
            raise Exception(f"API请求失败: {response.status_code} - {response.text}")
        return response

    @retry_with_exponential_backoff(retries=3, initial_delay=1)
    def _base_request(self, messages: List[Dict], stream: bool = False) -> Union[str, Generator[str, None, None]]:
        """基础请求方法"""
        data = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "max_tokens": self.max_tokens,
        }

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}

        response = self._make_request(self.url, headers, data, stream)
        # 非流式模式
        if not stream:
            try:
                return response.json()["choices"][0]["message"]["content"]
            except Exception as e:
                print(f"API响应错误: {response.text}")
                raise e

        # 流式模式
        def generate():
            try:
                for line in response.iter_lines():
                    if line:
                        line = line.decode("utf-8")
                        if line.startswith("data: "):
                            line = line[6:]
                            if line.strip() == "[DONE]":
                                break
                            try:
                                json_data = json.loads(line)
                                if json_data["choices"][0]["delta"].get("content"):
                                    yield json_data
                            except json.JSONDecodeError:
                                continue
            except Exception as e:
                print(f"流式传输错误: {str(e)}")
                raise e

        return generate()

    def chat(
        self,
        messages: List[Dict],
        stream: bool = False,
        temperature: float = None,
        top_p: float = None,
        max_tokens: int = None,
    ) -> Union[str, Generator[str, None, None]]:
        """
        聊天接口
        :param messages: 消息列表，格式为[{"role": "user", "content": "xxx"}]
        :param stream: 是否使用流式输出
        :param temperature: 控制输出的随机性，范围0-1
        :param top_p: 控制输出的多样性，范围0-1
        :param max_tokens: 控制输出的最大长度
        """
        # 如果提供了参数，则临时覆盖实例参数
        if temperature is not None:
            self.temperature = temperature
        if top_p is not None:
            self.top_p = top_p
        if max_tokens is not None:
            self.max_tokens = max_tokens

        return self._base_request(messages, stream)

    def complete(
        self, prompt: str, stream: bool = False, temperature: float = None, top_p: float = None, max_tokens: int = None
    ) -> Union[str, Generator[str, None, None]]:
        """
        单次补全接口
        :param prompt: 提示词
        :param stream: 是否使用流式输出
        :param temperature: 控制输出的随机性，范围0-1
        :param top_p: 控制输出的多样性，范围0-1
        :param max_tokens: 控制输出的最大长度
        """
        messages = [{"role": "user", "content": prompt}]
        return self.chat(messages, stream, temperature, top_p, max_tokens)

    async def async_complete(self, prompt: str) -> str:
        """
        异步单次补全接口
        :param prompt: 提示词
        :return: 补全结果
        """
        messages = [{"role": "user", "content": prompt}]
        data = {"model": self.model, "messages": messages, "stream": False}

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}

        async with aiohttp.ClientSession() as session:
            async with session.post(self.url, headers=headers, json=data) as response:
                result = await response.json()
                return result["choices"][0]["message"]["content"]

    def classify(self, text: str, categories: List[str], description: str = "", temperature: float = 0.2) -> str:
        """
        使用LLM进行文本分类
        :param text: 需要分类的文本
        :param categories: 可选的分类类别列表
        :param description: 分类任务的描述说明
        :param temperature: 温度参数，控制输出的随机性
        :return: 分类结果
        """
        prompt = f"""请将以下文本分类到给定的类别中。

分类说明：{description}

可选类别：
{chr(10).join(f'- {cat}' for cat in categories)}

待分类文本：
{text}

请直接返回对应的类别名称，不要添加任何解释。"""

        return self.chat(
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            stream=False,  # 确保返回字符串而不是生成器
        )

    @staticmethod
    def get_code_from_llm(response: str, lang: Optional[str] = "python"):
        """
        Extract Python code from the response
        response may be like this:
        好的，以下是生成的代码
        ```python or ```
        code i need
        ```
        请问还需要帮助吗
        """
        code_pattern = r"```{lang}(.*?)```".format(lang=lang)
        code_match = re.search(code_pattern, response, re.DOTALL)
        if code_match:
            return code_match.group(1)
        else:
            return response


if __name__ == "__main__":
    try:
        print("\n=== 测试 Chat ===")
        client = LLMClient("doubao", "default")

        print("\n1. 普通模式测试：")
        r = client.complete("你好，请简短介绍下自己")
        # response = asyncio.run(client.async_complete("你好，请简短介绍下自己"))
        print("响应:", r)
        print("\n2. 并发请求测试：")

        async def test_concurrent():
            tasks = [
                client.async_complete("讲个笑话"),
                client.async_complete("写一句诗"),
                client.async_complete("介绍下北京"),
            ]
            results = await asyncio.gather(*tasks)
            for i, result in enumerate(results, 1):
                print(f"\n请求 {i} 响应:", result)

        asyncio.run(test_concurrent())

        print("\n3. 流式模式测试：")
        print("响应: ", end="", flush=True)
        for chunk in client.complete("说一个简短的笑话", stream=True):
            print(chunk, end="", flush=True)
        print("\n\nDeepSeek Chat 测试完成")

        result = client.classify(
            text="还行吧！", categories=["正面评价", "负面评价", "中性评价"], description="对商品评论进行情感分类"
        )
        print(result)  # 输出: "正面评价"
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        raise e
