# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# core/middlewares/request_logging.py
# created: 2025-04-04 12:01:57
# updated: 2025-04-13 09:17:12

import time
import uuid

from fastapi import Request
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware

from src.infrastructures.logger import LogContext


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件, 自动为每个请求创建并注入请求ID, 并记录请求/响应日志"""

    async def dispatch(self, request: Request, call_next):
        """处理请求"""

        # 获取或生成请求ID
        request_id = request.headers.get("X-Request-ID")
        if not request_id:
            request_id = uuid.uuid4().hex
        LogContext.set("request_id", request_id)

        log_extra = {
            "method": request.method,
            "query": str(request.url.query),
            "request_id": request_id,
            "client_host": request.client.host if request.client else "unknown",
        }

        # 处理请求
        start_time = time.time()

        try:
            response = await call_next(request)
            process_time = time.time() - start_time

            # 记录响应信息
            log_extra["use_time"] = f"{process_time:.4f}s"
            if request.method != "HEAD":
                logger.info(f'"{request.method} {request.url} - {response.status_code}"', **log_extra)

            # 在响应头中添加请求ID
            response.headers["X-Request-ID"] = request_id
            return response

        except Exception as e:
            process_time = time.time() - start_time
            log_extra["use_time"] = f"{process_time:.4f}s"
            logger.exception(
                f'"{request.method} {request.url}"',
                **log_extra,
                error=str(e),
            )
            raise
