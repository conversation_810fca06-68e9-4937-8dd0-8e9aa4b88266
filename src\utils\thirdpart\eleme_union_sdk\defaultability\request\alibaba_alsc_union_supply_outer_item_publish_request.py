from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionSupplyOuterItemPublishRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.supply.outer.item.publish"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionSupplyOuterItemPublishOuterItemPublishRequest:
    def __init__(
        self,
        scene: str = None,
        outer_id: str = None,
        name: str = None,
        picture: str = None,
        origin_price: str = None,
        sell_price: str = None,
        promotion_price: str = None,
        quantity: int = None,
        sales_start_time: int = None,
        sales_end_time: int = None,
        state: int = None,
        extra: str = None,
    ):
        """
        业务场景
        """
        self.scene = scene
        """
            商品ID（唯一）
        """
        self.outer_id = outer_id
        """
            商品名称
        """
        self.name = name
        """
            图片
        """
        self.picture = picture
        """
            原价（元）
        """
        self.origin_price = origin_price
        """
            售价（元）
        """
        self.sell_price = sell_price
        """
            优惠价（最终，元）
        """
        self.promotion_price = promotion_price
        """
            数量（-1表示不限）
        """
        self.quantity = quantity
        """
            销售开始时间（毫秒）
        """
        self.sales_start_time = sales_start_time
        """
            销售截止时间（毫秒）
        """
        self.sales_end_time = sales_end_time
        """
            状态（0-未上架；1-上架）
        """
        self.state = state
        """
            扩展信息（JSON结构），包含价格信息和商品基础信息
        """
        self.extra = extra
