import json

from .scripts.dingtalk import (
    finish_ai_interaction,
    get_access_token,
    get_all_department_user_details,
    get_department_user_details,
    get_department_user_ids,
    prepare_ai_interaction,
    send_ai_interaction,
    update_ai_interaction,
)


def test_ai_interaction():
    """
    测试钉钉AI交互流程
    """
    # 客户端ID和密钥
    app_key = "dingpadaxolo4tb9mzw4"
    app_secret = "Ztmc4E1WQW03HEYMDOHm1u02BsoeXfRwtfsMuDBn_zkjlw0T3K54nmZgzhiv0gUQ"
    template_id = "e348bb94-c8c0-433c-b632-20da0fbb4656.schema"

    # 步骤1: 获取访问令牌
    print("步骤1: 获取访问令牌")
    token_result = get_access_token(app_key, app_secret)
    print(f"访问令牌结果: {token_result}")

    if "accessToken" not in token_result:
        print("获取访问令牌失败，退出测试")
        return

    access_token = token_result["accessToken"]
    print(f"成功获取访问令牌: {access_token}")

    # 步骤1.5: 获取部门用户ID列表
    print("\n步骤1.5: 获取部门用户ID列表")
    dept_id = 1  # 这里替换为实际的部门ID
    dept_users_result = get_department_user_ids(access_token, dept_id)
    print(f"部门用户ID列表结果: {dept_users_result}")

    # 步骤1.6: 获取部门用户详情
    print("\n步骤1.6: 获取部门用户详情")
    user_details = get_department_user_details(access_token, dept_id)
    print(f"部门用户详情结果: {user_details}")

    # 步骤1.7: 获取部门所有用户详情
    print("\n步骤1.7: 获取部门所有用户详情")
    all_users = get_all_department_user_details(access_token, dept_id)
    print(f"部门所有用户数量: {len(all_users)}")
    for i, user in enumerate(all_users[:3]):  # 只打印前3个用户信息
        print(f"用户{i+1}: {user.get('name')}, 手机: {user.get('mobile')}, unionid: {user.get('unionid')}")

    # 步骤2: 使用直接发送方法
    print("\n步骤2: 直接发送AI交互内容")
    direct_card_content = {
        "templateId": template_id,
        "cardData": {"title": "直接发送的测试标题", "desc": "这是直接发送的测试描述"},
    }

    send_result = send_ai_interaction(
        access_token=access_token,
        union_id="U6Eu30NBiiqThpii8KAYvscQiEiE",  # 这里应替换为实际的unionId
        content_type="basic_card_schema",
        content=json.dumps(direct_card_content),
    )
    print(f"直接发送AI交互结果: {send_result}")

    # 步骤3: 准备AI交互 (原步骤2)
    print("\n步骤3: 准备AI交互")
    card_content = {"templateId": template_id, "cardData": {"title": "测试标题", "desc": "这是一个测试描述"}}

    prepare_result = prepare_ai_interaction(
        access_token=access_token,
        union_id="U6Eu30NBiiqThpii8KAYvscQiEiE",  # 这里应替换为实际的unionId
        content_type="ai_card",
        content=json.dumps(card_content),
    )
    print(f"准备AI交互结果: {prepare_result}")

    if "conversationToken" not in prepare_result:
        print("准备AI交互失败，退出测试")
        return

    conversation_token = prepare_result["conversationToken"]
    print(f"成功获取会话令牌: {conversation_token}")

    # 步骤4: 更新AI交互 (原步骤3)
    print("\n步骤4: 更新AI交互")
    updated_card_content = {
        "templateId": template_id,
        "cardData": {"title": "更新后的测试标题", "desc": "这是更新后的测试描述"},
    }

    update_result = update_ai_interaction(
        access_token=access_token,
        conversation_token=conversation_token,
        content_type="ai_card",
        content=json.dumps(updated_card_content),
    )
    print(f"更新AI交互结果: {update_result}")

    # 步骤5: 完成AI交互 (原步骤4)
    print("\n步骤5: 完成AI交互")
    finish_result = finish_ai_interaction(access_token=access_token, conversation_token=conversation_token)
    print(f"完成AI交互结果: {finish_result}")

    print("\n测试完成")


# 运行测试
if __name__ == "__main__":
    test_ai_interaction()
