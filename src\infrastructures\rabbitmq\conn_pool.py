# encoding: utf-8
# <AUTHOR> <EMAIL>
# core/rabbitmq/conn_pool.py
# created: 2025-04-05 17:59:07
# updated: 2025-06-03 00:06:23

import asyncio
from contextlib import asynccontextmanager
from random import randint
from typing import TYPE_CHECKING, AsyncGenerator, List, Optional

import aio_pika
from aio_pika.abc import AbstractRobustChannel, AbstractRobustConnection
from loguru import logger

if TYPE_CHECKING:
    from .settings import RabbitmqSettings


class RabbitMQConnectionPool:
    """RabbitMQ连接池实现, 提供异步连接管理"""

    def __init__(self, config: "RabbitmqSettings", max_channels_per_connection: Optional[int] = None):
        self.config = config
        self.connections: List[AbstractRobustConnection] = []
        self.available_channels: List[AbstractRobustChannel] = []
        self.busy_channels: List[AbstractRobustChannel] = []
        self.connection_pool_size = config.pool_size
        self.max_channels_per_connection = max_channels_per_connection or config.max_channels_per_connection
        self.lock = asyncio.Lock()
        self.channel_lock = asyncio.Lock()
        self.initialized = False
        logger.info(
            f"🚀 RabbitMQ连接池初始化 - 连接数: {self.connection_pool_size}, 每连接最大通道: {self.max_channels_per_connection}"
        )

    async def initialize(self) -> None:
        """初始化连接池"""

        if self.initialized:
            return

        async with self.lock:
            if self.initialized:  # 双重检查锁定模式
                return

            for _ in range(self.connection_pool_size):
                conn = await self._create_connection()
                if conn:
                    self.connections.append(conn)

            self.initialized = True

        logger.info(f"RabbitMQ连接池初始化完成, 连接数: {len(self.connections)}")

    async def _create_connection(self) -> Optional[AbstractRobustConnection]:
        """创建单个RabbitMQ连接"""
        try:
            connection = await aio_pika.connect_robust(
                url=self.config.url,
                heartbeat=self.config.heartbeat,
                connection_timeout=self.config.connection_timeout,
            )
            logger.debug(f"✅ 创建RabbitMQ连接成功: {self.config.url}")
            return connection
        except Exception as e:
            logger.error(f"❌ RabbitMQ连接失败: {self.config.url}, 错误: {e}")
            return None

    async def _create_channel(self) -> Optional[AbstractRobustChannel]:
        """创建新的channel"""
        if not self.connections:
            logger.error("没有可用的连接创建channel")
            return None

        try:
            # 随机选择一个连接
            if not self.connections:
                return None
            connection = self.connections[randint(0, len(self.connections) - 1)]
            channel: AbstractRobustChannel = await connection.channel()  # type: ignore
            return channel
        except Exception as e:
            logger.error(f"创建channel失败: {e}")
            return None

    @asynccontextmanager
    async def get_connection(self) -> AsyncGenerator[AbstractRobustConnection, None]:
        """获取可用连接，使用异步上下文管理器确保资源正确释放"""
        if not self.initialized:
            await self.initialize()

        if not self.connections:
            raise RuntimeError("没有可用的RabbitMQ连接")

        try:
            connection = self.connections[randint(0, len(self.connections) - 1)]  # 随机选择一个连接, 简单负载均衡
            yield connection
        finally:
            pass

    @asynccontextmanager
    async def get_channel(self) -> AsyncGenerator[AbstractRobustChannel, None]:
        """获取一个通道，优先使用池中的可用channel"""
        if not self.initialized:
            await self.initialize()

        channel = None

        try:
            # 尝试从可用channel池中获取
            async with self.channel_lock:
                if self.available_channels:
                    channel = self.available_channels.pop()
                    self.busy_channels.append(channel)

            # 如果池中没有可用channel，创建新的
            if channel is None:
                channel = await self._create_channel()
                if channel is None:
                    raise RuntimeError("无法创建新的channel")

                async with self.channel_lock:
                    self.busy_channels.append(channel)

            yield channel

        finally:
            if channel is not None:
                async with self.channel_lock:
                    if channel in self.busy_channels:
                        self.busy_channels.remove(channel)

                    # 检查channel是否还有效，如果有效则放回池中
                    if not channel.is_closed and len(self.available_channels) < self.max_channels_per_connection * len(
                        self.connections
                    ):
                        self.available_channels.append(channel)
                    else:
                        # 如果channel已关闭或池已满，则关闭channel
                        try:
                            if not channel.is_closed:
                                await channel.close()
                        except Exception as e:
                            logger.warning(f"关闭channel时出错: {e}")

    async def close(self) -> None:
        """关闭所有连接和通道"""
        # 先关闭所有通道
        all_channels = self.available_channels + self.busy_channels

        for channel in all_channels:
            if not channel.is_closed:
                try:
                    await channel.close()
                except Exception as e:
                    logger.warning(f"关闭channel时出错: {e}")

        # 再关闭所有连接
        for conn in self.connections:
            if not conn.is_closed:
                try:
                    await conn.close()
                except Exception as e:
                    logger.warning(f"关闭连接时出错: {e}")

        self.available_channels = []
        self.busy_channels = []
        self.connections = []
        self.initialized = False
        logger.info("RabbitMQ连接池已关闭")
