import os
from typing import List, Union

import requests
from dotenv import load_dotenv


class EmbeddingClient:
    def __init__(
        self,
        api_key: str = "sk-drzlekjzjbhcrhwjlyhdkufalzgwyusepsnaydgyduqvoeun",
        model: str = "BAAI/bge-large-zh-v1.5",
    ):
        """
        初始化Embedding客户端

        Args:
            api_key: API密钥，如果为None则从环境变量中读取
            model: 模型名称，默认使用BAAI/bge-large-zh-v1.5
        """
        load_dotenv()  # 加载.env文件
        self.api_key = api_key or os.getenv("SILICONFLOW_APIKEY")
        if not self.api_key:
            raise ValueError("API密钥未提供，请在参数中传入或在.env文件中设置SILICONFLOW_APIKEY")
        self.model = model
        self.url = "https://api.siliconflow.cn/v1/embeddings"

    def get_embedding(self, text: Union[str, List[str]]) -> List[List[float]]:
        """
        获取文本的embedding向量

        Args:
            text: 输入文本，可以是字符串或字符串列表

        Returns:
            embedding向量列表

        Raises:
            ValueError: 如果API调用失败
        """
        payload = {"model": self.model, "input": text, "encoding_format": "float"}

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}

        response = requests.post(self.url, json=payload, headers=headers)

        if response.status_code != 200:
            raise ValueError(f"API调用失败: {response.text}")

        result = response.json()
        return [data["embedding"] for data in result["data"]]


if __name__ == "__main__":
    # 创建客户端实例，从环境变量获取API密钥
    client = EmbeddingClient()

    # 获取单个文本的embedding
    text = "测试文本"
    embedding = client.get_embedding(text)
    # print(embedding)
    # 获取多个文本的embedding
    texts = ["文本1", "文本2"]
    embeddings = client.get_embedding(texts)
    # print(embeddings)
