# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/schemas.py
# created: 2024-11-30 22:42:40
# updated: 2025-05-30 11:25:34

from typing import TYPE_CHECKING, Dict, List, Optional

from pydantic import BaseModel, Field

from src.databases.models.passport import PassportTenant
from src.domains.passport.entities import AppEntity, TenantEntity
from src.infrastructures.fastapi.response import BaseResponse


class BenefitsItem(BaseModel):
    code: str = Field(description="权益编码")
    name: str = Field(description="权益名称")
    category: str = Field(description="权益分类")


class BenefitsInfo(BaseModel):
    code: str = Field(description="权益编码")
    name: str = Field(description="权益名称")
    category: str = Field(description="权益分类")
    description: str = Field(description="权益描述")
    icon: str = Field(description="权益图标")
    price: float = Field(description="权益价格")
    original_price: float = Field(description="权益原价")


class AutoApplyBenefitsRequestPayload(BaseModel):
    product_code: str = Field(description="权益产品编码")
    agent_id: str = Field(description="AI agent ID")
    agent_user_id: str = Field(description="AI agent user ID")


class AutoApplyBenefitsBlindRequestPayload(BaseModel):
    agent_id: str = Field(description="AI agent ID")
    agent_user_id: str = Field(description="AI agent user ID")


class QuerySubscribeStateRequest(BaseModel):
    agent_id: str = Field(description="AI agent ID")
    agent_user_id: str = Field(description="AI agent user ID")


class ElemeChannelUrl(BaseModel):
    url: str = Field(..., description="Eleme渠道版访问链接")


class PassportEnv(BaseModel):
    app_id: str = Field(..., description="应用ID")
    tenant_id: Optional[str] = Field("", description="租户ID")


class OSSTokenResponseData(BaseModel):
    access_key_id: str = Field(..., description="阿里云临时AccessKeyId")
    access_key_secret: str = Field(..., description="阿里云临时AccessKeySecret")
    security_token: str = Field(..., description="阿里云STS临时Token")
    expiration: str = Field(..., description="Token过期时间（ISO8601格式）")
    policy: str = Field(..., description="OSS上传策略Base64编码")
    signature: str = Field(..., description="OSS上传签名")
    bucket: str = Field(..., description="OSS Bucket名称")
    endpoint: str = Field(..., description="OSS上传域名")


class Env(BaseModel):
    app: AppEntity
    tenant: Optional[TenantEntity] = None


ElemeChannelUrlResponse = BaseResponse[ElemeChannelUrl]
BenefitsListResponse = BaseResponse[List[BenefitsItem]]
OSSTokenResponse = BaseResponse[OSSTokenResponseData]
