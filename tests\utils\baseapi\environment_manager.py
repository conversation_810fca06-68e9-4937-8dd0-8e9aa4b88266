# encoding: utf-8
# tests/utils/baseapi/environment_manager.py
# created: 2025-08-02 10:35:00

import asyncio
import os
import sqlite3
import tempfile
import uuid
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, Optional

import redis.asyncio as redis
from loguru import logger

from src.containers.main import Container as ApplicationContainer


class TestEnvironmentConfig:
    """测试环境配置"""
    
    def __init__(self, environment_id: str):
        self.environment_id = environment_id
        self.temp_dir = Path(tempfile.mkdtemp(prefix=f"baseapi_test_{environment_id}_"))
        self.db_path = self.temp_dir / "test.db"
        self.redis_db = hash(environment_id) % 16  # 使用0-15的Redis数据库


class EnvironmentManager:
    """测试环境管理器
    
    负责创建、管理和销毁独立的测试环境，确保并行测试不互相干扰
    """
    
    def __init__(self):
        self._environments: Dict[str, TestEnvironmentConfig] = {}
        self._redis_pool: Optional[redis.ConnectionPool] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup_all()
    
    async def initialize(self):
        """初始化环境管理器"""
        # 创建Redis连接池
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self._redis_pool = redis.ConnectionPool.from_url(redis_url)
        logger.info("环境管理器初始化完成")
    
    @asynccontextmanager
    async def create_environment(self, test_name: str = None) -> AsyncGenerator[TestEnvironmentConfig, None]:
        """创建独立的测试环境
        
        Args:
            test_name: 测试名称，用于生成环境ID
            
        Yields:
            TestEnvironmentConfig: 测试环境配置
        """
        environment_id = f"{test_name or 'test'}_{uuid.uuid4().hex[:8]}"
        config = TestEnvironmentConfig(environment_id)
        
        try:
            # 初始化数据库
            await self._setup_database(config)
            
            # 初始化Redis
            await self._setup_redis(config)
            
            self._environments[environment_id] = config
            logger.info(f"创建测试环境: {environment_id}")
            
            yield config
            
        finally:
            # 清理环境
            await self._cleanup_environment(environment_id)
    
    async def _setup_database(self, config: TestEnvironmentConfig):
        """设置测试数据库"""
        # 创建SQLite数据库文件
        conn = sqlite3.connect(config.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS test_users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                email TEXT,
                tenant_id TEXT,
                app_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS test_sessions (
                id TEXT PRIMARY KEY,
                user_id INTEGER,
                token TEXT,
                expires_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES test_users (id)
            )
        """)
        conn.commit()
        conn.close()
        
        logger.debug(f"数据库初始化完成: {config.db_path}")
    
    async def _setup_redis(self, config: TestEnvironmentConfig):
        """设置测试Redis"""
        if not self._redis_pool:
            return
            
        try:
            redis_client = redis.Redis(connection_pool=self._redis_pool, db=config.redis_db)
            await redis_client.flushdb()  # 清空Redis数据库
            await redis_client.aclose()
            logger.debug(f"Redis初始化完成: db={config.redis_db}")
        except Exception as e:
            logger.warning(f"Redis初始化失败: {e}")
    
    async def _cleanup_environment(self, environment_id: str):
        """清理测试环境"""
        if environment_id not in self._environments:
            return
            
        config = self._environments[environment_id]
        
        try:
            # 清理Redis
            if self._redis_pool:
                try:
                    redis_client = redis.Redis(connection_pool=self._redis_pool, db=config.redis_db)
                    await redis_client.flushdb()
                    await redis_client.aclose()
                except Exception as e:
                    logger.warning(f"Redis清理失败: {e}")
            
            # 清理数据库文件
            if config.db_path.exists():
                config.db_path.unlink()
            
            # 清理临时目录
            if config.temp_dir.exists():
                config.temp_dir.rmdir()
                
            del self._environments[environment_id]
            logger.info(f"清理测试环境: {environment_id}")
            
        except Exception as e:
            logger.error(f"清理环境失败 {environment_id}: {e}")
    
    async def cleanup_all(self):
        """清理所有测试环境"""
        environment_ids = list(self._environments.keys())
        for env_id in environment_ids:
            await self._cleanup_environment(env_id)
        
        # 关闭Redis连接池
        if self._redis_pool:
            await self._redis_pool.aclose()
            self._redis_pool = None
        
        logger.info("所有测试环境已清理")
    
    def get_container_config(self, config: TestEnvironmentConfig) -> Dict[str, Any]:
        """获取容器配置"""
        return {
            "database": {
                "url": f"sqlite:///{config.db_path}",
                "echo": False,
            },
            "redis": {
                "url": f"redis://localhost:6379/{config.redis_db}",
            },
            "environment_id": config.environment_id,
        }
    
    def create_test_container(self, config: TestEnvironmentConfig) -> ApplicationContainer:
        """创建测试用的依赖注入容器"""
        container = ApplicationContainer()
        container.config.from_dict(self.get_container_config(config))
        container.wire(packages=["src"])
        return container


# 全局环境管理器实例
_environment_manager: Optional[EnvironmentManager] = None


async def get_environment_manager() -> EnvironmentManager:
    """获取全局环境管理器实例"""
    global _environment_manager
    if _environment_manager is None:
        _environment_manager = EnvironmentManager()
        await _environment_manager.initialize()
    return _environment_manager


async def cleanup_environment_manager():
    """清理全局环境管理器"""
    global _environment_manager
    if _environment_manager:
        await _environment_manager.cleanup_all()
        _environment_manager = None