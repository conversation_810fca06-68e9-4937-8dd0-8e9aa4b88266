# encoding: utf-8

import asyncio
import hashlib
from typing import Any, Dict, Optional, Set
from urllib.parse import urlparse

from loguru import logger
from playwright.async_api import APIResponse, Request, Route


class CachedResource:
    """缓存的资源对象"""

    def __init__(self, url: str, status: int, headers: dict[str, str], body: bytes):
        self.url = url
        self.status = status
        self.headers = headers
        self.body = body
        self.content_type = headers.get("content-type", "")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "url": self.url,
            "status": self.status,
            "headers": self.headers,
            "body": self.body,
            "content_type": self.content_type,
        }


class ResourceCache:
    """资源缓存管理器 - 专门用于CSS和JS文件缓存"""

    def __init__(self) -> None:
        self._cache: Dict[str, CachedResource] = {}
        self._cacheable_extensions = {".css", ".js"}
        self._cacheable_content_types = {
            "text/css",
            "application/javascript",
            "text/javascript",
            "application/x-javascript",
        }

        # 不缓存文件白名单（支持URL片段匹配）
        self._no_cache_whitelist = {
            # 常见的不需要缓存的文件模式
            "tdum.alibaba.com/dss.js",  # 数据统计服务文件
            "mmstat.com",
            "o2o-ad-log-gateway.alibaba.com",
        }

        # 图片占位符功能配置
        self.enable_image_placeholder = True  # 启用图片占位符
        self._image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".ico", ".tiff", ".tif"}
        # 1像素透明PNG的base64编码
        self._transparent_pixel_base64 = (
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        )

        # 图片白名单（不使用占位符的图片模式）
        self._image_whitelist = {
            # 常见的需要正常加载的图片模式
            "fourier.alibaba.com",
            "mmstat.com",
            "o2o-ad-log-gateway.alibaba.com",
        }

        # 并发安全控制
        self._cache_lock = asyncio.Lock()  # 保护缓存读写操作
        self._caching_urls: Set[str] = set()  # 正在缓存的URL，防止重复缓存

    def _generate_cache_key(self, url: str) -> str:
        """生成缓存键"""
        # 使用URL的MD5哈希作为缓存键，确保键的唯一性
        return hashlib.md5(url.encode("utf-8")).hexdigest()

    def is_basic_cacheable_resource(self, url: str) -> bool:
        """检查是否是基础的可缓存资源（仅检查文件扩展名）"""
        try:
            parsed_url = urlparse(url)
            path_lower = parsed_url.path.lower()
            return any(path_lower.endswith(ext) for ext in self._cacheable_extensions)
        except Exception:
            return False

    def has_url_parameters(self, url: str) -> bool:
        """检查URL是否包含查询参数"""
        try:
            parsed_url = urlparse(url)
            return len(parsed_url.query) > 0
        except Exception:
            return False

    def is_in_no_cache_whitelist(self, url: str) -> bool:
        """检查URL是否在不缓存白名单中"""
        try:
            url_lower = url.lower()
            for pattern in self._no_cache_whitelist:
                if pattern in url_lower:
                    return True
            return False
        except Exception:
            return False

    def add_no_cache_pattern(self, pattern: str) -> None:
        """动态添加不缓存模式到白名单"""
        self._no_cache_whitelist.add(pattern.lower())
        logger.info(f"📝 已添加不缓存模式: {pattern}")

    def remove_no_cache_pattern(self, pattern: str) -> None:
        """从白名单中移除不缓存模式"""
        self._no_cache_whitelist.discard(pattern.lower())
        logger.info(f"🗑️ 已移除不缓存模式: {pattern}")

    def get_no_cache_patterns(self) -> Set[str]:
        """获取当前的不缓存模式列表"""
        return self._no_cache_whitelist.copy()

    def is_in_image_whitelist(self, url: str) -> bool:
        """检查图片URL是否在白名单中（不使用占位符）"""
        try:
            url_lower = url.lower()
            for pattern in self._image_whitelist:
                if pattern in url_lower:
                    return True
            return False
        except Exception:
            return False

    # ===== 图片占位符功能 =====

    def is_image_without_params(self, url: str) -> bool:
        """检查是否是不带参数的图片请求"""
        try:
            parsed_url = urlparse(url)

            # 检查是否有查询参数
            if parsed_url.query:
                return False

            # 检查文件扩展名是否是图片类型
            path_lower = parsed_url.path.lower()

            for ext in self._image_extensions:
                if path_lower.endswith(ext):
                    return True

            return False

        except Exception:
            return False

    def get_image_placeholder_response(self) -> Dict[str, Any]:
        """获取图片占位符响应数据"""
        import base64

        image_data = base64.b64decode(self._transparent_pixel_base64)

        return {
            "status": 200,
            "headers": {
                "Content-Type": "image/png",
                "Content-Length": str(len(image_data)),
                "Cache-Control": "public, max-age=31536000",  # 缓存1年
            },
            "body": image_data,
        }

    def should_handle_as_image_placeholder(self, url: str) -> bool:
        """判断是否应该处理为图片占位符"""
        # 检查功能是否启用
        if not self.enable_image_placeholder:
            return False

        # 检查是否在白名单中（白名单中的图片不使用占位符）
        if self.is_in_image_whitelist(url):
            return False

        # 检查是否是不带参数的图片
        return self.is_image_without_params(url)

    def set_image_placeholder_enabled(self, enabled: bool) -> None:
        """启用/禁用图片占位符功能"""
        self.enable_image_placeholder = enabled

    def is_image_placeholder_enabled(self) -> bool:
        """检查图片占位符功能是否启用"""
        return self.enable_image_placeholder

    async def handle_route(
        self, route: Route, request: Request, enable_resource_cache: bool = True, current_page_url: Optional[str] = None
    ) -> None:
        """统一的路由处理器 - 分别处理图片和CSS/JS资源"""
        try:
            url = request.url

            # 检查是否启用缓存功能
            if not enable_resource_cache:
                await route.continue_()
                return

            # 检查是否来自指定页面路径，只处理来自该页面的资源请求
            # 优先使用传入的当前页面URL，否则使用referer头
            page_url = current_page_url or request.headers.get("referer", "")

            if "/adminiappsub/pages/h5/index" not in page_url:
                await route.continue_()
                return

            # 判断资源类型并分别处理
            if self.is_image_resource(url):
                await self._handle_image_resource(route, request, url)
            elif self.is_basic_cacheable_resource(url):
                await self._handle_css_js_resource(route, request, url)
            else:
                # 其他资源类型，正常处理
                await route.continue_()
                return

        except Exception:
            # 发生错误时继续正常请求
            try:
                await route.continue_()
            except Exception:
                pass  # 避免二次错误

    def is_image_resource(self, url: str) -> bool:
        """判断是否为图片资源"""
        try:
            parsed_url = urlparse(url)
            path_lower = parsed_url.path.lower()
            return any(path_lower.endswith(ext) for ext in self._image_extensions)
        except Exception:
            return False

    async def _handle_image_resource(self, route: Route, request: Request, url: str) -> None:
        """处理图片资源"""
        try:
            # 检查是否应该使用占位符
            if self.should_handle_as_image_placeholder(url):
                # 使用占位符
                placeholder_response = self.get_image_placeholder_response()
                await route.fulfill(
                    status=placeholder_response["status"],
                    headers=placeholder_response["headers"],
                    body=placeholder_response["body"],
                )
                return

            # 正常加载图片
            await route.continue_()

        except Exception:
            await route.continue_()

    async def _handle_css_js_resource(self, route: Route, request: Request, url: str) -> None:
        """处理CSS/JS资源"""
        try:
            # 1. 检查是否在不缓存白名单中
            if self.is_in_no_cache_whitelist(url):
                await route.continue_()
                return

            # 2. 检查URL是否有参数，有参数就跳过缓存，正常请求
            if self.has_url_parameters(url):
                await route.continue_()
                return

            # 3. 尝试从缓存获取资源
            cached_resource = await self.get_cached_resource(url)
            if cached_resource:
                # 有缓存时：直接返回缓存内容
                await route.fulfill(
                    status=cached_resource.status, headers=cached_resource.headers, body=cached_resource.body
                )
                return

            # 4. 缓存未命中，发送正常请求
            response = await route.fetch()

            # 5. 异步处理缓存决策（基于响应头）
            asyncio.create_task(self._cache_response_async(url, response))

            # 6. 立即返回响应给浏览器
            await route.fulfill(status=response.status, headers=dict(response.headers), body=await response.body())

        except Exception:
            await route.continue_()

    async def _send_background_request(self, route: Route, request: Request) -> None:
        """在后台发送原始请求（用于数据统计），不等待响应体"""
        try:
            # 发送请求但不等待响应体，仅用于触发服务器端的统计逻辑
            await route.fetch()
        except Exception as e:
            logger.debug(f"📊 后台请求失败: {request.url} - {e}")

    async def _cache_response_async(self, url: str, response: APIResponse) -> None:
        """异步缓存响应"""
        try:
            cached = await self.cache_resource(url, response)
            if not cached:
                logger.debug(f"📝 资源未被缓存: {url}")
        except Exception as e:
            logger.debug(f"📝 异步缓存失败: {url} - {e}")

    def _is_cacheable_by_response(self, response: APIResponse) -> bool:
        """根据响应头判断资源是否可缓存（基于HTTP缓存标准）"""
        try:
            # 1. 检查响应状态码
            if response.status != 200:
                return False

            headers = response.headers

            # 2. 检查Content-Type（必须是CSS或JS）
            content_type = headers.get("content-type", "").lower()
            if not any(ct in content_type for ct in self._cacheable_content_types):
                return False

            # 3. 检查Cache-Control头（优先级最高）
            cache_control = headers.get("cache-control", "").lower()
            if cache_control:
                # 明确禁止缓存
                if "no-cache" in cache_control or "no-store" in cache_control:
                    return False

                # 明确允许缓存
                if any(directive in cache_control for directive in ["public", "max-age", "immutable"]):
                    return True

                # 私有缓存也允许
                if "private" in cache_control:
                    return True

            # 4. 检查Expires头（如果没有Cache-Control）
            expires = headers.get("expires", "")
            if expires:
                # 如果是过去的时间，说明已过期，不缓存
                if "thu, 01 jan 1970" in expires.lower():
                    return False

                # 有有效的expires头，允许缓存
                return True

            # 5. 检查Last-Modified头（启发式缓存）
            last_modified = headers.get("last-modified", "")
            if last_modified:
                return True

            # 6. 默认情况：对于静态资源（CDN），如果没有明确禁止，允许缓存
            # 这是因为CSS/JS文件通常是静态资源，应该被缓存
            return True

        except Exception:
            return False

    async def get_cached_resource(self, url: str) -> Optional[CachedResource]:
        """获取缓存的资源（线程安全）"""
        cache_key = self._generate_cache_key(url)

        async with self._cache_lock:
            cached_resource = self._cache.get(cache_key)

            if cached_resource:
                return cached_resource

            return None

    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "total_size_bytes": sum(len(resource.body) for resource in self._cache.values()),
            "total_size_mb": sum(len(resource.body) for resource in self._cache.values()) / 1024 / 1024,
        }

    async def cache_resource(self, url: str, response: APIResponse) -> bool:
        """缓存资源（基于HTTP缓存标准，并发安全）"""

        # 检查是否正在被其他实例缓存，避免重复缓存
        async with self._cache_lock:
            if url in self._caching_urls:
                return False

            # 再次检查是否已经在缓存中（可能在等待锁的过程中被其他实例缓存了）
            cache_key = self._generate_cache_key(url)
            if cache_key in self._cache:
                return True  # 虽然不是这个实例缓存的，但资源已缓存，算成功

            # 标记为正在缓存
            self._caching_urls.add(url)

        try:
            # 基于响应头判断是否可缓存（利用网站本身的缓存设置）
            if not self._is_cacheable_by_response(response):
                return False

            # 获取响应体
            body = await response.body()

            # 创建缓存资源对象
            cached_resource = CachedResource(url=url, status=response.status, headers=dict(response.headers), body=body)

            # 存储到缓存（需要再次获取锁）
            async with self._cache_lock:
                self._cache[cache_key] = cached_resource
            return True

        except Exception:
            return False
        finally:
            # 无论成功失败，都要从正在缓存集合中移除
            async with self._cache_lock:
                self._caching_urls.discard(url)

    async def clear_cache(self) -> None:
        """清空缓存（线程安全）"""
        async with self._cache_lock:
            self._cache.clear()
            self._caching_urls.clear()  # 同时清空正在缓存的URL集合
            logger.info("🧹 资源缓存已清空")


# 全局资源缓存实例
resource_cache = ResourceCache()
