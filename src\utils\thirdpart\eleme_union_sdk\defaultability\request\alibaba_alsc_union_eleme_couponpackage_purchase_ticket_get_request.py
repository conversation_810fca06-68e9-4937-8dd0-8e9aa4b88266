from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.couponpackage.purchase.ticket.get"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetCouponPackagePurchaseTicketDetailRequest:
    def __init__(
        self,
        ticket_id: str = None,
        purchase_id: str = None,
        item_id: str = None,
        outer_order_id: str = None,
        ext_info: str = None,
    ):
        """
        凭证ID
        """
        self.ticket_id = ticket_id
        """
            采购单ID
        """
        self.purchase_id = purchase_id
        """
            商品ID
        """
        self.item_id = item_id
        """
            外部订单ID（淘宝）
        """
        self.outer_order_id = outer_order_id
        """
            扩展字段（json结构）
        """
        self.ext_info = ext_info
