# encoding: utf-8
# src/infrastructures/gateways/bifrost/errors.py
# created: 2025-07-30 10:16:27


class BifrostError(Exception):
    """饿了么权益发放SDK异常基类"""

    def __init__(self, resp_code: str, message: str):
        self.resp_code = resp_code
        self.message = message
        super().__init__(f"[{resp_code}] {message}")


class BifrostSystemError(BifrostError):
    """系统错误，可重试"""

    pass


class BifrostBusinessError(BifrostError):
    """业务错误，不可重试"""

    pass
