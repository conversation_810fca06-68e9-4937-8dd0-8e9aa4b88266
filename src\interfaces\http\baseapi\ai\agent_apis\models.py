from typing import List, Optional

from pydantic import BaseModel


class Context(BaseModel):
    # 这里可以根据实际需求添加更多上下文字段
    pass


class Coupon(BaseModel):
    similarity: float
    title: str
    picture: str
    description: str
    h5_url: str
    scheme_url: str


class CouponListResponse(BaseModel):
    total: int
    couponlist: List[Coupon]


class RecommendReason(BaseModel):
    content: str


class ShopItem(BaseModel):
    picture: str
    price: str
    title: str
    origin_price: Optional[str] = None


class Store(BaseModel):
    shop_id: Optional[str] = None
    category: str
    indistinct_monthly_sales: str
    h5_url: str
    service_rating: str
    shop_logo: str
    title: str
    delivery_distance: int
    delivery_price: str
    delivery_time: int
    recommend_description: str
    recommend_reasons: List[RecommendReason]
    skus: Optional[List[ShopItem]] = None


class StoreListResponse(BaseModel):
    total: int
    storelist: List[Store]
