# encoding: utf-8
# src/infrastructures/fastapi/middlewares/application_error.py
# created: 2025-07-30 18:40:51

from fastapi import Request
from fastapi.responses import JSONResponse

from src.infrastructures.errors import BusinessError
from src.infrastructures.exceptions import ApplicationError


async def application_error_handler(request: Request, exc: Exception) -> JSONResponse:
    if isinstance(exc, ApplicationError) or isinstance(exc, BusinessError):
        return JSONResponse(status_code=200, content={"message": exc.message, "code": exc.code})
    return JSONResponse(status_code=500, content={"message": "Internal server error"})
