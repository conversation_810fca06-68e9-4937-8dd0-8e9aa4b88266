# encoding: utf-8
# src/infrastructures/gateways/wifi_master/client.py
# created: 2025-07-30 10:54:36

from typing import Union

import aiohttp
from loguru import logger

from .errors import WifiMasterSSOError
from .settings import WifiMasterSettings


class WifiMasterGateway:

    def __init__(self, config: Union[dict, WifiMasterSettings]):
        if isinstance(config, dict):
            self.config = WifiMasterSettings(**config)
        else:
            self.config = config

    async def parse_auth_code(self, auth_code) -> str:
        """解析wifi万能钥匙auth_code, 返回用户手机号"""
        wifi_master_url = self.config.sso_url
        app_id = self.config.app_id

        async with aiohttp.ClientSession() as session:
            logger.info(f"请求WiFi万能钥匙SSO接口: {wifi_master_url}")
            async with session.post(wifi_master_url, json={"appId": app_id, "code": auth_code}) as response:
                data = await response.json()
                logger.debug(f"WiFi万能钥匙SSO接口返回数据: {data}")

                if data.get("errcode") == "fail":
                    logger.error(f"WiFi万能钥匙SSO错误: {data.get('error')}")
                    raise WifiMasterSSOError

                return data.get("data", {}).get("phone")
