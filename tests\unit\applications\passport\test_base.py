# encoding: utf-8
# tests/unit/applications/passport/test_base.py
# created: 2025-08-02 10:50:00

"""测试基础测试类的 fixtures"""

import pytest
from unittest.mock import MagicMock

from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from tests.unit.applications.passport.base import BasePassportUnitTest

# Rebuild models to handle forward references
UserEntity.model_rebuild()
AppEntity.model_rebuild()
TenantEntity.model_rebuild()


class TestBasePassportUnitTest(BasePassportUnitTest):
    """测试基础测试类的各种 fixtures 是否正常工作"""

    @pytest.mark.asyncio
    async def test_mock_user_repository(self, mock_user_repository):
        """测试 UserRepository Mock"""
        # 测试默认返回值
        assert await mock_user_repository.get_by_uid("any_uid") is None
        assert await mock_user_repository.get_by_phone("any_phone") is None

        # 测试创建用户
        result = await mock_user_repository.create_by_phone("13800138000")
        assert result.id == 1
        assert result.uid == "test_uid"

        # 验证方法被调用
        mock_user_repository.get_by_uid.assert_called_once_with("any_uid")
        mock_user_repository.get_by_phone.assert_called_once_with("any_phone")

    @pytest.mark.asyncio
    async def test_mock_app_repository(self, mock_app_repository):
        """测试 AppRepository Mock"""
        assert await mock_app_repository.get_by_appid("any_appid") is None
        assert await mock_app_repository.get_by_id(1) is None

        mock_app_repository.get_by_appid.assert_called_once_with("any_appid")
        mock_app_repository.get_by_id.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_mock_tenant_repository(self, mock_tenant_repository):
        """测试 TenantRepository Mock"""
        assert await mock_tenant_repository.get_by_tenant_id("any_tenant") is None
        assert await mock_tenant_repository.gets_by_appid("app_id") == []

        mock_tenant_repository.get_by_tenant_id.assert_called_once_with("any_tenant")
        mock_tenant_repository.gets_by_appid.assert_called_once_with("app_id")

    @pytest.mark.asyncio
    async def test_mock_user_repository_relations(self, mock_user_repository):
        """测试 UserRepository 中的用户关系相关方法 Mock"""
        user = MagicMock()
        app = MagicMock()

        assert await mock_user_repository.get_user_relations(user, app) == []
        assert await mock_user_repository.get_user_relation(user, app, "tenant_id") is None

        result = await mock_user_repository.get_or_create_user_relation(user, "app_id", "tenant_id")
        assert result.id == 1

        await mock_user_repository.remove_user_relation("uid", "app_id", "tenant_id")
        mock_user_repository.remove_user_relation.assert_called_once_with("uid", "app_id", "tenant_id")

    @pytest.mark.asyncio
    async def test_mock_redis(self, mock_redis):
        """测试 Redis Mock"""
        # 测试默认返回值
        assert await mock_redis.get("any_key") is None
        assert await mock_redis.set("key", "value") is True
        assert await mock_redis.delete("key") == 1
        assert await mock_redis.expire("key", 60) is True
        assert await mock_redis.ttl("key") == -2

        # 验证方法被调用
        mock_redis.get.assert_called_once_with("any_key")
        mock_redis.set.assert_called_once_with("key", "value")

    @pytest.mark.asyncio
    async def test_mock_sms_gateway(self, mock_sms_gateway):
        """测试 SMS 网关 Mock"""
        assert await mock_sms_gateway.send_sms("13800138000", "SMS_CODE", {"code": "123456"}) is True
        mock_sms_gateway.send_sms.assert_called_once_with("13800138000", "SMS_CODE", {"code": "123456"})

    @pytest.mark.asyncio
    async def test_mock_wechat_gateway(self, mock_wechat_gateway):
        """测试微信小程序网关 Mock"""
        result = await mock_wechat_gateway.code2session("auth_code")
        assert result["openid"] == "test_openid"
        assert result["session_key"] == "test_session_key"

        phone = await mock_wechat_gateway.get_phone_number("encrypted_data", "iv", "session_key")
        assert phone == "13800138000"

    @pytest.mark.asyncio
    async def test_mock_dingtalk_gateway(self, mock_dingtalk_gateway):
        """测试钉钉网关 Mock"""
        token = await mock_dingtalk_gateway.get_user_token("auth_code")
        assert token == "access_token"

        userinfo = await mock_dingtalk_gateway.get_userinfo("access_token")
        assert userinfo.union_id == "test_union_id"
        assert userinfo.nick == "测试用户"
        assert userinfo.mobile == "13800138000"

    def test_sample_user_entity(self, sample_user_entity):
        """测试示例用户实体 fixture"""
        assert isinstance(sample_user_entity, UserEntity)
        assert sample_user_entity.uid == "test_uid_123"
        assert sample_user_entity.phone == "13800138000"
        assert sample_user_entity.nickname == "测试用户"

    def test_sample_app_entity(self, sample_app_entity):
        """测试示例应用实体 fixture"""
        assert isinstance(sample_app_entity, AppEntity)
        assert sample_app_entity.app_id == "test_app_001"
        assert sample_app_entity.app_name == "测试应用"
        assert sample_app_entity.app_secret == "test_secret_key"

    def test_sample_tenant_entity(self, sample_tenant_entity, sample_app_entity):
        """测试示例租户实体 fixture"""
        assert isinstance(sample_tenant_entity, TenantEntity)
        assert sample_tenant_entity.tenant_id == "test_tenant_001"
        assert sample_tenant_entity.tenant_name == "测试租户"
        assert sample_tenant_entity.app == sample_app_entity
