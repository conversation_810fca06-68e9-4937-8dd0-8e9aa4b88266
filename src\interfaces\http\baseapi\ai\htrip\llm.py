import json
from typing import Dict, List, Optional

from ..utils.base import LLMClient


class HTripLLMClient(LLMClient):
    def __init__(self, provider: str, model: str, temperature: float = 0.2, top_p: float = 0.3, max_tokens: int = 4000):
        super().__init__(provider, model, temperature, top_p, max_tokens)

    def classify(self, prompt: str) -> Dict[str, str]:
        """
        对用户输入进行意图分类
        :param prompt: 用户输入的文本
        :return: 包含type和keyword的字典，type为分类结果，keyword为关键词
        """
        system_prompt = """你是一个分类助手，需要对用户的输入进行意图分类。
        你需要返回一个JSON格式的结果，包含两个字段：
        - type: 从["点餐", "领优惠券", "其他"]中选择一个
        - keyword: 提取用户输入中的关键词
        
        示例：
        用户输入：我想吃麻辣烫
        返回：{"type": "点餐", "keyword": "麻辣烫"}
        
        用户输入：有没有超市优惠券
        返回：{"type": "领优惠券", "keyword": "超市"}

        用户输入：我想吃麻辣烫，有没有相关的红包
        返回：{"type": "领优惠券", "keyword": "麻辣烫"}
        """

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": prompt}]

        try:
            response = self.chat(messages)
            # 解析返回的JSON字符串为字典
            json_str = LLMClient.get_code_from_llm(response, "json")
            result = json.loads(json_str)
            return {"type": result.get("type", "其他"), "keyword": result.get("keyword", "")}
        except Exception:
            # 如果解析失败，返回默认值
            return {"type": "其他", "keyword": ""}

    def generate_store_response(self, user_prompt: str, stores: List[Dict]) -> str:
        """
        生成店铺推荐回复
        :param user_prompt: 用户输入的文本
        :param stores: 店铺信息列表
        :return: 生成的回复文本
        """
        system_prompt = """你是一个智能酒店助手，需要根据用户的点餐需求和找到的店铺信息，生成一个友好的回复。
        回复需要：
        1. 总结找到的店铺类型和数量
        2. 提到最受欢迎或最具特色的店铺
        3. 如果店铺有特色菜品（SKU），可以推荐1-2个最具代表性的
        4. 提醒用户可以通过扫描电视上的二维码查看详细信息和下单
        5. 语气要自然友好，避免机械化的表达
        6. 注意返回中不要有任何链接、表格、格式标签或其他无法朗读的内容，确保内容可以被朗读
        7. 请将回复内容控制在20字以内，简明扼要。
        
        示例回复：
        "为您找到了3家评分很高的火锅店，其中川味火锅最受欢迎，月售超过1000单。他们家的招牌毛肚（38元）和特色虾滑（28元）都很不错。您可以扫描电视上的二维码查看完整的菜单并下单。"
        """

        user_prompt = f"""
        用户问题：{user_prompt}
        
        找到的店铺信息：
        {stores}
        
        请生成合适的回复。重点关注用户的具体需求，并结合店铺的评分、月售、配送时间和特色菜品来给出建议。
        """

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

        return self.chat(messages)

    def generate_store_not_found_response(self, user_prompt: str) -> str:
        """
        生成未找到店铺的回复
        :param user_prompt: 用户输入的文本
        :return: 生成的回复文本
        """
        system_prompt = """你是一个智能酒店助手，需要在没有找到合适店铺时，生成一个友好的回复。
        回复需要：
        1. 表达歉意
        2. 建议用户尝试其他搜索关键词
        3. 语气要自然友好
        4. 请将回复内容控制在20字以内，简明扼要。
        
        示例回复：
        "抱歉没有找到符合您要求的店铺。您可以尝试搜索'中餐'、'快餐'或'火锅'等分类，我来帮您查找。"
        """

        user_prompt = f"用户搜索词：{user_prompt}"

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

        return self.chat(messages)

    def generate_coupon_response(self, user_prompt: str, coupons: List[Dict]) -> str:
        """
        生成优惠券推荐回复
        :param user_prompt: 用户输入的文本
        :param coupons: 优惠券信息列表
        :return: 生成的回复文本
        """
        system_prompt = """你是一个智能酒店助手，需要根据用户的需求和找到的优惠券信息，生成一个友好的回复。
        回复需要：
        1. 总结找到的优惠券数量和类型
        2. 提到最大优惠力度（如果有）
        3. 提醒用户可以通过扫描电视上的二维码领取使用
        4. 语气要自然友好，避免机械化的表达
        5. 请将回复内容控制在20字以内，简明扼要。
        
        示例回复：
        "为您找到了3张美食优惠券，最高可减30元，还可以叠加其他优惠。扫描电视上的二维码就能领取使用了。"
        """

        user_prompt = f"""
        用户问题：{user_prompt}
        
        找到的优惠券信息：
        {coupons}
        
        请生成合适的回复。
        """

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

        return self.chat(messages)

    def generate_coupon_not_found_response(self, user_prompt: str) -> str:
        """
        生成未找到优惠券的回复
        :param user_prompt: 用户输入的文本
        :return: 生成的回复文本
        """
        system_prompt = """你是一个智能酒店助手，需要在没有找到合适优惠券时，生成一个友好的回复。
        回复需要：
        1. 表达歉意
        2. 提供其他建议
        3. 语气要自然友好
        4. 请将回复内容控制在20字以内，简明扼要。
        
        示例回复：
        "抱歉，当前没有找到相关的优惠券。您可以稍后再来查看，或者直接浏览商家的优惠活动。"
        """

        user_prompt = f"用户搜索词：{user_prompt}"

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

        return self.chat(messages)

    def generate_guide_response(self, user_prompt: str) -> str:
        """
        生成引导性回复
        :param user_prompt: 用户输入的文本
        :return: 生成的回复文本
        """
        system_prompt = """你是一个智能酒店助手，需要引导用户使用点餐或领券功能。
        回复需要：
        1. 说明可以提供的服务（点餐和领券）
        2. 给出具体的使用建议
        3. 语气要自然友好
        4. 请将回复内容控制在20字以内，简明扼要。
        
        示例回复：
        "我可以帮您点外卖或者领取优惠券。如果想点外卖，告诉我想吃什么类型的美食；如果想领优惠券，我可以帮您找到当前可用的优惠券。"
        """

        user_prompt = f"用户输入：{user_prompt}"

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}]

        return self.chat(messages)


if __name__ == "__main__":
    # 初始化客户端
    client = HTripLLMClient("chatglm", "glm-4-plus")  # 或其他支持的提供商和模型

    # 使用示例
    result = client.classify("我想吃麻辣烫")
    print(result)
    # 预期输出: {"type": "点餐", "keyword": "麻辣烫"}

    result = client.classify("有没有超市优惠券")
    print(result)
    # 预期输出: {"type": "领优惠券", "keyword": "超市"}

    result = client.classify("今天天气真好")
    # 预期输出: {"type": "其他", "keyword": ""}
    print(result)
