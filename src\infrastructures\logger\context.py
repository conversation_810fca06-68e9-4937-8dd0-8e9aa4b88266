# encoding: utf-8
# src/infrastructures/logger/context.py
# created: 2025-08-14 14:32:00

from contextvars import ContextVar
from typing import Any, Dict, Optional


class LogContext:
    """简化的日志上下文管理

    使用 ContextVar 实现线程/协程安全的上下文存储
    支持设置默认值和动态上下文
    """

    _vars: Dict[str, ContextVar] = {}
    _defaults: Dict[str, Any] = {}

    @classmethod
    def set(cls, key: str, value: Any, default: bool = False) -> None:
        """设置上下文值

        Args:
            key: 上下文键
            value: 上下文值
            default: 是否设置为默认值
        """
        if key not in cls._vars:
            cls._vars[key] = ContextVar(key, default=None)

        cls._vars[key].set(value)

        if default:
            cls._defaults[key] = value

    @classmethod
    def get(cls, key: str, default: Any = None) -> Optional[Any]:
        """获取上下文值

        Args:
            key: 上下文键
            default: 默认值

        Returns:
            上下文值，如果不存在则返回默认值
        """
        if key not in cls._vars:
            return cls._defaults.get(key, default)

        value = cls._vars[key].get()
        if value is not None:
            return value

        return cls._defaults.get(key, default)

    @classmethod
    def get_all(cls) -> Dict[str, Any]:
        """获取所有上下文

        Returns:
            包含所有上下文键值对的字典
        """
        result = {}

        # 合并默认值和当前值
        all_keys = set(cls._vars.keys()) | set(cls._defaults.keys())

        for key in all_keys:
            value = cls.get(key)
            if value is not None:
                result[key] = value

        return result

    @classmethod
    def clear(cls, key: Optional[str] = None) -> None:
        """清除上下文

        Args:
            key: 要清除的键，如果为None则清除所有
        """
        if key:
            if key in cls._vars:
                cls._vars[key].set(None)
            if key in cls._defaults:
                del cls._defaults[key]
        else:
            # 清除所有上下文
            for var in cls._vars.values():
                var.set(None)
            cls._defaults.clear()

    @classmethod
    def update(cls, **kwargs) -> None:
        """批量更新上下文

        Args:
            **kwargs: 要更新的键值对
        """
        for key, value in kwargs.items():
            cls.set(key, value)
