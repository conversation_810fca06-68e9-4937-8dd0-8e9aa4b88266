from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbcpaRefundOrderGetRequest(BaseRequest):

    def __init__(
        self,
        date_type: int = None,
        end_date: str = None,
        page_size: int = None,
        page_number: int = None,
        start_date: str = None,
        pid: str = None,
    ):
        """
        时间维度，1.订单结算时间 2.维权创建时间 3.维权完成时间 4更新时间
        """
        self._date_type = date_type
        """
            查询结束时间
        """
        self._end_date = end_date
        """
            每页返回数据大小，默认20，最大返回50
        """
        self._page_size = page_size
        """
            页码，默认第一页，取值范围1~50
        """
        self._page_number = page_number
        """
            查询开始时间
        """
        self._start_date = start_date
        """
            推广位pid
        """
        self._pid = pid

    @property
    def date_type(self):
        return self._date_type

    @date_type.setter
    def date_type(self, date_type):
        if isinstance(date_type, int):
            self._date_type = date_type
        else:
            raise TypeError("date_type must be int")

    @property
    def end_date(self):
        return self._end_date

    @end_date.setter
    def end_date(self, end_date):
        if isinstance(end_date, str):
            self._end_date = end_date
        else:
            raise TypeError("end_date must be str")

    @property
    def page_size(self):
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        if isinstance(page_size, int):
            self._page_size = page_size
        else:
            raise TypeError("page_size must be int")

    @property
    def page_number(self):
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        if isinstance(page_number, int):
            self._page_number = page_number
        else:
            raise TypeError("page_number must be int")

    @property
    def start_date(self):
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        if isinstance(start_date, str):
            self._start_date = start_date
        else:
            raise TypeError("start_date must be str")

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.kbcpa.refund.order.get"

    def to_dict(self):
        request_dict = {}
        if self._date_type is not None:
            request_dict["date_type"] = convert_basic(self._date_type)

        if self._end_date is not None:
            request_dict["end_date"] = convert_basic(self._end_date)

        if self._page_size is not None:
            request_dict["page_size"] = convert_basic(self._page_size)

        if self._page_number is not None:
            request_dict["page_number"] = convert_basic(self._page_number)

        if self._start_date is not None:
            request_dict["start_date"] = convert_basic(self._start_date)

        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
