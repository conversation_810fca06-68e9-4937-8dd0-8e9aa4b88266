# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/tools/dingtalk.py
# created: 2025-04-02 16:53:21
# updated: 2025-05-26 11:02:31

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from redis.asyncio import Redis

from src.infrastructures import errors

from ...schemas import BaseResponse

router = APIRouter(tags=["tools"])


@router.get("/dingtalk/suit_ticket")
@inject
async def get_dingtalk_suit_ticket(redis: Redis = Depends(Provide["redis0"])):
    """
    获取钉钉Suitkey
    """
    suite_ticket = await redis.get("ddSuiteTicket")

    if not suite_ticket:
        raise errors.DingtalkSuitTicketNotFoundError
    return BaseResponse(data=suite_ticket)
