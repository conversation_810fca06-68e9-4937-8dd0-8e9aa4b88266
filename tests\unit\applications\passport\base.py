# encoding: utf-8
# tests/unit/applications/passport/base.py
# created: 2025-08-02 10:35:00

"""基础测试类和公共固件"""

from typing import AsyncGenerator
from unittest.mock import AsyncMock, MagicMock

import pytest
from redis.asyncio import Redis

from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from src.infrastructures.gateways.dingtalk.gateways import DingtalkGateway
from src.infrastructures.gateways.aliyun.sms.client import AliyunSmsGateway as SmsGateway
from src.infrastructures.gateways.wechat.wechat_mp import WechatMpGateway
from src.repositories.passport import (
    AppRepository,
    TenantRepository,
    UserRepository,
)


class BasePassportUnitTest:
    """Passport 模块单元测试基类，提供通用的 mock fixtures"""

    @pytest.fixture
    def mock_user_repository(self) -> AsyncMock:
        """Mock UserRepository"""
        mock = AsyncMock(spec=UserRepository)

        # 设置默认返回值
        mock.get_by_uid.return_value = None
        mock.get_by_phone.return_value = None
        mock.create_by_phone.return_value = MagicMock(id=1, uid="test_uid")
        mock.create_by_dingtalk_user.return_value = MagicMock(id=1, uid="test_uid")
        mock.create_by_wechat_user.return_value = MagicMock(id=1, uid="test_uid")
        mock.save.return_value = None

        # 添加用户关系相关方法的 mock
        mock.get_user_relations.return_value = []
        mock.get_user_relation.return_value = None
        mock.get_or_create_user_relation.return_value = MagicMock(id=1)
        mock.remove_user_relation.return_value = None

        return mock

    @pytest.fixture
    def mock_app_repository(self) -> AsyncMock:
        """Mock AppRepository"""
        mock = AsyncMock(spec=AppRepository)

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.get_by_appid.return_value = None
        mock.get_by_app_secret.return_value = None

        return mock

    @pytest.fixture
    def mock_tenant_repository(self) -> AsyncMock:
        """Mock TenantRepository"""
        mock = AsyncMock(spec=TenantRepository)

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.get_by_tenant_id.return_value = None
        mock.gets_by_appid.return_value = []

        return mock

    @pytest.fixture
    def mock_redis(self) -> AsyncMock:
        """Mock Redis 客户端"""
        mock = AsyncMock(spec=Redis)

        # 设置默认返回值为 async 方法
        mock.get = AsyncMock(return_value=None)
        mock.set = AsyncMock(return_value=True)
        mock.delete = AsyncMock(return_value=1)
        mock.expire = AsyncMock(return_value=True)
        mock.ttl = AsyncMock(return_value=-2)  # 表示键不存在

        return mock

    @pytest.fixture
    def mock_sms_gateway(self) -> AsyncMock:
        """Mock SMS 网关"""
        mock = AsyncMock(spec=SmsGateway)

        # 设置默认返回值
        mock.send_sms.return_value = True

        return mock

    @pytest.fixture
    def mock_wechat_gateway(self) -> AsyncMock:
        """Mock 微信小程序网关"""
        mock = AsyncMock(spec=WechatMpGateway)

        # 设置默认返回值
        mock.code2session.return_value = {"openid": "test_openid", "session_key": "test_session_key", "unionid": None}
        mock.get_phone_number.return_value = "13800138000"

        return mock

    @pytest.fixture
    def mock_dingtalk_gateway(self) -> AsyncMock:
        """Mock 钉钉网关"""
        mock = AsyncMock(spec=DingtalkGateway)

        # 设置默认返回值
        mock.get_user_token.return_value = "access_token"
        mock_userinfo = MagicMock()
        mock_userinfo.union_id = "test_union_id"
        mock_userinfo.open_id = "test_open_id"
        mock_userinfo.nick = "测试用户"
        mock_userinfo.avatar_url = "http://avatar.url"
        mock_userinfo.mobile = "13800138000"
        mock_userinfo.email = "<EMAIL>"
        mock_userinfo.state_code = "86"
        mock.get_userinfo.return_value = mock_userinfo

        return mock

    @pytest.fixture
    def sample_user_entity(self) -> UserEntity:
        """创建示例用户实体"""
        return UserEntity(
            uid="test_uid_123",
            phone="13800138000",
            nickname="测试用户",
            avatar_url="https://example.com/avatar.jpg",
            email="<EMAIL>",
        )

    @pytest.fixture
    def sample_app_entity(self) -> AppEntity:
        """创建示例应用实体"""
        return AppEntity(
            id=1,
            app_id="test_app_001",
            app_name="测试应用",
            app_secret="test_secret_key",
            wechat_app_id="wx_test_123",
            wechat_app_secret="wx_secret",
            dingtalk_app_id="dt_test_123",
            dingtalk_app_secret="dt_secret",
        )

    @pytest.fixture
    def sample_tenant_entity(self, sample_app_entity) -> TenantEntity:
        """创建示例租户实体"""
        return TenantEntity(tenant_id="test_tenant_001", tenant_name="测试租户", app=sample_app_entity)
