import base64
import hashlib
import hmac
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from aliyunsdkcore.client import AcsClient
from aliyunsdksts.request.v20150401 import AssumeRoleRequest
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from src.infrastructures.config_compat import config

from ..schemas import OSSTokenResponse, OSSTokenResponseData

router = APIRouter(tags=["OSS"])


class GetTokenParams(BaseModel):
    bucket_name: Optional[str] = Field(None, description="桶名称")


@router.get("/token", response_model=OSSTokenResponse, name="获取OSS临时Token")
def get_oss_token(params: GetTokenParams = Query(...)) -> OSSTokenResponse:
    oss_conf = config.thirdpart.aliyun.oss
    client = AcsClient(oss_conf.access_key_id, oss_conf.access_key_secret, "cn-shanghai")
    request = AssumeRoleRequest.AssumeRoleRequest()
    request.set_accept_format("JSON")
    request.set_RoleArn(oss_conf.role_arn)
    request.set_RoleSessionName(oss_conf.role_session_name)

    bucket = params.bucket_name or "hicaspian-customer-info"
    try:
        response = client.do_action_with_exception(request)
        if response is None:
            raise HTTPException(status_code=500, detail="获取STS Token失败")
        res_dict = json.loads(response.decode())
        credentials = res_dict["Credentials"]
        # 生成policy
        expire_time = 300  # 5分钟
        now = datetime.utcnow()
        expiration = (now + timedelta(seconds=expire_time)).strftime("%Y-%m-%dT%H:%M:%SZ")
        policy_dict = {
            "expiration": expiration,
            "conditions": [["content-length-range", 0, 1048576000], ["starts-with", "$key", ""]],
        }
        policy = base64.b64encode(json.dumps(policy_dict).encode()).decode()
        # 生成signature
        h = hmac.new(credentials["AccessKeySecret"].encode(), policy.encode(), hashlib.sha1)
        signature = base64.encodebytes(h.digest()).strip().decode()
        endpoint = f"{bucket}.oss-cn-shanghai.aliyuncs.com"
        return OSSTokenResponse(
            data=OSSTokenResponseData(
                access_key_id=credentials["AccessKeyId"],
                access_key_secret=credentials["AccessKeySecret"],
                security_token=credentials["SecurityToken"],
                expiration=credentials["Expiration"],
                policy=policy,
                signature=signature,
                bucket=bucket,
                endpoint=endpoint,
            )
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取STS Token失败: {str(e)}") from e
