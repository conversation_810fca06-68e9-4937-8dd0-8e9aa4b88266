from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionRetailItempromotionQueryRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.retail.itempromotion.query"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionRetailItempromotionQueryRetailItemPromotionQueryRequest:
    def __init__(self, session_id: str = None, page_number: int = None, page_size: int = None):
        """
        会话ID（查询第一页为空，从第二页开始赋值，取值来自第一页返回结果）
        """
        self.session_id = session_id
        """
            请求页（从1开始）
        """
        self.page_number = page_number
        """
            每页数（1~20）
        """
        self.page_size = page_size
