# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/tools/__init__.py
# created: 2025-03-23 22:09:33
# updated: 2025-04-14 12:07:28

from fastapi import APIRouter

from .benefits import router as benefits_router
from .delivery import router as delivery_router
from .dingtalk import router as dingtalk_router

router = APIRouter(tags=["工具"])

router.include_router(dingtalk_router)
router.include_router(benefits_router)
router.include_router(delivery_router)
