# encoding: utf-8
# src/infrastructures/databases/redis.py
# created: 2025-07-28 02:03:34

from typing import Union

from redis.asyncio import Redis, from_url

from .settings import DatabaseSettings


class RedisManager:

    def __init__(self, config: Union["DatabaseSettings", dict]):
        self.config = config if isinstance(config, DatabaseSettings) else DatabaseSettings.model_validate(config)
        self._redis = from_url(f"{self.config.redis_uri}/31")

    @property
    def client(self) -> Redis:
        return self._redis

    async def close(self):
        await self._redis.close()
