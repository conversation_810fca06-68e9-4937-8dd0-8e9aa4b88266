# encoding: utf-8
# src/infrastructures/gateways/bifrost/schemas.py
# created: 2025-07-30 10:16:27

import json
from typing import Optional, Union

import toml
from loguru import logger
from pydantic import BaseModel, Field, model_validator

from src.utils.rsa_util import RSAUtil


class BifrostRightsResult(BaseModel):
    code: str = Field(validation_alias="respCode")
    out_trade_no: str = Field(..., validation_alias="orderId")
    trade_no: str = Field(..., validation_alias="tradeNo")
    detail: str = Field(..., validation_alias="detail")


class RightsGrantResult(BaseModel):
    success: bool = Field(True, description="是否成功")
    code: Optional[str] = Field("", description="响应码")
    message: Optional[str] = Field("", description="响应消息")
    out_trade_no: Optional[str] = Field("", description="商户订单号")
    trade_no: Optional[str] = Field("", description="交易号")
    response: dict = Field(..., description="响应数据")


class RightsOrderDetail(BaseModel):
    schedule_no: str = Field(..., description="饿了么发放流水号", validation_alias="scheduleNo")
    grant_result: str = Field(
        ...,
        description="权益发放结果, ungranted - 未发放, granted - 已发放, grantFaild - 发放失败",
        validation_alias="grantResult",
    )
    grant_time: Optional[str] = Field(
        "",
        description="权益发放时间,仅grantResult为granted时有值, 格式:yyyy-MM-dd HH:mm:ss",
        validation_alias="grantTime",
    )


class RightsOrderResult(BaseModel):
    out_trade_no: str = Field(..., description="商户订单号", validation_alias="outTradeNo")
    trade_no: str = Field(..., description="饿了么受理此笔权益发放申请唯一流水号", validation_alias="tradeNo")
    mobile: str = Field(..., description="用户手机号", validation_alias="mobile")
    eleme_accepted_time: str = Field(
        ..., description="饿了么受理时间, 格式:yyyy-MM-dd HH:mm:ss", validation_alias="elemeAcceptedTime"
    )
    apply_result: str = Field(
        ...,
        description="权益发放结果, accepted - 接受申请待处理, processed - 已处理, faild - 其他原因失败",
        validation_alias="applyResult",
    )
    detail: list[RightsOrderDetail] = Field(..., description="权益发放结果详情")


class RightsQueryResultData(BaseModel):
    out_trade_no: str = Field(..., description="商户订单号", validation_alias="outTradeNo")
    trade_no: str = Field(..., description="饿了么受理此笔权益发放申请唯一流水号", validation_alias="tradeNo")
    mobile: str = Field(..., description="用户手机号", validation_alias="mobile")
    eleme_accepted_time: str = Field(
        ..., description="饿了么受理时间, 格式:yyyy-MM-dd HH:mm:ss", validation_alias="elemeAcceptedTime"
    )
    apply_result: str = Field(
        ...,
        description="权益发放结果, accepted - 接受申请待处理, processed - 已处理, faild - 其他原因失败",
        validation_alias="applyResult",
    )
    detail: list[RightsOrderDetail] = Field(..., description="权益发放结果详情")


class RightsQueryResult(BaseModel):
    req_id: str = Field(..., description="请求ID", validation_alias="reqId")
    resp_code: str = Field(..., description="响应码", validation_alias="respCode")
    decrypt_data: Optional[RightsQueryResultData] = Field(None, description="解密数据", validation_alias="decryptData")
    data: Optional[str] = Field(None, description="响应数据", validation_alias="data")
    timestamp: int = Field(..., description="时间戳", validation_alias="timestamp")
    sign: str = Field(..., description="签名", validation_alias="sign")

    def decrypt_with_key(self, private_key: str) -> None:
        """解密 data 字段并填充到 decrypt_data"""
        if self.data:
            try:
                decrypted = json.loads(RSAUtil.decrypt(self.data, private_key))
                self.decrypt_data = RightsQueryResultData(**decrypted)
            except Exception as e:
                logger.warning(f"解密失败: {self.data}, error: {e}")


class RightsApplyResultData(BaseModel):
    out_trade_no: str = Field(..., description="商户订单号", validation_alias="outTradeNo")
    trade_no: str = Field(..., description="饿了么受理此笔权益发放申请唯一流水号", validation_alias="tradeNo")


class RightsApplyResult(BaseModel):
    req_id: str = Field(..., description="请求ID", validation_alias="reqId")
    resp_code: str = Field(..., description="响应码", validation_alias="respCode")
    data: Optional[str] = Field(None, description="响应数据", validation_alias="data")
    decrypt_data: Optional[RightsApplyResultData] = Field(None, description="解密数据", validation_alias="decryptData")
    timestamp: int = Field(..., description="时间戳", validation_alias="timestamp")
    sign: str = Field(..., description="签名", validation_alias="sign")

    def decrypt_with_key(self, private_key: str) -> None:
        """解密 data 字段并填充到 decrypt_data"""
        if self.data:
            try:
                decrypted = json.loads(RSAUtil.decrypt(self.data, private_key))
                self.decrypt_data = RightsApplyResultData(**decrypted)
            except Exception as e:
                logger.warning(f"解密失败: {self.data}, error: {e}")
