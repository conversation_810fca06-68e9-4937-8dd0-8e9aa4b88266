# encoding: utf-8
# src/domains/activity/entities/activity.py
# created: 2025-07-29 10:46:59

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from pydantic import BaseModel

from ..schemas import ActivityLinks

if TYPE_CHECKING:
    from src.databases.models.delivery import DeliveryPage
    from src.infrastructures.gateways.bifrost import BifrostGateway
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway


class ActivityEntity(BaseModel):

    id: int
    name: str
    description: str
    code: str
    access_url: str
    union_active_id: str
    union_zone_pid: str

    created_at: datetime
    updated_at: datetime

    custom_behavior: Optional[str] = ""

    @classmethod
    async def from_delivery_page(cls, deliver_page: "DeliveryPage") -> "ActivityEntity":
        return cls(
            id=deliver_page.id,
            name=deliver_page.name,
            description=deliver_page.description,
            code=deliver_page.code,
            access_url=deliver_page.url,
            union_active_id=deliver_page.union_active_id,
            union_zone_pid=deliver_page.union_zone_pid,
            created_at=deliver_page.created_at,
            updated_at=deliver_page.updated_at,
            custom_behavior=deliver_page.custom_behavior,
        )

    async def get_links_with_eleme_miniapp(
        self, bifrost_client: "BifrostGateway", union_gateway: "ElemeUnionDeliveryGateway", extra_info: dict
    ) -> ActivityLinks:
        if self.access_url != "":
            # 获取微信小程序相关信息
            wechat_shortlink = await bifrost_client.get_applet_short_link(self.access_url)
            wechat_schema = await bifrost_client.get_applet_schema(self.access_url)
            return ActivityLinks.model_validate(
                {
                    "sid": "direct_url",
                    "h5_promotion": {"h5_url": self.access_url},
                    "wx_promotion": {
                        "h5_short_link": wechat_shortlink,
                        "scheme_url": wechat_schema,
                    },
                }
            )
        links = await union_gateway.get_union_activity_url(self.union_active_id, self.union_zone_pid, extra_info)
        return ActivityLinks.model_validate(links.model_dump())
