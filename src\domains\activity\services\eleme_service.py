# encoding: utf-8
# src/domains/activity/services/eleme_service.py
# created: 2025-07-29 18:30:30

from typing import Optional

from src.utils.eleme.channels import ElemeChannelsUtils


class ElemeActivityService:

    def build_channel_params(
        self,
        eleme_channel: str,
        mobile: str,
        user_open_id: str,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
    ) -> dict:
        """构建渠道参数"""
        channel_params = ElemeChannelsUtils.generate_channels_params(eleme_channel, mobile, user_open_id)
        if latitude and longitude:
            channel_params["latitude"] = latitude
            channel_params["longitude"] = longitude
        return channel_params
