# encoding: utf-8
# src/interfaces/consumers/benefits/order_notice.py
# created: 2025-04-06 19:36:31

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.domains.benefits.messages import BenefitsOrderNoticeMessage, BenefitsOrderNoticeMessageContent
from src.infrastructures.rabbitmq.consumers import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import OrderNoticeCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class BenefitsOrderNoticeConsumer(BaseConsumer):
    """权益订单通知消费者"""

    exchange_name = "benefits.topic"
    queue_name = "benefits_order_notice"
    routing_key = "benefits.order_notice"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        order_notice_service: "OrderNoticeCommandService",
    ):
        self.order_notice_service = order_notice_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = BenefitsOrderNoticeMessage.model_validate_json(message.body)
        payload: BenefitsOrderNoticeMessageContent = msg.payload
        logger.info(f"BenefitsOrderNoticeConsumer 收到消息: {payload.order_id}")

        await self.order_notice_service.notify_order(payload.order_id)

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 订单通知失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "BenefitsOrderNoticeConsumer 回滚: 消息[{message_id}] 通知失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
