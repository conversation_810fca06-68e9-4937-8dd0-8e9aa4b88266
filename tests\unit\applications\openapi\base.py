# encoding: utf-8
# tests/unit/applications/openapi/base.py
# created: 2025-08-02 14:55:00

"""OpenAPI 应用层测试基础类和公共固件"""

from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import Request
from redis.asyncio import Redis

# 避免复杂的依赖链，使用最小化导入


class BaseOpenapiUnitTest:
    """OpenAPI 应用层单元测试基类，提供通用的 mock fixtures"""

    @pytest.fixture
    def mock_customer_repository(self) -> AsyncMock:
        """Mock CustomerRepository"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.get_by_token.return_value = None
        mock.get_by_ak.return_value = None
        mock.create.return_value = MagicMock(id=1, token="test_token")
        mock.save.return_value = None

        return mock

    @pytest.fixture
    def mock_benefits_repository(self) -> AsyncMock:
        """Mock BenefitsRepository"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_customer_id.return_value = []
        mock.get_by_id.return_value = None
        mock.create.return_value = MagicMock(id=1)
        mock.save.return_value = None
        mock.update_balance.return_value = None

        return mock

    @pytest.fixture
    def mock_pages_repository(self) -> AsyncMock:
        """Mock PagesRepository"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.get_by_activity_id.return_value = None
        mock.create.return_value = MagicMock(id=1)
        mock.save.return_value = None

        return mock

    @pytest.fixture
    def mock_product_order_repository(self) -> AsyncMock:
        """Mock ProductOrderRepository"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.create.return_value = MagicMock(id=1, order_no="test_order_001")
        mock.save.return_value = None

        return mock

    @pytest.fixture
    def mock_redis(self) -> AsyncMock:
        """Mock Redis 客户端"""
        mock = AsyncMock(spec=Redis)

        # 设置默认返回值为 async 方法
        mock.get = AsyncMock(return_value=None)
        mock.set = AsyncMock(return_value=True)
        mock.setex = AsyncMock(return_value=True)
        mock.delete = AsyncMock(return_value=1)
        mock.expire = AsyncMock(return_value=True)
        mock.ttl = AsyncMock(return_value=-2)  # 表示键不存在
        mock.exists = AsyncMock(return_value=0)

        return mock

    @pytest.fixture
    def mock_bifrost_gateway(self) -> AsyncMock:
        """Mock Bifrost 网关"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_activities.return_value = []
        mock.get_activity_detail.return_value = None

        return mock

    @pytest.fixture
    def mock_eleme_union_delivery_gateway(self) -> AsyncMock:
        """Mock 饿了么联盟网关"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_activities.return_value = []
        mock.get_activity_detail.return_value = None

        return mock

    @pytest.fixture
    def mock_eleme_aixincan_gateway(self) -> AsyncMock:
        """Mock 饿了么爱心餐网关"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_aixincan_info.return_value = {"status": "success", "data": {}}
        mock.submit_order.return_value = {"status": "success", "order_id": "test_order"}

        return mock

    @pytest.fixture
    def mock_wifimaster_gateway(self) -> AsyncMock:
        """Mock WiFiMaster 网关"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_activities.return_value = []
        mock.get_activity_detail.return_value = None

        return mock

    @pytest.fixture
    def mock_benefits_product_service(self) -> AsyncMock:
        """Mock Benefits Product Service"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.get_available_products.return_value = []
        mock.check_stock.return_value = True
        mock.deduct_stock.return_value = True

        return mock

    @pytest.fixture
    def mock_delivery_page_service(self) -> AsyncMock:
        """Mock Delivery Page Service"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_by_id.return_value = None
        mock.create_page.return_value = MagicMock(id=1)

        return mock

    @pytest.fixture
    def mock_eleme_activity_service(self) -> AsyncMock:
        """Mock Eleme Activity Service"""
        mock = AsyncMock()

        # 设置默认返回值
        mock.get_activities.return_value = []
        mock.get_activity_detail.return_value = None

        return mock

    @pytest.fixture
    def mock_config(self) -> Dict[str, Any]:
        """Mock 配置对象"""
        return {
            "openapi": {
                "token_expire_seconds": 3600,
                "aksk_expire_seconds": 7200,
            },
            "redis": {
                "cache_prefix": "test:",
                "default_expire": 300,
            },
            "eleme": {
                "timeout": 30,
                "retry_times": 3,
            },
        }

    @pytest.fixture
    def mock_request(self) -> MagicMock:
        """Mock FastAPI Request"""
        mock = MagicMock(spec=Request)
        mock.headers = {}
        mock.query_params = {}
        mock.path_params = {}
        return mock

    @pytest.fixture
    def sample_customer_data(self) -> Dict[str, Any]:
        """创建示例客户数据"""
        return {
            "id": 1,
            "code": "test_customer_001",
            "name": "测试客户",
            "description": "测试客户描述",
            "app_id": "test_app_001",
            "tenant_id": "test_tenant_001",
            "balance": 10000,
        }

    @pytest.fixture
    def sample_benefit_product_data(self) -> Dict[str, Any]:
        """创建示例权益产品数据"""
        return {
            "id": 1,
            "code": "benefit_001",
            "name": "测试权益产品",
            "type": "coupon",
            "description": "测试权益产品描述",
            "detail": {"test": "data"},
            "enabled": True,
            "sale_price": 1000,
            "stock": 100,
            "customer_id": 1,
        }

    @pytest.fixture
    def sample_activity_data(self) -> Dict[str, Any]:
        """创建示例活动数据"""
        return {
            "id": 1,
            "name": "测试活动",
            "description": "测试活动描述",
            "code": "activity_001",
            "access_url": "https://example.com/activity",
            "union_active_id": "union_001",
            "union_zone_pid": "zone_001",
        }
