{"data": {"description": "最高可领6元红包", "end_time": 1739988000, "id": "12042", "link": {"alipay_mini_url": "alipays://platformapi/startapp?appId=2021001110676437&page=plugin-private%3A%2F%2F2021003183669766%2Fpages%2Fwh-coupon-guide%2Findex%3Fscene%3D516e2a81999347e9ad6baaca03afeefe", "alipay_promotion": {"alipay_scheme_url": "alipays://platformapi/startapp?appId=2021001110676437&page=plugin-private%3A%2F%2F2021003183669766%2Fpages%2Fwh-coupon-guide%2Findex%3Fscene%3D516e2a81999347e9ad6baaca03afeefe", "app_id": "2021001110676437", "app_path": "plugin-private%3A%2F%2F2021003183669766%2Fpages%2Fwh-coupon-guide%2Findex%3Fscene%3D516e2a81999347e9ad6baaca03afeefe", "h5_url": "https://render.alipay.com/p/s/i/?scheme=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FappId%3D2021001110676437%26page%3Dplugin-private%253A%252F%252F2021003183669766%252Fpages%252Fwh-coupon-guide%252Findex%253Fscene%253D516e2a81999347e9ad6baaca03afeefe"}, "app_promotion": {}, "full_taobao_word": "98 HU7405   2:/biNteRcf8xT₴十最高可领6元红包", "h5_promotion": {"tj_h5_url": "https://r.ele.me/midd#wx?id=stkxs25m&scene=516e2a81999347e9ad6baaca03afeefe"}, "taobao_promotion": {"app_id": "8251537", "app_path": "plugin-private://2021003183669766/pages/wh-coupon-guide/index?scene=516e2a81999347e9ad6baaca03afeefe", "h5_url": "https://m.duanqu.com/?_ariver_appid=8251537&page=plugin-private%3A%2F%2F2021003183669766%2Fpages%2Fwh-coupon-guide%2Findex%3Fscene%3D516e2a81999347e9ad6baaca03afeefe", "scheme_url": "tbopen://m.taobao.com/tbopen/index.html?&action=ali.open.nav&module=h5&h5Url=https%3A%2F%2Fm.duanqu.com%2F%3F_ariver_appid%3D8251537%26page%3Dplugin-private%253A%252F%252F2021003183669766%252Fpages%252Fwh-coupon-guide%252Findex%253Fscene%253D516e2a81999347e9ad6baaca03afeefe"}, "taobao_word": "￥biNteRcf8xT￥/ HU7405", "tb_scheme_url": "tbopen://m.taobao.com/tbopen/index.html?&action=ali.open.nav&module=h5&h5Url=https%3A%2F%2Fm.duanqu.com%2F%3F_ariver_appid%3D8251537%26page%3Dplugin-private%253A%252F%252F2021003183669766%252Fpages%252Fwh-coupon-guide%252Findex%253Fscene%253D516e2a81999347e9ad6baaca03afeefe", "wx_appid": "wxece3a9a4c82f58c9", "wx_path": "ad-bdlm-sub/pages/wh-coupon-guide/index?scene=516e2a81999347e9ad6baaca03afeefe", "wx_promotion": {"wx_app_id": "wxece3a9a4c82f58c9", "wx_path": "ad-bdlm-sub/pages/wh-coupon-guide/index?scene=516e2a81999347e9ad6baaca03afeefe"}}, "picture": "https://img.alicdn.com/imgextra/i2/6000000000587/O1CN01d2QJJS1GCtVjxePr0_!!6000000000587-0-o2oad.jpg", "start_time": 1739966400, "title": "零售夜间活动", "expanded_content": "外卖配送，果蔬商超，京东便利店，盒马生鲜，百康药房，叮当快药，养生堂药店，饮料,甜品,奶,饼干,鸡翅,坚果,零食,奥利奥,方便面，水果,毛巾，韩式炸鸡，煲仔饭", "vector": [0.0345895, 0.01931712, -0.007517637, -0.008745006, 0.029150022, 0.051744774, -0.0070434264, -0.019763436, -0.062484257, 0.06867689, 0.02633265, 0.025788704, -0.020502647, 0.022608701, -0.041953716, 0.016081328, 0.033864234, -0.0044108583, 0.02809002, 0.016946064, 0.03506371, -0.015523433, -0.033808447, -0.03601213, 0.0009370895, -0.03255318, -0.019107908, -0.010272244, 0.010844087, -0.026820809, -0.0051291487, -0.0010573857, 0.01941475, 0.05113109, 0.04346003, 0.038076345, -0.010251324, 0.01585817, 0.035286866, -0.04134003, 0.00306668, -0.022120543, -0.00541507, -0.028871074, 0.011750666, -0.0019369422, -0.0027092784, -0.031214233, 0.0505453, 0.0005365382, 0.027016072, 0.01226672, 0.023975544, 0.0046130954, 0.011234613, 0.029735811, 0.004491056, -0.040252134, -0.0220787, 0.015621064, -0.046165824, 0.10237376, 0.021172121, -0.0048118457, 0.010551192, -0.0026081598, -0.0009100665, -0.05300004, 0.07046216, -0.016778696, 0.020460805, 0.016374223, 0.036625817, -0.01888475, 0.04906688, 0.018647645, -0.037016343, -4.116111e-05, -0.011722771, 0.012371325, 0.0031242128, 0.0154258, 0.038801607, -0.010125797, -0.004532898, 0.014296063, -0.0148679055, 0.45680454, 0.04965267, 0.034450024, 0.024435807, 0.019303173, -0.047588456, -0.025426071, -0.0012979779, -0.041367926, 0.022273963, 0.0062519126, -0.02931739, -0.02453344, 0.02378028, -0.079053745, -0.014121721, 0.0120505355, -0.023027122, 0.020544488, -0.010070007, -0.060587414, 0.0006219659, -0.023222385, -0.022706332, -0.042902138, -0.036737394, -0.056849517, 0.0140938265, 0.005456912, -0.0473374, -0.012643299, 0.006377439, -0.00045198223, -0.007831453, 0.029233705, -0.011164877, 0.011827377, -0.012587509, 0.010223429, 0.02372449, -0.0128804045, 0.021158174, -0.032915812, 0.04058687, -0.026625546, 0.013180273, -0.03037739, 0.009037902, 0.06086636, -0.054673724, -0.039554767, -0.0047281613, -0.02442186, 0.028243441, -0.013856721, 0.019107908, -0.022706332, 0.04485477, -0.014351852, -0.02920581, 0.028592126, -0.007852375, -0.008738033, 0.03336213, -0.026248967, 0.0060531623, -0.01117185, 0.023375807, -0.048230037, 0.007782637, -0.00829869, 0.026834756, 0.009372639, 0.008584611, -0.0016022051, 0.027908705, -0.017434223, -0.018075803, 0.003549608, 0.0037762527, 0.011157903, 0.047923192, 0.025021598, -0.0122457985, -0.0035548382, -0.022790017, -0.02670923, -0.025691072, -0.0016937348, -0.010795271, 0.026569756, -0.032887917, 0.04145161, -0.004240003, 0.014435537, 0.04549635, -0.039945293, 0.024603177, -0.0308516, 0.018842908, -0.020195805, 0.011053298, -0.059638992, 0.022176333, 0.015676854, 0.0100909285, -0.015676854, -0.024645017, 0.0140938265, -0.019930804, 0.018187381, -0.011687904, -0.018605802, 0.028675811, -0.0061473073, 0.007936059, 0.014965537, -0.029735811, -0.002242041, -0.019805277, 0.017155277, 0.06454847, 0.014003168, -0.037546344, 0.00044457268, 0.004229543, -0.02043291, -0.012434088, -0.01311751, 0.058300044, 0.029456863, -0.0526653, 0.009916586, -0.0055057276, 0.0012064483, 0.0005565876, -0.0036437528, -0.0047072405, 0.0074409265, 0.017852645, 0.037853185, -0.054841094, 0.022580806, -0.020209752, 0.0107255345, 0.05763057, 0.006736584, 0.017810803, 0.03757424, -0.01183435, -0.05124267, -0.012203956, -0.0073781633, -0.07687795, 0.0027912192, 0.009944481, -0.025426071, 0.03054476, 0.014163563, -0.00012999393, 0.018828962, 0.042595293, -0.01765738, 0.01852212, -0.014491326, -0.0135986945, -0.013054746, -0.03319476, -0.0049234247, 0.002010166, 0.021702122, -0.039554767, 0.019024225, 0.0033491142, -0.01521659, -0.028703704, 0.0048223063, 0.0073014526, -0.029540548, 0.018982382, -0.014086853, 0.0212837, 0.010941719, 0.03537055, -0.013382509, -0.023919754, -0.0077268477, -0.043487925, 0.003368292, -0.047644246, -0.00024647632, 0.0121621145, 0.0036088843, -0.007789611, -0.034924235, -0.011534482, 0.027071862, -0.013096589, -0.0044736215, -0.040670555, 0.0022699358, -0.022371596, -0.019623961, -0.019191593, 0.029484758, -0.013298825, -0.06912321, -0.006088031, 0.03863424, -0.017950276, -0.015509485, -0.040001083, -0.021158174, 0.040782135, 0.02644423, 0.05241425, -0.07012742, -0.034812655, -0.0020955936, -0.0032166142, 0.004226056, 0.01654159, 0.045719508, 0.015411854, -0.0023745412, 0.018898698, -0.045356877, 0.0026221073, -0.014784222, -0.00082812563, 0.009588823, -0.010544218, 0.014533169, -0.017225012, -0.0012352148, -0.024435807, -0.03715582, 0.008947243, 0.01788054, 0.035286866, -0.008842638, -0.059081096, -0.0074060583, 0.004871122, 0.008507901, -0.01351501, -0.0042574373, -0.004013358, 0.020474752, -0.012971062, 0.011562377, -0.0065866495, 0.046946876, 0.0830148, -0.0070364526, -0.001397353, -0.01909396, 0.04106108, 0.028243441, -0.03235792, -0.011109087, -0.011513561, 0.029986864, -0.0039889505, 0.041563187, -0.0018968435, -0.017685276, 0.019512383, -0.009756192, -0.0039227004, -0.0015446722, 0.0050140824, 0.032887917, 0.023905806, 0.0069074393, -0.0095121125, -0.004131911, 0.04959688, -0.036430553, -0.020335278, -0.019345013, 0.011764614, -0.018508172, 0.020976858, 0.06627794, -0.029484758, 0.018759225, 0.028396863, 0.022204226, 0.039247923, -0.02884318, 0.026667388, 0.03213476, 0.0199587, 0.009191322, -0.029122127, -0.02085133, -0.006262373, -0.002869673, -0.014086853, 0.005188425, -0.0032724037, -0.015091063, -0.016708959, -0.022803964, -0.03740687, 0.0108650075, -0.03313897, 0.021409227, -0.020837383, -0.051298458, -0.00939356, 0.0051535564, 0.04064266, 0.036625817, -0.02383607, -0.034561604, 0.0388295, -0.0070782946, 0.03693266, 0.030712128, -0.020153962, -0.010969614, -0.036793184, 0.027434494, -0.031102654, 0.016192907, 0.032860026, 0.0045468453, -0.024826335, -0.023696596, -0.015202642, 0.022748174, 0.047002666, 0.053334776, -0.018926593, -0.0048571746, 0.013089615, -0.014393695, 0.015551327, -0.010432639, -0.045105822, -0.04672372, -7.992502e-05, -0.005924149, 0.015091063, 0.031939495, 0.0057149385, 0.011046324, -0.012022641, 0.037602134, -0.015146853, 0.020781593, -0.03255318, -0.0142054055, 0.013438299, 0.024840282, -0.014616853, 0.01404501, -0.00092750066, -0.003884345, -0.01149264, -0.053808987, 0.0034519762, 0.042818453, -0.025607387, -0.025746861, 0.04644477, -0.03230213, -0.020739753, 0.03417108, -0.01580238, 0.002242041, 0.0081592165, 0.032106865, -0.029763706, -0.00248089, -0.028703704, 0.0014226326, -0.033417918, 0.04251161, 0.004260924, 0.016430013, 0.010251324, 0.0006359133, -0.016025538, 0.01548159, -0.014268168, 0.016234748, -0.036848973, -0.047978982, -0.008521848, -0.021646332, -0.027908705, 0.020237647, 0.004693293, -0.040614765, 0.01905212, -0.035286866, -0.0021374358, -0.009937507, -0.049820036, 0.024575282, -0.025844492, 0.003533917, -0.059415832, 0.016248696, -0.021771858, 0.009763165, -0.0026813836, -0.012169088, -0.033278447, -0.03272055, 0.023543175, 0.003141647, -0.00071741827, 0.021478964, -0.0010800501, -0.033557393, -0.05194004, -0.012601457, -0.067282155, 0.010014218, 0.017741065, 0.0047525694, 0.024254492, 0.028815284, -0.012008693, 0.019247383, 0.03428266, 0.040363714, 0.042818453, -0.013975274, -0.0282016, -0.0047490825, -0.009930533, -0.04463161, -0.0025192453, -0.023822123, 0.021116331, -0.028215546, -0.011381061, -0.04028003, -0.019080015, 0.0047455956, 0.035984237, 0.0022472714, -0.016876329, -0.014853958, -0.011067245, -0.031939495, 0.0017259881, -0.025956072, 0.015356064, -0.01311751, -0.051633194, 0.028424758, 0.008145269, 0.062428467, 0.034924235, -0.0010451816, 0.008235927, 0.031158444, -0.047058456, -0.00089263223, -0.022845807, -0.016499748, -0.032915812, 0.014937643, 0.047421087, -0.033864234, 0.0345895, 0.013528957, -0.00017303467, -0.007810532, 0.0026848705, -0.014337906, 0.0058265175, 0.0038808582, 0.023013175, 0.04714214, -0.025607387, 0.032692656, 0.010634876, -0.00613336, -0.04917846, 0.017420275, 0.018396592, -0.018954488, -0.025481861, 0.036430553, -0.037183713, 0.006802834, 0.015760537, -0.04393424, 0.01638817, -0.021562647, 0.021241859, -0.008507901, 0.0049966485, -0.03475687, -0.06929058, 0.05012688, 0.0003166491, 0.009574875, -0.0038529634, -0.0351195, -0.03559371, -0.027057914, -0.009818954, 0.004288819, -0.02606765, 0.005565004, 0.014407642, 0.033780552, -0.05763057, 0.0026465151, -0.021785805, -0.013319747, 0.0936148, 0.05863478, -0.017782908, -0.025147123, 0.01888475, 0.049875826, 0.02920581, 0.04691898, -0.08083901, -0.038076345, 0.016806591, 0.052637406, 0.015328169, 0.011904088, 0.027950548, -0.0044596745, -0.027476337, -0.053725302, -0.025579492, -0.010976587, -0.016527643, 0.011346192, -0.031102654, -0.0041040163, 0.04990372, 0.03612371, -0.0009684711, 0.025579492, -0.008835664, 0.062484257, 0.04134003, -0.017266855, -0.029986864, -0.005924149, 0.0314095, 0.0034938185, 0.025509756, -0.02495186, -0.029122127, -0.042651083, 0.014003168, -0.02219028, 0.037127923, -0.0045398716, -0.023877913, 0.018159486, 0.013152378, -0.007894216, 0.00587882, -0.026806861, -0.0027127652, 0.0064018467, 0.015788432, -0.015830275, 0.028103968, 0.009247112, 0.01039777, 0.009763165, -0.027127651, -0.035677396, 0.014700538, 0.046528455, 0.024979755, -0.013445273, 0.0023518768, -0.0060950043, 0.07420006, -0.031186339, -0.07989059, -0.017810803, -0.018396592, 0.0057114516, -0.054087933, -0.017280802, -0.0047909245, 0.019386856, -0.0149236955, -0.008982112, 0.054729514, -0.012950141, -0.031046866, 0.018591857, 0.015955802, 0.03857845, -0.0037971737, -0.025342386, 0.03576108, -0.013480142, -0.01591396, -0.004986188, 0.03171634, -0.017336592, 0.0046409904, 0.0061089518, -0.07224742, 0.009798033, -0.037546344, 0.0070085577, 0.008361453, -0.011618166, 0.051549513, 0.03835529, 0.022246068, 0.004780464, 0.012789746, 0.0016649683, -0.030712128, 0.013577773, -0.005655662, 0.009302901, -0.03107476, 0.015844222, -0.021088436, 0.022469226, -0.00013794829, -0.008347506, -0.057044778, -0.013710273, -0.0019020737, -0.030879496, -0.006816781, 0.025677124, 0.0048571746, -0.003748358, 0.02298528, -0.040531084, -0.018285014, -0.0075315842, 0.007705927, -0.03576108, 0.022971332, 0.0043411218, 0.026458178, 0.0020485213, 0.01096264, -0.06661268, 0.00795698, 0.04081003, -0.0136544835, 0.056598462, -0.09355902, 0.008438164, -0.029791601, 0.029707916, -0.0056207934, 0.0021862516, -0.014393695, -0.03442213, 0.028703704, 0.024059229, 0.012713036, -0.028675811, 0.017573697, 0.033780552, -0.031130549, -0.02756002, 0.0016832743, -0.043404244, 0.01888475, -0.06293057, -0.005021056, 0.02096291, -0.031800024, 0.03500792, 0.023027122, 0.021730015, 0.009212243, 0.012420141, -0.025440019, -0.024840282, 0.0141008, 0.05857899, 0.0059380964, 0.0020258566, 0.031130549, 0.021785805, 0.008905401, -0.0070085577, -0.005387175, -0.03623529, 0.013870668, 0.0057149385, -0.026193177, -0.027308967, -0.005659149, 0.013005931, -0.020349225, -0.042009503, -0.06304215, -0.038606346, -0.043599505, 0.00803369, 0.018326856, -0.010948692, -0.0009815468, 0.012922246, -0.02937318, 0.030237917, -0.02756002, -0.001973554, 0.013863694, 0.036430553, -0.032636866, 0.033613183, -0.021255804, 0.034868445, 0.008905401, 0.015286327, 0.027727388, 0.026430283, -0.011185798, -0.052497935, 0.039275818, -0.032525286, 0.02362686, 0.044994242, -0.037267394, -0.06337689, -0.023975544, -0.03793687, -0.023487385, -0.019805277, -0.02096291, 0.0033822393, -0.009030928, -0.042288452, -0.057240043, -0.016234748, 0.007705927, -0.014079879, -0.00829869, 0.0043167137, -0.014477379, -0.013891589, 0.033390023, 0.031186339, 0.014240273, -0.073363215, 0.017001854, 0.030237917, -0.01026527, 0.016904224, -0.031186339, 0.06572005, -0.026667388, 0.00861948, 0.0041807266, -0.002423357, -0.012224877, 0.018173434, 0.06376742, 0.03048897, 0.023933701, 0.005516188, 0.003030068, -0.016206853, -0.0139683, -0.01718317, 0.0063321097, 0.006781913, -0.0308516, 0.01697396, -0.033111077, 0.01841054, -0.0070469133, 0.020293435, 0.059638992, -0.01852212, 0.042288452, 0.008026716, -0.0441574, -0.03905266, -0.012315535, -0.0017312184, -0.040949505, 0.032162655, -0.017113434, 0.008919349, -0.0633211, -0.0005565876, 0.023082912, 0.002601186, 0.02654186, 0.00854277, 0.026221072, 0.0043690163, -0.022915544, -0.012273693, 0.0030196074, -0.008814744, -0.013019878, -0.01117185, 0.002121745, -0.03090739, 0.024756597, -0.016820539, 0.02644423, 0.0037100026, 0.047281615, 0.026695283, -0.01920554, 0.009533034, -0.02650002, 0.014798169, 0.006806321, 0.03400371, 0.017866593, 0.008054611, -0.027211336, 0.0345895, -0.016583433, -0.01771317, 0.020474752, -0.04022424, -0.03202318, -0.010014218, 0.033864234, 0.031744234, 0.028382916, 0.005097767, 0.004417832, -0.008745006, -0.02133949, -0.020181857, -0.016485801, 0.02228791, 0.01841054, -0.021995015, -0.0055371094, 0.00057271426, -0.018312909, -0.00093447434, 0.006304215, 0.014533169, -0.0027877323, 0.009651586, 0.0052546747, 0.007538558, 0.057909515, 0.007245663, -0.036207397, 0.005770728, 0.039471082, 0.0055789514, -0.015439748, -0.015118958, -0.006740071, -0.008835664, -0.020865278, 0.011318298, -0.007322374, -0.06532952, -0.008689217, 0.011123034, -0.034310553, -0.028327126, 0.039443187, 0.024045281, -0.04831372, -0.0399174, -0.025830545, 0.042651083, 0.025760809, -0.025440019, -0.025983967, -0.017545803, 0.0215487, -0.014072905, -0.011395008, -0.01873133, -0.015969748, 0.029066337, -0.039498977, 0.017964223, -0.020446857, -0.02447765, 0.012001719, 0.016457906, -0.00782448, 0.057965305, 0.006018294, -0.013180273, -0.025286598, 0.017517908, -0.017378435, 0.019010277, -0.014602905, 0.025774756, -0.031158444, 0.014644748, 0.045831088, -0.001306695, -0.026834756, -0.018591857, -0.009198296, 0.01941475, -0.025983967, -0.038606346, -0.059806358, 0.010307113, -0.057965305, -0.030879496, -0.015565274, 0.0040552, -0.019512383, -0.018856855, -0.009114612, -0.051521618, 0.008528822, -0.025091333, -0.016876329, 0.041367926, -0.05205162, 0.018257119, -0.040726345, -0.06415794, -0.03235792, 0.015523433]}, "message": "success", "result_code": 0, "request_id": "16mq7fq3ky8le"}