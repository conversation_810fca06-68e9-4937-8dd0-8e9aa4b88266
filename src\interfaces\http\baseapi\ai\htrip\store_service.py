import asyncio
import logging
import random
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from ..agent_apis.eleme_store import (
    async_getStoreDetail,
    async_getStoreDetailLLM,
    client,
    getStoreDetail,
    getStoreH5Url,
    searchStoreByName,
)
from ..agent_apis.eleme_wxscheme import get_wx_scheme_url
from ..agent_apis.models import Coupon, CouponListResponse, RecommendReason, ShopItem, Store, StoreListResponse
from ..agent_apis.query import search_activity

# 配置logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 默认经纬度(杭州)
DEFAULT_LATITUDE = "30.285743"
DEFAULT_LONGITUDE = "120.024741"

# 饿了么联盟PID
ELEME_PID = "alsc_28806810_9518007_26738131"


class StoreService:
    """店铺服务类"""

    @staticmethod
    async def get_store_list(
        store_tag: str,
        current_input: Optional[str] = "",
        sender: Optional[str] = None,
        longitude: Optional[str] = None,
        latitude: Optional[str] = None,
    ) -> Dict[str, Any]:
        """获取店铺列表"""
        try:
            # 搜索店铺
            stores = searchStoreByName(store_tag, longitude or "", latitude or "")

            if not stores:
                return {"code": 0, "data": StoreListResponse(total=0, storelist=[]), "msg": "未找到相关店铺"}
            store_details = await asyncio.gather(*[async_getStoreDetail(store["shop_id"]) for store in stores])
            store_detail_recommands = await asyncio.gather(
                *[async_getStoreDetailLLM(detail) for detail in store_details]
            )

            # 转换为Store对象
            store_list = []
            for idx, store in enumerate(stores):
                store_detail = store_details[idx]
                if not store_detail or "data" not in store_detail:
                    logger.warning(f"店铺 {store['shop_id']} 详情数据无效")
                    continue

                store_data = store_detail["data"]
                try:
                    store_obj = Store(
                        shop_id=store["shop_id"],
                        category=store["category_1_name"],
                        indistinct_monthly_sales=store["indistinct_monthly_sales"][2:],
                        # h5_url=getStoreH5Url(store_data['link']['h5_url']),
                        h5_url=store_data["link"]["mini_qrcode"],
                        service_rating=store["service_rating"],
                        shop_logo=store["shop_logo"],
                        title=store["title"],
                        delivery_distance=store["delivery_distance"],
                        delivery_price=store["delivery_price"],
                        delivery_time=store["delivery_time"],
                        recommend_reasons=[
                            RecommendReason(content=reason) for reason in store_data.get("recommend_reasons", [])
                        ],
                        recommend_description=store_detail_recommands[idx],
                        skus=(
                            [
                                ShopItem(
                                    picture=item.get("picture", ""),
                                    price=item.get("price", ""),
                                    title=item.get("title", ""),
                                    origin_price=item.get("origin_price", ""),
                                )
                                for item in store_data.get("items", [])[:3]
                            ]
                            if store_data.get("items")
                            else None
                        ),
                    )
                    store_list.append(store_obj)
                except Exception as e:
                    logger.error(f"处理店铺 {store['shop_id']} 数据时出错: {str(e)}")

            return {"code": 0, "data": StoreListResponse(total=len(store_list), storelist=store_list), "msg": "success"}
        except Exception as e:
            logger.error(f"获取店铺列表过程中发生错误: {str(e)}")
            raise


class CouponService:
    """优惠券服务类"""

    @staticmethod
    def get_scheme_url(link_data: dict, activity_id: str) -> str:
        """
        根据优先级获取 scheme_url
        优先级：微信 > 支付宝 > 淘宝 > 饿了么
        """
        # 1. 微信小程序
        if link_data.get("wx_url_scheme"):
            return link_data.get("wx_url_scheme") or ""

        # 2. 支付宝小程序
        if alipay_data := link_data.get("alipay_promotion", {}):
            if alipay_scheme := alipay_data.get("alipay_scheme_url"):
                return alipay_scheme

        # 3. 淘宝
        if taobao_data := link_data.get("taobao_promotion", {}):
            if taobao_url := taobao_data.get("h5_url"):
                return taobao_url

        # 4. 饿了么
        if ele_scheme := link_data.get("ele_scheme_url"):
            return ele_scheme

        return ""

    @staticmethod
    def get_h5_url(link_data: dict) -> str:
        """获取h5_url，优先从h5_promotion中获取"""
        if h5_data := link_data.get("h5_promotion", {}):
            if tj_h5_url := h5_data.get("tj_h5_url"):
                return tj_h5_url
        return link_data.get("h5_url", "")

    @staticmethod
    async def get_coupon_list(
        coupon_tag: str, current_input: Optional[str] = "", sender: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取优惠券列表"""
        try:
            # 拼接搜索文本
            search_text = f"{current_input} {coupon_tag}"

            # 搜索相似活动，获取前3个结果
            search_results = search_activity(search_text, top_k=3)

            if not search_results:
                logger.warning("未找到相关优惠券")
                # 返回默认优惠券
                default_activity = {
                    "data": {
                        "title": "饿了么天天领红包",
                        "picture": "https://img.alicdn.com/imgextra/i1/6000000000548/O1CN011Fv1r4S4jdPORaj_!!6000000000548-2-o2oad.png",
                        "description": "最高抢66元大额红包",
                        "link": {"h5_url": "https://ppe-h5.ele.me/adminiappsub/pages/h5/index", "ele_scheme_url": ""},
                    }
                }
                return {
                    "code": 0,
                    "data": CouponListResponse(
                        total=1,
                        couponlist=[
                            Coupon(
                                similarity=1.0,
                                title=default_activity["data"]["title"],
                                description=default_activity["data"]["description"],
                                picture=default_activity["data"]["picture"],
                                h5_url=default_activity["data"]["link"]["h5_url"],
                                scheme_url=default_activity["data"]["link"].get("ele_scheme_url", ""),
                            )
                        ],
                    ),
                    "msg": "未找到相关优惠券，返回默认优惠券",
                }

            # 将搜索结果转换为Coupon对象列表
            coupons = []
            for activity, similarity in search_results:
                activity_data = activity.get("data", {})
                link_data = activity_data.get("link", {})

                # 根据优先级获取 scheme_url
                scheme_url = CouponService.get_scheme_url(link_data, activity_data.get("id", ""))
                # 获取h5_url
                h5_url = CouponService.get_h5_url(link_data)
                if not h5_url:
                    h5_url = scheme_url

                coupon = Coupon(
                    similarity=similarity,
                    title=activity_data.get("title", ""),
                    picture=activity_data.get("picture", ""),
                    description=activity_data.get("description", ""),
                    h5_url=h5_url,
                    scheme_url=scheme_url,
                )
                coupons.append(coupon)

            return {"code": 0, "data": CouponListResponse(total=len(coupons), couponlist=coupons), "msg": "success"}

        except Exception as e:
            logger.error(f"获取优惠券列表过程中发生错误: {str(e)}")
            return {
                "code": -1,
                "data": CouponListResponse(total=0, couponlist=[]),
                "msg": f"获取优惠券列表失败: {str(e)}",
            }


if __name__ == "__main__":
    asyncio.run(
        StoreService.get_store_list(
            store_tag="奶茶", current_input="奶茶", sender="18817802888", longitude="120.024741", latitude="30.285743"
        )
    )
