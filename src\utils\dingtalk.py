import json
import time
from typing import Any, Dict, List, Optional

import requests
from loguru import logger

# clientid: dingpadaxolo4tb9mzw4
# clientsecret: Ztmc4E1WQW03HEYMDOHm1u02BsoeXfRwtfsMuDBn_zkjlw0T3K54nmZgzhiv0gUQ

# TODO: update to use dingtalk SDK


class DingTalkClient:
    """钉钉 API 客户端类"""

    def __init__(self, app_key: str, app_secret: str):
        """
        初始化钉钉客户端

        Args:
            app_key (str): 应用的 AppKey
            app_secret (str): 应用的 AppSecret
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self._access_token: Optional[str] = None
        self.update_token_time = 0

    def _get_access_token(self) -> str | None:
        """
        获取访问令牌，如果已存在且未过期则直接返回

        Returns:
            str: 访问令牌
        """
        if not self._access_token:
            url = "https://api.dingtalk.com/v1.0/oauth2/accessToken"
            headers = {"Content-Type": "application/json"}
            payload = {"appKey": self.app_key, "appSecret": self.app_secret}

            response = requests.post(url, headers=headers, data=json.dumps(payload))
            result = response.json()

            if "accessToken" not in result:
                logger.error(f"获取 access_token 失败: {result}")
                raise Exception("获取访问令牌失败")

            self._access_token = result["accessToken"]
            self.update_token_time = time.time()

        if time.time() - self.update_token_time > 7200:
            self._access_token = None
            return self._get_access_token()

        return self._access_token

    def prepare_ai_interaction(
        self,
        open_conversation_id: Optional[str] = None,
        union_id: Optional[str] = None,
        content_type: Optional[str] = None,
        content: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        准备 AI 交互会话

        Args:
            open_conversation_id: 会话ID
            union_id: 用户unionId
            content_type: 内容类型
            content: 内容

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://api.dingtalk.com/v1.0/aiInteraction/prepare"
        headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": self._get_access_token()}

        payload = {}
        if open_conversation_id:
            payload["openConversationId"] = open_conversation_id
        if union_id:
            payload["unionId"] = union_id
        if content_type:
            payload["contentType"] = content_type
        if content:
            payload["content"] = content

        response = requests.post(url, headers=headers, data=json.dumps(payload))
        return response.json()

    def update_ai_interaction(
        self, conversation_token: str, content_type: Optional[str] = None, content: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        更新 AI 交互内容

        Args:
            conversation_token: 会话令牌
            content_type: 内容类型
            content: 内容

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://api.dingtalk.com/v1.0/aiInteraction/update"
        headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": self._get_access_token()}
        print(f"更新 AI 交互内容: {conversation_token}, {content_type}, {content}")
        payload = {"conversationToken": conversation_token}
        if content_type:
            payload["contentType"] = content_type
        if content:
            payload["content"] = content

        response = requests.post(url, headers=headers, data=json.dumps(payload))
        return response.json()

    def finish_ai_interaction(self, conversation_token: str) -> Dict[str, Any]:
        """
        完成 AI 交互

        Args:
            conversation_token: 会话令牌

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://api.dingtalk.com/v1.0/aiInteraction/finish"
        headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": self._get_access_token()}

        payload = {"conversationToken": conversation_token}
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        return response.json()

    def get_department_user_details(self, dept_id: int, cursor: int = 0, size: int = 10) -> Dict[str, Any]:
        """
        获取部门用户详细信息列表

        Args:
            dept_id: 部门ID，根部门传1
            cursor: 分页查询的游标
            size: 分页大小

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://oapi.dingtalk.com/topapi/v2/user/list"
        headers = {"Content-Type": "application/json"}
        params = {"access_token": self._get_access_token()}
        payload = {"dept_id": dept_id, "cursor": cursor, "size": size}

        response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
        return response.json()

    def get_department_user_ids(self, dept_id: int) -> Dict[str, Any]:
        """
        获取部门用户ID列表

        Args:
            dept_id: 部门ID

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://oapi.dingtalk.com/topapi/user/listid"
        headers = {"Content-Type": "application/json"}
        params = {"access_token": self._get_access_token()}
        payload = {"dept_id": dept_id}
        response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
        return response.json()

    def send_ai_interaction(
        self,
        union_id: Optional[str] = None,
        content_type: Optional[str] = None,
        content: Optional[str] = None,
        open_conversation_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        直接发送 AI 交互内容

        Args:
            open_conversation_id: 会话ID
            union_id: 用户unionId
            content_type: 内容类型
            content: 内容

        Returns:
            Dict[str, Any]: API响应结果
        """
        url = "https://api.dingtalk.com/v1.0/aiInteraction/send"
        headers = {"Content-Type": "application/json", "x-acs-dingtalk-access-token": self._get_access_token()}

        payload = {}
        if open_conversation_id:
            payload["openConversationId"] = open_conversation_id
        if union_id:
            payload["unionId"] = union_id
        if content_type:
            payload["contentType"] = content_type
        if content:
            payload["content"] = content
        print(f"发送 AI 交互内容: {payload}")
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        return response.json()

    def get_all_department_user_details(self, dept_id: int, size: int = 100) -> List[Dict[str, Any]]:
        """
        获取部门所有用户详细信息列表（自动处理分页）

        Args:
            dept_id: 部门ID，根部门传1
            size: 每页大小

        Returns:
            List[Dict[str, Any]]: 所有用户详细信息列表
        """
        all_users = []
        cursor = 0
        has_more = True

        while has_more:
            result = self.get_department_user_details(dept_id, cursor, size)
            if result.get("errcode") != 0:
                logger.error(f"获取部门用户详情失败: {result}")
                break

            response_data = result.get("result", {})
            users = response_data.get("list", [])
            all_users.extend(users)

            next_cursor = response_data.get("next_cursor", 0)
            has_more = next_cursor != 0
            cursor = next_cursor

        return all_users

    def send_aicard(self, template_id: str, union_id: str, content_type: str = "ai_card"):
        """
        发送 AI 卡片
        """
        card_content = {
            "templateId": template_id,
        }
        prepare_result = self.prepare_ai_interaction(
            union_id=union_id, content_type=content_type, content=json.dumps(card_content)  # 这里应替换为实际的unionId
        )
        print(f"prepare_result: {prepare_result}")
        conversation_token = prepare_result.get("result", {}).get("conversationToken")
        print(f"conversation_token: {conversation_token}")
        if not conversation_token:
            return None

        self.finish_ai_interaction(conversation_token=conversation_token)

    def get_user_by_mobile(self, mobile: str) -> Dict[str, Any]:
        """
        通过手机号获取用户信息

        Args:
            mobile (str): 用户的手机号

        Returns:
            Dict[str, Any]: API响应结果，包含用户信息
        """
        url = "https://oapi.dingtalk.com/topapi/v2/user/getbymobile"
        headers = {"Content-Type": "application/json"}
        params = {"access_token": self._get_access_token()}
        payload = {"mobile": mobile}

        response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
        return response.json()

    def get_user_by_userid(self, userid: str) -> Dict[str, Any]:
        """
        通过 userid 获取用户详细信息

        Args:
            userid (str): 用户的 userid

        Returns:
            Dict[str, Any]: API响应结果，包含用户信息
        """
        url = "https://oapi.dingtalk.com/topapi/v2/user/get"
        headers = {"Content-Type": "application/json"}
        params = {"access_token": self._get_access_token()}
        payload = {"userid": userid}

        response = requests.post(url, headers=headers, params=params, data=json.dumps(payload))
        js = response.json()
        if js.get("errcode") != 0:
            logger.error(f"获取用户信息失败: {js}")
            raise Exception(f"获取用户信息失败: {js}")
        return js.get("result")
