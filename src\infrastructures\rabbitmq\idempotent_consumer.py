# encoding: utf-8
# src/infrastructures/rabbitmq/idempotent_consumer.py
# created: 2025-08-18 17:30:00

import hashlib
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.infrastructures.rabbitmq.consumers import BaseConsumer

if TYPE_CHECKING:
    import redis.asyncio as redis
    from aio_pika.abc import AbstractIncomingMessage

    from src.containers import Container


class IdempotentConsumer(BaseConsumer):
    """支持幂等性的消费者基类"""

    # 消息处理记录的过期时间（秒）
    MESSAGE_PROCESSED_TTL = 86400  # 24小时

    @inject
    async def is_message_processed(
        self,
        message: "AbstractIncomingMessage",
        redis_client: "redis.Redis" = Provide["Container.infrastructures.redis_manager.provided.client"],
    ) -> bool:
        """
        检查消息是否已经被处理过

        Args:
            message: 消息对象
            redis_client: Redis客户端

        Returns:
            bool: True表示已处理，False表示未处理
        """
        message_key = self._get_message_key(message)
        result = await redis_client.get(message_key)
        return result is not None

    @inject
    async def mark_message_processed(
        self,
        message: "AbstractIncomingMessage",
        redis_client: "redis.Redis" = Provide["Container.infrastructures.redis_manager.provided.client"],
    ) -> None:
        """
        标记消息已处理

        Args:
            message: 消息对象
            redis_client: Redis客户端
        """
        message_key = self._get_message_key(message)
        await redis_client.setex(message_key, self.MESSAGE_PROCESSED_TTL, "1")
        logger.debug(f"消息已标记为已处理: {message_key}")

    def _get_message_key(self, message: "AbstractIncomingMessage") -> str:
        """
        生成消息的唯一标识键

        Args:
            message: 消息对象

        Returns:
            str: 消息的唯一标识键
        """
        # 使用消息ID生成键
        if message.message_id:
            return f"mq:processed:{self.queue_name}:{message.message_id}"

        # 如果没有消息ID，使用消息内容的哈希值
        content_hash = hashlib.md5(message.body).hexdigest()
        return f"mq:processed:{self.queue_name}:{content_hash}"

    async def process_with_idempotency(self, message: "AbstractIncomingMessage") -> None:
        """
        带幂等性检查的消息处理

        Args:
            message: 消息对象
        """
        # 检查消息是否已处理
        if await self.is_message_processed(message):
            logger.info(f"消息已处理，跳过: queue={self.queue_name}, " f"message_id={message.message_id}")
            return

        try:
            # 处理消息
            await self.process(message)

            # 标记消息已处理
            await self.mark_message_processed(message)

        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            raise

    async def process(self, message: "AbstractIncomingMessage") -> None:
        """
        子类需要实现的消息处理逻辑

        Args:
            message: 消息对象
        """
        raise NotImplementedError("子类必须实现process方法")
