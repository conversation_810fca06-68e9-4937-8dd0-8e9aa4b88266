# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/__init__.py
# created: 2024-12-08 01:56:54
# updated: 2025-04-18 03:13:18

from src.infrastructures.fastapi.application import register_app

from .routers.benefits import router as benefits_router
from .routers.customers import router as customers_router
from .routers.delivery import router as delivery_router
from .routers.tools import router as tools_router


def get_mis_app(config=None):
    app = register_app(
        name="misapi",
        version="0.0.1",
        description="MIS服务API, 提供api服务给web端",
        config=config,
    )

    app.include_router(benefits_router, prefix="/benefits")
    app.include_router(customers_router, prefix="/customers")
    app.include_router(delivery_router, prefix="/delivery")
    app.include_router(tools_router, prefix="/tools")

    return app
