# encoding: utf-8
# scripts/process_benefits_processing.py
# created: 2025-08-25 12:00:40

import asyncio
from datetime import datetime, timezone
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger
from tortoise.expressions import Q

from deploys.scheduler.settings import config
from src.containers import Container
from src.databases.models.benefits import BenefitsSkuChargeRecord, BenefitsSkuChargeRecordStatus
from src.domains.benefits.messages import SkuChargeCheckMessage, SkuChargeMessage, SkuChargeMessageContent
from src.interfaces.schedulers.main import lifespan

if TYPE_CHECKING:
    from src.infrastructures.rabbitmq import RabbitMQProducer


@inject
async def process_benefits_processing(
    producer: "RabbitMQProducer" = Provide[Container.infrastructures.rabbitmq_producer],
):
    """处理待处理和处理中的充值记录"""

    # 设置时间范围：2025年8月24日0点至今
    start_time = datetime(2025, 8, 24, 0, 0, 0, tzinfo=timezone.utc)

    logger.info(f"开始处理 {start_time} 以来的充值记录...")

    # 查询所有 processing 和 pending 状态的记录
    records = await BenefitsSkuChargeRecord.filter(
        Q(status__in=[BenefitsSkuChargeRecordStatus.PROCESSING, BenefitsSkuChargeRecordStatus.PENDING])
        & Q(created_at__gte=start_time)
    ).prefetch_related("sku")

    logger.info(f"找到 {len(records)} 条需要处理的记录")

    processing_count = 0
    pending_count = 0

    for record in records:
        try:
            if record.status == BenefitsSkuChargeRecordStatus.PROCESSING:
                # 对于 processing 的记录，发送状态查询消息
                logger.info(f"发送状态查询消息: record_id={record.id}, charge_order_id={record.charge_order_id}")

                await producer.publish_message(
                    SkuChargeCheckMessage(
                        payload=SkuChargeMessageContent(
                            record_id=record.id, retry_count=0, last_retry_at=datetime.now()
                        )
                    ),
                    exchange_name="benefits.topic",
                    routing_key="benefits.sku_charge_check",
                )
                processing_count += 1

            elif record.status == BenefitsSkuChargeRecordStatus.PENDING:
                # 对于 pending 的记录，重新发起充值消息
                logger.info(f"重新发送充值消息: record_id={record.id}, charge_order_id={record.charge_order_id}")

                await producer.publish_message(
                    SkuChargeMessage(payload=SkuChargeMessageContent(record_id=record.id, retry_count=0)),
                    exchange_name="benefits.topic",
                    routing_key="benefits.sku_charge",
                )
                pending_count += 1

            # 避免发送消息过快
            await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"处理记录 {record.id} 时出错: {e}")
            continue

    logger.info(f"处理完成！共处理 {len(records)} 条记录:")
    logger.info(f"  - Processing 状态: {processing_count} 条（已发送状态查询消息）")
    logger.info(f"  - Pending 状态: {pending_count} 条（已重新发送充值消息）")


async def main(container: Container):
    async with lifespan(container):
        await process_benefits_processing()


if __name__ == "__main__":
    container = Container()
    container.config.from_pydantic(config)
    container.wire(modules=[__name__, "src.domains.benefits.services.charge_strategy"])
    asyncio.run(main(container))
