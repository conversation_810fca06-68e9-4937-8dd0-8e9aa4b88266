# encoding: utf-8
# <AUTHOR> <EMAIL>
# utils/eleme/channels.py
# created: 2025-03-19 23:09:54
# updated: 2025-05-05 08:30:14

import time
import uuid
from urllib.parse import parse_qsl, urlencode, urlparse, urlunparse

import jwt

from src.infrastructures.config_compat import config


class ElemeChannelsUtils:

    @classmethod
    def generate_jwt(cls, eleme_channel: str, open_id: str, mobile: str) -> str:
        channel_config = config.thirdpart.eleme.channels.get(eleme_channel)
        if not channel_config:
            return ""
        eleme_app_id = channel_config.eleme_channel_app_id
        eleme_app_secret = channel_config.eleme_channel_secret
        payload = {
            "device_id": uuid.uuid4().hex,
            "open_id": open_id,
            "source": eleme_app_id,
            "mobile": mobile,
            "nickname": "",
            "aud": "ele.me",
            "iat": int(time.time()) - 30,
            "exp": int(time.time()) + 60 * 60 * 24 * 30,  # 30天过期时间
        }
        return jwt.encode(payload, eleme_app_secret, algorithm="HS256")

    @classmethod
    def generate_channels_params(cls, eleme_channel: str, mobile: str, user_open_id: str) -> dict:
        """生成饿了么渠道版参数"""
        channel_config = config.thirdpart.eleme.channels.get(eleme_channel)
        if not channel_config:
            return {}

        token = cls.generate_jwt(eleme_channel, user_open_id, mobile)
        params = {
            "from": channel_config.eleme_channle_no,
            "opensite_source": channel_config.eleme_channel_source,
            "jwt": token,
        }
        if channel_config.eleme_welfare3pp:
            params["welfare3pp"] = channel_config.eleme_welfare3pp
        if channel_config.eleme_channel_pay_type:
            params["payType"] = channel_config.eleme_channel_pay_type
        return params

    @classmethod
    def generate_channels_url_params(cls, eleme_channel: str, mobile: str, user_open_id: str) -> str:
        """生成饿了么渠道版参数"""
        params = cls.generate_channels_params(eleme_channel, mobile, user_open_id)
        return urlencode(params)

    @classmethod
    def url_with_params(cls, url: str, params: dict) -> str:
        parsed = urlparse(url)
        query_dict = dict(parse_qsl(parsed.query))
        query_dict.update(params)
        new_query = urlencode(query_dict)
        return urlunparse(parsed._replace(query=new_query))

    @classmethod
    def channel_url_with_params(cls, eleme_channel: str, params: dict) -> str:
        channel_config = config.thirdpart.eleme.channels.get(eleme_channel)
        if not channel_config:
            return ""
        if not channel_config.eleme_channel_url:
            return ""
        return cls.url_with_params(channel_config.eleme_channel_url, params)
