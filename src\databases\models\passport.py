# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/passport.py
# created: 2025-01-03 16:18:14
# updated: 2025-04-16 10:22:50

from enum import StrEnum
from typing import Any, Optional, Sequence

import nanoid
from pydantic import BaseModel, Field
from tortoise import fields
from tortoise.models import Model


class ThirdPlatformEnum(StrEnum):
    WECHAT = "wechat"
    ALIPAY = "alipay"
    DINGTALK = "dingtalk"
    UNKNOWN = "unknown"


class UserSourceEnum(StrEnum):
    LOCAL = "local"
    DINGTALK = "dingtalk"
    WECHAT = "wechat"
    PHONE = "phone"


def generate_app_secret():
    return nanoid.generate(size=64, alphabet="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")


def code_generate():
    return nanoid.generate(alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890", size=16)


class PassportAppConfig(BaseModel):
    dingtalk_app_id: Optional[str] = Field(None, description="钉钉应用ID")
    dingtalk_app_secret: Optional[str] = Field(None, description="钉钉应用密钥")
    wechat_app_id: Optional[str] = Field(None, description="微信应用ID")
    wechat_app_secret: Optional[str] = Field(None, description="微信应用密钥")


class PassportApp(Model):
    id = fields.BigIntField(primary_key=True, description="主键ID")
    name = fields.CharField(max_length=255, description="应用名称", null=False, default="")
    app_id = fields.CharField(max_length=64, description="应用ID", null=False, default="", unique=True)
    app_secret = fields.CharField(max_length=64, description="应用密钥", null=False, default=generate_app_secret)
    dingtalk_app_id = fields.CharField(max_length=64, description="钉钉应用ID", null=False, default="")
    dingtalk_app_secret = fields.CharField(max_length=64, description="钉钉应用密钥", null=False, default="")
    wechat_app_id = fields.CharField(max_length=64, description="微信应用ID", null=False, default="")
    wechat_app_secret = fields.CharField(max_length=64, description="微信应用密钥", null=False, default="")
    alipay_aes_key = fields.CharField(max_length=128, description="支付宝AES密钥", null=False, default="")
    third_party_config = fields.JSONField(description="第三方配置", null=True, default=dict)  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "passport_app"

    def __str__(self):
        return f"{self.name}[{self.app_id}]"

    class PydanticMeta:
        exclude = ("benefits_product_orders", "benefits_sku_charge_records")


class PassportTenant(Model):
    tenant_id = fields.CharField(max_length=32, description="租户ID", default=code_generate, primary_key=True)
    name = fields.CharField(max_length=64, null=False, description="租户名称", default="")
    app: fields.ForeignKeyRelation[PassportApp] = fields.ForeignKeyField(
        "models.PassportApp", description="关联应用", db_constraint=False, null=True
    )  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "passport_tenants"
        table_description = "passport租户"

    def __str__(self):
        return f"{self.name}[{self.tenant_id}]"

    class PydanticMeta:
        exclude = ["app", "benefits_product_orders", "benefits_sku_charge_records"]


class PassportThirdUser(Model):
    id = fields.IntField(description="ID", primary_key=True)
    user = fields.ForeignKeyField("models.PassportUser", description="用户", db_constraint=False)
    open_id = fields.CharField(description="open_id", max_length=255, null=True, blank=True)
    union_id = fields.CharField(description="union_id", max_length=255, null=True, blank=True)
    profile = fields.JSONField(description="profile", null=True, blank=True)
    platform = fields.CharEnumField(ThirdPlatformEnum, description="来源平台", default=ThirdPlatformEnum.UNKNOWN)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "passport_third_users"
        table_description = "passport第三方用户"


class PassportUserRelations(Model):
    id = fields.IntField(description="ID", primary_key=True)
    user = fields.ForeignKeyField("models.PassportUser", description="用户", db_constraint=False)  # type: ignore
    tenant = fields.ForeignKeyField(
        "models.PassportTenant", description="租户", db_constraint=False, null=True, blank=True
    )  # type: ignore
    app = fields.ForeignKeyField("models.PassportApp", description="应用", db_constraint=False)  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "passport_user_relations"
        table_description = "passport用户关系"


class PassportUser(Model):
    uid = fields.CharField(max_length=16, unique=True, null=False, description="用户ID", default=code_generate)
    avatar_url = fields.CharField(max_length=255, null=True, description="头像")
    username = fields.CharField(max_length=32, null=False, description="用户名", default="")
    nickname = fields.CharField(max_length=32, null=False, description="昵称", default="")
    password = fields.CharField(max_length=128, null=False, description="密码", default="")
    email = fields.CharField(max_length=128, null=True, description="邮箱")
    phone = fields.CharField(max_length=16, null=False, description="手机号", unique=True, default="")
    source = fields.CharEnumField(UserSourceEnum, description="用户来源", default=UserSourceEnum.LOCAL, null=False)
    is_active = fields.BooleanField(default=True, description="是否激活", null=False)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "passport_user"
