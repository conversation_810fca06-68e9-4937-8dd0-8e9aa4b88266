# encoding: utf-8
# tests/unit/applications/openapi/queries/test_benefits.py
# created: 2025-08-02 16:30:00

"""OpenAPI 权益查询服务测试"""

from unittest.mock import AsyncMock, MagicMock

import pytest

from src.applications.openapi.dto import BenefitsOrderDTO, BenefitsProductDTO
from src.applications.openapi.errors import BenefitOrderNotFoundError
from src.applications.openapi.queries.benefits import BenefitsQueryService
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.unit
class TestBenefitsQueryService(BaseOpenapiUnitTest):
    """测试权益查询服务"""

    @pytest.fixture
    def mock_product_service(self):
        """Mock 权益产品服务"""
        return AsyncMock()

    @pytest.fixture
    def mock_customer_benefits_repo(self):
        """Mock 客户权益仓储"""
        return AsyncMock()

    @pytest.fixture
    def benefits_service(
        self,
        mock_product_service,
        mock_customer_benefits_repo,
    ):
        """权益查询服务实例"""
        return BenefitsQueryService(
            product_service=mock_product_service,
            customer_benefits_repo=mock_customer_benefits_repo,
        )

    @pytest.fixture
    def sample_customer(self):
        """示例客户"""
        return CustomerEntity(
            id=1,
            code="test_customer_001",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app_001",
            tenant_id="test_tenant_001",
            balance=10000,
        )

    @pytest.fixture
    def sample_order_data(self):
        """示例订单数据"""
        return {
            "id": 1,
            "order_id": "order_123456",
            "out_order_id": "third_order_789",
            "status": "completed",
            "product_code": "benefit_product_001",
            "price": 1000,
            "app_id": "test_app_001",
            "tenant_id": "test_tenant_001",
        }

    @pytest.fixture
    def sample_product_data(self):
        """示例产品数据"""
        return {
            "id": 1,
            "code": "benefit_product_001", 
            "name": "测试权益产品",
            "price": 1000,
            "stock": 100,
        }

    @pytest.mark.asyncio
    async def test_init(
        self,
        mock_product_service,
        mock_customer_benefits_repo,
    ):
        """测试服务初始化"""
        service = BenefitsQueryService(
            product_service=mock_product_service,
            customer_benefits_repo=mock_customer_benefits_repo,
        )
        
        assert service.product_service == mock_product_service
        assert service.customer_benefits_repo == mock_customer_benefits_repo

    @pytest.mark.asyncio
    async def test_get_order_by_order_id_success(
        self,
        benefits_service,
        mock_product_service,
        sample_customer,
        sample_order_data,
    ):
        """测试根据订单ID获取订单成功"""
        order_id = "order_123456"
        
        # 创建mock订单，确保验证通过（因为 validate_source 的逻辑是不相等时返回True）
        mock_order = MagicMock()
        mock_order.app_id = "different_app"  # 与客户不同的app_id
        mock_order.tenant_id = "different_tenant"  # 与客户不同的tenant_id
        mock_order.model_dump.return_value = sample_order_data
        
        # 设置mock返回值
        mock_product_service.get_order_by_order_id.return_value = mock_order
        
        # 执行查询
        result = await benefits_service.get_order_by_order_id(sample_customer, order_id)
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == sample_order_data["order_id"]
        assert result.third_order_id == sample_order_data["out_order_id"]
        assert result.status == sample_order_data["status"]
        assert result.product_code == sample_order_data["product_code"]
        assert result.price == sample_order_data["price"]
        
        # 验证方法调用
        mock_product_service.get_order_by_order_id.assert_called_once_with(order_id)

    @pytest.mark.asyncio
    async def test_get_order_by_order_id_source_validation_failed(
        self,
        benefits_service,
        mock_product_service,
        sample_customer,
        sample_order_data,
    ):
        """测试订单来源验证失败"""
        order_id = "order_123456"
        
        # 创建匹配来源的mock订单（因为 validate_source 相等时返回False，表示验证失败）
        mock_order = MagicMock()
        mock_order.app_id = sample_customer.app_id
        mock_order.tenant_id = sample_customer.tenant_id
        mock_order.model_dump.return_value = sample_order_data
        
        # 设置mock返回值
        mock_product_service.get_order_by_order_id.return_value = mock_order
        
        # 验证抛出异常
        with pytest.raises(BenefitOrderNotFoundError):
            await benefits_service.get_order_by_order_id(sample_customer, order_id)
        
        # 验证方法调用
        mock_product_service.get_order_by_order_id.assert_called_once_with(order_id)

    @pytest.mark.asyncio
    async def test_get_order_by_out_order_id_success(
        self,
        benefits_service,
        mock_product_service,
        sample_customer,
        sample_order_data,
    ):
        """测试根据第三方订单ID获取订单成功"""
        out_order_id = "third_order_789"
        
        # 创建mock订单，确保验证通过（因为 validate_source 的逻辑是不相等时返回True）
        mock_order = MagicMock()
        mock_order.app_id = "different_app"  # 与客户不同的app_id
        mock_order.tenant_id = "different_tenant"  # 与客户不同的tenant_id
        mock_order.model_dump.return_value = sample_order_data
        
        # 设置mock返回值
        mock_product_service.get_order_by_out_order_id.return_value = mock_order
        
        # 执行查询
        result = await benefits_service.get_order_by_out_order_id(sample_customer, out_order_id)
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == sample_order_data["order_id"]
        assert result.third_order_id == sample_order_data["out_order_id"]
        assert result.status == sample_order_data["status"]
        assert result.product_code == sample_order_data["product_code"]
        assert result.price == sample_order_data["price"]
        
        # 验证方法调用
        mock_product_service.get_order_by_out_order_id.assert_called_once_with(out_order_id)

    @pytest.mark.asyncio
    async def test_get_order_by_out_order_id_source_validation_failed(
        self,
        benefits_service,
        mock_product_service,
        sample_customer,
        sample_order_data,
    ):
        """测试根据第三方订单ID获取订单时来源验证失败"""
        out_order_id = "third_order_789"
        
        # 创建匹配来源的mock订单（因为 validate_source 相等时返回False，表示验证失败）
        mock_order = MagicMock()
        mock_order.app_id = sample_customer.app_id
        mock_order.tenant_id = sample_customer.tenant_id
        mock_order.model_dump.return_value = sample_order_data
        
        # 设置mock返回值
        mock_product_service.get_order_by_out_order_id.return_value = mock_order
        
        # 验证抛出异常
        with pytest.raises(BenefitOrderNotFoundError):
            await benefits_service.get_order_by_out_order_id(sample_customer, out_order_id)
        
        # 验证方法调用
        mock_product_service.get_order_by_out_order_id.assert_called_once_with(out_order_id)

    @pytest.mark.asyncio
    async def test_get_products_success(
        self,
        benefits_service,
        mock_customer_benefits_repo,
        sample_customer,
    ):
        """测试获取权益产品列表成功"""
        # 创建mock产品数据
        mock_product_1 = MagicMock()
        mock_product_1.product = MagicMock()
        mock_product_1.product.code = "product_001"
        mock_product_1.product.name = "测试权益产品1"
        mock_product_1.id = 1
        mock_product_1.sale_price = 1000
        mock_product_1.stock = 100
        
        mock_product_2 = MagicMock()
        mock_product_2.product = MagicMock()
        mock_product_2.product.code = "product_002"
        mock_product_2.product.name = "测试权益产品2"
        mock_product_2.id = 2
        mock_product_2.sale_price = 2000
        mock_product_2.stock = 50
        
        mock_products = [mock_product_1, mock_product_2]
        
        # 设置mock返回值
        mock_customer_benefits_repo.gets_all_by_customer.return_value = mock_products
        
        # 执行查询
        result = await benefits_service.get_products(sample_customer)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) == 2
        
        # 验证第一个产品
        product_1 = result[0]
        assert isinstance(product_1, BenefitsProductDTO)
        assert product_1.id == 1
        assert product_1.code == "product_001"
        assert product_1.name == "测试权益产品1"
        assert product_1.price == 1000
        assert product_1.stock == 100
        
        # 验证第二个产品
        product_2 = result[1]
        assert isinstance(product_2, BenefitsProductDTO)
        assert product_2.id == 2
        assert product_2.code == "product_002"
        assert product_2.name == "测试权益产品2"
        assert product_2.price == 2000
        assert product_2.stock == 50
        
        # 验证方法调用
        mock_customer_benefits_repo.gets_all_by_customer.assert_called_once_with(
            customer_code=sample_customer.code
        )

    @pytest.mark.asyncio
    async def test_get_products_empty_list(
        self,
        benefits_service,
        mock_customer_benefits_repo,
        sample_customer,
    ):
        """测试获取权益产品列表为空"""
        # 设置mock返回空列表
        mock_customer_benefits_repo.gets_all_by_customer.return_value = []
        
        # 执行查询
        result = await benefits_service.get_products(sample_customer)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) == 0
        
        # 验证方法调用
        mock_customer_benefits_repo.gets_all_by_customer.assert_called_once_with(
            customer_code=sample_customer.code
        )

    @pytest.mark.asyncio
    async def test_error_handling_order_service_exception(
        self,
        benefits_service,
        mock_product_service,
        sample_customer,
    ):
        """测试订单服务异常处理"""
        order_id = "order_123456"
        
        # 设置mock抛出异常
        mock_product_service.get_order_by_order_id.side_effect = Exception("Database error")
        
        # 验证异常传播
        with pytest.raises(Exception, match="Database error"):
            await benefits_service.get_order_by_order_id(sample_customer, order_id)

    @pytest.mark.asyncio
    async def test_error_handling_repository_exception(
        self,
        benefits_service,
        mock_customer_benefits_repo,
        sample_customer,
    ):
        """测试仓储异常处理"""
        # 设置mock抛出异常
        mock_customer_benefits_repo.gets_all_by_customer.side_effect = Exception("Repository error")
        
        # 验证异常传播
        with pytest.raises(Exception, match="Repository error"):
            await benefits_service.get_products(sample_customer)


@pytest.mark.openapi
@pytest.mark.query_service
@pytest.mark.integration
class TestBenefitsQueryServiceIntegration:
    """权益查询服务集成测试"""

    @pytest.mark.asyncio
    async def test_complete_benefits_query_flow(self):
        """测试完整的权益查询流程"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        order_data = OpenAPITestDataFactory.create_order_data()
        
        # 创建mock对象
        mock_product_service = AsyncMock()
        mock_customer_benefits_repo = AsyncMock()
        
        # 创建服务
        service = BenefitsQueryService(
            product_service=mock_product_service,
            customer_benefits_repo=mock_customer_benefits_repo,
        )
        
        # 创建客户实体
        customer = CustomerEntity(**customer_data)
        
        # 创建mock订单，确保验证通过（因为 validate_source 的逻辑是不相等时返回True）
        mock_order = MagicMock()
        mock_order.app_id = "different_app"  # 与客户不同的app_id
        mock_order.tenant_id = "different_tenant"  # 与客户不同的tenant_id
        mock_order.model_dump.return_value = order_data
        
        # 设置mock返回值
        mock_product_service.get_order_by_order_id.return_value = mock_order
        
        # 执行查询
        result = await service.get_order_by_order_id(customer, order_data["order_id"])
        
        # 验证结果
        assert isinstance(result, BenefitsOrderDTO)
        assert result.order_id == order_data["order_id"]
        assert result.status == order_data["status"]
        
        # 验证调用链
        mock_product_service.get_order_by_order_id.assert_called_once_with(order_data["order_id"])

    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """测试错误传播机制"""
        # 创建minimal服务实例
        service = BenefitsQueryService(
            product_service=AsyncMock(),
            customer_benefits_repo=AsyncMock(),
        )
        
        customer = CustomerEntity(
            id=1, code="test", name="test", description="test",
            app_id="test", tenant_id="test", balance=0
        )
        
        # 测试BenefitOrderNotFoundError (来源匹配，因为validate_source相等时返回False)
        mock_order = MagicMock()
        mock_order.app_id = customer.app_id
        mock_order.tenant_id = customer.tenant_id
        service.product_service.get_order_by_order_id.return_value = mock_order
        
        with pytest.raises(BenefitOrderNotFoundError):
            await service.get_order_by_order_id(customer, "test_order")
        
        # 测试其他异常的传播
        service.product_service.get_order_by_order_id.side_effect = Exception("Service error")
        
        with pytest.raises(Exception, match="Service error"):
            await service.get_order_by_order_id(customer, "test_order_2")