# encoding: utf-8
# src/infrastructures/rabbitmq/settings.py
# created: 2025-07-27 18:35:43

from typing import Optional

from pydantic import BaseModel


class RabbitmqSettings(BaseModel):
    """消息队列配置

    注意：生产环境必须通过环境变量或配置文件提供 RabbitMQ 连接信息
    """

    url: str = ""
    pool_size: int = 2
    max_channels_per_connection: int = 50
    heartbeat: int = 60
    connection_timeout: int = 10
    prefetch_count: int = 2  # 增加预取数量，提升并发性能
    consumer_instances: int = 1
    mandatory: bool = True
