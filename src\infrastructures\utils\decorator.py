# encoding: utf-8

import asyncio
import functools
import json
import pickle
from contextvars import Context<PERSON><PERSON>
from enum import Enum
from typing import Any, Callable, Optional, Union

from loguru import logger
from redis.asyncio import Redis

from src.infrastructures.exceptions import RetryableError

# 上下文变量，用于存储 Redis 实例
redis_context: ContextVar[Optional[Redis]] = ContextVar("redis_context", default=None)


class SerializationType(Enum):
    """序列化类型"""

    JSON = "json"
    PICKLE = "pickle"


def retry(max_attempts: int = 3, delay: float = 1.0):
    """
    通用装饰器：重试指定次数，失败时自动等待
    - 捕获所有异常
    - 返回值由函数自身决定
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except RetryableError as e:
                    logger.debug(
                        f"retry_attempt: {attempt}, {e.__getattribute__('retryable')}, {str(e)} {e.__class__}, {type(e)}"
                    )
                    if e.retryable:
                        logger.debug(f"重试 {attempt + 1} 次，失败: {e}")
                        if attempt < max_attempts - 1:
                            await asyncio.sleep(delay)
                        else:
                            raise e
                    else:
                        raise e

        return wrapper

    return decorator


def cache(
    key_prefix: str,
    ttl: int = 300,
    key_func: Optional[Callable] = None,
    redis_getter: Optional[Callable] = None,
    serialization: Union[SerializationType, str] = SerializationType.PICKLE,
) -> Callable:
    """
    缓存装饰器，支持多种 Redis 获取方式和序列化类型

    Args:
        key_prefix: 缓存键前缀
        ttl: 过期时间（秒），默认300秒
        key_func: 自定义键生成函数，接收函数参数返回键后缀
        redis_getter: 获取 Redis 实例的函数
        serialization: 序列化类型，支持 'pickle' (默认) 和 'json'

    使用示例:
        1. 在有 self.redis 的类中:
            @cache(key_prefix="shop", ttl=600)
            async def get_shop(self, shop_id: str):
                ...

        2. 通过 redis_getter:
            @cache(
                key_prefix="user",
                redis_getter=lambda: container.infrastructures.redis_manager().client
            )
            async def get_user(user_id: str):
                ...

        3. 通过上下文变量:
            redis_context.set(redis_client)
            @cache(key_prefix="config")
            async def get_config(key: str):
                ...

        4. 使用 JSON 序列化（适合简单数据结构）:
            @cache(key_prefix="simple", serialization="json")
            async def get_simple_data(id: str):
                return {"id": id, "name": "test"}

        5. 使用 Pickle 序列化（适合复杂对象，默认）:
            @cache(key_prefix="complex", serialization="pickle")
            async def get_complex_data(id: str):
                return ComplexObject(id)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # 1. 获取 Redis 实例
            redis_client = None

            # 优先使用 redis_getter
            if redis_getter:
                try:
                    redis_client = redis_getter()
                except Exception as e:
                    logger.warning(f"Failed to get redis from getter: {e}")

            # 尝试从 self.redis 获取（如果第一个参数是 self）
            if not redis_client and args:
                instance = args[0]
                if hasattr(instance, "redis"):
                    redis_client = instance.redis

            # 最后尝试从上下文获取
            if not redis_client:
                redis_client = redis_context.get()

            # 如果没有 Redis，直接执行函数
            if not redis_client:
                logger.debug(f"No redis client available for caching {func.__name__}")
                return await func(*args, **kwargs)

            # 确定序列化类型
            if isinstance(serialization, str):
                ser_type = SerializationType(serialization.lower())
            else:
                ser_type = serialization

            # 2. 生成缓存键
            if key_func:
                key_suffix = key_func(*args, **kwargs)
            else:
                # 默认使用所有参数生成键
                key_parts = []
                # 跳过 self 参数
                start_idx = 1 if args and hasattr(args[0], "__dict__") else 0
                for arg in args[start_idx:]:
                    key_parts.append(str(arg))
                for k, v in sorted(kwargs.items()):
                    key_parts.append(f"{k}:{v}")
                key_suffix = ":".join(key_parts) if key_parts else "default"

            cache_key = f"{key_prefix}:{key_suffix}"

            # 3. 尝试从缓存获取
            try:
                cached_value = await redis_client.get(cache_key)
                if cached_value:
                    logger.debug(f"Cache hit for key: {cache_key}")
                    if ser_type == SerializationType.JSON:
                        return json.loads(cached_value.decode() if isinstance(cached_value, bytes) else cached_value)
                    else:  # PICKLE
                        return pickle.loads(cached_value)
            except Exception as e:
                logger.warning(f"Failed to get from cache: {e}")

            # 4. 执行函数
            result = await func(*args, **kwargs)

            # 5. 存入缓存
            try:
                if ser_type == SerializationType.JSON:
                    serialized_data = json.dumps(result, ensure_ascii=False, default=str).encode()
                else:  # PICKLE
                    serialized_data = pickle.dumps(result)

                await redis_client.set(cache_key, serialized_data, ex=ttl)
                logger.debug(f"Cached result for key: {cache_key}, ttl: {ttl}s, serialization: {ser_type.value}")
            except Exception as e:
                logger.warning(f"Failed to cache result: {e}")

            return result

        return wrapper

    return decorator
