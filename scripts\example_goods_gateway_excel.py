# encoding: utf-8
# scripts/example_goods_gateway_excel.py
# created: 2025-08-18 17:00:00

"""
饿了么联盟商品网关示例脚本 - 导出商品信息到Excel

功能：
1. 获取新天特商品列表
2. 获取每个商品的详情
3. 获取商品的门店信息
4. 导出所有信息到Excel文件
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
import pandas as pd
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from redis.asyncio import Redis
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn

from src.infrastructures.gateways.eleme.union import ElemeUnionGoodsGateway
from src.infrastructures.gateways.eleme.union.settings import ElemeUnionSettings

console = Console()


async def fetch_all_goods(gateway: ElemeUnionGoodsGateway, max_pages: int = 5) -> List[Dict[str, Any]]:
    """获取所有商品信息"""
    all_goods = []
    session_id = None

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        # 获取商品列表
        list_task = progress.add_task("[cyan]获取商品列表...", total=max_pages)

        for page in range(1, max_pages + 1):
            try:
                goods_list = await gateway.get_goods_list(
                    page_size=20, page_number=page, item_type=4, session_id=session_id  # 新天特
                )

                if page == 1:
                    session_id = goods_list.session_id
                    console.print(f"[green]总共有 {goods_list.total_count} 个商品[/green]")

                for item in goods_list.records:
                    all_goods.append(
                        {
                            "item_id": item.item_id,
                            "item_name": item.item_name,
                            "item_type": item.item_type,
                        }
                    )

                progress.update(list_task, advance=1)

                # 如果没有更多商品，提前结束
                if len(goods_list.records) < 20:
                    break

            except Exception as e:
                console.print(f"[red]获取第{page}页商品列表失败: {e}[/red]")
                break

    return all_goods


async def fetch_goods_details(
    gateway: ElemeUnionGoodsGateway, goods_list: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """获取所有商品的详情"""
    detailed_goods = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        detail_task = progress.add_task("[cyan]获取商品详情...", total=len(goods_list))

        for goods in goods_list:
            try:
                # 获取商品详情
                detail = await gateway.get_goods_detail(item_id=goods["item_id"], item_type=4)

                # 构建商品详情字典
                goods_info = {
                    "商品ID": detail.item_id,
                    "商品名称": detail.item_name,
                    "商品类型": detail.item_type,
                    "业务类型": detail.biz_type,
                    "图片URL": detail.picture,
                    "原价(元)": detail.original_price_cent / 100,
                    "售价(元)": detail.sell_price_cent / 100,
                    "活动价(元)": detail.activity_price_cent / 100 if detail.activity_price_cent else None,
                    "折扣": detail.discount,
                    "佣金比例": detail.commission_rate,
                    "预估佣金(元)": detail.commission / 100 if detail.commission else None,
                    "适用门店数": detail.apply_shop_count,
                    "适用城市数": detail.apply_city_count,
                    "开始时间": datetime.fromtimestamp(detail.start_time).strftime("%Y-%m-%d %H:%M:%S"),
                    "结束时间": datetime.fromtimestamp(detail.end_time).strftime("%Y-%m-%d %H:%M:%S"),
                }

                # 添加限购信息
                if detail.purchase_limit:
                    limit = detail.purchase_limit
                    goods_info.update(
                        {
                            "每人每天限购": (
                                limit.item_daily_limit_per_user if limit.item_daily_limit_per_user > 0 else "不限"
                            ),
                            "每人终身限购": limit.item_limit_per_user if limit.item_limit_per_user > 0 else "不限",
                            "每人每订单限购": (
                                limit.item_limit_per_user_order
                                if limit.item_limit_per_user_order and limit.item_limit_per_user_order > 0
                                else "不限"
                            ),
                            "活动每人每天限购": (
                                limit.activity_daily_limit_per_user
                                if limit.activity_daily_limit_per_user > 0
                                else "不限"
                            ),
                            "活动每人限购": (
                                limit.activity_limit_per_user if limit.activity_limit_per_user > 0 else "不限"
                            ),
                        }
                    )
                else:
                    goods_info.update(
                        {
                            "每人每天限购": "不限",
                            "每人终身限购": "不限",
                            "每人每订单限购": "不限",
                            "活动每人每天限购": "不限",
                            "活动每人限购": "不限",
                        }
                    )

                # 添加凭证信息
                if detail.ticket:
                    goods_info.update(
                        {
                            "凭证价格(元)": detail.ticket.price / 100 if detail.ticket.price else None,
                            "凭证数量": detail.ticket.quantity,
                            "使用门槛(元)": detail.ticket.threshold / 100 if detail.ticket.threshold else None,
                        }
                    )
                else:
                    goods_info.update(
                        {
                            "凭证价格(元)": None,
                            "凭证数量": None,
                            "使用门槛(元)": None,
                        }
                    )

                # 添加服务信息（简化）
                if detail.services:
                    service_contents = []
                    for service in detail.services[:3]:
                        if service.contents:
                            service_contents.extend(service.contents[:2])
                    goods_info["服务信息"] = ", ".join(service_contents[:5])
                else:
                    goods_info["服务信息"] = ""

                # 添加使用规则（简化）
                if detail.item_rules:
                    rule_contents = []
                    for rule in detail.item_rules[:2]:
                        if rule.title:
                            rule_contents.append(rule.title)
                    goods_info["使用规则"] = ", ".join(rule_contents)
                else:
                    goods_info["使用规则"] = ""

                detailed_goods.append(goods_info)
                progress.update(detail_task, advance=1)

                # 避免请求过快
                await asyncio.sleep(0.05)  # 减少延迟到50ms

            except Exception as e:
                console.print(f"[yellow]获取商品 {goods['item_name']} 详情失败: {e}[/yellow]")
                progress.update(detail_task, advance=1)
                continue

    return detailed_goods


async def fetch_goods_stores_sample(
    gateway: ElemeUnionGoodsGateway, goods_list: List[Dict[str, Any]], sample_size: int = 5
) -> List[Dict[str, Any]]:
    """获取部分商品的门店信息作为示例"""
    stores_data = []

    # 只获取前几个商品的门店信息作为示例
    sample_goods = goods_list[:sample_size]

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeRemainingColumn(),
        console=console,
    ) as progress:
        stores_task = progress.add_task("[cyan]获取门店信息示例...", total=len(sample_goods))

        for goods in sample_goods:
            try:
                stores = await gateway.get_goods_stores(
                    item_id=goods["商品ID"], page_size=10, page_number=1, item_type=4  # 每个商品只获取前10个门店
                )

                for store in stores.records[:10]:
                    stores_data.append(
                        {
                            "商品ID": goods["商品ID"],
                            "商品名称": goods["商品名称"],
                            "门店ID": store.store_id,
                            "门店名称": store.name,
                            "城市编码": store.city_code,
                            "门店地址": store.address,
                            "经度": store.longitude,
                            "纬度": store.latitude,
                            "门店总数": stores.total_count,
                        }
                    )

                progress.update(stores_task, advance=1)
                await asyncio.sleep(0.1)

            except Exception as e:
                console.print(f"[yellow]获取商品 {goods['商品名称']} 门店信息失败: {e}[/yellow]")
                progress.update(stores_task, advance=1)
                continue

    return stores_data


async def main():
    """主函数"""

    # 配置信息
    config = ElemeUnionSettings(
        app_key=os.getenv("ELEME_APP_KEY", "34813436"),
        app_secret=os.getenv("ELEME_APP_SECRET", "929eafdde0fb34c57ff0290e271b54c3"),
        top_gateway_url="https://eco.taobao.com/router/rest",
    )

    # 创建Redis连接
    redis = Redis.from_url("redis://localhost:6379/0", decode_responses=True)

    try:
        # 创建网关实例
        gateway = ElemeUnionGoodsGateway(config=config, redis=redis)

        console.print("\n[bold cyan]========== 饿了么联盟商品信息导出 ==========[/bold cyan]\n")

        # 1. 获取商品列表
        console.print("[bold yellow]步骤 1: 获取商品列表[/bold yellow]")
        goods_list = await fetch_all_goods(gateway, max_pages=20)  # 获取前20页（约400个商品）
        console.print(f"[green]✓ 获取到 {len(goods_list)} 个商品[/green]\n")

        if not goods_list:
            console.print("[red]没有获取到商品数据，请检查配置[/red]")
            return

        # 2. 获取商品详情
        console.print("[bold yellow]步骤 2: 获取商品详情[/bold yellow]")
        detailed_goods = await fetch_goods_details(gateway, goods_list)
        console.print(f"[green]✓ 获取到 {len(detailed_goods)} 个商品的详情[/green]\n")

        # 3. 获取部分商品的门店信息
        console.print("[bold yellow]步骤 3: 获取门店信息示例[/bold yellow]")
        stores_data = await fetch_goods_stores_sample(gateway, detailed_goods, sample_size=10)  # 增加到10个商品
        console.print(f"[green]✓ 获取到 {len(stores_data)} 条门店信息[/green]\n")

        # 4. 导出到Excel
        console.print("[bold yellow]步骤 4: 导出到Excel文件[/bold yellow]")

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"eleme_goods_{timestamp}.xlsx"

        # 创建Excel writer
        with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
            # 导出商品详情
            if detailed_goods:
                df_goods = pd.DataFrame(detailed_goods)
                df_goods.to_excel(writer, sheet_name="商品详情", index=False)
                console.print(f"[green]✓ 导出商品详情到 '商品详情' 工作表[/green]")

            # 导出门店信息
            if stores_data:
                df_stores = pd.DataFrame(stores_data)
                df_stores.to_excel(writer, sheet_name="门店信息示例", index=False)
                console.print(f"[green]✓ 导出门店信息到 '门店信息示例' 工作表[/green]")

            # 创建统计信息表
            stats_data = {
                "统计项": ["商品总数", "有详情的商品数", "门店信息条数", "导出时间"],
                "数值": [
                    len(goods_list),
                    len(detailed_goods),
                    len(stores_data),
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                ],
            }
            df_stats = pd.DataFrame(stats_data)
            df_stats.to_excel(writer, sheet_name="统计信息", index=False)
            console.print(f"[green]✓ 导出统计信息到 '统计信息' 工作表[/green]")

        console.print(f"\n[bold green]========== 导出完成 ==========[/bold green]")
        console.print(f"[cyan]文件已保存到: {output_file}[/cyan]")
        console.print(f"[dim]文件包含 3 个工作表：商品详情、门店信息示例、统计信息[/dim]\n")

    except Exception as e:
        console.print(f"[bold red]发生错误: {e}[/bold red]")
        import traceback

        traceback.print_exc()

    finally:
        # 关闭Redis连接
        await redis.aclose()


def run_example():
    """运行示例"""
    console.print("[bold]启动饿了么联盟商品信息导出...[/bold]")

    # 检查环境变量
    required_env_vars = ["ELEME_APP_KEY", "ELEME_APP_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        console.print("[yellow]警告: 以下环境变量未设置:[/yellow]")
        for var in missing_vars:
            console.print(f"  - {var}")
        console.print("\n[dim]请设置环境变量或修改脚本中的配置信息[/dim]")
        console.print("[dim]示例: export ELEME_APP_KEY=your_app_key[/dim]\n")

    # 检查pandas是否安装
    try:
        import pandas
        import openpyxl
    except ImportError:
        console.print("[red]错误: 需要安装pandas和openpyxl库[/red]")
        console.print("[dim]请运行: poetry add pandas openpyxl[/dim]\n")
        return

    # 运行异步主函数
    asyncio.run(main())


if __name__ == "__main__":
    run_example()
