# encoding: utf-8
# src/repositories/growth_hacker/tasks.py
# created: 2025-07-25 17:11:54

from datetime import datetime, timedelta
from typing import List, Optional

from tortoise.exceptions import DoesNotExist

from src.databases.models.growth_hacker import GrowthHackerTask, TaskStatus


class TaskRepository:
    """Growth Hacker 任务仓库"""

    @staticmethod
    async def create(
        task_id: str,
        phone: str,
        city: Optional[str] = None,
        lat: Optional[str] = None,
        lng: Optional[str] = None,
        access_url: Optional[str] = None,
        batch_name: Optional[str] = None,
    ) -> GrowthHackerTask:
        """创建任务"""
        task = await GrowthHackerTask.create(
            task_id=task_id,
            phone=phone,
            city=city,
            lat=lat,
            lng=lng,
            access_url=access_url,
            batch_name=batch_name,
        )
        return task

    @staticmethod
    async def get_by_task_id(task_id: str) -> Optional[GrowthHackerTask]:
        """通过任务ID获取任务"""
        try:
            return await GrowthHackerTask.get(task_id=task_id)
        except DoesNotExist:
            return None

    @staticmethod
    async def get_pending_tasks(limit: int = 10) -> List[GrowthHackerTask]:
        """获取待处理任务"""
        return await GrowthHackerTask.filter(status=TaskStatus.PENDING).order_by("created_at").limit(limit)

    @staticmethod
    async def get_by_phone_and_status(phone: str, status: TaskStatus) -> List[GrowthHackerTask]:
        """根据手机号和状态获取任务"""
        return await GrowthHackerTask.filter(phone=phone, status=status).order_by("-created_at")

    @staticmethod
    async def update_status(
        task_id: str,
        status: TaskStatus,
        message: str = "",
        execution_time: Optional[float] = None,
        page_content_html: Optional[str] = None,
    ) -> bool:
        """更新任务状态"""
        update_data = {
            "status": status,
            "message": message,
        }

        if execution_time is not None:
            update_data["execution_time"] = execution_time  # type: ignore

        if page_content_html is not None:
            update_data["page_content_html"] = page_content_html

        if status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.ALREADY_CLAIMED, TaskStatus.RISK_DETECTED]:
            update_data["processed_at"] = datetime.now()  # type: ignore

        updated = await GrowthHackerTask.filter(task_id=task_id).update(**update_data)
        return updated > 0

    @staticmethod
    async def increment_retry_count(task_id: str) -> bool:
        """增加重试次数"""
        task = await TaskRepository.get_by_task_id(task_id)
        if task:
            task.retry_count += 1
            await task.save(update_fields=["retry_count"])
            return True
        return False

    @staticmethod
    async def get_tasks_by_batch(batch_name: str) -> List[GrowthHackerTask]:
        """根据批次名称获取任务"""
        return await GrowthHackerTask.filter(batch_name=batch_name).order_by("created_at")

    @staticmethod
    async def count_by_status(status: TaskStatus) -> int:
        """统计指定状态的任务数量"""
        return await GrowthHackerTask.filter(status=status).count()

    @staticmethod
    async def delete_old_tasks(days: int = 30) -> int:
        """删除旧任务"""

        cutoff_date = datetime.now() - timedelta(days=days)
        return await GrowthHackerTask.filter(created_at__lt=cutoff_date).delete()

    async def get_by_phone_process_date(self, phone: str, target_date: datetime) -> Optional[GrowthHackerTask]:
        """根据手机号和处理日期获取任务（检查当天是否已有成功任务）"""
        # 获取目标日期的开始和结束时间
        start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        return await GrowthHackerTask.get_or_none(
            phone=phone,
            status__in=[
                TaskStatus.SUCCESS,
                TaskStatus.ALREADY_CLAIMED,
                TaskStatus.RISK_DETECTED,
                TaskStatus.RUNNING,
            ],  # 成功状态的任务
            processed_at__gte=start_of_day,
            processed_at__lte=end_of_day,
        )
