# encoding: utf-8
# src/infrastructures/gateways/eleme/aixincan.py
# created: 2025-07-30 15:26:54

import base64
import hashlib
import hmac
import json
import time
import uuid
from typing import Any, Optional

import aiohttp
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from loguru import logger

from src.infrastructures.errors import BusinessError


class ElemeAixincanAESUtils:
    """饿了么爱心餐AES加密解密工具类"""

    SECRET_KEY = "TOos9tG4fEuNvkmOBExMtQ=="

    @classmethod
    def encrypt(cls, data: str, key: Optional[str] = None) -> str:
        if key is None:
            key = cls.SECRET_KEY
        key_bytes = key.encode("utf-8")
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        padded_data = pad(data.encode("utf-8"), AES.block_size)
        encrypted_data = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted_data).decode("utf-8")

    @classmethod
    def decrypt(cls, encrypted_data: str, key: Optional[str] = None) -> str:
        if key is None:
            key = cls.SECRET_KEY
        key_bytes = key.encode("utf-8")
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        encrypted_bytes = base64.b64decode(encrypted_data)
        decrypted_padded = cipher.decrypt(encrypted_bytes)
        return unpad(decrypted_padded, AES.block_size).decode("utf-8")


class UserAlreadyExistsError(BusinessError):
    """用户已存在"""

    def __init__(self):
        super().__init__(500, "数据推送失败，手机号已存在")


class AixincanSignError(BusinessError):
    """爱心餐验签错误"""

    def __init__(self):
        super().__init__(998, "接口验签失败，请检查")


class ElemeAixincanGateway:
    """饿了么爱心餐客户端"""

    BASE_URL = "https://dbl-api.ele.me"

    def __init__(self):
        self.access_key = "a78f9928f2789dd1842848d464fdc9ad"
        self.access_secret = "c99058a41ce8bfcfcd6d873c563b2c26HeiO5td1HgO6BXBvtYuBtFoQ3Tw21YQ8"
        self.interface_code = "00020240825101032381"

    @staticmethod
    def hmac_sha256(data: str, key: str) -> bytes:
        """HMAC-SHA256签名"""
        return hmac.new(key.encode("utf-8"), data.encode("utf-8"), hashlib.sha256).digest()

    def _gen_sign(self, base_params: dict[str, Any], biz_params: dict[str, Any], access_secret: str) -> str:
        key_list = ["accessKey", "sign", "interfaceCode", "timestampStr", "nonceStr"]
        params = {}
        if biz_params:
            for key, value in biz_params.items():
                if key in key_list:
                    key = "bizParam." + key
                params[key] = value
        params["accessKey"] = base_params["accessKey"]
        params["timestampStr"] = base_params["timestampStr"]
        params["nonceStr"] = base_params["nonceStr"]
        params["interfaceCode"] = base_params["interfaceCode"]

        params = dict(sorted(params.items()))

        str_list = []
        for key, value in params.items():
            if value is None or (isinstance(value, str) and not value.strip()):
                continue
            if isinstance(value, (str, int, float, bool)):
                str_list.append(f"{key}={value}")
            else:
                str_list.append(f"{key}={json.dumps(value, ensure_ascii=False, separators=(",", ":"), sort_keys=True)}")
        s = "&".join(str_list)
        s += f"&accessSecret={access_secret}"
        signature = self.hmac_sha256(s, access_secret)
        return base64.b64encode(signature).decode("utf-8")

    def _build_param(
        self,
        interface_code: str,
        access_key: str,
        access_secret: str,
        is_local_cache: bool,
        is_remote_cache: bool,
        biz_params: dict,
    ) -> dict:
        query_params = {}
        base_params = {}
        base_params["interfaceCode"] = interface_code
        base_params["accessKey"] = access_key
        base_params["nonceStr"] = str(uuid.uuid4().hex)
        base_params["timestampStr"] = str(int(time.time() * 1000))
        base_params["isLocalCache"] = is_local_cache  # type: ignore
        base_params["isRemoteCache"] = is_remote_cache  # type: ignore

        query_params["bizParam"] = biz_params

        other_extra = {"appName": "alsc_dbl_gateway"}
        query_params["otherExtra"] = other_extra
        sign = self._gen_sign(base_params, biz_params, self.access_secret)
        base_params["sign"] = sign
        query_params["baseParam"] = base_params
        return query_params

    async def add_aixincan_user(
        self,
        phone: str,
        province_name: str,
        city_name: str,
        district_name: Optional[str] = None,
        industry: Optional[str] = None,
    ) -> dict[str, Any]:
        biz_params = {
            "data": {
                "data": {
                    "acctMobile": ElemeAixincanAESUtils.encrypt(phone),
                    "provinceName": province_name,
                    "cityName": city_name,
                }
            }
        }
        if district_name:
            biz_params["data"]["data"]["districtName"] = district_name
        if industry:
            biz_params["data"]["data"]["industry"] = industry
        params = self._build_param(
            self.interface_code,
            self.access_key,
            self.access_secret,
            is_local_cache=False,
            is_remote_cache=False,
            biz_params=biz_params,
        )
        endpoint = f"{self.BASE_URL}/queryData/commonDataEnter"
        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, json=params) as response:
                response.raise_for_status()
                result = await response.json()
                if not result.get("success", False):
                    logger.error(f"饿了么爱心餐用户添加失败: {result}")
                    raise BusinessError(code=int(result.get("resultCode")), message=result.get("resultMessage"))
                else:
                    data = result.get("data", {}).get("queryForData", {})
                    if data.get("code", 0) == 500:
                        raise UserAlreadyExistsError()
                    return data
