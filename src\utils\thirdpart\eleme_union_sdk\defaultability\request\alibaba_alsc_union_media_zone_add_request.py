from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionMediaZoneAddRequest(BaseRequest):

    def __init__(self, zone_name: str = None, media_id: str = None):
        """
        推广位名称
        """
        self._zone_name = zone_name
        """
            媒体id，工具商渠道必填
        """
        self._media_id = media_id

    @property
    def zone_name(self):
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        if isinstance(zone_name, str):
            self._zone_name = zone_name
        else:
            raise TypeError("zone_name must be str")

    @property
    def media_id(self):
        return self._media_id

    @media_id.setter
    def media_id(self, media_id):
        if isinstance(media_id, str):
            self._media_id = media_id
        else:
            raise TypeError("media_id must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.media.zone.add"

    def to_dict(self):
        request_dict = {}
        if self._zone_name is not None:
            request_dict["zone_name"] = convert_basic(self._zone_name)

        if self._media_id is not None:
            request_dict["media_id"] = convert_basic(self._media_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
