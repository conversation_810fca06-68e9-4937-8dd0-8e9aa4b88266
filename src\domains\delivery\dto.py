# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/dto.py
# created: 2024-12-03 00:48:41
# updated: 2025-06-10 09:16:00

import json
from datetime import datetime
from enum import IntEnum, StrEnum
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, model_validator
from tortoise.contrib.pydantic import pydantic_model_creator

from src.databases.models.delivery import DeliveryPage, DeliveryUnionOrder
from src.utils.eleme.dto import UnionPageLinkDTO

DeliveryPageDTO = pydantic_model_creator(
    DeliveryPage,
    optional=(
        "id",
        "created_at",
        "updated_at",
        "code",
        "description",
        "comment",
    ),
)
UnionOrderDTO = pydantic_model_creator(DeliveryUnionOrder)


UnionActivityUrlDTO = UnionPageLinkDTO


# class UnionActivityUrlDTO(BaseModel):
#     sid: Optional[str] = Field("", description="sid")
#     h5_url: Optional[str] = Field("", description="h5 url")
#     eleme_url: Optional[str] = Field("", description="饿了么url")
#     taobao_url: Optional[str] = Field("", description="淘宝url")
#     wechat_schema: Optional[str] = Field("", description="wechat schema")
#     wechat_shortlink: Optional[str] = Field("", description="wechat short link")
#     wechat_openlink: Optional[str] = Field("", description="wechat open link")
#     alipay_schema: Optional[str] = Field("", description="alipay schema")
#     taobao_schema: Optional[str] = Field("", description="taobao schema")
#     eleme_schema: Optional[str] = Field("", description="eleme schema")


class OrderStateEnum(IntEnum):
    INVALID = 0
    ORDERED = 1
    PAID = 2
    RECEIVED = 4


class SettleStateEnum(IntEnum):
    # 1-已结算； 2-未结算；-99无需结算
    SETTLED = 1
    UNSETTLED = 2
    NO_SETTLEMENT_REQUIRED = -99


class UnionOrderInfoDTO(BaseModel):
    activity_info_remark_list: str = Field("", description="活动信息备注列表")
    activity_id: int = Field(-1, description="活动id")
    ad_zone_id: Optional[str] = Field("", description="广告位ID")
    ad_zone_name: Optional[str] = Field("", description="广告位名称")
    biz_order_id: int = Field(..., description="业务订单ID")
    category_name: Optional[str] = Field("", description="类别名称")
    channel_fee: int = Field(..., description="渠道费用（单位分）")
    channel_rate: float = Field(..., description="渠道费率")
    commission_fee: int = Field(..., description="佣金费用（单位分）")
    commission_rate: float = Field(..., description="佣金费率")
    ext_info: dict = Field(..., description="扩展信息")
    flow_type: int = Field(..., description="流量类型")
    full_settle_amount: int = Field(..., description="全额结算金额（单位分）")
    gmt_modified: datetime = Field(..., description="修改时间")
    income: int = Field(..., description="收入（单位分）")
    income_rate: float = Field(..., description="收入费率")
    item_id: str = Field(..., description="商品ID")
    media_id: str = Field(..., description="媒体ID")
    media_name: Optional[str] = Field("", description="媒体名称")
    order_item_status: int = Field(..., description="订单商品状态")
    order_state: int = Field(..., description="订单状态")
    parent_order_id: int = Field(..., description="父订单ID", serialization_alias="order_id")
    pay_amount: Optional[int] = Field(None, description="支付金额（单位分）")
    pay_time: Optional[datetime] = Field(None, description="支付时间")
    pic_url: Optional[str] = Field("", description="图片URL")
    pid: Optional[str] = Field("", description="推广位ID")
    platform_commission_fee: int = Field(..., description="平台佣金费用（单位分）")
    platform_commission_rate: float = Field(..., description="平台佣金费率")
    platform_type: int = Field(..., description="平台类型")
    product_num: int = Field(..., description="商品数量")
    settle: int = Field(..., description="结算金额（单位分）")
    settle_amount: int = Field(..., description="结算总金额（单位分）")
    settle_state: int = Field(..., description="结算状态")
    shop_name: str = Field(..., description="店铺名称")
    subsidy_fee: int = Field(..., description="补贴费用（单位分）")
    subsidy_rate: float = Field(..., description="补贴费率")
    title: str = Field(..., description="商品标题")
    tk_create_time: datetime = Field(..., description="淘客创建时间")
    trace_time: datetime = Field(..., description="跟踪时间")
    sid: Optional[str] = Field("", description="sid")
    receive_time: Optional[datetime] = Field(None, description="收货时间")
    settle_time: Optional[datetime] = Field(None, description="结算时间")
    unit_price: int = Field(..., description="单价（单位分）")
    source_info: Optional[dict] = Field(None, description="海狸扩展信息, 用于记录海狸来源相关扩展信息")

    @model_validator(mode="before")
    def model_verify(self):
        amount_fields = [
            "channel_fee",
            "commission_fee",
            "full_settle_amount",
            "income",
            "pay_amount",
            "platform_commission_fee",
            "settle",
            "settle_amount",
            "subsidy_fee",
            "unit_price",
        ]
        data = dict(self)
        for field in amount_fields:
            # 检查字段是否存在
            if field not in data:
                continue
            # 检查字段是否已经是整数，跳过已处理的数据
            if isinstance(data[field], int):
                continue
            # 转换金额字段，将字符串或浮点数转为整数（单位分）
            data[field] = int(float(data[field]) * 100)

        # 处理 ext_info 字段
        if "ext_info" in data and isinstance(data["ext_info"], str):
            data["ext_info"] = json.loads(data["ext_info"])

        return data


# ------------------------------------------------------------
# 统计信息
# ------------------------------------------------------------


class StatisticsByHoursDTO(BaseModel):
    time: str = Field(..., description="时间")
    count: int = Field(..., description="数量")
    order_amount: int = Field(..., description="订单金额")
    pay_amount: int = Field(..., description="支付金额")
    settle_amount: int = Field(..., description="结算金额")


class StatisticsDTO(BaseModel):
    order_count: int = Field(..., description="订单数量")
    order_amount: int = Field(..., description="订单金额")
    pay_amount: int = Field(..., description="支付金额")
    settle_amount: int = Field(..., description="结算金额")
    statistics_by_hours: list[StatisticsByHoursDTO] = Field(..., description="按小时统计信息")


class DingtalkOrderNotifyDTO(UnionOrderDTO):
    """钉钉订单通知消息"""

    user_from: Optional[str] = Field(None, description="订单渠道来源")
    user_open_id: Optional[str] = Field(None, description="用户钉钉open_id")


# -------------------------


class H5AccessLink(BaseModel):
    h5_img_url: Optional[str] = Field(None, description="h5图片链接")
    h5_qr_code: Optional[str] = Field(None, description="h5二维码链接")
    h5_url: Optional[str] = Field(None, description="h5链接")
    short_link: Optional[str] = Field(None, description="短链接")


class UserAddressPoiDTO(BaseModel):
    address_detail: str = Field(default="", description="详细地址", alias="addressDetail")
    complete_address: str = Field(default="", description="完整地址", alias="completeAddress")
    id: str = Field(default="", description="地址ID")
    latitude: str = Field(default="", description="纬度")
    longitude: str = Field(default="", description="经度")
    poi_id: str = Field(default="", description="POI ID", alias="poiId")
    poi_name: str = Field(default="", description="POI名称", alias="poiName")
    poi_type: str = Field(default="", description="POI类型", alias="poiType")

    class Config:
        populate_by_name = True


class ElemeAddressDTO(BaseModel):
    address: str = Field(default="", description="完整地址")
    address_detail: str = Field(default="", description="详细地址", alias="addressDetail")
    address_id: str = Field(default="", description="地址ID", alias="addressId")
    alsc_complete_address: str = Field(default="", description="饿了么完整地址", alias="alscCompleteAddress")
    alsc_district_id: int = Field(default=0, description="饿了么区域ID", alias="alscDistrictId")
    alsc_full_name: str = Field(default="", description="收货人姓名", alias="alscFullName")
    alsc_phone: str = Field(default="", description="收货人电话", alias="alscPhone")
    alsc_province_id: str = Field(default="", description="饿了么省份ID", alias="alscProvinceId")
    city_id: int = Field(default=0, description="城市ID", alias="cityId")
    city_name: str = Field(default="", description="城市名称", alias="cityName")
    coordinates_source: str = Field(default="", description="坐标来源", alias="coordinatesSource")
    delivery_address_type: str = Field(default="", description="配送地址类型", alias="deliveryAddressType")
    district_name: str = Field(default="", description="区域名称", alias="districtName")
    need_global_expand: bool = Field(default=False, description="是否需要全局扩展", alias="needGlobalExpand")
    poi_name: str = Field(default="", description="POI名称", alias="poiName")
    province_name: str = Field(default="", description="省份名称", alias="provinceName")
    receive_lat: str = Field(default="", description="收货纬度", alias="receiveLat")
    receive_lng: str = Field(default="", description="收货经度", alias="receiveLng")
    select_collection_poi: bool = Field(default=False, description="是否选择集合POI", alias="selectCollectionPoi")
    user_address_poi: Optional[UserAddressPoiDTO] = Field(
        default=None, description="用户地址POI信息", alias="userAddressPoi"
    )

    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        populate_by_name = True
        from_attributes = True


class SubjectDTO(BaseModel):
    subject_name: Optional[str] = Field(None, description="商品名称")
    real_amount: int = Field(..., description="实际支付金额")
    price: int = Field(..., description="单价")
    amount: int = Field(..., description="总价")
    quantity: int = Field(..., description="数量")
    unit: Optional[str] = Field(None, description="单位")
    subsidy_amount: int = Field(..., description="补贴金额")
    merchant_subsidy_amount: int = Field(..., description="商家补贴金额")
    agent_subsidy_amount: int = Field(..., description="代理补贴金额")


class ElemeOrderDTO(BaseModel):
    order_id: str = Field(..., description="订单ID")
    biz_scene: str = Field(..., description="业务场景")
    buyer_uid: str = Field(..., description="买家ID")
    buyer_phone: str = Field(..., description="买家手机号")
    total_amount: int = Field(..., description="订单总金额")
    merchant_subsidy_amount: int = Field(..., description="商家补贴金额")
    agent_subsidy_amount: int = Field(..., description="代理补贴金额")
    real_amount: int = Field(..., description="实际支付金额")
    payment_amount: int = Field(..., description="支付金额")
    trade_type: str = Field(..., description="交易类型")
    pay_time: datetime = Field(..., description="支付时间")
    payee_account_no: str = Field(..., description="收款账号")
    payer_account_no: str = Field(..., description="付款账号")
    address_info: ElemeAddressDTO = Field(..., description="地址信息")
    user_tag: str = Field(..., description="用户标签")
    subjects: list[SubjectDTO] = Field(..., description="商品信息")


class DeliveryWashOrderExportStatus(StrEnum):
    """洗单任务导出状态"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DeliveryWashOrderRunStatus(StrEnum):
    """洗单任务运行状态"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DeliveryWashOrderExportRecord(BaseModel):
    export_id: str = Field(description="导出任务ID")
    status: str = Field(description="导出状态: pending, processing, completed, failed")
    created_at: datetime = Field(description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    file_url: Optional[str] = Field(None, description="文件下载链接")
    error_message: Optional[str] = Field(None, description="错误信息")
    filters: Dict[str, Any] = Field(description="过滤条件")
    batch_name: Optional[str] = Field(None, description="批次名称")
    run_date: Optional[str] = Field(None, description="运行日期")
    run_status: Optional[str] = Field(None, description="运行状态: pending, processing, completed, failed")
    run_error_message: Optional[str] = Field(None, description="运行错误信息")
