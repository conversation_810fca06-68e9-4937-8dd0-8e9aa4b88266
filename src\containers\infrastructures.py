# encoding: utf-8
# src/containers/infrastructures.py
# created: 2025-07-27 16:14:01


from dependency_injector import containers, providers

# from src.infrastructures.browsers import BrowserManager  # 不再使用
from src.infrastructures.databases.redis import RedisManager
from src.infrastructures.databases.tortoise import init_tortoise
from src.infrastructures.gateways import BifrostGateway, WifiMasterGateway
from src.infrastructures.gateways.aliyun import AliyunSmsGateway
from src.infrastructures.gateways.eleme import (
    ElemeAixincanGateway,
    ElemeUnionBenefitsGateway,
    ElemeUnionDeliveryGateway,
)
from src.infrastructures.ip_proxy import ProxyManager
from src.infrastructures.rabbitmq import RabbitMQManager


class Infrastructures(containers.DeclarativeContainer):

    config = providers.Configuration()

    redis_manager = providers.Singleton(RedisManager, config=config.database)
    init_tortoise = providers.Callable(init_tortoise, config=config.database)

    # browser_manager = providers.Singleton(BrowserManager, config=config.browser)  # 不再使用
    ip_proxy_manager = providers.Singleton(ProxyManager, redis_manager=redis_manager)
    rabbitmq_manager = providers.Singleton(RabbitMQManager, config=config.rabbitmq)
    rabbitmq_producer = providers.Factory(lambda manager: manager.producer, manager=rabbitmq_manager)

    # -------------------Gateways----------------------
    bifrost_gateway = providers.Singleton(BifrostGateway, config=config.bifrost)
    wifimaster_gateway = providers.Singleton(WifiMasterGateway, config=config.wifimaster)
    eleme_aixincan_gateway = providers.Singleton(ElemeAixincanGateway)
    eleme_union_delivery_gateway = providers.Singleton(
        ElemeUnionDeliveryGateway, config=config.eleme_union, redis=redis_manager.provided.client
    )
    eleme_union_benefits_gateway = providers.Singleton(
        ElemeUnionBenefitsGateway, config=config.eleme_union, redis=redis_manager.provided.client
    )

    aliyun_sms_gateway = providers.Singleton(AliyunSmsGateway, config=config.sms)
