# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/ai/__init__.py
# created: 2025-04-07 01:08:10
# updated: 2025-04-18 03:12:32


from src.infrastructures.fastapi.application import register_app


def get_ai_app(config=None):
    app = register_app(
        name="ai_agent",
        version="0.0.1",
        description="ai agent api",
        config=config,
    )
    from .agent_apis.router import router as ai_router
    from .dingtalk import router as dingtalk_router
    from .htrip import router as htrip_router

    app.include_router(dingtalk_router, prefix="/dingtalk")
    app.include_router(htrip_router, prefix="/htrip")
    app.include_router(ai_router, prefix="/agent")
    return app
