# encoding: utf-8
# deploys/growth_hacker/loggers.py
# created: 2025-07-26 12:23:45

import sys
from contextvars import ContextVar
from pathlib import Path
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger

from .settings import config

if TYPE_CHECKING:
    from loguru import Logger

# Context Variables for tracking task_id and phone
task_id_var: ContextVar[str] = ContextVar("task_id", default="-")
phone_var: ContextVar[str] = ContextVar("phone", default="-")

# formatter: [2025-06-08T14:57:36.806Z] [INFO] Browser instance closed successfully 🔒


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "10 MB",
    retention: str = "7 days",
    console_output: bool = True,
) -> None:
    """
    配置 loguru logger

    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，如果为 None 则不输出到文件
        rotation: 日志轮转条件
        retention: 日志保留时间
        console_output: 是否输出到控制台
    """

    if config.debug:
        log_level = "DEBUG"

    # 移除默认的 logger
    logger.remove()

    # 自定义格式 - 包含文件名、行号、task_id 和 phone
    def format_log(record: Any) -> str:
        task_id = task_id_var.get()
        phone = phone_var.get()
        record["extra"]["task_id"] = task_id
        record["extra"]["phone"] = phone
        return "[{time:YYYY-MM-DDTHH:mm:ss.SSSZ}] [{level}] {message} task_id[{extra[task_id]}] phone[{extra[phone]}] [{file}:{line}]\n"

    # 控制台输出配置
    if console_output:
        logger.add(
            sys.stdout,
            format=format_log,
            level=log_level,
            colorize=True,  # 启用颜色
            backtrace=True,  # 启用回溯
            diagnose=True,  # 启用诊断信息
        )

    # 文件输出配置
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        logger.add(
            log_file,
            format=format_log,
            level=log_level,
            rotation=rotation,
            retention=retention,
            compression="zip",  # 压缩旧日志
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
        )

    logger.info("🚀 Logger 初始化完成")


def get_logger() -> "Logger":
    """获取配置好的 logger 实例"""
    return logger


# 便捷的初始化函数
def init_default_logger() -> None:
    """使用默认配置初始化 logger"""
    setup_logger(log_level="INFO", log_file="logs/app.log", console_output=True)


def init_debug_logger() -> None:
    """初始化调试模式的 logger"""
    setup_logger(log_level="DEBUG", log_file="logs/debug.log", console_output=True)


def init_production_logger() -> None:
    """初始化生产环境的 logger"""
    setup_logger(log_level="WARNING", log_file="logs/production.log", console_output=False)  # 生产环境不输出到控制台


# Context Variables 便捷函数
def set_task_id(task_id: str) -> None:
    """设置当前任务 ID"""
    task_id_var.set(task_id)


def get_task_id() -> str:
    """获取当前任务 ID"""
    return task_id_var.get()


def set_phone(phone: str) -> None:
    """设置当前手机号"""
    phone_var.set(phone)


def get_phone() -> str:
    """获取当前手机号"""
    return phone_var.get()


def clear_context() -> None:
    """清除所有上下文变量"""
    task_id_var.set("-")
    phone_var.set("-")


def patch_record(record):
    if "extra" not in record:
        record["extra"] = {}

    if "task_id" not in record["extra"]:
        record["extra"]["task_id"] = get_task_id()
    if "phone" not in record["extra"]:
        record["extra"]["phone"] = get_phone()
