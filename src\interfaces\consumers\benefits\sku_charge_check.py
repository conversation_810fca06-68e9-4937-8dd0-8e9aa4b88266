# encoding: utf-8
# src/interfaces/consumers/benefits/sku_charge_check.py
# created: 2025-04-06 18:21:54

from datetime import datetime
from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.domains.benefits.messages import SkuChargeCheckMessage, SkuChargeMessageContent
from src.infrastructures.rabbitmq.consumers import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import SkuChargeCheckCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class SkuChargeCheckConsumer(BaseConsumer):
    """权益SKU充值检查消费者"""

    exchange_name = "benefits.topic"
    queue_name = "benefits_sku_charge_check"
    routing_key = "benefits.sku_charge_check"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        check_service: "SkuChargeCheckCommandService",
    ):
        self.check_service = check_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = SkuChargeCheckMessage.model_validate_json(message.body)
        payload: SkuChargeMessageContent = msg.payload
        logger.info(f"SkuChargeCheckConsumer 收到消息: record_id={payload.record_id}")

        # 检查充值状态
        status, need_check_order, delay = await self.check_service.check_charge_status(
            payload.record_id, payload.retry_count
        )

        # 如果需要重试
        if status == RecordStatus.PROCESSING and delay > 0:
            await self.producer.publish_message(
                SkuChargeCheckMessage(
                    payload=SkuChargeMessageContent(
                        record_id=payload.record_id, retry_count=payload.retry_count + 1, last_retry_at=datetime.now()
                    )
                ),
                exchange_name="benefits.topic",
                delay=delay,
            )

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 检查失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "SkuChargeCheckConsumer 回滚: 消息[{message_id}] 检查失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
