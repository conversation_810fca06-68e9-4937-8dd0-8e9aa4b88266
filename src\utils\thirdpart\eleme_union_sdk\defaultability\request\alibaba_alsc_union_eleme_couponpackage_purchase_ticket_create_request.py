from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.couponpackage.purchase.ticket.create"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateCouponPackagePurchaseTicketCreateRequest:
    def __init__(
        self,
        purchase_id: str = None,
        item_id: str = None,
        mobile: str = None,
        outer_order_id: str = None,
        outer_item_id: str = None,
        ext_info: str = None,
    ):
        """
        采购单ID
        """
        self.purchase_id = purchase_id
        """
            商品ID
        """
        self.item_id = item_id
        """
            收货手机号
        """
        self.mobile = mobile
        """
            外部订单ID（若是在淘宝售卖，传淘宝订单ID，否则传接入方内部订单ID）
        """
        self.outer_order_id = outer_order_id
        """
            外部商品ID（若是在淘宝售卖，传淘宝商品ID，否则传接入方内部商品ID）
        """
        self.outer_item_id = outer_item_id
        """
            扩展字段（json结构）
        """
        self.ext_info = ext_info
