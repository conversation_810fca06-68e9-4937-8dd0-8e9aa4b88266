# encoding: utf-8
# deploys/growth_hacker/settings.py
# created: 2025-07-25 17:13:47

from pathlib import Path

from pydantic import Field
from pydantic_settings import SettingsConfigDict

from src.infrastructures.browsers import BrowserSettings
from src.infrastructures.gateways import WifiMasterSettings
from src.infrastructures.ip_proxy import IpProxySettings
from src.infrastructures.logger import LoggerSettings
from src.infrastructures.settings import BaseServiceSettings


class GrowthHackerSettings(BaseServiceSettings):
    """Growth Hacker 服务配置"""

    # Growth Hacker 服务特有的配置
    debug: bool = True
    browser: BrowserSettings = Field(default_factory=BrowserSettings)
    ip_proxy: IpProxySettings = Field(default_factory=IpProxySettings)
    logger: LoggerSettings = Field(default_factory=LoggerSettings)

    # 重写 wifimaster 以确保它在这个服务中也可用
    wifimaster: WifiMasterSettings = Field(default_factory=WifiMasterSettings)

    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",  # 方便嵌套字段注入
    )


config = GrowthHackerSettings()
