# encoding: utf-8
# src/infrastructures/gateways/regions/schemas.py
# created: 2025-08-08 03:45:27

from typing import List, Optional

from pydantic import BaseModel, Field


class RegionInfo(BaseModel):
    """地区信息模型"""

    province_name: str = Field(default="", description="省份简称")
    city_name: str = Field(default="", description="城市简称")
    district_name: str = Field(default="", description="区县名称")
    district_code: str = Field(default="", description="GB2260行政区划代码")
    province_full_name: str = Field(default="", description="省份全称")
    city_full_name: str = Field(default="", description="城市全称")
    district_full_name: str = Field(default="", description="区县全称")
    latitude: Optional[float] = Field(default=None, description="纬度")
    longitude: Optional[float] = Field(default=None, description="经度")
    found: bool = Field(default=False, description="是否找到匹配的地区")


class SearchResult(BaseModel):
    """搜索结果模型"""

    level: str = Field(description="级别：province/city/district")
    id: Optional[int] = Field(default=None, description="地区ID")
    province_id: Optional[int] = Field(default=None, description="省份ID")
    city_id: Optional[int] = Field(default=None, description="城市ID")
    district_id: Optional[int] = Field(default=None, description="区县ID")
    name: str = Field(description="地区名称")
    full_name: str = Field(description="地区全称")
    district_code: str = Field(description="GB2260代码")


class Statistics(BaseModel):
    """统计信息模型"""

    province_count: int = Field(description="省份数量")
    city_count: int = Field(description="城市数量")
    district_count: int = Field(description="区县数量")


# 保留原有的简化模型用于向后兼容
class CityInfo(BaseModel):
    """城市信息模型"""

    city_name: str = Field(..., description="城市中文名")
    city_pinyin: str = Field(..., description="城市拼音")
    province_name: str = Field(..., description="省份名称")
    district_code: Optional[str] = Field(None, description="行政区划代码")
    latitude: Optional[float] = Field(None, description="纬度")
    longitude: Optional[float] = Field(None, description="经度")


class SupportedCity(BaseModel):
    """支持的城市模型"""

    city_name: str = Field(..., description="城市中文名")
    city_pinyin: str = Field(..., description="城市拼音")
    province_name: str = Field(..., description="所属省份")


class RegionSearchResult(BaseModel):
    """地区搜索结果"""

    found: bool = Field(..., description="是否找到匹配结果")
    city_info: Optional[CityInfo] = Field(None, description="城市信息")
    message: str = Field("", description="结果说明")
