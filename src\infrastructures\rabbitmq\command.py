# # encoding: utf-8
# <AUTHOR> <EMAIL>
# # core/rabbitmq/command.py
# # created: 2025-01-26 22:29:57
# # updated: 2025-07-02 11:25:53

# import asyncio
# from contextlib import asynccontextmanager

# from src.infrastructures.config import Config, load_config
# from src.infrastructures.containers import Container
# from src.infrastructures.database import register_database
# from src.infrastructures.loggers import register_logger

# from .consumer_manage import RabbitMQManager


# @asynccontextmanager
# async def lifespan(depends: Container):
#     config: Config = depends.config()

#     register_logger("consumer", config.log)
#     database_mgr = await register_database("consumer", config.database)

#     yield

#     await database_mgr.close_all()  # safe close database connections
#     await depends.connection_pool().close()  # safe close rabbitmq connections


# async def main(depends: Container):
#     config: Config = depends.config()

#     # 创建消费者管理器，支持配置预取数量
#     consumer_mgr = RabbitMQManager(
#         connection_pool=depends.connection_pool(),
#         producer=depends.producer(),
#         lifespan=lifespan(depends),
#         prefetch_count=getattr(config.rabbitmq, "prefetch_count", 10),  # 从配置获取预取数量，默认10
#     )

#     # 注册消费者 - 现在支持多实例配置

#     # 权益相关消费者
#     from src.domains.benefits.consumers import (
#         BenefitsOrderNoticeConsumer,
#         ProductOrderExportConsumer,
#         SkuChargeCheckConsumer,
#         SkuChargeConsumer,
#         UnionPurchaseTicketSyncConsumer,
#         UnionSKUSyncConsumer,
#     )

#     # 根据业务重要性和处理复杂度配置不同的实例数量
#     consumer_mgr.register_consumer(UnionSKUSyncConsumer, instances=2)  # SKU同步，中等并发
#     consumer_mgr.register_consumer(SkuChargeConsumer, instances=5)  # 充值处理，高并发
#     consumer_mgr.register_consumer(SkuChargeCheckConsumer, instances=10)  # 充值检查，中等并发
#     consumer_mgr.register_consumer(UnionPurchaseTicketSyncConsumer, instances=2)  # 票据同步，中等并发
#     consumer_mgr.register_consumer(BenefitsOrderNoticeConsumer, instances=10)  # 订单通知，低并发
#     consumer_mgr.register_consumer(ProductOrderExportConsumer, instances=1)  # 产品订单导出，低并发

#     # 外卖相关消费者
#     from src.domains.delivery.consumers import (
#         DeliveryWashOrderExportConsumer,
#         DeliveryWashOrderRunConsumer,
#         DingtalkOrdersNotifyConsumer,
#         SyncElemeOrderConsumer,
#         UnionOrderNotifyConsumer,
#         UnionOrderSyncConsumer,
#     )

#     consumer_mgr.register_consumer(DingtalkOrdersNotifyConsumer, instances=2)  # 钉钉通知，中等并发
#     consumer_mgr.register_consumer(UnionOrderSyncConsumer, instances=10)  # 订单同步，高并发
#     consumer_mgr.register_consumer(SyncElemeOrderConsumer, instances=2)  # 饿了么订单同步，高并发
#     consumer_mgr.register_consumer(DeliveryWashOrderExportConsumer, instances=1)  # 洗单任务导出，低并发
#     consumer_mgr.register_consumer(DeliveryWashOrderRunConsumer, instances=1)  # 洗单任务运行，低并发
#     consumer_mgr.register_consumer(UnionOrderNotifyConsumer, instances=1)  # 订单通知，低并发

#     # 启动消费者
#     await consumer_mgr.start_consumer()


# if __name__ == "__main__":
#     depends = Container()
#     depends.config.set_provides(load_config())
#     asyncio.run(main(depends))
