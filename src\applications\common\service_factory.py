# encoding: utf-8
# src/applications/common/service_factory.py
# created: 2025-08-19 12:00:00

"""
服务工厂类 - 统一管理应用层服务实例

此模块提供了统一的服务实例管理，确保依赖注入的一致性和服务的单例模式。
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    import redis.asyncio as redis

    from src.applications.common.commands.benefits import (
        OrderNoticeCommandService,
        ProductOrderExportCommandService,
        SkuChargeCheckCommandService,
        SkuChargeCommandService,
        UnionPurchaseSyncCommandService,
    )
    from src.applications.common.commands.delivery import OrderSyncCommandService
    from src.applications.common.queries import (
        ActivityQueryService,
        ShopsQueryService,
        UnionPurchaseQueryService,
    )
    from src.applications.common.services import BenefitsSupplierService
    from src.domains.benefits.services import (
        BenefitsProductService,
        BenefitsSkuService,
    )
    from src.domains.benefits.services import (
        BenefitsSupplierService as DomainSupplierService,
    )
    from src.domains.delivery.services import DeliveryPageService
    from src.infrastructures.databases import RedisManager
    from src.infrastructures.gateways import BifrostGateway
    from src.infrastructures.gateways.eleme.union import (
        ElemeUnionBenefitsGateway,
        ElemeUnionDeliveryGateway,
    )
    from src.infrastructures.rabbitmq import RabbitMQProducer
    from src.repositories.benefits import (
        ProductOrderRepository,
        ProductRepository,
        PurchaseRepository,
        SkuChargeRecordRepository,
        SkuRepository,
        SupplierRepository,
    )
    from src.repositories.delivery import (
        DeliveryElemeOrderRepository,
        DeliveryOrderRepository,
        DeliveryPageRepository,
    )
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository
    from src.repositories.shops import ShopRepository


class ServiceFactory:
    """
    服务工厂类

    提供应用层所有服务的统一创建和管理。
    使用单例模式确保服务实例的唯一性。
    """

    _instances = {}

    @classmethod
    def get_order_notice_command_service(cls, order_repo: "ProductOrderRepository") -> "OrderNoticeCommandService":
        """获取订单通知命令服务"""
        key = "order_notice_command"
        if key not in cls._instances:
            from src.applications.common.commands.benefits.order_notice import OrderNoticeCommandService

            cls._instances[key] = OrderNoticeCommandService(order_repo)
        return cls._instances[key]

    @classmethod
    def get_product_order_export_command_service(
        cls, order_repo: "ProductOrderRepository", redis_client: "redis.Redis"
    ) -> "ProductOrderExportCommandService":
        """获取产品订单导出命令服务"""
        key = "product_order_export_command"
        if key not in cls._instances:
            from src.applications.common.commands.benefits.product_order_export import ProductOrderExportCommandService

            cls._instances[key] = ProductOrderExportCommandService(order_repo, redis_client)
        return cls._instances[key]

    @classmethod
    def get_sku_charge_command_service(
        cls, sku_charge_record_repo: "SkuChargeRecordRepository"
    ) -> "SkuChargeCommandService":
        """获取SKU充值命令服务"""
        key = "sku_charge_command"
        if key not in cls._instances:
            from src.applications.common.commands.benefits.sku_charge import SkuChargeCommandService

            cls._instances[key] = SkuChargeCommandService(sku_charge_record_repo)
        return cls._instances[key]

    @classmethod
    def get_sku_charge_check_command_service(
        cls, product_service: "BenefitsProductService", sku_charge_record_repo: "SkuChargeRecordRepository"
    ) -> "SkuChargeCheckCommandService":
        """获取SKU充值检查命令服务"""
        key = "sku_charge_check_command"
        if key not in cls._instances:
            from src.applications.common.commands.benefits.sku_charge_check import SkuChargeCheckCommandService

            cls._instances[key] = SkuChargeCheckCommandService(product_service, sku_charge_record_repo)
        return cls._instances[key]

    @classmethod
    def get_union_purchase_sync_command_service(
        cls,
        supplier_repo: "SupplierRepository",
        benefits_supplier_service: "DomainSupplierService",
        sku_service: "BenefitsSkuService",
        benefits_product_service: "BenefitsProductService",
    ) -> "UnionPurchaseSyncCommandService":
        """获取联盟采购同步命令服务"""
        key = "union_purchase_sync_command"
        if key not in cls._instances:
            from src.applications.common.commands.benefits.union_purchase_sync import UnionPurchaseSyncCommandService

            cls._instances[key] = UnionPurchaseSyncCommandService(
                supplier_repo, benefits_supplier_service, sku_service, benefits_product_service
            )
        return cls._instances[key]

    @classmethod
    def get_order_sync_command_service(
        cls,
        delivery_order_repo: "DeliveryOrderRepository",
        eleme_order_repo: "DeliveryElemeOrderRepository",
        eleme_gateway: "ElemeUnionDeliveryGateway",
        bifrost_gateway: "BifrostGateway",
    ) -> "OrderSyncCommandService":
        """获取订单同步命令服务"""
        key = "order_sync_command"
        if key not in cls._instances:
            from src.applications.common.commands.delivery.order_sync import OrderSyncCommandService

            cls._instances[key] = OrderSyncCommandService(
                delivery_order_repo, eleme_order_repo, eleme_gateway, bifrost_gateway
            )
        return cls._instances[key]

    @classmethod
    def get_activity_query_service(
        cls,
        delivery_page_service: "DeliveryPageService",
        delivery_page_repo: "DeliveryPageRepository",
        bifrost_gateway: "BifrostGateway",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        eleme_activity_service,
        app_repo: "AppRepository",
        tenant_repo: "TenantRepository",
        user_repo: "UserRepository",
    ) -> "ActivityQueryService":
        """获取活动查询服务"""
        key = "activity_query"
        if key not in cls._instances:
            from src.applications.common.queries.activity import ActivityQueryService

            cls._instances[key] = ActivityQueryService(
                delivery_page_service,
                delivery_page_repo,
                bifrost_gateway,
                eleme_union_delivery_gateway,
                eleme_activity_service,
                app_repo,
                tenant_repo,
                user_repo,
            )
        return cls._instances[key]

    @classmethod
    def get_shops_query_service(
        cls,
        shop_repo: "ShopRepository",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        redis_manager: "RedisManager",
    ) -> "ShopsQueryService":
        """获取店铺查询服务"""
        key = "shops_query"
        if key not in cls._instances:
            from src.applications.common.queries.shops import ShopsQueryService

            cls._instances[key] = ShopsQueryService(shop_repo, eleme_union_delivery_gateway, redis_manager)
        return cls._instances[key]

    @classmethod
    def get_union_purchase_query_service(
        cls, eleme_union_benefits_gateway: "ElemeUnionBenefitsGateway"
    ) -> "UnionPurchaseQueryService":
        """获取联盟采购查询服务"""
        key = "union_purchase_query"
        if key not in cls._instances:
            from src.applications.common.queries.benefits.purchases import UnionPurchaseQueryService

            cls._instances[key] = UnionPurchaseQueryService(eleme_union_benefits_gateway)
        return cls._instances[key]

    @classmethod
    def get_benefits_supplier_service(
        cls, supplier_repo: "SupplierRepository", purchase_repo: "PurchaseRepository", sku_repo: "SkuRepository"
    ) -> "BenefitsSupplierService":
        """获取权益供应商服务"""
        key = "benefits_supplier"
        if key not in cls._instances:
            from src.applications.common.services.benefits_supplier import BenefitsSupplierService

            cls._instances[key] = BenefitsSupplierService(supplier_repo, purchase_repo, sku_repo)
        return cls._instances[key]

    @classmethod
    def clear_instances(cls):
        """清除所有服务实例（主要用于测试）"""
        cls._instances.clear()


# 导出便捷函数
def get_service_factory() -> ServiceFactory:
    """获取服务工厂实例"""
    return ServiceFactory()
