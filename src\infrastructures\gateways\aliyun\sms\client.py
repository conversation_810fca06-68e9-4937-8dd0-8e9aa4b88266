# encoding: utf-8
# src/infrastructures/gateways/aliyun/sms/client.py
# created: 2025-08-01 08:07:16

import json
import random
import string
from typing import Dict, Optional

from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from loguru import logger

from .errors import AliyunSmsInvalidPhoneError, AliyunSmsSendError
from .settings import AliyunSmsSettings


class AliyunSmsGateway:
    """阿里云短信服务网关"""

    def __init__(self, config: AliyunSmsSettings | dict):
        if isinstance(config, dict):
            config = AliyunSmsSettings(**config)

        self.config = config
        self._client: Optional[DysmsapiClient] = None

    @property
    def client(self) -> DysmsapiClient:
        """获取阿里云短信客户端(懒加载)"""
        if self._client is None:
            config = open_api_models.Config(
                access_key_id=self.config.access_key_id,
                access_key_secret=self.config.access_key_secret,
                endpoint=self.config.endpoint,
            )
            self._client = DysmsapiClient(config)
        return self._client

    def generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return "".join(random.choices(string.digits, k=length))

    async def send_verification_code(
        self,
        phone_number: str,
        verification_code: str,
        template_code: Optional[str] = None,
        sign_name: Optional[str] = None,
    ) -> bool:
        """
        发送验证码短信

        Args:
            phone_number: 手机号码
            verification_code: 验证码
            template_code: 短信模板代码(可选，默认使用配置)
            sign_name: 短信签名(可选，默认使用配置)

        Returns:
            bool: 发送是否成功
        """
        if not self._validate_phone_number(phone_number):
            raise AliyunSmsInvalidPhoneError(f"无效的手机号: {phone_number}")

        # 构建模板参数
        template_param = json.dumps({"code": verification_code})

        return await self.send_sms(
            phone_number=phone_number, template_param=template_param, template_code=template_code, sign_name=sign_name
        )

    async def send_sms(
        self,
        phone_number: str,
        template_param: str,
        template_code: Optional[str] = None,
        sign_name: Optional[str] = None,
    ) -> bool:
        """
        发送短信(通用方法)

        Args:
            phone_number: 手机号码
            template_param: 模板参数(JSON字符串)
            template_code: 短信模板代码(可选，默认使用配置)
            sign_name: 短信签名(可选，默认使用配置)

        Returns:
            bool: 发送是否成功
        """
        request = dysmsapi_models.SendSmsRequest(
            phone_numbers=phone_number,
            sign_name=sign_name or self.config.sign_name,
            template_code=template_code or self.config.template_code,
            template_param=template_param,
        )

        try:
            response = await self.client.send_sms_with_options_async(request, util_models.RuntimeOptions())

            logger.info(
                f"阿里云短信发送响应 - "
                f"手机号: {phone_number}, "
                f"请求ID: {response.body.request_id}, "
                f"状态码: {response.body.code}, "
                f"消息: {response.body.message}"
            )

            # 阿里云返回OK表示成功
            if response.body.code == "OK":
                return True
            else:
                logger.error(
                    f"阿里云短信发送失败 - " f"错误码: {response.body.code}, " f"错误消息: {response.body.message}"
                )
                raise AliyunSmsSendError(f"短信发送失败: {response.body.message}")

        except Exception as e:
            error_message = UtilClient.assert_as_string(getattr(e, "message", str(e)))
            logger.error(f"阿里云短信发送异常 - " f"手机号: {phone_number}, " f"错误: {error_message}")
            raise AliyunSmsSendError(f"短信发送异常: {error_message}") from e

    def _validate_phone_number(self, phone_number: str) -> bool:
        """验证手机号格式(简单验证)"""
        # 中国手机号11位数字，以1开头
        if not phone_number or not phone_number.isdigit():
            return False
        if len(phone_number) != 11 or not phone_number.startswith("1"):
            return False
        return True
