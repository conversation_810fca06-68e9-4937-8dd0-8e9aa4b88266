# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/benefits/orders.py
# created: 2025-01-11 20:59:39
# updated: 2025-01-11 21:00:15

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

from src.domains.benefits.dto import BenefitsProductOrderDTO, BenefitsProductOrderWithCustomerDTO
from src.domains.benefits.services import ProductOrderService
from src.infrastructures import errors
from src.interfaces.http.baseapi import Container
from src.interfaces.http.baseapi.base.authorization import base_api_authentication
from src.repositories.benefits.product_order import ProductOrderExportFilters, ProductOrderFilters

from ...schemas import (
    ListData,
    ProductOrderExportRecordListResponse,
    ProductOrderExportRequest,
    ProductOrderExportResponse,
    ProductOrderListWithCustomerResponse,
    ProductOrderResponse,
)

if TYPE_CHECKING:
    from src.domains.passport.entities import UserEntity
    from src.repositories.benefits.product_order import ProductOrderRepository

router = APIRouter(tags=["benefits", "orders"])


@router.get("/orders", response_model=ProductOrderListWithCustomerResponse)
@inject
async def get_product_orders(
    params: ProductOrderFilters = Depends(),
    product_order_repository: "ProductOrderRepository" = Depends(
        Provide[Container.repositories.benefits_pdorder_repository]
    ),
):
    print(params.model_dump(exclude_none=True))
    total, orders, customers = await product_order_repository.get_product_orders(params)
    # 使用扩展DTO转换订单
    orders_dto = [
        await BenefitsProductOrderWithCustomerDTO.from_order_with_customers(order, customers) for order in orders
    ]
    return ProductOrderListWithCustomerResponse(data=ListData(total=total, data=orders_dto))


@router.post("/orders/export", response_model=ProductOrderExportResponse)
@inject
async def create_product_order_export(
    request: ProductOrderExportRequest,
    mis_user: "UserEntity" = Depends(base_api_authentication),
    product_order_service: ProductOrderService = Depends(Provide[Container.domains.benefits_product_order_service]),
):
    """发起产品订单导出任务"""
    # 构建导出过滤条件
    filters = ProductOrderExportFilters(**request.model_dump(exclude_none=True))

    # 调用service层创建导出任务
    export_record = await product_order_service.create_export_task(filters, mis_user)

    return ProductOrderExportResponse(data=export_record)


@router.get("/orders/export", response_model=ProductOrderExportRecordListResponse)
@inject
async def get_product_order_export_records(
    mis_user: "UserEntity" = Depends(base_api_authentication),
    product_order_service: ProductOrderService = Depends(Provide[Container.domains.benefits_product_order_service]),
):
    """获取用户的产品订单导出记录"""
    records = await product_order_service.get_export_records(mis_user)
    return ProductOrderExportRecordListResponse(data=records)


@router.get("/orders/{order_id}", response_model=ProductOrderResponse)
@inject
async def get_product_order(
    order_id: str,
    product_order_repository: "ProductOrderRepository" = Depends(
        Provide[Container.repositories.benefits_pdorder_repository]
    ),
):
    order = await product_order_repository.get_by_order_id(order_id)
    if not order:
        raise errors.ChargeOrderNotFoundError
    return ProductOrderResponse(data=await BenefitsProductOrderDTO.from_tortoise_orm(order))
