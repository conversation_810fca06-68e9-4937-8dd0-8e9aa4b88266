# encoding: utf-8
# src/infrastructures/rabbitmq/manager.py
# created: 2025-07-27 19:20:13

import asyncio
import signal
import socket
import time
from contextlib import AsyncExitStack
from typing import TYPE_CHECKING, Any, Callable, Optional, Type, Union

from loguru import logger

from .conn_pool import RabbitMQConnectionPool
from .consumers import BaseConsumer
from .producers import RabbitMQProducer
from .settings import RabbitmqSettings


class RabbitMQManager:
    """RabbitMQ消费者统一管理器"""

    def __init__(self, config: Union[RabbitmqSettings, dict]):
        if isinstance(config, dict):
            config = RabbitmqSettings(**config)

        self.config = config
        self.health_status = {"status": "初始化", "consumers": {}}
        self.consumers: list[dict[str, Any]] = []
        self.tasks: list[asyncio.Task] = []
        self.is_running = False
        self.should_exit = asyncio.Event()

        self.connection_pool = RabbitMQConnectionPool(config)
        self.producer = RabbitMQProducer(self.connection_pool)

        # 使用配置中的prefetch_count，如果未提供的话
        self.prefetch_count = config.prefetch_count
        self.default_consumer_instances = config.consumer_instances

    def register_consumer(
        self,
        consumer: Union[Type[BaseConsumer], Callable[[], BaseConsumer]],
        instances: Optional[int] = None,
        name: Optional[str] = None,
    ) -> None:
        """注册消费者类或工厂函数

        Args:
            consumer: 消费者类或工厂函数
            instances: 实例数量
            name: 消费者名称（用于工厂函数）
        """
        # 如果未指定实例数，使用配置中的默认值
        actual_instances = instances or self.default_consumer_instances

        # 获取消费者名称
        if name:
            consumer_name = name
        elif isinstance(consumer, type):
            consumer_name = consumer.__name__
        else:
            # 工厂函数，尝试获取返回类型的名称
            consumer_name = getattr(consumer, "__name__", "UnknownConsumer")

        self.consumers.append({"consumer": consumer, "instances": actual_instances, "name": consumer_name})
        self.health_status["consumers"][consumer_name] = {"status": "已注册", "instances": actual_instances}  # type: ignore
        logger.info(
            f"📝 已注册消费者 {consumer_name} x {actual_instances} (配置默认: {self.default_consumer_instances})"
        )

    async def check_health(self) -> dict[str, Any]:
        """健康检查"""
        self.last_health_check = time.time()
        self.health_status["status"] = "运行中" if self.is_running else "已停止"

        # 检查任务状态
        active_tasks = sum(1 for task in self.tasks if not task.done())
        self.health_status["active_tasks"] = active_tasks  # type: ignore

        logger.debug(f"健康检查: {self.health_status}")
        return self.health_status

    async def shutdown(self) -> None:
        """关闭消费者和连接"""
        if not self.is_running:
            return

        logger.info("正在关闭RabbitMQ消费者...")
        self.is_running = False
        self.should_exit.set()

        # 取消所有运行中的任务
        for task in self.tasks:
            if not task.done():
                task.cancel()

        if self.tasks:
            # 使用 return_exceptions=True 来避免 "Future exception was never retrieved" 错误
            results = await asyncio.gather(*self.tasks, return_exceptions=True)

            # 记录任何异常
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"任务 {i} 结束时有异常: {type(result).__name__}: {result}")
                elif isinstance(result, asyncio.CancelledError):
                    logger.debug(f"任务 {i} 已被取消")
                else:
                    logger.debug(f"任务 {i} 正常结束")

        self.tasks.clear()
        self.health_status["status"] = "已停止"
        logger.info("RabbitMQ消费者已关闭")

    async def _start_single_consumer(
        self,
        consumer_factory: Union[Type[BaseConsumer], Callable[[], BaseConsumer]],
        consumer_name: str,
        instance_id: int,
        exit_stack: AsyncExitStack,
    ) -> None:
        """启动单个消费者实例

        Args:
            consumer_factory: 消费者类或工厂函数
            consumer_name: 消费者名称
            instance_id: 实例ID
            exit_stack: 异步上下文管理器
        """
        if not self.connection_pool:
            raise ValueError("Connection pool is None")
        if not self.producer:
            raise ValueError("Producer is None")

        try:
            # 为每个消费者实例创建独立的channel
            channel = await exit_stack.enter_async_context(self.connection_pool.get_channel())
            await channel.set_qos(prefetch_count=self.prefetch_count)

            # 创建消费者实例
            if isinstance(consumer_factory, type):
                # 旧方式：直接使用类
                consumer_instance = consumer_factory(self.connection_pool, self.producer)
            else:
                # 新方式：使用工厂函数
                consumer_instance = consumer_factory()

            # 验证返回的是 BaseConsumer 实例
            if not isinstance(consumer_instance, BaseConsumer):
                raise TypeError(f"Consumer factory must return BaseConsumer instance, got {type(consumer_instance)}")

            # 声明队列和交换机
            exchange = await channel.get_exchange(consumer_instance.exchange_name)
            queue = await channel.declare_queue(consumer_instance.queue_name, durable=True)
            await queue.bind(exchange, routing_key=consumer_instance.routing_key)

            consumer_tag = f"{consumer_name}_{socket.gethostname()}_{instance_id}"
            logger.info(
                f"🔗 队列配置 - 队列: {consumer_instance.queue_name}, 路由键: {consumer_instance.routing_key}, "
                f"交换机: {consumer_instance.exchange_name}, 标签: {consumer_tag}, 预取: {self.prefetch_count}"
            )

            # 启动消费
            await queue.consume(
                consumer_instance.process_message,
                timeout=60 * 1000,
                consumer_tag=consumer_tag,
            )

            logger.info(f"🚀 消费者实例已启动: {consumer_tag} (预取数量: {self.prefetch_count})")
            self.health_status["consumers"][consumer_name]["status"] = "运行中"  # type: ignore

            # 等待退出信号
            await self.should_exit.wait()

        except asyncio.CancelledError:
            logger.info(f"消费者实例 {consumer_name}_{instance_id} 被取消")
            raise
        except Exception as e:
            logger.error(f"消费者实例 {consumer_name}_{instance_id} 运行出错: {e}")
            self.health_status["consumers"][consumer_name]["status"] = f"错误: {str(e)}"  # type: ignore
            raise

    async def start_consumer(self) -> None:
        """启动消费者消费"""
        if not self.connection_pool:
            raise ValueError("RabbitMQ connection pool cannot be None")  # 确保连接池非空

        # 使用AsyncExitStack管理异步上下文
        async with AsyncExitStack() as exit_stack:
            try:
                # 设置信号处理
                loop = asyncio.get_running_loop()
                for sig in (signal.SIGINT, signal.SIGTERM):
                    loop.add_signal_handler(sig, lambda: asyncio.create_task(self.shutdown()))

                self.is_running = True
                self.health_status["status"] = "运行中"

                # 启动健康检查任务
                health_check_task = asyncio.create_task(self.periodic_health_check())
                self.tasks.append(health_check_task)

                # 为每个消费者创建多个实例
                consumer_tasks = []
                for item in self.consumers:
                    consumer_factory = item["consumer"]
                    instances = item["instances"]
                    consumer_name = item["name"]

                    for instance_id in range(instances):
                        task = asyncio.create_task(
                            self._start_single_consumer(consumer_factory, consumer_name, instance_id, exit_stack),
                            name=f"{consumer_name}_instance_{instance_id}",
                        )
                        consumer_tasks.append(task)
                        self.tasks.append(task)

                logger.info(f"已启动 {len(consumer_tasks)} 个消费者实例")

                # 等待退出信号
                await self.should_exit.wait()

            except Exception as e:
                logger.error(f"消费者运行出错: {e}")
                self.health_status["status"] = f"错误: {str(e)}"
                raise
            finally:
                await self.shutdown()

    async def periodic_health_check(self, interval: int = 60) -> None:
        """定期健康检查"""
        while not self.should_exit.is_set():
            await self.check_health()
            try:
                await asyncio.wait_for(self.should_exit.wait(), timeout=interval)
            except asyncio.TimeoutError:
                pass  # 超时正常，继续下一次检查

    def start(self) -> None:
        """启动消费者"""
        asyncio.run(self.start_consumer())
