# encoding: utf-8
# src/infrastructures/logger/sinks/console.py
# created: 2025-08-14 14:42:00

import sys

from ..settings import ConsoleSinkConfig
from ..types import LogMessage
from .base import BaseSink


class ConsoleSink(BaseSink):
    """控制台输出实现

    输出日志到标准输出或标准错误
    支持彩色输出和序列化
    """

    def __init__(self, config: ConsoleSinkConfig, app_name: str):
        """初始化控制台输出

        Args:
            config: ConsoleSinkConfig 配置
            app_name: 应用名称
        """
        super().__init__(config, app_name)

        # 使用 loguru 的原生控制台输出
        # 这里直接返回 sys.stdout 让 loguru 处理
        self._stream = sys.stdout

    def write(self, message: LogMessage) -> None:
        """写入消息到控制台

        由于使用 loguru 的原生功能，这里直接传递

        Args:
            message: loguru Message 对象
        """
        # loguru 会自动处理格式化和彩色输出
        pass

    def __call__(self, message: LogMessage) -> None:
        """直接返回流对象让 loguru 处理

        对于控制台输出，我们返回流对象而不是自定义处理
        这样可以利用 loguru 的彩色和格式化功能
        """
        # 实际上，对于控制台，我们应该直接返回 sys.stdout
        # 让 loguru 的 add() 方法直接处理
        pass
