# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/services/authorization.py
# created: 2024-12-15 16:54:43
# updated: 2025-05-27 15:54:49

from typing import Optional

import jwt
import toml
from loguru import logger

from src.databases.models.passport import PassportApp, PassportTenant
from src.domains.passport.dto import AuthUserInfoDTO, UserInfoDTO
from src.infrastructures.config_compat import config

# base_jwt_secret: str = config.jwt.base_secret


class AuthorizationService:

    @classmethod
    async def generate_base_auth_token(
        cls, user_info: UserInfoDTO, app: PassportApp, tenant: Optional[PassportTenant] = None
    ) -> str:
        auth_user_info = AuthUserInfoDTO(
            uid=user_info.uid,
            app_id=app.app_id,
            app_name=app.name,
            tenant_id=tenant.tenant_id if tenant else None,
            tenant_name=tenant.name if tenant else "",
            phone=user_info.phone,
            nickname=user_info.nickname,
            avatar=user_info.avatar_url,
        )
        token = jwt.encode(auth_user_info.model_dump(), key=config.webapp.secret, algorithm="HS256")
        return token

    @classmethod
    async def decode_base_auth_token(cls, token: str) -> AuthUserInfoDTO:
        try:
            logger.debug(f"Decoding token: {token}")
            auth_user_info = jwt.decode(token, key=config.webapp.secret, algorithms=["HS256"])
            logger.debug(f"Decoded auth user info: {auth_user_info}")
            return AuthUserInfoDTO.model_validate(auth_user_info)
        except jwt.ExpiredSignatureError as e:
            raise ValueError("Token expired") from e
        except Exception as e:
            logger.exception(f"Decoded auth user info: {e}", exc_info=True)
            logger.error(f"Decode base auth token error: {e}")
            raise ValueError("Invalid token") from e
