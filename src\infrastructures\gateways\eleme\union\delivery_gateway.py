# encoding: utf-8
# src/infrastructures/gateways/eleme/union/delivery_gateway.py
# created: 2025-07-30 13:50:00

import json
from datetime import datetime
from typing import Any, Dict, Optional, Union

import nanoid
from loguru import logger
from redis.asyncio import Redis

from src.infrastructures import errors
from src.infrastructures.errors import BusinessError
from src.infrastructures.utils.decorator import cache
from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_officialactivity_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_officialactivity_wxscheme_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_storepromotion_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_promotion_storepromotion_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_kbcpx_positive_order_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_media_zone_add_request import *

from .dto import (
    UnionActivityInfoDto,
    UnionActivityInfoResponse,
    UnionOrderInfoDTO,
    UnionPageLinkDTO,
    UnionShopDetailDto,
    UnionShopDto,
)
from .settings import ElemeUnionSettings

# 常量定义
AI_PID = "alsc_28806810_9518007_26738131"
URL_REPLACEMENT_RULES = {"ad-bdlm-sub": "https://h5.ele.me/adminiappsub"}
ELEME_REDIRECT_PREFIX = "https://r.ele.me"


class ElemeUnionDeliveryGateway:
    """饿了么联盟网关"""

    def __init__(self, config: Union[ElemeUnionSettings, dict], redis: Redis):
        if isinstance(config, dict):
            config = ElemeUnionSettings(**config)

        self.config = config
        self.redis = redis

    def _get_client(self) -> Defaultability:
        """获取饿了么联盟SDK客户端"""
        client = TopApiClient(
            appkey=self.config.app_key,
            app_sercet=self.config.app_secret,
            top_gateway_url=self.config.top_gateway_url,
            verify_ssl=True,
        )
        return Defaultability(client=client)

    def _generate_sid(self) -> str:
        """生成唯一的SID"""
        return (
            "SID:"
            + datetime.now().strftime("%Y%m%d")
            + nanoid.generate(size=36, alphabet="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")
        )

    def _apply_url_replacement(self, url: str) -> str:
        """应用URL替换规则"""
        for old_pattern, new_pattern in URL_REPLACEMENT_RULES.items():
            url = url.replace(old_pattern, new_pattern)
        return url

    def _extract_link_info(self, link: Dict[str, Any]) -> Dict[str, Optional[str]]:
        """从链接信息中提取各种URL"""
        if not link:
            logger.warning("链接信息为空")
            return {}

        # 提取饿了么相关链接
        app_promotion = link.get("app_promotion", {})
        eleme_url = app_promotion.get("ele_url")
        eleme_schema = app_promotion.get("deep_link")

        # 提取H5链接
        h5_promotion = link.get("h5_promotion", {})
        h5_url = h5_promotion.get("h5_url")

        # 如果没有H5链接，尝试从微信链接转换
        if not h5_url:
            wx_promotion = link.get("wx_promotion", {})
            wx_path = wx_promotion.get("wx_path", "")
            if wx_path:
                h5_url = self._apply_url_replacement(wx_path)
                logger.info(f"使用微信链接转换H5链接: {wx_path} -> {h5_url}")

        # 处理饿了么重定向链接
        if h5_url and h5_url.startswith(ELEME_REDIRECT_PREFIX):
            eleme_url = h5_url
            h5_url = None
            logger.debug(f"检测到饿了么重定向链接: {eleme_url}")

        # 提取其他平台链接
        taobao_promotion = link.get("taobao_promotion", {})
        taobao_url = taobao_promotion.get("h5_url")
        taobao_schema = taobao_promotion.get("scheme_url")

        wx_promotion = link.get("wx_promotion", {})
        wechat_schema = wx_promotion.get("scheme_url")

        alipay_promotion = link.get("alipay_promotion", {})
        alipay_schema = alipay_promotion.get("alipay_scheme_url")

        # 提取微信小程序信息
        wechat_appid = link.get("wx_appid") or wx_promotion.get("wx_app_id")
        wechat_path = link.get("wx_path") or wx_promotion.get("wx_path")

        return {
            "h5_url": h5_url,
            "eleme_url": eleme_url,
            "eleme_schema": eleme_schema,
            "taobao_url": taobao_url,
            "taobao_schema": taobao_schema,
            "wechat_schema": wechat_schema,
            "alipay_schema": alipay_schema,
            "wechat_appid": wechat_appid,
            "wechat_path": wechat_path,
        }

    async def encode_sid(self, data: dict) -> str:
        """编码并存储SID数据"""
        sid = self._generate_sid()
        await self.redis.set(f"union:sid:{sid}", json.dumps(data))
        logger.debug(f"编码SID成功: {sid}, 数据: {data}")
        return sid

    async def decode_sid(self, sid: str) -> Optional[dict]:
        """解码SID数据"""
        data = await self.redis.get(f"union:sid:{sid}")
        if not data:
            logger.error(f"SID不存在或已过期: {sid}")
            return None

        decoded_data = json.loads(data)
        logger.debug(f"解码SID成功: {sid}, 数据: {decoded_data}")
        return decoded_data

    @logger.catch
    def fetch_remote_cps_order(
        self,
        page_size: int,
        page_number: int,
        start_time: datetime,
        end_time: datetime,
        date_type: int = 1,  # payid_time: 1, create_time: 2, settle_time: 3, update_time: 4
    ) -> tuple[list[UnionOrderInfoDTO], int]:
        """获取远程CPS订单"""

        ability = self._get_client()
        request = AlibabaAlscUnionKbcpxPositiveOrderGetRequest(
            date_type=date_type,
            biz_unit=2,  # cps order
            page_size=page_size,
            page_number=page_number,
            start_date=start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end_date=end_time.strftime("%Y-%m-%d %H:%M:%S"),
        )

        response = ability.alibaba_alsc_union_kbcpx_positive_order_get(request)

        if response.get("biz_error_code", 0) != 0:
            error_msg = f"获取CPS订单失败: {response}"
            logger.error(error_msg)
            raise BusinessError(
                code=response.get("biz_error_code", "UNKNOWN"), message=response.get("biz_error_desc", "未知错误")
            )

        order_list = [UnionOrderInfoDTO.model_validate(order) for order in response.get("result", [])]
        total = response.get("total_count", 0)

        logger.info(
            f"获取CPS订单成功: 总数={total}, 当前页={len(order_list)}, 开始时间={start_time}, 结束时间={end_time}, page_number={page_number}, page_size={page_size}"
        )
        return order_list, total

    @logger.catch
    async def get_union_activity_info(self, activity_id: str, pid: str, sid: str = "default") -> UnionActivityInfoDto:
        """获取联盟活动信息"""
        ability = self._get_client()
        request = AlibabaAlscUnionElemePromotionOfficialactivityGetRequest(
            AlibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest(
                activity_id=activity_id.strip(), pid=pid.strip(), sid=sid or "default"
            )
        )
        response = ability.alibaba_alsc_union_eleme_promotion_officialactivity_get(request)

        if response.get("result_code", -1) != 0:
            error_msg = f"获取联盟活动信息失败: activity_id={activity_id}, response={response}"
            logger.error(error_msg)
            raise errors.ElemeUnionSDKError

        activity_response = UnionActivityInfoResponse.model_validate(response)  # 使用新的DTO解析响应
        logger.info(f"获取联盟活动信息成功: activity_id={activity_id}, pid={pid}, sid={sid}")
        return activity_response.data

    @logger.catch
    async def get_shop_url(self, shop_id: str, sid: str = "default", pid: str = AI_PID) -> str:
        """获取饿了么店铺访问链接"""
        ability = self._get_client()
        request = AlibabaAlscUnionElemePromotionStorepromotionGetRequest(
            AlibabaAlscUnionElemePromotionStorepromotionGetSingleStorePromotionRequest(
                pid=pid,
                shop_id=shop_id.strip(),
                sid=sid or "default",
            )
        )

        response = ability.alibaba_alsc_union_eleme_promotion_storepromotion_get(request)

        if response.get("result_code", -1) != 0:
            error_msg = f"获取店铺链接失败: shop_id={shop_id}, response={response}"
            logger.error(error_msg)
            raise errors.ElemeUnionSDKError

        shop_url = response.get("data", {}).get("link", {}).get("h5_url")
        if not shop_url:
            raise errors.ElemeUnionSDKError

        logger.info(f"获取店铺链接成功: shop_id={shop_id}, url={shop_url}")
        return shop_url

    @cache(key_prefix="activity_links", ttl=60 * 60 * 24)
    async def get_union_activity_url(
        self, activity_id: str, pid: str, extra_info: Optional[dict] = None
    ) -> UnionPageLinkDTO:
        """获取联盟活动链接"""
        # 编码额外信息为SID
        sid = await self.encode_sid(extra_info) if extra_info else None

        # 获取活动信息
        activity_info = await self.get_union_activity_info(activity_id, pid, sid or "default")
        print(activity_info)
        link = activity_info.link
        logger.info(f"处理联盟活动链接: activity_id={activity_id}, pid={pid}, sid={sid}")

        # 将PromotionLinkDto转换为UnionPageLinkDTO
        link_data = {
            "sid": sid or "default",
            "h5_promotion": link.h5_promotion,
            "taobao_promotion": link.taobao_promotion,
            "wx_promotion": link.wx_promotion,
            "alipay_promotion": link.alipay_promotion,
            "app_promotion": link.app_promotion,
        }

        return UnionPageLinkDTO.model_validate(link_data)

    async def get_shop_list(
        self,
        pid: str,
        lat: float,
        lng: float,
        page_size: Optional[int] = 20,
        session_id: Optional[str] = None,
        extra_info: Optional[dict] = None,
        search_content: Optional[str] = None,
    ) -> tuple[str, list[UnionShopDto]]:
        ability = self._get_client()
        sid = await self.encode_sid(extra_info) if extra_info else None
        request = AlibabaAlscUnionElemePromotionStorepromotionQueryRequest(
            query_request=AlibabaAlscUnionElemePromotionStorepromotionQueryPromotionQueryRequest(
                session_id=session_id or "",
                page_size=page_size or 20,
                latitude=str(lat),
                longitude=str(lng),
                pid=pid,
                sort_type="distance",
                sid=sid or "default",
                include_dynamic=True,
                search_content=search_content or "",
            )
        )
        response = ability.alibaba_alsc_union_eleme_promotion_storepromotion_query(request)

        new_session_id = response["data"].get("session_id", "")
        records = [UnionShopDto.model_validate(record) for record in response["data"].get("records", [])]
        return new_session_id, records

    async def get_shop_detail(self, shop_id: str, pid: str, extra_info: Optional[dict] = None) -> UnionShopDetailDto:
        ability = self._get_client()
        sid = await self.encode_sid(extra_info) if extra_info else None
        request = AlibabaAlscUnionElemePromotionStorepromotionGetRequest(
            AlibabaAlscUnionElemePromotionStorepromotionGetSingleStorePromotionRequest(
                pid=pid, shop_id=shop_id.strip(), sid=sid or "default"
            )
        )
        response = ability.alibaba_alsc_union_eleme_promotion_storepromotion_get(request)
        return UnionShopDetailDto.model_validate(response["data"])
