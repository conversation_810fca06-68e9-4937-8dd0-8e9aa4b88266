from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeStorepromotionReviewbwcStockLockRequest(BaseRequest):

    def __init__(
        self,
        pid: str = None,
        shop_id: str = None,
        activity_id: str = None,
        sid: str = None,
        mobile: str = None,
        outer_order_id: str = None,
    ):
        """
        渠道PID
        """
        self._pid = pid
        """
            门店ID（加密）
        """
        self._shop_id = shop_id
        """
            活动ID
        """
        self._activity_id = activity_id
        """
            三方扩展id
        """
        self._sid = sid
        """
            领取手机号
        """
        self._mobile = mobile
        """
            领取资格唯一ID（用户在渠道平台报名领取资格的唯一标识，能够标记用户每次报名请求，渠道维护）
        """
        self._outer_order_id = outer_order_id

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def shop_id(self):
        return self._shop_id

    @shop_id.setter
    def shop_id(self, shop_id):
        if isinstance(shop_id, str):
            self._shop_id = shop_id
        else:
            raise TypeError("shop_id must be str")

    @property
    def activity_id(self):
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        if isinstance(activity_id, str):
            self._activity_id = activity_id
        else:
            raise TypeError("activity_id must be str")

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, sid):
        if isinstance(sid, str):
            self._sid = sid
        else:
            raise TypeError("sid must be str")

    @property
    def mobile(self):
        return self._mobile

    @mobile.setter
    def mobile(self, mobile):
        if isinstance(mobile, str):
            self._mobile = mobile
        else:
            raise TypeError("mobile must be str")

    @property
    def outer_order_id(self):
        return self._outer_order_id

    @outer_order_id.setter
    def outer_order_id(self, outer_order_id):
        if isinstance(outer_order_id, str):
            self._outer_order_id = outer_order_id
        else:
            raise TypeError("outer_order_id must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.storepromotion.reviewbwc.stock.lock"

    def to_dict(self):
        request_dict = {}
        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._shop_id is not None:
            request_dict["shop_id"] = convert_basic(self._shop_id)

        if self._activity_id is not None:
            request_dict["activity_id"] = convert_basic(self._activity_id)

        if self._sid is not None:
            request_dict["sid"] = convert_basic(self._sid)

        if self._mobile is not None:
            request_dict["mobile"] = convert_basic(self._mobile)

        if self._outer_order_id is not None:
            request_dict["outer_order_id"] = convert_basic(self._outer_order_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
