# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/sku_charge_record.py
# created: 2025-02-09 21:44:15
# updated: 2025-04-06 16:08:28

from datetime import datetime
from typing import Any, Optional, Sequence, Tuple

from pydantic import BaseModel, Field

from src.databases.models.benefits import BenefitsSku, BenefitsSkuChargeRecord, BenefitsSkuChargeRecordStatus


class SkuChargeRecordFilters(BaseModel):
    sku_code: Optional[str] = Field(None, description="SKU编码")
    charge_order_id: Optional[str] = Field(None, description="充值订单ID")
    out_order_id: Optional[str] = Field(None, description="外部订单ID")
    status: Optional[BenefitsSkuChargeRecordStatus] = Field(None, description="状态")
    account: Optional[str] = Field(None, description="账户")
    page: int = Field(default=1, ge=1, description="页码, 最小值为1")
    page_size: int = Field(default=10, ge=1, le=100, description="每页条数, 最小值为1")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")


class SkuChargeRecordRepository:

    @classmethod
    async def update_status(
        cls,
        record_id: int,
        status: BenefitsSkuChargeRecordStatus,
        detail: Optional[dict[str, Any]] = None,
        charged_at: Optional[datetime] = None,
    ) -> None:
        update_data = {}
        if detail:
            update_data["detail"] = detail
        if charged_at:
            update_data["charged_at"] = charged_at
        await BenefitsSkuChargeRecord.filter(id=record_id).update(status=status, **update_data)

    @classmethod
    async def gets_by_order_id(cls, charge_order_id: str) -> Sequence[BenefitsSkuChargeRecord]:
        return await BenefitsSkuChargeRecord.filter(charge_order_id=charge_order_id).all()

    @classmethod
    async def get_by_id(cls, record_id: int) -> BenefitsSkuChargeRecord | None:
        return await BenefitsSkuChargeRecord.filter(id=record_id).prefetch_related("sku").first()

    @classmethod
    async def get_sku_charge_records(
        cls, filters: SkuChargeRecordFilters
    ) -> Tuple[int, Sequence[BenefitsSkuChargeRecord]]:
        query = BenefitsSkuChargeRecord.all()
        if filters.sku_code:
            query = query.filter(sku__code=filters.sku_code)
        if filters.out_order_id:
            query = query.filter(out_order_id=filters.out_order_id)
        if filters.status:
            query = query.filter(status=filters.status)
        if filters.account:
            query = query.filter(account=filters.account)
        if filters.start_time:
            query = query.filter(created_at__gte=filters.start_time)
        if filters.end_time:
            query = query.filter(created_at__lte=filters.end_time)
        if filters.charge_order_id:
            query = query.filter(charge_order_id=filters.charge_order_id)
        count = await query.count()
        records = (
            await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).order_by("-id").all()
        )
        return count, records

    @classmethod
    async def create_record(
        cls, sku: BenefitsSku, charge_order_id: str, account: str, source_identify: str
    ) -> BenefitsSkuChargeRecord:
        return await BenefitsSkuChargeRecord.create(
            sku=sku,
            charge_order_id=charge_order_id,
            account=account,
            supplier_sku_code=sku.third_part_code,
            source_identify=source_identify,
            created_at=datetime.now(),
        )

    @classmethod
    async def create_multi_records(cls, records: Sequence[BenefitsSkuChargeRecord]) -> None:
        """批量创建充值记录"""
        return await BenefitsSkuChargeRecord.bulk_create(records)

    @classmethod
    async def get_by_supplier_order_id(cls, supplier_order_id: str) -> BenefitsSkuChargeRecord | None:
        """根据供应商订单ID获取充值记录"""
        return await BenefitsSkuChargeRecord.filter(supplier_order_id=supplier_order_id).prefetch_related("sku").first()
