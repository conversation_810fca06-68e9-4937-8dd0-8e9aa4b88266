# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/datas/models/haili_benefits.py
# created: 2025-01-22 12:01:47
# updated: 2025-03-01 20:45:59

import nanoid
from tortoise import fields
from tortoise.models import Model


def generate_contract_code():
    return "CTC" + nanoid.generate("0123456789abcdefghijklmnopqrstuvwxyz", 10)


class DTHailiBenefitsContract(Model):
    id = fields.BigIntField(primary_key=True)
    contract_code = fields.CharField(max_length=255, null=False, default=generate_contract_code, description="合同编码")
    contract_name = fields.CharField(max_length=255, null=False, default="", description="合同名称")
    customer_name = fields.CharField(max_length=255, null=False, default="", description="客户名称")
    city_ent_name = fields.CharField(max_length=255, null=False, default="", description="城市合伙人名称")
    amount = fields.IntField(null=False, default=0, description="金额(单位分)")
    user_count = fields.IntField(null=False, default=0, description="用户数量")
    paid_at = fields.DatetimeField(null=True, description="支付时间")
    active_id = fields.CharField(max_length=255, null=False, default="", description="活动ID")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "dt_benefits_contract"
        table_description = "DT权益售卖合同表"
