# encoding: utf-8
# src/applications/passport/services/user.py
# created: 2025-07-31 17:10:00

import asyncio
from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity

from ..dto import TenantInfoDTO, UserInfoDTO, UserRelationDTO
from ..errors import (
    AppNotFoundError,
    TenantNotFoundError,
    UserNotFoundError,
    UserTenantRelationNotFoundError,
)

if TYPE_CHECKING:
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class UserService:
    """用户命令服务"""

    def __init__(
        self, user_repository: "UserRepository", app_repository: "AppRepository", tenant_repository: "TenantRepository"
    ):
        self.user_repository = user_repository
        self.app_repository = app_repository
        self.tenant_repository = tenant_repository

    async def switch_user_tenant(self, user: "UserEntity", app: "AppEntity", tenant_id: str) -> UserInfoDTO:
        """切换用户租户"""
        user_relation = await self.user_repository.get_user_relation(user, app, tenant_id)
        if not user_relation or not user_relation.tenant:
            raise UserTenantRelationNotFoundError

        tenant = await TenantEntity.from_model(user_relation.tenant)
        await user.switch_tenant(tenant)

        logger.info(f"用户切换租户成功: user={user.uid}, app={app.app_id}, tenant={tenant_id}")
        return UserInfoDTO.from_user_entity(user)

    async def create_relation(self, uid: str, app_id: str, tenant_id: str) -> UserRelationDTO:
        """创建用户关系"""
        user, app, tenant = await asyncio.gather(
            self.user_repository.get_by_uid(uid),
            self.app_repository.get_by_appid(app_id),
            self.tenant_repository.get_by_tenant_id(tenant_id),
        )

        if not user:
            raise UserNotFoundError
        if not app:
            raise AppNotFoundError
        if not tenant:
            raise TenantNotFoundError

        logger.info(f"创建用户关系: uid={uid}, app_id={app_id}, tenant_id={tenant_id}")
        await self.user_repository.get_or_create_user_relation(user, app.app_id, tenant.tenant_id)
        return UserRelationDTO(uid=uid, app_id=app_id, tenant_id=tenant_id)

    async def remove_user_relation(self, uid: str, app_id: str, tenant_id: str) -> None:
        """删除用户关系"""
        logger.info(f"删除用户关系: uid={uid}, app_id={app_id}, tenant_id={tenant_id}")
        await self.user_repository.remove_user_relation(uid, app_id, tenant_id)
        return None

    async def update_user_profile(
        self,
        user_entity: "UserEntity",
        nickname: Optional[str] = None,
        avatar_url: Optional[str] = None,
        email: Optional[str] = None,
    ) -> UserInfoDTO:
        """更新用户资料"""
        logger.info(
            f"更新用户资料: [{user_entity.uid}]{user_entity.nickname}, "
            f"nickname: {user_entity.nickname}-->{nickname}, "
            f"avatar_url: {user_entity.avatar_url}-->{avatar_url}, "
            f"email: {user_entity.email}-->{email}"
        )
        user_entity.nickname = nickname or user_entity.nickname
        user_entity.avatar_url = avatar_url or user_entity.avatar_url
        user_entity.email = email or user_entity.email

        await self.user_repository.save(user_entity)
        return UserInfoDTO.from_user_entity(user_entity)
