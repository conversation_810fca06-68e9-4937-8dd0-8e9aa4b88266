# encoding: utf-8
# src/domains/customer/entities/benefit_product.py
# created: 2025-07-29 08:53:54

from typing import TYPE_CHECKING, Literal, Optional

from pydantic import BaseModel

if TYPE_CHECKING:
    from src.databases.models.customer import CustomerBenefits


class BenefitProductEntity(BaseModel):

    id: int
    code: str
    name: str
    type: str
    description: str
    detail: Optional[dict] = {}
    enabled: bool

    sale_price: int
    stock: int

    customer_id: int

    @property
    def sale_mode(self) -> Literal["stock", "finacial"]:
        return "stock" if self.stock > 0 else "finacial"

    def decrease_stock(self, count: int = 1) -> int:
        self.stock -= count
        return self.stock

    @classmethod
    async def from_model(cls, model: "CustomerBenefits") -> "BenefitProductEntity":
        await model.fetch_related("product", "customer")
        return cls(
            id=model.id,
            code=model.product.code,
            name=model.product.name,
            type=model.product.type,
            description=model.product.description,
            detail=model.product.detail or {},
            enabled=model.product.enabled,
            sale_price=model.sale_price,
            stock=model.stock,
            customer_id=model.customer.id,
        )
