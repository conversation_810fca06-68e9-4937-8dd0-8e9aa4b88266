# encoding: utf-8
# src/applications/openapi/queries/activities.py
# created: 2025-07-29 09:55:44

import urllib.parse
from typing import TYPE_CHECKING, Optional, Union

from loguru import logger

from deploys.openapi.settings import OpenapiSettings
from src.domains.activity.entities import ActivityEntity
from src.interfaces.http.openapi.schemas import DineInAccessUrlPayload
from src.utils.eleme.channels import ElemeChannelsUtils

from ..dto import ActivityLinksDTO
from ..errors import ActivityNotFoundError, AixincanAddUserError

if TYPE_CHECKING:
    from src.domains.activity.services import ElemeActivityService
    from src.domains.customer.entities import CustomerEntity
    from src.domains.delivery.services import DeliveryPageService
    from src.infrastructures.databases.redis import RedisManager
    from src.infrastructures.fastapi.settings import FastapiSettings
    from src.infrastructures.gateways import BifrostGateway
    from src.infrastructures.gateways.eleme import ElemeAixincanGateway
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway
    from src.infrastructures.gateways.wifi_master import WifiMasterGateway
    from src.repositories.delivery import DeliveryPageRepository


class ActivitiesQueryService:

    def __init__(
        self,
        delivery_page_service: "DeliveryPageService",
        delivery_page_repo: "DeliveryPageRepository",
        bifrost_gateway: "BifrostGateway",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        eleme_activity_service: "ElemeActivityService",
        wifi_master_gateway: "WifiMasterGateway",
        redis: "RedisManager",
        config: Union[dict, "OpenapiSettings"],
        eleme_aixincan_gateway: "ElemeAixincanGateway",
    ):
        self.delivery_page_service = delivery_page_service
        self.delivery_page_repo = delivery_page_repo
        self.bifrost_gateway = bifrost_gateway
        self.eleme_union_delivery_gateway = eleme_union_delivery_gateway
        self.eleme_activity_service = eleme_activity_service
        self.wifi_master_gateway = wifi_master_gateway
        self.redis = redis.client
        self.config = config if isinstance(config, OpenapiSettings) else OpenapiSettings(**config)
        self.eleme_aixincan_gateway = eleme_aixincan_gateway

    async def get_eleme_pure_url(
        self, customer: "CustomerEntity", page_code: str, extra_info: dict
    ) -> ActivityLinksDTO:
        """获取饿了么活动纯净版链接"""
        page_info = await self.delivery_page_repo.get_page_by_code(page_code)
        if not page_info:
            raise ActivityNotFoundError

        extra_info = {
            **extra_info,
            "customer_code": customer.code,
            "customer_name": customer.name,
        }
        activity = await ActivityEntity.from_delivery_page(page_info)
        return await activity.get_links_with_eleme_miniapp(
            self.bifrost_gateway, self.eleme_union_delivery_gateway, extra_info
        )

    async def get_eleme_url_with_auth(
        self,
        customer: "CustomerEntity",
        page_code: str,
        extra_info: dict,
        jwt_token: str,
        eleme_channel: str,
        latitude: Optional[float],
        longitude: Optional[float],
    ) -> ActivityLinksDTO:
        """获取饿了么活动链接, 包含渠道版token联登"""
        page_info = await self.delivery_page_repo.get_page_by_code(page_code)
        if not page_info:
            raise ActivityNotFoundError
        activity = await ActivityEntity.from_delivery_page(page_info)

        open_id, phone = customer.parse_jwt(jwt_token)

        if activity.custom_behavior == "aixincan":
            result = await self.eleme_aixincan_gateway.add_aixincan_user(
                phone=phone,
                province_name="",
                city_name="",
                district_name=None,
                industry=None,
            )
            if not result.get("success", False):
                raise AixincanAddUserError

        channel_params = self.eleme_activity_service.build_channel_params(
            eleme_channel=eleme_channel,
            mobile=phone,
            user_open_id=open_id,
            latitude=latitude,
            longitude=longitude,
        )

        extra_info = {
            **extra_info,
            "customer_code": customer.code,
            "customer_name": customer.name,
        }
        links = await activity.get_links_with_eleme_miniapp(
            self.bifrost_gateway, self.eleme_union_delivery_gateway, extra_info
        )
        if links.h5_promotion and links.h5_promotion.h5_url:
            links.h5_promotion.h5_url = ElemeChannelsUtils.url_with_params(links.h5_promotion.h5_url, channel_params)
        return links

    async def get_eleme_wifimaster_url(
        self,
        customer: "CustomerEntity",
        page_code: str,
        auth_code: str,
        latitude: Optional[float],
        longitude: Optional[float],
        extra_info: dict,
    ) -> str:
        phone = await self.wifi_master_gateway.parse_auth_code(auth_code)
        user_exists = await self.redis.exists(f"wifi_master_user:{phone}")

        links = await self.get_eleme_pure_url(customer, page_code, extra_info)
        channel_params = self.eleme_activity_service.build_channel_params(
            eleme_channel="wifi_master",
            mobile=phone,
            user_open_id=f"wifimaster_{phone}",
            latitude=latitude,
            longitude=longitude,
        )

        if links.h5_promotion and links.h5_promotion.h5_url:
            access_url = ElemeChannelsUtils.url_with_params(links.h5_promotion.h5_url, channel_params)
        else:
            access_url = ""

        if not user_exists:
            logger.info(f"wifi_master_user:{phone} not exists, redirect to middle page")
            access_url = (
                f"{self.config.fastapi.hostname.base_fe}/wifiMaster/landing?redir={urllib.parse.quote(access_url)}"
            )

        return access_url

    async def get_dine_in_access_url(
        self,
        customer: "CustomerEntity",
        payload: DineInAccessUrlPayload,
    ) -> str:
        params = {
            "enterprise": payload.enterprise,
            "enterpriseCode": payload.enterprise_code,
            "pname": payload.pname,
            "cityname": payload.cityname,
            "adname": payload.adname,
            "address": payload.address,
            "addressName": payload.address_name,
            "location": f"{payload.longitude},{payload.latitude}",
            "paymentUrl": "https://opendocs.alipay.com/pre-open/07wrzc?pathHash=e7fef87a",
            "scanUrl": "https://opendocs.alipay.com/pre-open/07wrzc?pathHash=e7fef87a",
        }
        open_id, phone = customer.parse_jwt(payload.jwt)

        channel_params = self.eleme_activity_service.build_channel_params(
            eleme_channel="dine_in",
            mobile=phone,
            user_open_id=f"dine_in_{phone}",
        )

        params.update(channel_params)

        return ElemeChannelsUtils.channel_url_with_params("dine_in", params)
