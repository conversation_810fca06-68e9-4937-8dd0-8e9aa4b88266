# encoding: utf-8
# src/applications/common/commands/benefits/order_notice.py
# created: 2025-08-19 10:00:00

from typing import TYPE_CHECKING
from urllib.parse import urlparse

import aiohttp
from loguru import logger

from src.databases.models.benefits import BenefitsProductOrderStatus as OrderStatus
from src.infrastructures.errors import ChargeOrderNotFoundError

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsProductOrder
    from src.repositories.benefits import ProductOrderRepository


class OrderNoticeCommandService:
    """权益订单通知命令服务"""

    def __init__(self, order_repo: "ProductOrderRepository"):
        self.order_repo = order_repo

    async def notify_order(self, order_id: str) -> None:
        """
        处理订单通知

        Args:
            order_id: 订单ID
        """
        logger.info(f"开始处理订单通知: {order_id}")

        # 获取订单
        order = await self.order_repo.get_by_order_id(order_id)
        if not order:
            logger.error(f"订单未找到: {order_id}")
            raise ChargeOrderNotFoundError

        # 检查订单状态
        if order.status not in [OrderStatus.SUCCESS, OrderStatus.FAILED]:
            logger.info(f"订单[{order_id}]状态为{order.status}，无需通知，跳过")
            return

        # 验证通知URL
        if not self._validate_notify_url(order.notify_url):
            logger.error(f"订单[{order_id}]通知URL无效: {order.notify_url}")
            return

        # 发送通知
        await self._send_notification(order)

    def _validate_notify_url(self, notify_url: str) -> bool:
        """
        验证通知URL是否有效

        Args:
            notify_url: 通知URL

        Returns:
            bool: URL是否有效
        """
        try:
            url = urlparse(notify_url)
            return bool(url.scheme and url.netloc)
        except Exception as e:
            logger.error(f"解析通知URL失败: {e}")
            return False

    async def _send_notification(self, order: "BenefitsProductOrder") -> None:
        """
        发送订单通知

        Args:
            order: 订单对象

        Raises:
            ConnectionError: 发送通知失败时抛出
        """
        order_body = {
            "order_id": order.order_id,
            "status": order.status.value,
            "price": order.price,
            "third_order_id": order.out_order_id,
            "product_code": order.product_code,
            "created_at": order.created_at.isoformat(),
            "updated_at": order.updated_at.isoformat(),
        }

        logger.debug(f"准备发送订单通知 - 订单[{order.order_id}]，URL: {order.notify_url}")

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(
                    order.notify_url, params=order_body, timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_text = await response.text()
                    logger.info(
                        f"订单通知发送完成 - 订单[{order.order_id}]，"
                        f"状态: {order.status}，响应码: {response.status}，"
                        f"响应内容: {response_text[:200] if response_text else 'Empty'}"
                    )

                    # 检查响应状态码
                    if response.status >= 400:
                        logger.warning(
                            f"订单通知收到异常响应 - 订单[{order.order_id}]，"
                            f"响应码: {response.status}，响应内容: {response_text}"
                        )

            except aiohttp.ClientTimeout as e:
                logger.error(f"发送订单通知超时 - 订单[{order.order_id}]，" f"URL: {order.notify_url}，错误: {e}")
                raise ConnectionError(f"发送订单通知超时: 订单[{order.order_id}]") from e
            except aiohttp.ClientError as e:
                logger.error(
                    f"发送订单通知失败 - 订单[{order.order_id}]，"
                    f"URL: {order.notify_url}，错误类型: {type(e).__name__}，"
                    f"错误详情: {e}"
                )
                raise ConnectionError(f"发送订单通知失败: 订单[{order.order_id}]，{type(e).__name__}") from e
            except Exception as e:
                logger.exception(f"发送订单通知时发生未知错误 - 订单[{order.order_id}]，" f"URL: {order.notify_url}")
                raise ConnectionError(f"发送订单通知时发生未知错误: 订单[{order.order_id}]") from e
