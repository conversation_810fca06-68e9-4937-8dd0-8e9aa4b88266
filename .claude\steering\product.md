# Product Steering Document

## Product Purpose

Build and maintain a modular monolithic backend service for Hicaspian's multi-tenant business platform supporting benefits management, delivery services, and customer engagement features.

## Core Features

### 1. Benefits Management (权益管理)
- Manage product SKUs, orders, and charging strategies
- Support multiple benefit types including Eleme coupons and union benefits
- Handle supplier integration and benefit distribution

### 2. Delivery Services (配送服务)
- Process and track delivery orders
- Integrate with Eleme Union and other delivery platforms
- Support order synchronization and status updates

### 3. Passport & Authentication (认证系统)
- Multi-tenant user management
- Support multiple login methods: SMS, WeChat, DingTalk
- Application-level access control and authorization

### 4. Customer Management (客户管理)
- B2B customer relationship management
- Customer channels and subscription management
- Secret key management for API authentication

### 5. Growth Hacker Tools
- Task automation and user profiling
- Browser automation capabilities
- Data analysis and reporting

## User Value Proposition

- **For Enterprise Clients**: Provide a unified API platform to manage benefits distribution and delivery services
- **For End Users**: Access various benefits and services through multiple channels (WeChat, DingTalk, Web)
- **For Internal Teams**: Centralized management system (MIS) for operations and customer service

## Key Business Rules

1. **Multi-tenancy**: Each tenant operates independently with isolated data
2. **Authentication**: All external APIs require either Bearer token or HMAC authentication
3. **Order Processing**: Orders must follow strict state transitions with proper validation
4. **Benefit Distribution**: Benefits can only be distributed through approved channels with proper charging
5. **Data Isolation**: Customer data must be strictly isolated between different tenants