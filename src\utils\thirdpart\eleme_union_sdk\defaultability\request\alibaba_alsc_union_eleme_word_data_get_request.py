from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeWordDataGetRequest(BaseRequest):

    def __init__(self, word_report_request: object = None):
        """
        查询rquest
        """
        self._word_report_request = word_report_request

    @property
    def word_report_request(self):
        return self._word_report_request

    @word_report_request.setter
    def word_report_request(self, word_report_request):
        if isinstance(word_report_request, object):
            self._word_report_request = word_report_request
        else:
            raise TypeError("word_report_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.word.data.get"

    def to_dict(self):
        request_dict = {}
        if self._word_report_request is not None:
            request_dict["word_report_request"] = convert_struct(self._word_report_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeWordDataGetSecondFloorWordReportRequest:
    def __init__(self, words: list = None):
        """
        二楼口令
        """
        self.words = words
