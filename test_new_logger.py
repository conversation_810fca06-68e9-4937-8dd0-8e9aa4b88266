# encoding: utf-8
# test_new_logger.py
# created: 2025-08-14 15:10:00

"""
新日志模块测试脚本
测试基本功能：控制台输出、文件输出、上下文管理等
"""

import asyncio
import time

from src.infrastructures.logger import setup, LogContext, get_logger


def test_basic_logging():
    """测试基本日志功能"""
    print("\n=== 测试基本日志功能 ===")
    
    # 初始化日志器
    logger = setup({
        "app_name": "test_app",
        "sinks": [
            {
                "type": "console",
                "level": "DEBUG",
                "colorize": True
            },
            {
                "type": "file",
                "level": "INFO",
                "path": "logs/test_{app_name}.log",
                "rotation": "1 MB",
                "retention": "3 days"
            }
        ],
        "context_fields": {
            "environment": "development",
            "version": "1.0.0"
        }
    })
    
    # 测试不同级别的日志
    logger.debug("这是 DEBUG 级别日志")
    logger.info("这是 INFO 级别日志")
    logger.warning("这是 WARNING 级别日志")
    logger.error("这是 ERROR 级别日志")
    
    return logger


def test_context_management():
    """测试上下文管理"""
    print("\n=== 测试上下文管理 ===")
    
    logger = get_logger()
    
    # 设置请求级别的上下文
    LogContext.set("request_id", "req-123456")
    LogContext.set("user_id", "user-789")
    
    logger.info("处理用户请求")
    
    # 更新上下文
    LogContext.update(
        action="create_order",
        order_id="order-001"
    )
    
    logger.info("创建订单成功")
    
    # 清除特定上下文
    LogContext.clear("order_id")
    
    logger.info("请求处理完成")


def test_exception_logging():
    """测试异常日志"""
    print("\n=== 测试异常日志 ===")
    
    logger = get_logger()
    
    try:
        # 模拟异常
        result = 10 / 0
    except Exception as e:
        logger.exception("计算出错")


def test_performance():
    """测试日志性能"""
    print("\n=== 测试日志性能 ===")
    
    logger = get_logger()
    
    start_time = time.time()
    count = 1000
    
    for i in range(count):
        logger.info(f"性能测试日志 #{i}")
    
    elapsed = time.time() - start_time
    print(f"写入 {count} 条日志耗时: {elapsed:.3f} 秒")
    print(f"平均每条日志: {elapsed/count*1000:.3f} 毫秒")


async def test_async_logging():
    """测试异步日志"""
    print("\n=== 测试异步日志 ===")
    
    logger = get_logger()
    
    async def async_task(task_id: int):
        LogContext.set("task_id", f"task-{task_id}")
        logger.info(f"异步任务 {task_id} 开始")
        await asyncio.sleep(0.1)
        logger.info(f"异步任务 {task_id} 完成")
    
    # 并发执行多个异步任务
    tasks = [async_task(i) for i in range(5)]
    await asyncio.gather(*tasks)


def test_bound_logger():
    """测试绑定日志器"""
    print("\n=== 测试绑定日志器 ===")
    
    logger = get_logger()
    
    # 创建绑定了特定上下文的日志器
    user_logger = logger.bind(user_id="user-123", session_id="session-456")
    
    user_logger.info("用户登录")
    user_logger.info("用户查看商品")
    user_logger.info("用户退出")


def main():
    """主测试函数"""
    print("开始测试新日志模块...")
    
    # 1. 基本功能测试
    test_basic_logging()
    
    # 2. 上下文管理测试
    test_context_management()
    
    # 3. 异常日志测试
    test_exception_logging()
    
    # 4. 绑定日志器测试
    test_bound_logger()
    
    # 5. 性能测试
    test_performance()
    
    # 6. 异步日志测试
    asyncio.run(test_async_logging())
    
    print("\n✅ 所有测试完成！")
    print("请检查 logs/test_test_app.log 文件查看日志输出")


if __name__ == "__main__":
    main()