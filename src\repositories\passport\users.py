# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/repositories/users.py
# created: 2025-03-24 22:29:32
# updated: 2025-04-01 15:16:54

from typing import TYPE_CHECKING, Optional, Sequence

from src.databases.models.passport import (
    PassportUser,
    PassportUserRelations,
    UserSourceEnum,
)
from src.domains.passport.dto import DingtalkUserInfoDTO, WechatUserInfoDTO

if TYPE_CHECKING:
    from src.domains.passport.entities import AppEntity, UserEntity


class UserRepository:

    async def get_user_relations(self, user: "UserEntity", app: "AppEntity") -> Sequence[PassportUserRelations]:
        return (
            await PassportUserRelations.filter(user__uid=user.uid, app__app_id=app.app_id)
            .prefetch_related("tenant")
            .all()
        )

    async def get_user_relation(
        self, user: "UserEntity", app: "AppEntity", tenant_id: str
    ) -> Optional[PassportUserRelations]:
        return (
            await PassportUserRelations.filter(user__uid=user.uid, app__app_id=app.app_id, tenant_id=tenant_id)
            .prefetch_related("tenant")
            .first()
        )

    async def save(self, user: "UserEntity") -> None:
        await PassportUser.update_or_create(
            uid=user.uid,
            defaults={
                "phone": user.phone,
                "nickname": user.nickname,
                "avatar_url": user.avatar_url,
                "email": user.email,
            },
        )

    async def remove_user_relation(self, uid: str, app_id: str, tenant_id: str) -> None:
        await PassportUserRelations.filter(user__uid=uid, app__app_id=app_id, tenant_id=tenant_id).delete()
        return None

    @classmethod
    async def get_by_uid(cls, uid: str) -> Optional[PassportUser]:
        return await PassportUser.get_or_none(uid=uid)

    @classmethod
    async def get_by_phone(cls, phone: str) -> Optional[PassportUser]:
        return await PassportUser.get_or_none(phone=phone)

    @classmethod
    async def create_by_phone(cls, phone: str) -> PassportUser:
        return await PassportUser.create(phone=phone, nickname=phone)

    @classmethod
    async def create_by_dingtalk_user(cls, dingtalk_user_info: DingtalkUserInfoDTO) -> PassportUser:
        return await PassportUser.create(
            phone=dingtalk_user_info.mobile,
            nickname=dingtalk_user_info.nickname,
            avatar_url=dingtalk_user_info.avatar_url,
            email=dingtalk_user_info.email,
            source=UserSourceEnum.DINGTALK,
        )

    @classmethod
    async def create_by_wechat_user(cls, wechat_user_info: WechatUserInfoDTO) -> PassportUser:
        return await PassportUser.create(
            phone=wechat_user_info.phone,
            nickname=wechat_user_info.nickname or wechat_user_info.phone,
            avatar_url=wechat_user_info.avatar_url,
            source=UserSourceEnum.WECHAT,
        )

    @classmethod
    async def get_or_create_user_relation(
        cls, user: PassportUser, app_id: str, tenant_id: Optional[str] = None
    ) -> PassportUserRelations:
        # 首先根据 app_id 获取 PassportApp 对象
        from src.repositories.passport.apps import AppRepository

        app = await AppRepository.get_by_appid(app_id)
        if not app:
            raise ValueError(f"App with app_id {app_id} not found")

        tenant = None
        if tenant_id:
            from src.repositories.passport.tenants import TenantRepository

            tenant = await TenantRepository.get_by_tenant_id(tenant_id)

        relation = await PassportUserRelations.filter(user=user, app__app_id=app_id, tenant_id=tenant_id).first()
        if relation:
            return relation

        return await PassportUserRelations.create(user=user, app=app, tenant=tenant)

    @classmethod
    async def update_user_profile(
        cls,
        user: PassportUser,
        nickname: Optional[str] = None,
        avatar_url: Optional[str] = None,
        email: Optional[str] = None,
    ) -> PassportUser:
        """更新用户资料"""
        update_fields = {}

        if nickname is not None:
            update_fields["nickname"] = nickname
        if avatar_url is not None:
            update_fields["avatar_url"] = avatar_url
        if email is not None:
            update_fields["email"] = email

        if update_fields:
            await user.update_from_dict(update_fields)
            await user.save()

        return user
