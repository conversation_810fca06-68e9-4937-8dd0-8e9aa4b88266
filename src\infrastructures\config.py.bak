# encoding: utf-8
# <AUTHOR> <EMAIL>
# core/config.py
# created: 2025-04-04 21:12:33
# updated: 2025-06-03 00:42:35

from datetime import datetime, timedelta
from functools import lru_cache
from typing import Any, List, Optional

import toml
from pydantic import BaseModel

# Database Config Schemas
# Include Redis, Mysql(Tortoise), MongoDB


class DatabaseSimpleConnetionConfig(BaseModel):
    uri: Optional[str] = ""
    database: Optional[str] = ""


class DatabaseTortoiseConfig(BaseModel):
    """tortoise config, eg:
    {
        "timezone":"Asia/Shanghai",
        "connections":{
            "default":"mysql://beaver_service:beaver_service123456@47.102.111.71:63306/beaver_service_bak"
        },
        "apps":{
            "models":{
                "models":[
                    "aerich.models",
                    "src.databases.models.customer",
                    "src.databases.models.benefits",
                    "src.databases.models.datas",
                    "src.databases.models.delivery",
                    "src.databases.models.passport"
                ],
                "default_connection":"default"
            }
        }
    }
    """

    class Connections(BaseModel):
        default: str

    class Apps(BaseModel):

        class AppsModelConfig(BaseModel):
            models: list[str] = []
            default_connection: Optional[str] = ""

        models: AppsModelConfig

    timezone: Optional[str] = "Asia/Shanghai"
    connections: Connections  # mysql connection uri
    apps: Apps  # tortoise orm models config


class DatabaseConfig(BaseModel):
    redis: DatabaseSimpleConnetionConfig
    mongodb: DatabaseSimpleConnetionConfig
    tortoise: DatabaseTortoiseConfig


# Rabbitmq Config Schemas


class RabbitmqConfig(BaseModel):
    host: str
    vhost: str
    username: str
    password: str
    port: Optional[int] = 5672
    pool_size: Optional[int] = 5


class LogHandlerConfig(BaseModel):
    # common optionals
    level: str = "INFO"
    colorize: Optional[bool] = None
    serialize: Optional[bool] = None
    backtrace: Optional[bool] = None
    diagnose: Optional[bool] = None
    enqueue: Optional[bool] = None

    # file optionals
    rotation: Optional[str | int | timedelta | datetime] = None
    retention: Optional[str | int | timedelta | datetime] = None
    compression: Optional[str] = None
    encoding: Optional[str] = None


class LogSLSConfig(BaseModel):
    access_key_id: str
    access_key_secret: str
    project: str
    logstore: str
    endpoint: str


class LogHandlersConfig(BaseModel):
    console: LogHandlerConfig
    file: LogHandlerConfig
    sls: LogSLSConfig


class LogConfig(BaseModel):
    dir: str
    handlers: LogHandlersConfig


class BaseConfig(BaseModel):
    secret: str


class FastAPIConfig(BaseModel):
    secret: str
    allow_hosts: List[str] = ["*"]
    cors_origins: List[str] = []
    allow_methods: List[str] = ["HEAD", "OPTIONS", "GET", "POST", "PUT", "DELETE"]
    allow_headers: List[str] = ["*"]
    allow_credentials: bool = True


# third part config schemas


class ThirdpartElemeUnionConfig(BaseModel):
    app_key: str
    app_secret: str
    top_gateway_url: str
    verify_ssl: bool = True


class ThirdpartBifrostConfig(BaseModel):
    public_key: str
    private_key: str
    account: str
    secret_key: str
    base_url: str
    prefix: str


class ThirdpartWifiMasterConfig(BaseModel):
    sso_url: str
    app_id: str


class ThirdpartElemeConfig(BaseModel):

    class UnionConfig(BaseModel):
        app_key: str
        app_secret: str
        top_gateway_url: str
        verify_ssl: bool = True

    class ChannelConfig(BaseModel):
        eleme_app_id: Optional[str] = None
        eleme_channle_no: str
        eleme_channel_source: str
        eleme_channel_app_id: str
        eleme_channel_secret: str
        eleme_welfare3pp: Optional[str] = None

    union: UnionConfig
    channels: dict[str, ChannelConfig]


class ThirdpartAliyunConfig(BaseModel):
    class SMSConfig(BaseModel):
        """aliyun sms config, eg:
        access_key_id = "LTAI5tCX3VPTGfx5URDAvybo"
        access_key_secret = "******************************"
        endpoint = "dysmsapi.aliyuncs.com"
        sign_name = "南京里海数据科技"
        template_code = "SMS_302800024"
        repeat_interval = 60
        """

        access_key_id: str
        access_key_secret: str
        endpoint: str
        sign_name: str
        template_code: str
        repeat_interval: int

    class OSSConfig(BaseModel):
        """aliyun oss config, eg:
        access_key_id = "LTAI5tCX3VPTGfx5URDAvybo"
        access_key_secret = "******************************"
        role_arn = "acs:ram::1252168895612261:role/oss-uploader"
        role_session_name = "web-upload-session"
        """

        access_key_id: str
        access_key_secret: str
        role_arn: str
        role_session_name: str

    sms: SMSConfig
    oss: OSSConfig


class DingtalkConfig(BaseModel):
    order_notify_url: str


class ThirdpartWechatBenefitsMpConfig(BaseModel):
    app_id: str
    app_secret: str


class ThirdpartWechatConfig(BaseModel):
    benefits_mp: ThirdpartWechatBenefitsMpConfig


class ThirdpartAlipayConfig(BaseModel):
    ad_biz_token: str
    app_id: str
    server_url: str
    alipay_public_key: str
    app_private_key: str


# ad_biz_token = "608a66b8e6374dba92286a08e8b98290"
# app_id = "2021004195613243"
# server_url = "https://openapi.alipay.com/gateway.do"
# alipay_public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9e4tZtI2P1wQQL/adWdJ/eNqdKaCyKsp2Of7XWJzWPHkURU0ETcVbBcLRZv8JHtTNba00jj5Z0JHn9C3SazFu1cew7Lgu9I/lghQcD1WE+DDPFrnfY+wLHZTtT0RZ0JL6bVHNsHoKouE4h4Eg+RK0ErnQsTmWoclRkpH79NT74ABPzcwd3B84mW2BJSZAgtngF2iT5sXff6oDSIR0BPTzOmyjIpupTrmM5KJFFU0W5rDwMjFDlXnIAeYFDXkoB/eFkRalQd1D4WkIijvx2g4Jnruo7RKDfMk1quqxM0M9bkJlUJsEgNn84txIX4Mq+rkH/PWJ8Ap1WVmKJx7fghJgQIDAQAB"
# app_private_key = "MIIEogIBAAKCAQEArMpFjH7Tj/t81QpslgfgcpuiH53rtn6ca7i/7QqiIofDCoSGR4pAASijlT5mlmdO0FUos+pQyEv6Ucs4DqiRN+PXMvwv7hJ0gVBG3+BNrCoYxnZ5+4rmTwHJJrj/XWCTYrRWgFQhps79W5NiMIpcS5tNXGOmtEQfrucgCHuIczXsjFSbM9CIoFwunLdmmFiyl8qylhPnyaMvWKPT1u4yJ7iImJBb/E1H1MBk2uvyQ9tCAU8CR+5TJo69BxSHTGLyKJE0C19zVUlJtAVmTUAQqnH1jCFcXR0oyNPfWOvEJB0M3xCh0zV02GSwEWSou+zw8FN5jg3dMhsrCP0BIX0cpwIDAQABAoIBABiN0d5KM4Q1Z6LajV2wltuSdDJr/Y/8Y/wGz/c+WJXxluzKsk4+PiQsAzr1GBztZ0zBnTwb4wjjixnOeBPVLnWzwePz5Fe/daDeqIOt2zvI66ZgNatiLKIzjcMb7OX3EFqpZ9VqnzGWHnjB8+UT9FOmKRMk8g6R07LGE05BR72fQqfLdMuRJQUiT4WmKWuwbyig21CVCpOlPLXkyzARm9Gn347pHPQVyAQTQK8deDn6UBtefK/4suRqtppw/i6iASq1cOvRlkhq3iQ/LY+65LBcUqK86QywKwzCOgD9uEI5iBmE5veFBwTrUaGu52wsgYxUyVFiP7hLDYxALYCLC/kCgYEA84xHsYoRPUW4I3MhXOqBD/NeM3cueOpehidP2Q7lYFbYbgvc00WXrq398rf3KVpP7E9tLKUeGpYhtidzooU9/NZAV/2EoQGq4NdjxZIbKDFIvq9giUo+qi1d8MclijfKvVHN/tXP6L2LQkgEsbd6PtWygz0xo0cOezUxLd4RpJ0CgYEAtZ/dgaVn3WuJf8wNMsdF7FZNGzcvrM/vTKm2sDyJP64VXtY98TEfbRBZXTUtQ6LNOOVL90Tt/qSO62iIOCkitbGS+sBn47oJYRtg8+njuLD9Xs0m+jpyTqRU88lH9wSLbD+T7yYUaf5Ahd3Pwe9dyN5yhC3aFEyOryG7x15X6RMCgYBBY9yr8mIGjX2PJw5CIERev/z+3HUBygtvAYcSxEkZwLeDdHPp1bmQEO8qB/K4i6MB06GAZyTaBo0ulxEZBaVLCUtlVJATmpsCm1ISbdQUyoa5i+Tjd6ezkVKznwSZQ7mPczNXxJh56MpHFYcNIDIWRNIVIoz9RcrBkdqOch1SkQKBgEVp1sdQxvRZwBAmlTi97hxYE1n+amsagTOEfTy3tiCvUJ2RNdUzV/Zf1DeKNkVuOA6xm6niHy8+Bx0zMzR7jDdyqWDKHiprlGAXjaNK1WxlwD/2GhpyMfOaXJlbaPSgOQTi+4/ftteXg2NmFZvh9q7pTUz1FS7C35lRRCn7BZAfAoGAA/YSmYEallEcWT+y/lDSkF4JOl/yrhgmy6Hh6TsX8OA2inLarLUeQNLrT25cc/l0SZrtYEHPL1h8Ylh3FnR8S21VelLf/GNNe37P/quzuvMS9MZkMru+KdKm//se6sJiE8qJODOpdT5IrnbPKqiBbwVcxQoCG022AX+L08VL+i4="


class ThirdpartConfig(BaseModel):
    eleme_union: ThirdpartElemeUnionConfig
    bifrost: ThirdpartBifrostConfig
    dingtalk: DingtalkConfig
    wifimaster: ThirdpartWifiMasterConfig
    aliyun: ThirdpartAliyunConfig
    eleme: ThirdpartElemeConfig
    wechat: ThirdpartWechatConfig
    alipay: ThirdpartAlipayConfig


class HostConfig(BaseModel):
    base_fe: str


class Config(BaseModel):
    webapp: FastAPIConfig
    log: LogConfig
    database: DatabaseConfig
    rabbitmq: RabbitmqConfig
    thirdpart: ThirdpartConfig
    hostname: HostConfig


@lru_cache(maxsize=1)
def load_config(path: str = ".env.toml") -> Config:
    config = Config.model_validate(toml.load(open(path)))
    return config


config = load_config()
