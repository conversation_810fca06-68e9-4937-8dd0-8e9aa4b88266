# encoding: utf-8
# src/infrastructures/gateways/wechat/wechat_mp.py
# created: 2025-08-02 15:07:42

import json
import time
from typing import Any, Dict, Optional

import aiohttp
from Crypto.Cipher import AES
from pydantic import BaseModel, Field


class WechatError(Exception):
    """微信API错误"""

    def __init__(self, errcode: int, errmsg: str):
        self.errcode = errcode
        self.errmsg = errmsg
        super().__init__(f"微信API错误 [{errcode}]: {errmsg}")


class AccessTokenResponse(BaseModel):
    """获取access_token响应"""

    access_token: str
    expires_in: int


class LoginResponse(BaseModel):
    """小程序登录响应"""

    openid: str
    session_key: str
    unionid: Optional[str] = None

    class Config:
        populate_by_name = True


class PhoneNumberResponse(BaseModel):
    """获取手机号响应"""

    phone_number: str = Field(validation_alias="phoneNumber")
    pure_phone_number: str = Field(validation_alias="purePhoneNumber")
    country_code: int = Field(validation_alias="countryCode")  # 区号是数字类型，如86
    watermark: Dict[str, Any]

    class Config:
        populate_by_name = True


class WechatMpGateway:
    """微信小程序服务端SDK"""

    BASE_URL = "https://api.weixin.qq.com"

    def __init__(self, app_id: str, app_secret: str):
        self.app_id = app_id
        self.app_secret = app_secret
        self._access_token: Optional[str] = None
        self._token_expires_at: float = 0

    async def _request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """发送HTTP请求"""
        async with aiohttp.ClientSession() as session:
            if method.upper() == "GET":
                async with session.get(url, params=params, **kwargs) as response:
                    # 微信API有时返回text/plain但内容是JSON，所以先尝试解析JSON
                    try:
                        result = await response.json()
                    except Exception:
                        # 如果JSON解析失败，尝试读取文本内容并手动解析
                        text_content = await response.text()
                        try:
                            result = json.loads(text_content)
                        except json.JSONDecodeError as e:
                            # 如果仍然无法解析，返回原始文本
                            raise WechatError(-1, f"无法解析响应内容: {text_content}") from e
            else:
                async with session.post(url, json=data, **kwargs) as response:
                    if response.content_type == "application/json":
                        result = await response.json()
                    else:
                        # 尝试解析JSON，如果失败则处理为二进制内容
                        try:
                            result = await response.json()
                        except Exception:
                            # 处理二进制响应（如小程序码图片）
                            content = await response.read()
                            return {"content": content, "content_type": response.content_type}

            # 检查微信API错误
            if "errcode" in result and result["errcode"] != 0:
                raise WechatError(result["errcode"], result.get("errmsg", "未知错误"))

            return result

    async def get_access_token(self) -> str:
        """获取接口调用凭据"""
        # 检查token是否过期
        if self._access_token and time.time() < self._token_expires_at:
            return self._access_token

        url = f"{self.BASE_URL}/cgi-bin/token"
        params = {"grant_type": "client_credential", "appid": self.app_id, "secret": self.app_secret}

        result = await self._request("GET", url, params=params)

        self._access_token = result["access_token"]
        # 提前5分钟过期，避免边界情况
        self._token_expires_at = time.time() + result["expires_in"] - 300

        # 确保返回非None值
        if self._access_token is None:
            raise WechatError(-1, "获取access_token失败")

        return self._access_token

    async def code2session(self, js_code: str) -> LoginResponse:
        """小程序登录"""
        url = f"{self.BASE_URL}/sns/jscode2session"
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": js_code,
            "grant_type": "authorization_code",
        }

        result = await self._request("GET", url, params=params)
        return LoginResponse(**result)

    async def get_unlimited_qrcode(
        self,
        scene: str,
        page: Optional[str] = None,
        check_path: bool = False,
        env_version: str = "release",
        width: int = 430,
        auto_color: bool = False,
        line_color: Optional[Dict[str, int]] = None,
        is_hyaline: bool = False,
    ) -> bytes:
        """获取不限制的小程序码

        Args:
            scene: 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
            page: 页面路径，不能携带参数，如果不填写这个字段，默认跳主页面
            check_path: 检查page是否存在，为true时page必须是已经发布的小程序存在的页面
            env_version: 要打开的小程序版本。正式版为"release"，体验版为"trial"，开发版为"develop"
            width: 二维码的宽度，单位px，最小280px，最大1280px
            auto_color: 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
            line_color: 自定义线条颜色，如{"r":0,"g":0,"b":0}
            is_hyaline: 是否需要透明底色
        """
        access_token = await self.get_access_token()
        url = f"{self.BASE_URL}/wxa/getwxacodeunlimit?access_token={access_token}"

        data = {
            "scene": scene,
            "check_path": check_path,
            "env_version": env_version,
            "width": width,
            "auto_color": auto_color,
            "is_hyaline": is_hyaline,
        }

        if page:
            data["page"] = page
        if line_color:
            data["line_color"] = line_color

        result = await self._request("POST", url, data=data)

        if isinstance(result, dict) and "content" in result:
            return result["content"]
        else:
            raise WechatError(-1, "获取小程序码失败")

    async def get_qrcode(
        self,
        path: str,
        width: int = 430,
        auto_color: bool = False,
        line_color: Optional[Dict[str, int]] = None,
        is_hyaline: bool = False,
    ) -> bytes:
        """获取小程序码

        Args:
            path: 扫码进入的小程序页面路径，最大长度128字节，不能为空
            width: 二维码的宽度，单位px，最小280px，最大1280px
            auto_color: 自动配置线条颜色
            line_color: 自定义线条颜色
            is_hyaline: 是否需要透明底色
        """
        access_token = await self.get_access_token()
        url = f"{self.BASE_URL}/wxa/getwxacode?access_token={access_token}"

        data = {"path": path, "width": width, "auto_color": auto_color, "is_hyaline": is_hyaline}

        if line_color:
            data["line_color"] = line_color

        result = await self._request("POST", url, data=data)

        if isinstance(result, dict) and "content" in result:
            return result["content"]
        else:
            raise WechatError(-1, "获取小程序码失败")

    async def get_phone_number(self, code: str) -> PhoneNumberResponse:
        """获取手机号

        Args:
            code: 手机号获取凭证
        """
        access_token = await self.get_access_token()
        url = f"{self.BASE_URL}/wxa/business/getuserphonenumber?access_token={access_token}"

        data = {"code": code}

        result = await self._request("POST", url, data=data)

        # 微信API返回的是 phone_info 对象，包含手机号相关信息
        phone_info = result.get("phone_info", {})
        return PhoneNumberResponse(**phone_info)

    async def decrypt_data(self, encrypted_data: str, iv: str, session_key: str) -> Dict[str, Any]:
        """解密微信数据（如手机号、用户信息等）

        Args:
            encrypted_data: 加密数据
            iv: 初始向量
            session_key: 会话密钥
        """
        import base64

        # Base64解码
        session_key_bytes = base64.b64decode(session_key)
        encrypted_data_bytes = base64.b64decode(encrypted_data)
        iv_bytes = base64.b64decode(iv)

        # AES解密
        cipher = AES.new(session_key_bytes, AES.MODE_CBC, iv_bytes)
        decrypted = cipher.decrypt(encrypted_data_bytes)

        # 去除填充
        padding = decrypted[-1]
        if isinstance(padding, str):
            padding = ord(padding)
        decrypted = decrypted[:-padding]

        # 解析JSON
        return json.loads(decrypted.decode("utf-8"))
