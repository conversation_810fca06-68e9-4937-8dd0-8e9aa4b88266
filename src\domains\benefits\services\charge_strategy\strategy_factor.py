# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/charge_strategy/strategy_factor.py
# created: 2025-04-06 16:41:59
# updated: 2025-04-07 07:58:25

from typing import Dict, Type

from .strategy import ChargeStrategy

_strategy_classes: Dict[str, Type[ChargeStrategy]] = {}


def register_strategy(strategy_type: str):
    """策略注册装饰器"""

    def decorator(cls):
        _strategy_classes[strategy_type] = cls
        return cls

    return decorator


class ChargeStrategyFactory:
    @staticmethod
    def get_strategy(strategy_type: str) -> ChargeStrategy:
        """根据策略类型获取策略实例"""
        if strategy_type not in _strategy_classes:
            # 懒加载策略实现
            from .implements.eleme_city_strategy import ElemeCityChargeStrategy
            from .implements.eleme_strategy import ElemeStrategy
            from .implements.eleme_union_strategy import ElemeUnionChargeStrategy

            # 可以在这里导入其他策略

        strategy_class = _strategy_classes.get(strategy_type)
        if not strategy_class:
            raise ValueError(f"不支持的充值策略类型: {strategy_type}")

        return strategy_class()
