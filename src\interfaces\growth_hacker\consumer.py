# encoding: utf-8
# src/interfaces/growth_hacker/consumer.py
# created: 2025-07-27 19:03:08

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger

from deploys.growth_hacker.loggers import clear_context, set_phone, set_task_id
from src.applications.growth_hacker.schemas import TaskMessageContent
from src.containers import Container
from src.infrastructures.rabbitmq.consumers import BaseConsumer

from .schemas import TaskMessage

if TYPE_CHECKING:
    from aio_pika.abc import AbstractIncomingMessage

    from src.applications.growth_hacker.services import TaskService


class GrowthHackerConsumer(BaseConsumer):
    """Growth Hacker Consumer"""

    queue_name = "gh_task"
    exchange_name = "tasks"
    routing_key = "gh.task"

    @inject
    async def process(
        self,
        message: "AbstractIncomingMessage",
        task_service: "TaskService" = Provide[Container.applications.growth_hacker_task_service],
    ) -> None:
        task: TaskMessageContent = TaskMessage.model_validate_json(message.body).payload

        if not task.city:
            logger.warning(f"任务[{task.task_id}] 城市为空, 跳过执行")
            return

        set_task_id(task.task_id)
        set_phone(task.phone)
        logger.info(
            f"收到消息: task_id:{task.task_id} phone:{task.phone} city:{task.city} batch_name:{task.batch_name}"
        )

        valid = await task_service.check_build_task(task)
        if not valid:
            logger.warning(f"任务[{task.task_id}] 构建失败, 跳过执行")
            return
        await task_service.execute_task(task)

        clear_context()

    async def rollback(self, message: "AbstractIncomingMessage", exp: Exception) -> None:
        logger.exception(f"Growth Hacker Consumer Rollback: {exp}")
        clear_context()
