from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponOrderCreateRequest(BaseRequest):

    def __init__(self, order_dto: object = None):
        """
        订单对象
        """
        self._order_dto = order_dto

    @property
    def order_dto(self):
        return self._order_dto

    @order_dto.setter
    def order_dto(self, order_dto):
        if isinstance(order_dto, object):
            self._order_dto = order_dto
        else:
            raise TypeError("order_dto must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.coupon.order.create"

    def to_dict(self):
        request_dict = {}
        if self._order_dto is not None:
            request_dict["order_dto"] = convert_struct(self._order_dto)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponOrderCreateOrderDto:
    def __init__(
        self,
        outer_order_id: str = None,
        item_id: str = None,
        quantity: int = None,
        order_fee: int = None,
        title: str = None,
        ext_info: str = None,
        phone: str = None,
        skip_pay: bool = None,
        item_type: int = None,
    ):
        """
        外部订单号
        """
        self.outer_order_id = outer_order_id
        """
            加密后的商品ID
        """
        self.item_id = item_id
        """
            订单份数
        """
        self.quantity = quantity
        """
            订单金额
        """
        self.order_fee = order_fee
        """
            商品名称
        """
        self.title = title
        """
            扩展字段，json格式。淘宝订单号tbOrderId
        """
        self.ext_info = ext_info
        """
            手机号
        """
        self.phone = phone
        """
            true预下单不支付，false下单并支付
        """
        self.skip_pay = skip_pay
        """
            商品类型
        """
        self.item_type = item_type
