# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/benefits/skus.py
# created: 2025-01-11 21:27:29
# updated: 2025-05-26 11:04:14

from fastapi import APIRouter, Query

from src.domains.benefits.dto import (
    BenefitsSkuChargeRecordDetailDTO,
    BenefitsSkuChargeRecordDTO,
    BenefitsSkuCreatedDTO,
    BenefitsSkuDetailDTO,
    BenefitsSkuDTO,
    BenefitsSkuUpdateDTO,
)
from src.infrastructures import errors
from src.repositories.benefits.sku import SkuFilters, SkuRepository
from src.repositories.benefits.sku_charge_record import SkuChargeRecordFilters, SkuChargeRecordRepository

from ...schemas import (
    BaseResponse,
    ListData,
    SkuListResponse,
    SkuRecordResponse,
    SkuRecordsResponse,
    SkuResponse,
)

router = APIRouter(tags=["benefits", "skus"])


@router.post("/skus", response_model=SkuResponse)
async def create_sku(sku: BenefitsSkuCreatedDTO):  # type: ignore
    sku = await SkuRepository.create_sku(sku)
    return SkuResponse(data=await BenefitsSkuDetailDTO.from_tortoise_orm(sku))


@router.put("/skus/{sku_code}", response_model=SkuResponse)
async def update_sku(sku_code: str, updated_fields: BenefitsSkuUpdateDTO):  # type: ignore
    sku = await SkuRepository.update_sku(sku_code, updated_fields)
    if not sku:
        raise errors.SkuNotFoundError
    return SkuResponse(data=await BenefitsSkuDetailDTO.from_tortoise_orm(sku))


@router.delete("/skus/{sku_code}", response_model=SkuResponse)
async def delete_sku(sku_code: str):
    await SkuRepository.delete_by_code(sku_code)
    return BaseResponse(message="删除成功")


@router.get("/skus", response_model=SkuListResponse)
async def get_skus(params: SkuFilters = Query(...)):
    count, skus = await SkuRepository.get_skus_by_params(params)
    skus = [await BenefitsSkuDTO.from_tortoise_orm(sku) for sku in skus]  # type: ignore
    return SkuListResponse(data=ListData(total=count, data=skus))


@router.get("/skus/{sku_code}", response_model=SkuResponse)
async def get_sku(sku_code: str):
    sku = await SkuRepository.get_sku_by_code(sku_code)
    if not sku:
        raise errors.SkuNotFoundError
    return SkuResponse(data=await BenefitsSkuDetailDTO.from_tortoise_orm(sku))


@router.get("/skus/{sku_code}/records", response_model=SkuRecordsResponse)
async def get_sku_records_by_sku_code(sku_code: str, params: SkuChargeRecordFilters = Query(...)):
    params.sku_code = sku_code
    count, records = await SkuChargeRecordRepository.get_sku_charge_records(params)
    records = [await BenefitsSkuChargeRecordDTO.from_tortoise_orm(record) for record in records]  # type: ignore
    return SkuRecordsResponse(data=ListData(total=count, data=records))


@router.get("/sku/records", response_model=SkuRecordsResponse)
async def get_sku_records(params: SkuChargeRecordFilters = Query(...)):
    count, records = await SkuChargeRecordRepository.get_sku_charge_records(params)
    records = [await BenefitsSkuChargeRecordDTO.from_tortoise_orm(record) for record in records]  # type: ignore
    return SkuRecordsResponse(data=ListData(total=count, data=records))


@router.get("/sku/records/{record_id}", response_model=SkuRecordResponse)
async def get_sku_record(record_id: int):
    record = await SkuChargeRecordRepository.get_by_id(record_id)
    if not record:
        raise errors.ChargeRecordNotFoundError
    return SkuRecordResponse(data=await BenefitsSkuChargeRecordDetailDTO.from_tortoise_orm(record))
