# encoding: utf-8
# src/repositories/growth_hacker/users.py
# created: 2025-07-25 17:20:00

from typing import Any, Dict, List, Optional

from tortoise.exceptions import DoesNotExist

from src.databases.models.growth_hacker import UserDeviceCache


class UserRepository:
    """Growth Hacker 用户设备缓存仓库"""

    async def create_or_update(
        self,
        phone: str,
        local_storage: Optional[Dict[str, Any]] = None,
        session_storage: Optional[Dict[str, Any]] = None,
        cookies: Optional[List[Dict[str, Any]]] = None,
        device_config: Optional[Dict[str, Any]] = None,
        webview_config: Optional[Dict[str, Any]] = None,
    ) -> UserDeviceCache:
        """创建或更新用户设备缓存"""
        defaults = {}

        if local_storage is not None:
            defaults["local_storage"] = local_storage
        if session_storage is not None:
            defaults["session_storage"] = session_storage
        if cookies is not None:
            defaults["cookies"] = cookies  # type: ignore
        if device_config is not None:
            defaults["device_config"] = device_config
        if webview_config is not None:
            defaults["webview_config"] = webview_config

        cache, created = await UserDeviceCache.update_or_create(phone=phone, defaults=defaults)

        if not created:
            # 如果是更新，确保时间戳也更新
            await cache.update_timestamp()

        return cache

    async def get_by_phone(self, phone: str) -> Optional[UserDeviceCache]:
        """通过手机号获取用户设备缓存"""
        try:
            return await UserDeviceCache.get_or_none(phone=phone)
        except DoesNotExist:
            return None

    async def update_browser_data(
        self,
        phone: str,
        local_storage: Optional[Dict[str, Any]] = None,
        session_storage: Optional[Dict[str, Any]] = None,
        cookies: Optional[List[Dict[str, Any]]] = None,
    ) -> bool:
        """更新浏览器数据"""
        cache = await self.get_by_phone(phone)
        if not cache:
            return False

        update_fields = []

        if local_storage is not None:
            cache.local_storage = local_storage
            update_fields.append("local_storage")

        if session_storage is not None:
            cache.session_storage = session_storage
            update_fields.append("session_storage")

        if cookies is not None:
            cache.cookies = cookies
            update_fields.append("cookies")

        if update_fields:
            update_fields.append("updated_at")
            await cache.save(update_fields=update_fields)
            return True

        return False

    async def update_device_config(
        self,
        phone: str,
        device_config: Optional[Dict[str, Any]] = None,
        webview_config: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """更新设备配置"""
        cache = await self.get_by_phone(phone)
        if not cache:
            return False

        update_fields = []

        if device_config is not None:
            cache.device_config = device_config
            update_fields.append("device_config")

        if webview_config is not None:
            cache.webview_config = webview_config
            update_fields.append("webview_config")

        if update_fields:
            update_fields.append("updated_at")
            await cache.save(update_fields=update_fields)
            return True

        return False

    async def merge_storage_data(
        self,
        phone: str,
        local_storage_update: Dict[str, Any],
        session_storage_update: Dict[str, Any],
    ) -> bool:
        """合并存储数据（不覆盖现有数据）"""
        cache = await self.get_by_phone(phone)
        if not cache:
            # 如果不存在，创建新的
            await self.create_or_update(
                phone=phone,
                local_storage=local_storage_update,
                session_storage=session_storage_update,
            )
            return True

        # 合并 local_storage
        if local_storage_update:
            cache.local_storage.update(local_storage_update)

        # 合并 session_storage
        if session_storage_update:
            cache.session_storage.update(session_storage_update)

        await cache.save(update_fields=["local_storage", "session_storage", "updated_at"])
        return True

    async def delete_by_phone(self, phone: str) -> bool:
        """删除用户设备缓存"""
        deleted = await UserDeviceCache.filter(phone=phone).delete()
        return deleted > 0

    async def get_recent_users(self, limit: int = 100) -> List[UserDeviceCache]:
        """获取最近更新的用户"""
        return await UserDeviceCache.all().order_by("-updated_at").limit(limit)

    async def count_total(self) -> int:
        """统计总用户数"""
        return await UserDeviceCache.all().count()

    async def upsert_user_profile(
        self,
        phone: str,
        local_storage: dict,
        session_storage: dict,
        cookies: list,
        device_config: dict,
        webview_config: dict,
    ) -> UserDeviceCache:
        """更新或插入用户配置文件"""

        profile_data = {
            "local_storage": local_storage,
            "session_storage": session_storage,
            "cookies": cookies,
            "device_config": device_config,
            "webview_config": webview_config,
        }
        return await self.create_or_update(phone=phone, **profile_data)  # type: ignore
