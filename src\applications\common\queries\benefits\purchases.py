# encoding: utf-8
# src/applications/common/queries/benefits/purchases.py
# created: 2025-08-12 15:30:00

from typing import TYPE_CHECKING

from loguru import logger

if TYPE_CHECKING:
    from src.infrastructures.gateways.eleme.union import ElemeUnionBenefitsGateway
    from src.infrastructures.gateways.eleme.union.dto import CouponPackagePurchaseListDto


class UnionPurchaseQueryService:
    """饿了么联盟权益购买查询服务"""

    def __init__(self, eleme_union_benefits_gateway: "ElemeUnionBenefitsGateway"):
        self.gateway = eleme_union_benefits_gateway

    async def get_all_purchases(self) -> list:
        """
        获取所有权益购买订单

        Returns:
            所有购买订单列表
        """
        all_purchases = []
        page_number = 1
        page_size = 20

        while True:
            # 获取一页数据
            purchase_list: "CouponPackagePurchaseListDto" = await self.gateway.get_purchase_list(
                page=page_number, page_size=page_size
            )

            logger.info(
                "获取联盟权益购买订单第{page}页，本页{count}条", page=page_number, count=len(purchase_list.records)
            )

            # 添加到总列表
            all_purchases.extend(purchase_list.records)

            # 如果本页数据不足page_size，说明已经是最后一页
            if len(purchase_list.records) < page_size:
                break

            page_number += 1

        logger.info("获取联盟权益购买订单完成，共{total}条", total=len(all_purchases))

        return all_purchases
