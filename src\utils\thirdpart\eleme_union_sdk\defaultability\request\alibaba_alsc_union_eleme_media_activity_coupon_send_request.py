from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeMediaActivityCouponSendRequest(BaseRequest):

    def __init__(self, send_request: object = None):
        """
        请求对象
        """
        self._send_request = send_request

    @property
    def send_request(self):
        return self._send_request

    @send_request.setter
    def send_request(self, send_request):
        if isinstance(send_request, object):
            self._send_request = send_request
        else:
            raise TypeError("send_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.media.activity.coupon.send"

    def to_dict(self):
        request_dict = {}
        if self._send_request is not None:
            request_dict["send_request"] = convert_struct(self._send_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeMediaActivityCouponSendMediaActivityCouponSendRequest:
    def __init__(self, mobile: str = None, media_activity_id: str = None):
        """
        领券手机号
        """
        self.mobile = mobile
        """
            媒体出资活动ID
        """
        self.media_activity_id = media_activity_id
