# encoding: utf-8
# tests/unit/applications/openapi/factories.py
# created: 2025-08-02 15:10:00

"""OpenAPI 应用层测试数据工厂"""

from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, MagicMock

from faker import Faker

fake = Faker('zh_CN')


class OpenAPITestDataFactory:
    """OpenAPI 测试数据工厂类"""

    @staticmethod
    def create_customer_data(
        customer_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建客户测试数据"""
        data = {
            "id": fake.random_int(min=1, max=999999),
            "code": customer_id or f"customer_{fake.random_int(min=1000, max=9999)}",
            "name": fake.company(),
            "description": fake.text(max_nb_chars=100),
            "app_id": f"app_{fake.random_int(min=100, max=999)}",
            "tenant_id": f"tenant_{fake.random_int(min=100, max=999)}",
            "balance": fake.random_int(min=0, max=100000),
        }
        data.update(kwargs)
        return data

    @staticmethod
    def create_customer_secret_data(
        customer_id: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建客户密钥测试数据"""
        data = {
            "id": fake.random_int(min=1, max=999999),
            "customer_id": customer_id or fake.random_int(min=1, max=999999),
            "access_key": fake.sha256(),
            "secret_key": fake.sha256(),
            "jwt_secret": fake.sha256(),
            "token": fake.uuid4(),
            "enabled": True,
            "expires_at": fake.future_datetime(),
        }
        data.update(kwargs)
        return data

    @staticmethod
    def create_benefit_product_data(
        product_id: Optional[str] = None,
        customer_id: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建权益产品测试数据"""
        data = {
            "id": fake.random_int(min=1, max=999999),
            "code": product_id or f"product_{fake.random_int(min=1000, max=9999)}",
            "name": fake.word() + "权益产品",
            "type": fake.random_element(elements=("coupon", "discount", "gift", "cash")),
            "description": fake.text(max_nb_chars=200),
            "detail": {
                "category": fake.word(),
                "value": fake.random_int(min=1, max=1000),
                "unit": "yuan"
            },
            "enabled": True,
            "sale_price": fake.random_int(min=100, max=10000),
            "stock": fake.random_int(min=0, max=1000),
            "customer_id": customer_id or fake.random_int(min=1, max=999999),
        }
        data.update(kwargs)
        return data

    @staticmethod
    def create_activity_data(
        activity_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建活动测试数据"""
        data = {
            "id": fake.random_int(min=1, max=999999),
            "name": fake.sentence(nb_words=3),
            "description": fake.text(max_nb_chars=200),
            "code": activity_id or f"activity_{fake.random_int(min=1000, max=9999)}",
            "access_url": fake.url(),
            "union_active_id": f"union_{fake.random_int(min=10000, max=99999)}",
            "union_zone_pid": f"zone_{fake.random_int(min=100, max=999)}",
            "created_at": fake.past_datetime(),
            "updated_at": fake.past_datetime(),
        }
        data.update(kwargs)
        return data

    @staticmethod
    def create_order_data(
        order_no: Optional[str] = None,
        customer_id: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建订单测试数据"""
        data = {
            "id": fake.random_int(min=1, max=999999),
            "order_id": order_no or f"ORDER_{fake.random_int(min=100000, max=999999)}",
            "out_order_id": f"THIRD_{fake.random_int(min=100000, max=999999)}",
            "customer_id": customer_id or fake.random_int(min=1, max=999999),
            "product_id": fake.random_int(min=1, max=999999),
            "product_code": f"product_{fake.random_int(min=1000, max=9999)}",
            "quantity": fake.random_int(min=1, max=10),
            "price": fake.random_int(min=100, max=10000),
            "total_amount": fake.random_int(min=100, max=50000),
            "status": fake.random_element(elements=("pending", "paid", "shipped", "completed", "cancelled")),
            "app_id": f"app_{fake.random_int(min=100, max=999)}",
            "tenant_id": f"tenant_{fake.random_int(min=100, max=999)}",
            "created_at": fake.past_datetime(),
            "updated_at": fake.past_datetime(),
        }
        data.update(kwargs)
        return data


class OpenAPIMockFactory:
    """OpenAPI Mock 对象工厂类"""

    @staticmethod
    def create_mock_request(
        headers: Optional[Dict[str, str]] = None,
        query_params: Optional[Dict[str, str]] = None,
        path_params: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> MagicMock:
        """创建 Mock FastAPI Request 对象"""
        mock_request = MagicMock()
        mock_request.headers = headers or {}
        mock_request.query_params = query_params or {}
        mock_request.path_params = path_params or {}
        
        # 设置其他属性
        for key, value in kwargs.items():
            setattr(mock_request, key, value)
            
        return mock_request

    @staticmethod
    def create_mock_customer_repository(
        **return_values
    ) -> AsyncMock:
        """创建 Mock CustomerRepository"""
        mock = AsyncMock()
        
        # 默认返回值
        defaults = {
            "get_by_id": None,
            "get_by_token": None,
            "get_by_ak": None,
            "create": MagicMock(id=1),
            "save": None,
            "update": None,
            "delete": None,
        }
        
        # 应用自定义返回值
        defaults.update(return_values)
        
        # 设置方法返回值
        for method, return_value in defaults.items():
            if hasattr(mock, method):
                getattr(mock, method).return_value = return_value
        
        return mock

    @staticmethod
    def create_mock_gateway(
        gateway_name: str = "mock_gateway",
        **return_values
    ) -> AsyncMock:
        """创建通用 Mock Gateway"""
        mock = AsyncMock()
        
        # 常用的默认方法
        common_methods = {
            "get_activities": [],
            "get_activity_detail": None,
            "get_data": {},
            "post_data": {"status": "success"},
            "call_api": {"code": 200, "data": {}},
        }
        
        # 应用默认返回值
        common_methods.update(return_values)
        
        # 设置方法返回值
        for method, return_value in common_methods.items():
            setattr(mock, method, AsyncMock(return_value=return_value))
        
        return mock

    @staticmethod
    def create_mock_redis(
        **return_values
    ) -> AsyncMock:
        """创建 Mock Redis 客户端"""
        mock = AsyncMock()
        
        # 默认返回值
        defaults = {
            "get": None,
            "set": True,
            "setex": True,
            "delete": 1,
            "expire": True,
            "ttl": -2,
            "exists": 0,
            "hget": None,
            "hset": True,
            "hdel": 1,
            "incr": 1,
            "decr": -1,
        }
        
        # 应用自定义返回值
        defaults.update(return_values)
        
        # 设置异步方法返回值
        for method, return_value in defaults.items():
            setattr(mock, method, AsyncMock(return_value=return_value))
        
        return mock

    @staticmethod
    def create_mock_service(
        service_name: str = "mock_service",
        **return_values
    ) -> AsyncMock:
        """创建通用 Mock Service"""
        mock = AsyncMock()
        
        # 常用的默认方法
        common_methods = {
            "get_by_id": None,
            "create": MagicMock(id=1),
            "update": None,
            "delete": True,
            "list": [],
            "count": 0,
        }
        
        # 应用默认返回值和自定义返回值
        common_methods.update(return_values)
        
        # 设置方法返回值
        for method, return_value in common_methods.items():
            if callable(return_value):
                setattr(mock, method, return_value)
            else:
                setattr(mock, method, AsyncMock(return_value=return_value))
        
        return mock


class OpenAPIFixtureFactory:
    """OpenAPI 测试固件工厂类"""

    @staticmethod
    def create_authentication_fixtures():
        """创建认证相关的测试固件"""
        return {
            "valid_token": fake.uuid4(),
            "invalid_token": "invalid_token_123",
            "expired_token": "expired_token_456",
            "valid_access_key": fake.sha256()[:32],
            "valid_secret_key": fake.sha256(),
            "invalid_access_key": "invalid_ak",
            "invalid_secret_key": "invalid_sk",
        }

    @staticmethod
    def create_error_scenarios():
        """创建错误场景测试数据"""
        return {
            "network_timeout": {
                "exception": "asyncio.TimeoutError",
                "message": "Network timeout",
            },
            "database_error": {
                "exception": "DatabaseError", 
                "message": "Database connection failed",
            },
            "validation_error": {
                "exception": "ValidationError",
                "message": "Invalid input parameters",
            },
            "authentication_error": {
                "exception": "AuthenticationError",
                "message": "Authentication failed",
            },
            "authorization_error": {
                "exception": "AuthorizationError", 
                "message": "Access denied",
            },
        }

    @staticmethod
    def create_performance_test_data():
        """创建性能测试数据"""
        return {
            "concurrent_users": [10, 50, 100, 200],
            "request_rates": [1, 5, 10, 20],  # 每秒请求数
            "test_duration": 60,  # 秒
            "expected_response_time": 1000,  # 毫秒
            "expected_success_rate": 0.99,  # 99%
        }

    @staticmethod
    def create_test_config():
        """创建测试配置"""
        return {
            "openapi": {
                "token_expire_seconds": 3600,
                "aksk_expire_seconds": 7200,
                "max_retry_times": 3,
                "timeout_seconds": 30,
            },
            "redis": {
                "cache_prefix": "test:",
                "default_expire": 300,
                "max_connections": 10,
            },
            "database": {
                "pool_size": 5,
                "max_overflow": 10,
                "pool_timeout": 30,
            },
            "gateways": {
                "eleme": {
                    "timeout": 30,
                    "retry_times": 3,
                    "base_url": "https://api.eleme.test",
                },
                "bifrost": {
                    "timeout": 30,
                    "retry_times": 3,
                    "base_url": "https://api.bifrost.test",
                },
            },
        }