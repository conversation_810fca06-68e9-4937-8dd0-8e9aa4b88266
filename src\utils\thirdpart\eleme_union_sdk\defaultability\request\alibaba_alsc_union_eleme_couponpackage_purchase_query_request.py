from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponpackagePurchaseQueryRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询request
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.couponpackage.purchase.query"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponpackagePurchaseQueryCouponPackagePurchaseQueryRequest:
    def __init__(self, page_number: int = None, page_size: int = None):
        """
        请求页（从1开始）
        """
        self.page_number = page_number
        """
            每页数（1-20）
        """
        self.page_size = page_size
