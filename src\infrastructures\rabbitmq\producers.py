# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# core/rabbitmq/producers.py
# created: 2025-01-26 23:03:23
# updated: 2025-04-13 11:26:55

from typing import TYPE_CHECKING, Optional

from loguru import logger

if TYPE_CHECKING:
    from src.infrastructures.rabbitmq import Message, RabbitMQConnectionPool


class RabbitMQProducer:
    def __init__(self, conn_pool: "RabbitMQConnectionPool"):
        self.conn_pool = conn_pool

    async def publish_message(
        self,
        message: "Message",
        exchange_name: Optional[str] = None,
        routing_key: Optional[str] = None,
        timeout: int = 10,
        delay: Optional[int] = None,
    ):
        """发送消息

        :param message: 消息对象
        :param exchange_name: 交换机名称，如果不提供则使用消息中的exchange_name
        :param routing_key: 路由键，如果不提供则使用消息中的routing_key
        :param timeout: 超时时间（秒）
        :param delay: 延迟时间，单位: 毫秒(ms)
        """
        # 优先使用参数中的exchange_name，否则使用消息中的
        target_exchange = exchange_name or message.exchange_name or "amq.topic"
        target_routing_key = routing_key or message.routing_key or "amq.topic"

        try:
            async with self.conn_pool.get_channel() as channel:
                exchange = await channel.get_exchange(target_exchange)
                rmq_message = message.to_rmq_message()

                # 如果设置了延迟，添加延迟头部
                if delay:
                    if rmq_message.headers is None:
                        rmq_message.headers = {}
                    rmq_message.headers["x-delay"] = delay

                result = await exchange.publish(
                    rmq_message,
                    routing_key=target_routing_key,
                    timeout=timeout,
                )

                logger.info(
                    "Message[{message_id}] published successfully to exchange '{exchange}' "
                    "with routing key '{routing_key}', delay: {delay}ms",
                    message_id=message.message_id,
                    exchange=target_exchange,
                    routing_key=target_routing_key,
                    delay=delay or 0,
                )

                return result

        except Exception as e:
            logger.error(
                "Failed to publish message[{message_id}] to exchange '{exchange}' "
                "with routing key '{routing_key}': {error}",
                message_id=message.message_id,
                exchange=target_exchange,
                routing_key=target_routing_key,
                error=str(e),
            )
            raise
