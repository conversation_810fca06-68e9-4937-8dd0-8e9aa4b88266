# encoding: utf-8
# src/infrastructures/gateways/regions/gateway.py
# created: 2025-08-08 03:45:27

import json
import urllib.request
from functools import lru_cache
from typing import List, Optional

from loguru import logger

from .schemas import CityInfo, RegionInfo, RegionSearchResult, SearchResult, Statistics, SupportedCity


class RegionGateway:
    """地区信息网关 - 基于饿了么地区数据提供完整的地区服务"""

    def __init__(self):
        self._china_json_url = "https://saturn.elemecdn.com/model/china.json"
        self._data = None
        self._province_map = {}
        self._city_map = {}
        self._district_map = {}
        self._alsc_district_map = {}  # 新增：以 alsc_district_id 为键的映射

        # 加载数据
        self._load_data()

    def _load_data(self):
        """从网络加载china.json数据并构建索引"""
        try:
            logger.debug(f"正在从 {self._china_json_url} 获取地区数据...")
            with urllib.request.urlopen(self._china_json_url) as response:
                self._data = json.loads(response.read().decode("utf-8"))
            logger.debug("地区数据加载成功")
        except Exception as e:
            logger.error(f"加载地区数据失败: {e}")
            raise

        # 构建索引
        self._build_index()

    def _build_index(self):
        """构建省市区三级索引"""
        if not self._data:
            return

        provinces = self._data.get("provinceList", [])

        for province in provinces:
            # 省份索引
            p_id = province.get("id")
            if p_id is None:
                continue

            self._province_map[p_id] = {
                "id": p_id,
                "name": province.get("name", ""),
                "fullName": province.get("fullName", ""),
                "districtCode": province.get("districtCode", ""),
                "pinyin": province.get("pinyin", ""),
                "region": province.get("region", ""),
                "latitude": province.get("latitude"),
                "longitude": province.get("longitude"),
            }

            # 获取所有城市（直辖市 + 普通城市）
            cities = province.get("directCityList", []) + province.get("cityList", [])

            for city in cities:
                c_id = city.get("id")
                if c_id is None:
                    continue

                # 城市索引 - 使用(province_id, city_id)作为键
                city_key = (p_id, c_id)
                self._city_map[city_key] = {
                    "id": c_id,
                    "name": city.get("name", ""),
                    "fullName": city.get("fullName", ""),
                    "districtCode": city.get("districtCode", ""),
                    "pinyin": city.get("pinyin", ""),
                    "latitude": city.get("latitude"),
                    "longitude": city.get("longitude"),
                    "type": city.get("type"),
                    "isEleme": city.get("isEleme"),
                    "province_id": p_id,
                }

                # 处理区县
                districts = city.get("districtList", [])

                # 处理可能的县级市（lowerCityList）
                lower_cities = city.get("lowerCityList", [])
                for lower_city in lower_cities:
                    lc_id = lower_city.get("id")
                    if lc_id is None:
                        continue

                    # 将县级市也作为区县处理
                    district_key = (p_id, c_id, lc_id)
                    district_data = {
                        "id": lc_id,
                        "name": lower_city.get("name", ""),
                        "fullName": lower_city.get("fullName", ""),
                        "districtCode": lower_city.get("districtCode", ""),
                        "pinyin": lower_city.get("pinyin", ""),
                        "latitude": lower_city.get("latitude"),
                        "longitude": lower_city.get("longitude"),
                        "type": lower_city.get("type"),
                        "isEleme": lower_city.get("isEleme"),
                        "province_id": p_id,
                        "city_id": c_id,
                        "is_lower_city": True,
                    }
                    self._district_map[district_key] = district_data
                    self._alsc_district_map[lc_id] = district_data

                    # 县级市下的区县
                    sub_districts = lower_city.get("districtList", [])
                    for sub_district in sub_districts:
                        sd_id = sub_district.get("id")
                        if sd_id is None:
                            continue

                        # 使用县级市ID作为city_id
                        sub_district_key = (p_id, lc_id, sd_id)
                        sub_district_data = {
                            "id": sd_id,
                            "name": sub_district.get("name", ""),
                            "fullName": sub_district.get("fullName", ""),
                            "districtCode": sub_district.get("districtCode", ""),
                            "pinyin": sub_district.get("pinyin", ""),
                            "latitude": sub_district.get("latitude"),
                            "longitude": sub_district.get("longitude"),
                            "type": sub_district.get("type"),
                            "isEleme": sub_district.get("isEleme"),
                            "province_id": p_id,
                            "city_id": lc_id,
                            "parent_city_id": c_id,
                        }
                        self._district_map[sub_district_key] = sub_district_data
                        self._alsc_district_map[sd_id] = sub_district_data

                # 处理普通区县
                for district in districts:
                    d_id = district.get("id")
                    if d_id is None:
                        continue

                    district_key = (p_id, c_id, d_id)
                    district_data = {
                        "id": d_id,
                        "name": district.get("name", ""),
                        "fullName": district.get("fullName", ""),
                        "districtCode": district.get("districtCode", ""),
                        "pinyin": district.get("pinyin", ""),
                        "latitude": district.get("latitude"),
                        "longitude": district.get("longitude"),
                        "type": district.get("type"),
                        "isEleme": district.get("isEleme"),
                        "province_id": p_id,
                        "city_id": c_id,
                    }
                    self._district_map[district_key] = district_data
                    self._alsc_district_map[d_id] = district_data

        logger.debug(
            f"索引构建完成: {len(self._province_map)}个省份, {len(self._city_map)}个城市, {len(self._district_map)}个区县"
        )

    # 核心查询功能
    def get_region_info(self, province_id: int, city_id: int, district_id: int) -> RegionInfo:
        """
        根据省市区ID获取地区信息

        Args:
            province_id: 省份ID
            city_id: 城市ID
            district_id: 区县ID

        Returns:
            RegionInfo: 地区信息对象
        """
        result_data = {
            "province_name": "",
            "city_name": "",
            "district_name": "",
            "district_code": "",
            "province_full_name": "",
            "city_full_name": "",
            "district_full_name": "",
            "latitude": None,
            "longitude": None,
            "found": False,
        }

        # 查找省份
        province = self._province_map.get(province_id)
        if province:
            result_data["province_name"] = province["name"]
            result_data["province_full_name"] = province["fullName"]

        # 查找城市
        city_key = (province_id, city_id)
        city = self._city_map.get(city_key)
        if city:
            result_data["city_name"] = city["name"]
            result_data["city_full_name"] = city["fullName"]

        # 查找区县
        district_key = (province_id, city_id, district_id)
        district = self._district_map.get(district_key)
        if district:
            result_data["district_name"] = district["name"]
            result_data["district_full_name"] = district["name"]
            result_data["district_code"] = str(district["districtCode"])  # 确保是字符串
            result_data["latitude"] = district["latitude"]
            result_data["longitude"] = district["longitude"]
            result_data["found"] = True

        return RegionInfo.model_validate(result_data)

    def get_province_info(self, province_id: int) -> Optional[dict]:
        """获取省份信息"""
        return self._province_map.get(province_id)

    def get_city_info_by_id(self, province_id: int, city_id: int) -> Optional[dict]:
        """根据ID获取城市信息"""
        return self._city_map.get((province_id, city_id))

    def get_district_info(self, province_id: int, city_id: int, district_id: int) -> Optional[dict]:
        """获取区县信息"""
        return self._district_map.get((province_id, city_id, district_id))

    def get_gb_info_by_alsc_district_id(self, alsc_district_id: int) -> Optional[RegionInfo]:
        """
        根据饿了么区县ID获取国标信息

        Args:
            alsc_district_id: 饿了么区县ID

        Returns:
            RegionInfo对象，如果找不到返回None
        """
        if not alsc_district_id:
            return None

        # 直接通过 alsc_district_id 查找区县信息
        district_data = self._alsc_district_map.get(alsc_district_id)
        if not district_data:
            logger.warning(f"未找到alsc_district_id={alsc_district_id}对应的国标信息")
            return None

        # 获取对应的城市和省份信息
        province_id = district_data["province_id"]
        city_id = district_data["city_id"]

        city_key = (province_id, city_id)
        city_data = self._city_map.get(city_key)
        province_data = self._province_map.get(province_id, {})

        return RegionInfo(
            province_name=province_data.get("name", ""),
            city_name=city_data.get("name", "") if city_data else "",
            district_name=district_data.get("name", ""),
            district_code=str(district_data.get("districtCode", "")),
            province_full_name=province_data.get("fullName", ""),
            city_full_name=city_data.get("fullName", "") if city_data else "",
            district_full_name=district_data.get("fullName", ""),
            latitude=district_data.get("latitude"),
            longitude=district_data.get("longitude"),
            found=True,
        )

    def search_by_name(self, name: str, level: str = "all") -> List[SearchResult]:
        """
        根据名称搜索地区

        Args:
            name: 地区名称（支持模糊匹配）
            level: 搜索级别 ('province', 'city', 'district', 'all')

        Returns:
            List[SearchResult]: 匹配的地区列表
        """
        results = []

        if level in ["province", "all"]:
            for p_id, province in self._province_map.items():
                if name in province["name"] or name in province["fullName"]:
                    results.append(
                        SearchResult(
                            level="province",
                            id=p_id,
                            name=province["name"],
                            full_name=province["fullName"],
                            district_code=str(province["districtCode"]),
                        )
                    )

        if level in ["city", "all"]:
            for (p_id, c_id), city in self._city_map.items():
                if name in city["name"] or name in city["fullName"]:
                    results.append(
                        SearchResult(
                            level="city",
                            province_id=p_id,
                            city_id=c_id,
                            name=city["name"],
                            full_name=city["fullName"],
                            district_code=str(city["districtCode"]),
                        )
                    )

        if level in ["district", "all"]:
            for (p_id, c_id, d_id), district in self._district_map.items():
                if name in district["name"] or name in district["fullName"]:
                    results.append(
                        SearchResult(
                            level="district",
                            province_id=p_id,
                            city_id=c_id,
                            district_id=d_id,
                            name=district["name"],
                            full_name=district["fullName"],
                            district_code=str(district["districtCode"]),
                        )
                    )

        return results

    def get_statistics(self) -> Statistics:
        """获取数据统计信息"""
        return Statistics(
            province_count=len(self._province_map),
            city_count=len(self._city_map),
            district_count=len(self._district_map),
        )

    # 城市相关的便捷方法（向后兼容）
    def city_to_pinyin(self, city_name: str) -> Optional[str]:
        """
        将城市中文名转换为拼音 - 支持所有饿了么数据中的城市

        Args:
            city_name: 城市中文名

        Returns:
            城市拼音，如果找不到则返回 None
        """
        try:
            search_results = self.search_by_name(city_name, level="city")
            if search_results:
                # 取第一个匹配结果
                city_result = search_results[0]
                # 从 city 结果中获取拼音信息
                if city_result.province_id is not None and city_result.city_id is not None:
                    city_key = (city_result.province_id, city_result.city_id)
                    city_data = self._city_map.get(city_key)
                    if city_data and city_data.get("pinyin"):
                        return city_data["pinyin"]
        except Exception as e:
            logger.warning(f"搜索城市拼音失败: {city_name}, {e}")
        logger.debug(f"未找到城市 {city_name} 对应的拼音")
        return None

    def get_district_code_by_pinyin(self, city_pinyin: str) -> Optional[str]:
        """
        根据城市拼音获取行政区划代码

        Args:
            city_pinyin: 城市拼音（如 "shenzhen"）

        Returns:
            行政区划代码（如 "440300"），如果找不到返回 None
        """
        # 直接搜索拼音匹配的城市
        for city_data in self._city_map.values():
            if city_data.get("pinyin") == city_pinyin:
                district_code = city_data.get("districtCode")
                if district_code:
                    logger.debug(f"找到城市区划代码: {city_pinyin} -> {district_code}")
                    return str(district_code)

        logger.debug(f"未找到拼音 {city_pinyin} 对应的区划代码")
        return None

    def get_city_info(self, city_name: str) -> RegionSearchResult:
        """
        获取城市详细信息

        Args:
            city_name: 城市中文名

        Returns:
            城市信息搜索结果
        """
        try:
            # 从饿了么数据中搜索详细信息
            search_results = self.search_by_name(city_name, level="city")
            if not search_results:
                return RegionSearchResult(found=False, city_info=None, message=f"未在饿了么数据中找到城市: {city_name}")

            # 取第一个匹配结果
            city_result = search_results[0]

            # 检查必要的 ID 是否存在
            if city_result.province_id is None or city_result.city_id is None:
                return RegionSearchResult(found=False, city_info=None, message=f"城市 {city_name} 缺少必要的ID信息")

            # 获取城市拼音
            city_pinyin = self.city_to_pinyin(city_name) or ""

            # 获取省份信息
            province_data = self.get_province_info(city_result.province_id)
            province_name = province_data.get("name", "") if province_data else ""

            # 获取城市详细数据
            city_data = self.get_city_info_by_id(city_result.province_id, city_result.city_id)

            city_info = CityInfo(
                city_name=city_name,
                city_pinyin=city_pinyin,
                province_name=province_name,
                district_code=city_result.district_code,
                latitude=city_data.get("latitude") if city_data else None,
                longitude=city_data.get("longitude") if city_data else None,
            )

            return RegionSearchResult(found=True, city_info=city_info, message="成功获取城市信息")

        except Exception as e:
            logger.error(f"获取城市信息失败: {city_name}, {e}")
            return RegionSearchResult(found=False, city_info=None, message=f"获取城市信息时发生错误: {str(e)}")

    def get_all_cities(self) -> List[SupportedCity]:
        """
        获取所有城市列表（支持所有饿了么数据中的城市）

        Returns:
            所有城市列表
        """
        cities = []
        for (province_id, _), city_data in self._city_map.items():
            city_name = city_data.get("name", "")
            city_pinyin = city_data.get("pinyin", "")

            # 获取省份信息
            province_data = self.get_province_info(province_id)
            province_name = province_data.get("name", "") if province_data else ""

            if city_name and city_pinyin:  # 只包含有名称和拼音的城市
                cities.append(SupportedCity(city_name=city_name, city_pinyin=city_pinyin, province_name=province_name))

        # 按城市名排序
        cities.sort(key=lambda x: x.city_name)
        return cities

    def get_city_district_codes(self) -> dict[str, str]:
        """
        获取所有城市的区划代码映射

        Returns:
            城市拼音到区划代码的映射字典
        """
        codes = {}
        for city_data in self._city_map.values():
            city_pinyin = city_data.get("pinyin")
            district_code = city_data.get("districtCode")
            if city_pinyin and district_code:
                codes[city_pinyin] = str(district_code)
        return codes

    # 兼容性方法
    def is_supported_city(self, city_pinyin: str) -> bool:
        """
        检查城市拼音是否支持（所有饿了么数据中的城市都支持）

        Args:
            city_pinyin: 城市拼音

        Returns:
            是否支持该城市
        """
        return any(city.get("pinyin") == city_pinyin for city in self._city_map.values())

    def get_supported_cities(self) -> List[SupportedCity]:
        """获取支持的城市列表（兼容方法）"""
        return self.get_all_cities()


# 单例实例
region_gateway = RegionGateway()


# 兼容函数（向后兼容原来的 utils 模块）
@lru_cache(maxsize=10000)
def get_region_info(province_id: int, city_id: int, district_id: int) -> RegionInfo:
    """
    便捷函数：根据省市区ID获取地区信息

    Args:
        province_id: 省份ID
        city_id: 城市ID
        district_id: 区县ID

    Returns:
        RegionInfo: 地区信息对象
    """
    return region_gateway.get_region_info(province_id, city_id, district_id)


@lru_cache(maxsize=1000)
def search_by_name(name: str, level: str = "all") -> List[SearchResult]:
    """
    便捷函数：根据名称搜索地区

    Args:
        name: 地区名称
        level: 搜索级别

    Returns:
        List[SearchResult]: 搜索结果列表
    """
    return region_gateway.search_by_name(name, level)


def get_default_instance():
    """获取默认实例（兼容方法）"""
    return region_gateway
