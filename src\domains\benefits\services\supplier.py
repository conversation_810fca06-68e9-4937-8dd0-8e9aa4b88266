# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/supplier.py
# created: 2024-12-08 01:28:28
# updated: 2025-05-05 00:11:34

from typing import TYPE_CHECKING, Union

from loguru import logger
from tortoise.transactions import atomic

from src.domains.benefits.dto import PurchaseDTO, PurchaseUpdateFields
from src.domains.benefits.utils.type_cast import (
    eleme_union_coupon_package_to_sku_create_fields,
    eleme_union_purchase_to_purchase_create_fields,
)
from src.utils.eleme.dto import CouponPackagePurchaseDto
from src.utils.eleme.union_benefits import ElemeUnionBenefitsUtils

if TYPE_CHECKING:
    from src.repositories.benefits import PurchaseRepository, SkuRepository, SupplierRepository


class BenefitsSupplierService:

    def __init__(
        self,
        supplier_repo: "SupplierRepository",
        sku_repo: "SkuRepository",
        purchase_repo: "PurchaseRepository",
    ):
        self.supplier_repo = supplier_repo
        self.sku_repo = sku_repo
        self.purchase_repo = purchase_repo

    @atomic()
    async def sync_eleme_union_purchase(
        self,
        purchase: Union["CouponPackagePurchaseDto", str],
        supplier_id: int,
    ) -> PurchaseDTO:
        """从饿了么联盟同步采购单, 包含SKU处理和采购单同步"""
        if isinstance(purchase, str):
            purchase = CouponPackagePurchaseDto(purchase_id=purchase, purchase_name="purchase采购单", ext_info="")

        # lock purchase record
        purchase_record = await self.purchase_repo.select_for_update_by_purchase_id(purchase.purchase_id)

        # 检查采购单是否已存在，存在则更新，不存在则创建
        purchase_detail = ElemeUnionBenefitsUtils.get_purchase_detail(purchase.purchase_id)
        logger.info(
            "处理联盟权益采购单: {purchase_id}-{purchase_name}, detail: {purchase_detail}".format(
                purchase_id=purchase.purchase_id,
                purchase_name=purchase.purchase_name,
                purchase_detail=purchase_detail.model_dump(),
            )
        )

        # 遍历处理采购单的SKU清单
        for coupon_package in purchase_detail.coupon_packages:
            logger.info(f"处理联盟权益SKU详情: {coupon_package.model_dump_json()}")
            sku = await self.sku_repo.get_by_third_code(coupon_package.item_id)
            if not sku:
                # type cast, 将联盟权益SKU详情转换为联盟权益SKU创建字段
                sku_create_fields = eleme_union_coupon_package_to_sku_create_fields(
                    coupon_package,
                    purchase_detail,
                    supplier_id,
                )
                sku = await self.sku_repo.create_sku(sku_create_fields)
                logger.info(
                    "联盟权益SKU[{item_id}]不存在, 创建联盟权益SKU[{sku_id}], create_fields: {fields}".format(
                        item_id=coupon_package.item_id,
                        sku_id=sku.id,
                        fields=sku_create_fields.model_dump_json(),
                    )
                )

        #  type cast, 将联盟权益采购单转换为采购单创建字段
        purchase_fields = eleme_union_purchase_to_purchase_create_fields(
            purchase,
            purchase_detail,
            sku.id,
        )

        if purchase_record:
            update_fileds = PurchaseUpdateFields.model_validate(purchase_fields.model_dump())
            purchase_record = await self.purchase_repo.update_purchase(purchase_record, update_fileds)
            logger.info(
                "同步联盟采购单[{purchase_id}], 已存在更新采购单: {update_fileds}",
                purchase_id=purchase.purchase_id,
                update_fileds=update_fileds.model_dump(),
            )
        else:
            purchase_record = await self.purchase_repo.create_purchase(**purchase_fields.model_dump())
            logger.info(
                "同步联盟采购单[{purchase_id}], 不存在创建采购单: {purchase_fields}",
                purchase_id=purchase.purchase_id,
                purchase_fields=purchase_fields.model_dump(),
            )

        return await PurchaseDTO.from_tortoise_orm(purchase_record)
