# encoding: utf-8
# src/applications/passport/queries/user_query_service.py
# created: 2025-07-31 17:10:00

from typing import TYPE_CHECKING, List, Optional

from loguru import logger

from ..dto import TenantInfoDTO, UserInfoDTO
from ..errors import AppNotFoundError, UserNotFoundError

if TYPE_CHECKING:
    from src.domains.passport.entities import AppEntity, UserEntity
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class UserQueryService:
    """用户查询服务"""

    def __init__(
        self, user_repository: "UserRepository", tenant_repository: "TenantRepository", app_repository: "AppRepository"
    ):
        self.user_repository = user_repository
        self.tenant_repository = tenant_repository
        self.app_repository = app_repository

    async def get_user_tenants(self, user: "UserEntity", app: "AppEntity") -> List[TenantInfoDTO]:
        """获取用户在指定应用下的所有租户列表"""
        user_relations = await self.user_repository.get_user_relations(user, app)

        # 构建租户DTO列表
        tenant_dtos = []
        for relation in user_relations:
            if relation.tenant:
                tenant_dto = TenantInfoDTO(
                    tenant_id=relation.tenant.tenant_id,
                    tenant_name=relation.tenant.name,
                    app_id=app.app_id,
                    app_name=app.app_name,
                )
                tenant_dtos.append(tenant_dto)
        return tenant_dtos
