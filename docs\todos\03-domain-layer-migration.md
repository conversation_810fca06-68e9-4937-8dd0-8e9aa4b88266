# 第三阶段：领域层迁移任务

## 任务1：分析和准备领域模型

### 1.1 领域依赖分析
```python
# scripts/analyze_domain_dependencies.py
"""分析领域层的外部依赖，识别需要解耦的部分"""
import os
import ast
from collections import defaultdict

def analyze_domain_imports(domain_path):
    """分析领域层的导入依赖"""
    external_deps = defaultdict(list)
    
    for root, dirs, files in os.walk(domain_path):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r') as f:
                    try:
                        tree = ast.parse(f.read())
                        for node in ast.walk(tree):
                            if isinstance(node, ast.ImportFrom):
                                module = node.module or ''
                                # 识别外部依赖
                                if module.startswith(('apis', 'core', 'fastapi', 'starlette')):
                                    external_deps[file_path].append(module)
                    except:
                        pass
    
    return external_deps

# 分析每个领域
for domain in ['benefits', 'passport', 'delivery', 'customer', 'aiagent', 'activity']:
    domain_path = f'domains/{domain}'
    if os.path.exists(domain_path):
        deps = analyze_domain_imports(domain_path)
        print(f"\n{domain} 领域的外部依赖:")
        for file, imports in deps.items():
            if imports:
                print(f"  {file}: {', '.join(set(imports))}")
```

### 1.2 创建领域层基础结构
```python
# src/domains/base.py
"""领域层基础类"""
from abc import ABC, abstractmethod
from typing import Any, Optional, TypeVar, Generic
from datetime import datetime
from dataclasses import dataclass, field
from uuid import UUID, uuid4

T = TypeVar('T')

@dataclass
class Entity(ABC, Generic[T]):
    """实体基类"""
    id: T
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def __eq__(self, other):
        if not isinstance(other, Entity):
            return False
        return self.id == other.id

@dataclass(frozen=True)
class ValueObject(ABC):
    """值对象基类"""
    pass

class DomainEvent(ABC):
    """领域事件基类"""
    
    def __init__(self):
        self.event_id = str(uuid4())
        self.occurred_at = datetime.utcnow()
    
    @property
    @abstractmethod
    def event_name(self) -> str:
        """事件名称"""
        pass

class DomainService(ABC):
    """领域服务基类"""
    pass

class Specification(ABC, Generic[T]):
    """规约模式基类"""
    
    @abstractmethod
    def is_satisfied_by(self, entity: T) -> bool:
        """检查实体是否满足规约"""
        pass
    
    def and_(self, other: 'Specification[T]') -> 'Specification[T]':
        """与操作"""
        return AndSpecification(self, other)
    
    def or_(self, other: 'Specification[T]') -> 'Specification[T]':
        """或操作"""
        return OrSpecification(self, other)
    
    def not_(self) -> 'Specification[T]':
        """非操作"""
        return NotSpecification(self)
```

## 任务2：迁移权益(Benefits)领域

### 2.1 迁移领域服务
```python
# scripts/migrate_benefits_domain.py
"""迁移权益领域"""
import os
import shutil
from pathlib import Path

def migrate_benefits_domain():
    """迁移权益领域代码"""
    src_base = 'domains/benefits'
    dst_base = 'src/domains/benefits'
    
    # 创建目标目录结构
    os.makedirs(f'{dst_base}/entities', exist_ok=True)
    os.makedirs(f'{dst_base}/value_objects', exist_ok=True)
    os.makedirs(f'{dst_base}/services', exist_ok=True)
    os.makedirs(f'{dst_base}/events', exist_ok=True)
    os.makedirs(f'{dst_base}/specifications', exist_ok=True)
    
    # 迁移服务层
    if os.path.exists(f'{src_base}/services'):
        for file in os.listdir(f'{src_base}/services'):
            if file.endswith('.py'):
                shutil.copy2(
                    f'{src_base}/services/{file}',
                    f'{dst_base}/services/{file}'
                )
    
    # 迁移DTO
    if os.path.exists(f'{src_base}/dto.py'):
        shutil.copy2(f'{src_base}/dto.py', f'{dst_base}/dto.py')
    
    print("✅ 权益领域迁移完成")

# 执行迁移
migrate_benefits_domain()
```

### 2.2 重构领域模型
```python
# src/domains/benefits/entities/sku.py
"""SKU实体"""
from dataclasses import dataclass
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
from src.domains.base import Entity

@dataclass
class Sku(Entity[int]):
    """SKU实体"""
    # 基本信息
    name: str
    code: str
    category: str
    status: str = "active"
    
    # 价格信息
    price: Decimal
    cost: Decimal
    currency: str = "CNY"
    
    # 库存信息
    stock: int = 0
    reserved: int = 0
    
    # 规则配置
    min_purchase: int = 1
    max_purchase: Optional[int] = None
    
    # 时间信息
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    
    @property
    def available_stock(self) -> int:
        """可用库存"""
        return self.stock - self.reserved
    
    def can_purchase(self, quantity: int) -> bool:
        """检查是否可以购买"""
        if self.status != "active":
            return False
        
        if quantity < self.min_purchase:
            return False
        
        if self.max_purchase and quantity > self.max_purchase:
            return False
        
        if quantity > self.available_stock:
            return False
        
        # 检查有效期
        now = datetime.utcnow()
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_to and now > self.valid_to:
            return False
        
        return True
    
    def reserve_stock(self, quantity: int) -> None:
        """预留库存"""
        if quantity > self.available_stock:
            raise ValueError(f"Insufficient stock: {self.available_stock} < {quantity}")
        self.reserved += quantity
    
    def release_stock(self, quantity: int) -> None:
        """释放库存"""
        if quantity > self.reserved:
            raise ValueError(f"Cannot release more than reserved: {self.reserved} < {quantity}")
        self.reserved -= quantity
    
    def deduct_stock(self, quantity: int) -> None:
        """扣减库存"""
        if quantity > self.stock:
            raise ValueError(f"Insufficient stock: {self.stock} < {quantity}")
        self.stock -= quantity
        if quantity <= self.reserved:
            self.reserved -= quantity
```

### 2.3 创建领域服务
```python
# src/domains/benefits/services/sku_service.py
"""SKU领域服务"""
from typing import List, Optional
from decimal import Decimal
from src.domains.base import DomainService
from src.domains.benefits.entities.sku import Sku
from src.domains.benefits.events import SkuCreated, SkuStockChanged
from src.domains.benefits.specifications import ActiveSkuSpecification

class SkuDomainService(DomainService):
    """SKU领域服务"""
    
    def calculate_discount(
        self,
        sku: Sku,
        quantity: int,
        discount_rules: List[dict]
    ) -> Decimal:
        """计算折扣"""
        base_amount = sku.price * quantity
        discount_amount = Decimal('0')
        
        for rule in discount_rules:
            if self._is_rule_applicable(sku, quantity, rule):
                if rule['type'] == 'percentage':
                    discount_amount += base_amount * Decimal(str(rule['value'])) / 100
                elif rule['type'] == 'fixed':
                    discount_amount += Decimal(str(rule['value']))
        
        return min(discount_amount, base_amount)  # 折扣不能超过原价
    
    def _is_rule_applicable(self, sku: Sku, quantity: int, rule: dict) -> bool:
        """检查规则是否适用"""
        # 检查SKU类别
        if 'categories' in rule and sku.category not in rule['categories']:
            return False
        
        # 检查最小数量
        if 'min_quantity' in rule and quantity < rule['min_quantity']:
            return False
        
        # 检查时间范围
        if 'valid_from' in rule or 'valid_to' in rule:
            now = datetime.utcnow()
            if rule.get('valid_from') and now < rule['valid_from']:
                return False
            if rule.get('valid_to') and now > rule['valid_to']:
                return False
        
        return True
    
    def batch_update_prices(
        self,
        skus: List[Sku],
        price_adjustment: Decimal,
        adjustment_type: str = 'percentage'
    ) -> List[Sku]:
        """批量更新价格"""
        updated_skus = []
        
        for sku in skus:
            if adjustment_type == 'percentage':
                new_price = sku.price * (1 + price_adjustment / 100)
            else:
                new_price = sku.price + price_adjustment
            
            # 确保价格不低于成本
            if new_price >= sku.cost:
                sku.price = new_price
                updated_skus.append(sku)
        
        return updated_skus
```

### 2.4 创建领域事件
```python
# src/domains/benefits/events.py
"""权益领域事件"""
from dataclasses import dataclass
from typing import Optional
from decimal import Decimal
from src.domains.base import DomainEvent

@dataclass
class SkuCreated(DomainEvent):
    """SKU创建事件"""
    sku_id: int
    name: str
    code: str
    category: str
    price: Decimal
    
    @property
    def event_name(self) -> str:
        return "sku.created"

@dataclass
class SkuStockChanged(DomainEvent):
    """SKU库存变更事件"""
    sku_id: int
    previous_stock: int
    current_stock: int
    change_type: str  # 'reserve', 'release', 'deduct', 'replenish'
    change_quantity: int
    
    @property
    def event_name(self) -> str:
        return "sku.stock_changed"

@dataclass
class SkuPriceChanged(DomainEvent):
    """SKU价格变更事件"""
    sku_id: int
    previous_price: Decimal
    current_price: Decimal
    
    @property
    def event_name(self) -> str:
        return "sku.price_changed"
```

### 2.5 创建规约
```python
# src/domains/benefits/specifications.py
"""权益领域规约"""
from src.domains.base import Specification
from src.domains.benefits.entities.sku import Sku

class ActiveSkuSpecification(Specification[Sku]):
    """活跃SKU规约"""
    
    def is_satisfied_by(self, sku: Sku) -> bool:
        return sku.status == "active"

class InStockSpecification(Specification[Sku]):
    """有库存规约"""
    
    def __init__(self, min_stock: int = 1):
        self.min_stock = min_stock
    
    def is_satisfied_by(self, sku: Sku) -> bool:
        return sku.available_stock >= self.min_stock

class PriceRangeSpecification(Specification[Sku]):
    """价格范围规约"""
    
    def __init__(self, min_price: Optional[Decimal] = None, max_price: Optional[Decimal] = None):
        self.min_price = min_price
        self.max_price = max_price
    
    def is_satisfied_by(self, sku: Sku) -> bool:
        if self.min_price and sku.price < self.min_price:
            return False
        if self.max_price and sku.price > self.max_price:
            return False
        return True
```

## 任务3：迁移其他领域

### 3.1 创建迁移脚本模板
```python
# scripts/migrate_domain_template.py
"""领域迁移模板"""
import os
import re
from typing import List, Dict

class DomainMigrator:
    """领域迁移器"""
    
    def __init__(self, domain_name: str):
        self.domain_name = domain_name
        self.src_path = f'domains/{domain_name}'
        self.dst_path = f'src/domains/{domain_name}'
    
    def create_structure(self):
        """创建领域目录结构"""
        subdirs = [
            'entities',
            'value_objects',
            'services',
            'events',
            'specifications',
            'exceptions'
        ]
        
        for subdir in subdirs:
            os.makedirs(f'{self.dst_path}/{subdir}', exist_ok=True)
    
    def migrate_services(self):
        """迁移服务层"""
        src_services = f'{self.src_path}/services'
        if os.path.exists(src_services):
            for file in os.listdir(src_services):
                if file.endswith('.py'):
                    self._refactor_service_file(
                        f'{src_services}/{file}',
                        f'{self.dst_path}/services/{file}'
                    )
    
    def _refactor_service_file(self, src_file: str, dst_file: str):
        """重构服务文件，移除外部依赖"""
        with open(src_file, 'r') as f:
            content = f.read()
        
        # 移除 FastAPI 相关导入
        content = re.sub(r'from fastapi import .*\n', '', content)
        content = re.sub(r'from apis.* import .*\n', '', content)
        
        # 替换导入路径
        content = re.sub(
            r'from domains\.' + self.domain_name,
            f'from src.domains.{self.domain_name}',
            content
        )
        
        with open(dst_file, 'w') as f:
            f.write(content)
    
    def migrate(self):
        """执行迁移"""
        print(f"开始迁移 {self.domain_name} 领域...")
        self.create_structure()
        self.migrate_services()
        print(f"✅ {self.domain_name} 领域迁移完成")

# 迁移所有领域
domains = ['passport', 'delivery', 'customer', 'aiagent', 'activity']
for domain in domains:
    if os.path.exists(f'domains/{domain}'):
        migrator = DomainMigrator(domain)
        migrator.migrate()
```

## 任务4：领域层测试

### 4.1 创建领域测试基础
```python
# tests/unit/domains/conftest.py
"""领域层测试配置"""
import pytest
from decimal import Decimal
from datetime import datetime
from src.domains.benefits.entities.sku import Sku

@pytest.fixture
def sample_sku():
    """示例SKU"""
    return Sku(
        id=1,
        name="Test SKU",
        code="TEST001",
        category="test",
        price=Decimal("100.00"),
        cost=Decimal("60.00"),
        stock=100,
        reserved=0
    )

@pytest.fixture
def expired_sku():
    """过期SKU"""
    return Sku(
        id=2,
        name="Expired SKU",
        code="EXP001",
        category="test",
        price=Decimal("50.00"),
        cost=Decimal("30.00"),
        stock=50,
        valid_to=datetime(2020, 1, 1)
    )
```

### 4.2 创建实体测试
```python
# tests/unit/domains/benefits/test_sku_entity.py
"""SKU实体测试"""
import pytest
from decimal import Decimal
from src.domains.benefits.entities.sku import Sku

class TestSkuEntity:
    """SKU实体测试"""
    
    def test_available_stock_calculation(self, sample_sku):
        """测试可用库存计算"""
        assert sample_sku.available_stock == 100
        
        sample_sku.reserved = 20
        assert sample_sku.available_stock == 80
    
    def test_can_purchase_with_valid_conditions(self, sample_sku):
        """测试正常购买条件"""
        assert sample_sku.can_purchase(1) is True
        assert sample_sku.can_purchase(50) is True
    
    def test_cannot_purchase_insufficient_stock(self, sample_sku):
        """测试库存不足"""
        assert sample_sku.can_purchase(101) is False
    
    def test_cannot_purchase_expired_sku(self, expired_sku):
        """测试过期SKU"""
        assert expired_sku.can_purchase(1) is False
    
    def test_reserve_stock(self, sample_sku):
        """测试预留库存"""
        sample_sku.reserve_stock(30)
        assert sample_sku.reserved == 30
        assert sample_sku.available_stock == 70
    
    def test_reserve_stock_insufficient(self, sample_sku):
        """测试预留库存不足"""
        with pytest.raises(ValueError):
            sample_sku.reserve_stock(101)
```

## 验证步骤

### 1. 检查领域层纯净性
```bash
# 检查是否还有外部依赖
grep -r "from fastapi" src/domains/
grep -r "from apis" src/domains/
grep -r "from core" src/domains/
```

### 2. 运行领域层测试
```bash
poetry run pytest tests/unit/domains -v
```

### 3. 检查测试覆盖率
```bash
poetry run pytest tests/unit/domains --cov=src/domains --cov-report=html
```

## 完成标准

- [ ] 所有领域代码迁移到新结构
- [ ] 领域层不依赖外部协议（HTTP、消息队列等）
- [ ] 实体、值对象、领域服务分离清晰
- [ ] 领域事件定义完整
- [ ] 规约模式实现
- [ ] 单元测试覆盖率 > 80%
- [ ] 领域层可独立运行和测试