# encoding: utf-8
# docs/settings-cleanup-plan.md
# created: 2025-08-18 17:00:00

# Settings 配置系统清理计划

## 需要清理的文件

### 1. 可以直接删除的文件（未被引用）
- [ ] `src/interfaces/consumers/main.py` - 旧的消费者入口，被 `deploys/consumer/main.py` 替代

### 2. 需要迁移后删除的文件
- [ ] `src/infrastructures/config.py` - 旧的配置系统，被新的 settings 系统替代

## 还在使用旧 config.py 的文件

需要迁移以下文件：

1. **HTTP 接口相关**
   - `src/interfaces/http/baseapi/base/routers/oss.py`
   - `src/domains/passport/services/authorization.py`

2. **消费者相关**  
   - `src/interfaces/consumers/delivery/dingtalk_orders_notify.py`
   - `src/interfaces/consumers/delivery/orders_notify.py`

3. **工具类**
   - `src/utils/eleme/channels.py`
   - `src/utils/eleme/union_benefits.py`
   - `src/utils/oss.py`

4. **网关相关**
   - `src/infrastructures/gateways/bifrost/schemas.py`

5. **已注释（可忽略）**
   - `src/infrastructures/rabbitmq/command.py` - 已注释掉的导入

## 迁移策略

### 方案 1：创建兼容层（推荐）
创建一个兼容的 config 对象，让旧代码继续工作：

```python
# src/infrastructures/config_compat.py
# 兼容层，用于平滑迁移

from .settings import get_current_config

# 提供兼容的 config 对象
config = get_current_config()

# 兼容的 load_config 函数
def load_config(path: str = ".env.toml"):
    return config
```

### 方案 2：直接修改引用
直接修改所有引用旧 config 的代码，使用新的配置系统。

## 执行步骤

1. **创建兼容层**
2. **修改导入路径**
3. **测试验证**
4. **删除旧文件**
5. **最终清理兼容层**

## 风险评估

- **低风险**：删除未引用的文件
- **中风险**：修改正在使用的配置引用
- **需测试**：确保所有服务正常启动和运行

## 回滚方案

保留旧文件的备份，如果出现问题可以快速恢复：
```bash
# 备份
cp src/infrastructures/config.py src/infrastructures/config.py.bak

# 恢复
cp src/infrastructures/config.py.bak src/infrastructures/config.py
```