# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/purchase.py
# created: 2025-03-31 20:32:52
# updated: 2025-04-13 21:48:27


from datetime import datetime

from src.databases.models.benefits import BenefitsSKUPurchase
from src.domains.benefits.dto import PurchaseUpdateFields


class PurchaseRepository:

    @classmethod
    async def update_purchase(
        cls,
        purchase: BenefitsSKUPurchase,
        update_fileds: PurchaseUpdateFields,
    ) -> BenefitsSKUPurchase:
        for key, value in update_fileds.model_dump().items():
            if value is not None:
                setattr(purchase, key, value)
        await purchase.save()
        return purchase

    @classmethod
    async def create_purchase(
        cls,
        purchase_id: str,
        purchase_name: str,
        sku_id: int,
        purchase_stock: int,
        remain_stock: int,
        unit_price: int,
        total_price: int,
        sales_start_time: datetime,
        sales_end_time: datetime,
        detail: dict,
    ) -> BenefitsSKUPurchase:
        purchase, created = await BenefitsSKUPurchase.update_or_create(
            purchase_id=purchase_id,
            defaults={
                "purchase_name": purchase_name,
                "sku_id": sku_id,
                "purchase_stock": purchase_stock,
                "remain_stock": remain_stock,
                "unit_price": unit_price,
                "total_price": total_price,
                "sales_start_time": sales_start_time,
                "sales_end_time": sales_end_time,
                "detail": detail,
            },
        )
        return purchase

    @classmethod
    async def get_by_purchase_id(cls, purchase_id: str) -> BenefitsSKUPurchase | None:
        return await BenefitsSKUPurchase.get_or_none(purchase_id=purchase_id)

    @classmethod
    async def select_for_update_by_purchase_id(cls, purchase_id: str) -> BenefitsSKUPurchase | None:
        """lock the purchase for update"""
        return await BenefitsSKUPurchase.filter(purchase_id=purchase_id).select_for_update().first()

    @classmethod
    async def gets_available_by_sku(cls, sku_id: int) -> list[BenefitsSKUPurchase]:
        purchases = (
            await BenefitsSKUPurchase.filter(
                sku_id=sku_id,
                sales_start_time__lte=datetime.now(),
                sales_end_time__gte=datetime.now(),
                remain_stock__gt=1,  # 至少剩余1张, 安全库存
            )
            .order_by("-remain_stock")
            .all()
        )
        return purchases

    @classmethod
    async def calculate_total_remain_stock_by_sku(cls, sku_id: int) -> int:
        """计算SKU所有有效采购单的剩余库存总和"""
        purchases = await BenefitsSKUPurchase.filter(
            sku_id=sku_id,
            sales_start_time__lte=datetime.now(),
            sales_end_time__gte=datetime.now(),
        ).all()

        total_stock = sum(purchase.remain_stock for purchase in purchases)
        return total_stock
