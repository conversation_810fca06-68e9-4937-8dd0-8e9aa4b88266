# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/aiagent/services/auto_benefits/auto_benefits_factor.py
# created: 2025-04-18 00:41:35
# updated: 2025-04-22 09:17:26


from abc import ABC, abstractmethod
from typing import Optional, Type

from src.domains.benefits.dto import BenefitsProductDTO


class AutoBenefitStrategy(ABC):

    @abstractmethod
    async def apply(
        self, phone: str, union_id: str, agent_id: str, agent_user_id: str, uid: str
    ) -> Optional[BenefitsProductDTO]:
        pass


class AutoBenefitsFactor:

    _strategies = {}

    @classmethod
    def get_strategy(cls, agent_id: str, type: str) -> AutoBenefitStrategy:
        return cls._strategies.get(f"{agent_id}.{type}", None)

    @classmethod
    def register_strategy(cls, agent_id: str, type: str, strategy: Type[AutoBenefitStrategy]):
        cls._strategies[f"{agent_id}.{type}"] = strategy()
