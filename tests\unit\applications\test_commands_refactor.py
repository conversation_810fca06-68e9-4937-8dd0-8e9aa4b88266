# encoding: utf-8
# tests/unit/applications/test_commands_refactor.py  
# created: 2025-08-19 13:30:00

"""
重构后的命令服务单元测试

这个测试文件验证了本次重构的关键改动：
1. OrderNotifyCommandService 从 common 层移至 openapi 层
2. OrderSyncCommandService 在 common 层处理订单同步
3. 异常类从 exceptions.py 迁移到 errors.py
"""

import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest


class TestOrderNotifyCommandServiceRefactor:
    """测试 OrderNotifyCommandService 重构后的功能"""

    def test_service_location(self):
        """验证服务已从 common 层移至 openapi 层"""
        # 验证可以从正确的位置导入
        from src.applications.openapi.commands.delivery import OrderNotifyCommandService
        
        assert OrderNotifyCommandService is not None
        assert OrderNotifyCommandService.__module__ == "src.applications.openapi.commands.delivery.order_notify"

    def test_channel_determination_logic(self):
        """测试订单渠道判断逻辑"""
        from src.applications.openapi.commands.delivery.order_notify import OrderChannel
        
        # 创建服务实例（使用Mock依赖）
        mock_config = Mock()
        mock_customer_query = Mock()
        mock_customer_benefit = Mock()
        mock_eleme_gateway = Mock()
        
        from src.applications.openapi.commands.delivery import OrderNotifyCommandService
        
        service = OrderNotifyCommandService(
            config=mock_config,
            customer_query_service=mock_customer_query,
            customer_benefit_service=mock_customer_benefit,
            eleme_union_delivery_gateway=mock_eleme_gateway,
        )
        
        # 创建测试订单
        mock_order = Mock()
        
        # 测试权益订单（无广告位名称）
        mock_order.ad_zone_name = None
        channel = service._determine_order_channel(mock_order)
        assert channel == OrderChannel.BENEFITS
        
        # 测试钉钉订单
        mock_order.ad_zone_name = "dingtalk_zone"
        channel = service._determine_order_channel(mock_order)
        assert channel == OrderChannel.DINGTALK
        
        # 测试客户订单
        mock_order.ad_zone_name = "customer_zone"
        channel = service._determine_order_channel(mock_order)
        assert channel == OrderChannel.CUSTOMER

    def test_base_order_data_building(self):
        """测试基础订单数据构建"""
        from src.applications.openapi.commands.delivery import OrderNotifyCommandService
        
        # 创建服务实例
        service = OrderNotifyCommandService(
            config=Mock(),
            customer_query_service=Mock(),
            customer_benefit_service=Mock(),
            eleme_union_delivery_gateway=Mock(),
        )
        
        # 创建测试订单
        mock_order = Mock()
        mock_order.biz_order_id = "BIZ123"
        mock_order.order_state = 1
        mock_order.category_name = "餐饮"
        mock_order.shop_name = "测试店铺"
        mock_order.pay_amount = 100.0
        mock_order.settle_amount = 90.0
        mock_order.tk_create_time = datetime(2025, 8, 19, 10, 0, 0)
        mock_order.pay_time = datetime(2025, 8, 19, 10, 30, 0)
        mock_order.receive_time = None
        
        # 构建数据
        data = service._build_base_order_data(mock_order)
        
        # 验证结果
        assert data["order_id"] == "BIZ123"
        assert data["order_state"] == "1"
        assert data["category_name"] == "餐饮"
        assert data["shop_name"] == "测试店铺"
        assert data["pay_amount"] == "100.0"
        assert data["settle_amount"] == "90.0"
        assert data["tk_create_time"] == "2025-08-19 10:00:00"
        assert data["pay_time"] == "2025-08-19 10:30:00"
        assert data["receive_time"] == ""


class TestOrderSyncCommandServiceRefactor:
    """测试 OrderSyncCommandService 重构后的功能"""

    def test_service_location(self):
        """验证服务在 common 层"""
        from src.applications.common.commands.delivery import OrderSyncCommandService
        
        assert OrderSyncCommandService is not None
        assert OrderSyncCommandService.__module__ == "src.applications.common.commands.delivery.order_sync"

    def test_parse_eleme_order_logic(self):
        """测试解析饿了么订单逻辑"""
        from src.applications.common.commands.delivery import OrderSyncCommandService
        
        # 创建服务实例
        service = OrderSyncCommandService(
            delivery_order_repository=Mock(),
            eleme_order_repository=Mock(),
            eleme_union_delivery_gateway=Mock(),
            bifrost_gateway=Mock(),
        )
        
        # 创建测试数据
        order_data = {
            "alscOrderModelInfo": {
                "orderInfo": {
                    "buyerUserId": 12345,
                    "totalAmount": 100.0,
                    "merchantSubsidyAmount": 5.0,
                    "agentSubsidyAmount": 3.0,
                    "realAmount": 92.0,
                    "bizScene": "ELEME_RETAIL",
                    "extInfo": {
                        "buyerBindPhone": "***********",
                        "alscAddressInfo": json.dumps({
                            "address": "测试地址",
                            "latitude": 31.23,
                            "longitude": 121.47,
                            "city": "上海",
                            "district": "浦东新区",
                        }),
                        "userTags": "VIP",
                    },
                },
                "paymentOrderInfoList": [
                    {
                        "paymentAmount": 92.0,
                        "extInfo": {
                            "tradeType": "ALIPAY",
                            "payTime": "2025-08-19 10:30:00",
                            "ppPayDetail": json.dumps([
                                {
                                    "payeeAccountNo": "payee123",
                                    "payerAccountNo": "payer456",
                                }
                            ]),
                        },
                    }
                ],
                "subOrderInfoList": [
                    {
                        "subject": "测试商品",
                        "realAmount": 92.0,
                        "price": 100.0,
                        "amount": 100.0,
                        "quantity": 1,
                        "unit": "份",
                        "subsidyAmount": 8.0,
                        "merchantSubsidyAmount": 5.0,
                        "agentSubsidyAmount": 3.0,
                    }
                ],
            }
        }
        
        # 解析订单
        result = service._parse_eleme_order("ORDER123", order_data)
        
        # 验证结果
        assert result.order_id == "ORDER123"
        assert result.buyer_uid == "12345"
        assert result.buyer_phone == "***********"
        assert result.total_amount == 100.0
        assert result.payment_amount == 92.0
        assert result.trade_type == "ALIPAY"
        assert result.payee_account_no == "payee123"
        assert result.payer_account_no == "payer456"
        assert len(result.subjects) == 1
        assert result.subjects[0].subject_name == "测试商品"


class TestExceptionMigration:
    """测试异常类迁移"""

    def test_exceptions_in_errors_file(self):
        """验证异常类已迁移到 errors.py"""
        from src.applications.openapi.errors import (
            DeliveryCommandError,
            OrderSyncError,
            OrderNotifyError,
            DeliveryValidationError,
            DeliveryNetworkError,
            RetryableError,
            NonRetryableError,
        )
        
        # 验证所有异常类都可以正常导入
        assert DeliveryCommandError is not None
        assert OrderSyncError is not None
        assert OrderNotifyError is not None
        assert DeliveryValidationError is not None
        assert DeliveryNetworkError is not None
        assert RetryableError is not None
        assert NonRetryableError is not None
        
        # 验证继承关系
        assert issubclass(OrderSyncError, DeliveryCommandError)
        assert issubclass(OrderNotifyError, DeliveryCommandError)
        assert issubclass(RetryableError, DeliveryCommandError)
        assert issubclass(NonRetryableError, DeliveryCommandError)

    def test_exception_messages(self):
        """测试异常消息"""
        from src.applications.openapi.errors import (
            OrderSyncError,
            OrderNotifyError,
            DeliveryNetworkError,
        )
        
        # 测试默认消息
        sync_error = OrderSyncError()
        assert str(sync_error) == "订单同步失败"
        assert sync_error.code == 400
        
        notify_error = OrderNotifyError()
        assert str(notify_error) == "订单通知失败"
        assert notify_error.code == 400
        
        network_error = DeliveryNetworkError()
        assert str(network_error) == "网络连接失败"
        assert network_error.code == 503
        
        # 测试自定义消息
        custom_error = OrderSyncError("自定义错误消息")
        assert str(custom_error) == "自定义错误消息"


class TestConsumerRefactoring:
    """测试消费者重构"""

    def test_orders_notify_consumer_uses_service(self):
        """验证 UnionOrderNotifyConsumer 使用应用服务"""
        from src.interfaces.consumers.delivery import UnionOrderNotifyConsumer
        
        # 创建消费者实例
        mock_pool = Mock()
        mock_producer = Mock()
        mock_notify_service = AsyncMock()
        
        consumer = UnionOrderNotifyConsumer(
            conn_pool=mock_pool,
            producer=mock_producer,
            order_notify_service=mock_notify_service,
        )
        
        # 验证服务被正确注入
        assert consumer.order_notify_service is mock_notify_service

    def test_sync_eleme_order_consumer_uses_service(self):
        """验证 SyncElemeOrderConsumer 使用应用服务"""
        from src.interfaces.consumers.delivery import SyncElemeOrderConsumer
        
        # 创建消费者实例
        mock_pool = Mock()
        mock_producer = Mock()
        mock_sync_service = AsyncMock()
        
        consumer = SyncElemeOrderConsumer(
            conn_pool=mock_pool,
            producer=mock_producer,
            order_sync_service=mock_sync_service,
        )
        
        # 验证服务被正确注入
        assert consumer.order_sync_service is mock_sync_service

    def test_union_order_sync_consumer_uses_service(self):
        """验证 UnionOrderSyncConsumer 使用应用服务"""
        from src.interfaces.consumers.delivery import UnionOrderSyncConsumer
        
        # 创建消费者实例
        mock_pool = Mock()
        mock_producer = Mock()
        mock_sync_service = AsyncMock()
        
        consumer = UnionOrderSyncConsumer(
            conn_pool=mock_pool,
            producer=mock_producer,
            order_sync_service=mock_sync_service,
        )
        
        # 验证服务被正确注入
        assert consumer.order_sync_service is mock_sync_service


class TestContainerConfiguration:
    """测试容器配置更新"""

    def test_openapi_order_notify_service_registration(self):
        """验证 OrderNotifyCommandService 在 openapi 容器中注册"""
        from src.containers.applications import Applications
        
        # 验证服务在容器中注册
        assert hasattr(Applications, 'openapi_order_notify_command_service')

    def test_common_order_sync_service_registration(self):
        """验证 OrderSyncCommandService 在 common 容器中注册"""
        from src.containers.applications import Applications
        
        # 验证服务在容器中注册
        assert hasattr(Applications, 'common_order_sync_command_service')