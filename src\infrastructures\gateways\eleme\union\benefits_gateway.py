# encoding: utf-8
# src/infrastructures/gateways/eleme/union/benefits_gateway.py
# created: 2025-08-03 15:08:42

from typing import Union

from loguru import logger
from redis.asyncio import Redis

from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_detail_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_create_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_refund_request import *

from .dto import (
    CouponPackagePurchaseDetailDto,
    CouponPackagePurchaseDetailResponse,
    CouponPackagePurchaseListDto,
    CouponPackagePurchaseListResponse,
    CouponPackagePurchaseTicketCreateResultDto,
    CouponPackagePurchaseTicketCreateResultResponse,
    CouponPackagePurchaseTicketDetailResultDto,
    CouponPackagePurchaseTicketDetailResultResponse,
)
from .settings import ElemeUnionSettings


class ElemeUnionBenefitsGateway:

    def __init__(self, config: Union[ElemeUnionSettings, dict], redis: Redis):
        if isinstance(config, dict):
            config = ElemeUnionSettings(**config)

        self.config = config
        self.redis = redis

    def _get_client(self) -> Defaultability:
        client = TopApiClient(
            appkey=self.config.app_key,
            app_sercet=self.config.app_secret,
            top_gateway_url=self.config.top_gateway_url,
            verify_ssl=True,
        )
        ability = Defaultability(client=client)
        return ability

    async def get_purchase_list(self, page: int = 1, page_size: int = 20) -> CouponPackagePurchaseListDto:
        """获取权益购买订单列表"""
        client = self._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseQueryRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseQueryCouponPackagePurchaseQueryRequest(
                page_number=page, page_size=page_size
            )
        )
        response = CouponPackagePurchaseListResponse.model_validate(
            client.alibaba_alsc_union_eleme_couponpackage_purchase_query(request)
        )
        return response.data

    def get_purchase_detail(self, purchase_id: str) -> CouponPackagePurchaseDetailDto:
        client = self._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseDetailGetRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseDetailGetCouponPackagePurchaseDetailRequest(
                purchase_id=purchase_id
            )
        )
        try:
            response = CouponPackagePurchaseDetailResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_detail_get(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟采购单详情获取失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    def create_ticket(
        self, purchase_id: str, item_id: str, mobile: str, outer_order_id: str, outer_item_id: str
    ) -> CouponPackagePurchaseTicketCreateResultDto:
        client = self._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateCouponPackagePurchaseTicketCreateRequest(
                purchase_id=purchase_id,
                item_id=item_id,
                mobile=mobile,
                outer_order_id=outer_order_id,
                outer_item_id=outer_item_id,
            )
        )
        try:
            response = CouponPackagePurchaseTicketCreateResultResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_create(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟卡券充值失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    def get_ticket_detail(
        self, ticket_id: str, purchase_id: str, item_id: str, outer_order_id: str
    ) -> CouponPackagePurchaseTicketDetailResultDto:
        client = self._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetCouponPackagePurchaseTicketDetailRequest(
                ticket_id=ticket_id,
                purchase_id=purchase_id,
                item_id=item_id,
                outer_order_id=outer_order_id,
            )
        )
        try:
            response = CouponPackagePurchaseTicketDetailResultResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_get(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟卡券详情获取失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    def refund_ticket(self, ticket_id: str, outer_order_id: str, purchase_id: str, item_id: str, mobile: str) -> bool:
        client = self._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketRefundRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketRefundCouponPackagePurchaseTicketRefundRequest(
                ticket_id=ticket_id,
                outer_order_id=outer_order_id,
                purchase_id=purchase_id,
                item_id=item_id,
                mobile=mobile,
            )
        )
        try:
            client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_refund(request)
            return True
        except Exception as e:
            logger.error(f"饿了么联盟卡券退款失败[{ticket_id}], 异常错误: {str(e)}")
            raise e
