# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/tools/benefits.py
# created: 2025-04-14 11:54:40
# updated: 2025-04-14 13:48:22

from typing import TYPE_CHECKING
from uuid import uuid4

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from loguru import logger

from src.interfaces.http.baseapi import Container
from src.interfaces.http.baseapi.base.authorization import base_api_authentication

from ...schemas import ChargeBenefitsPayload, ProductOrderResponse

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsProductService
    from src.domains.passport.entities import UserEntity
    from src.repositories.passport import AppRepository

router = APIRouter(tags=["tools"])


@router.post(
    "/benefits/{product_code}/manual_charge",
    response_model=ProductOrderResponse,
    description="手动充值",
)
@inject
async def charge_benefits(
    product_code: str,
    payload: ChargeBenefitsPayload,
    product_service: "BenefitsProductService" = Depends(Provide[Container.domains.benefits_product_service]),
    app_repo: "AppRepository" = Depends(Provide[Container.repositories.passport_app_repository]),
    mis_user: "UserEntity" = Depends(base_api_authentication),
) -> None:
    logger.info(
        f"Manual charge benefits, benefit_code: {product_code}, phone: {payload.phone}, "
        f"op_user: {mis_user.nickname}-{mis_user.uid}"
    )
    app = await app_repo.get_by_appid("HICASPIAN_MIS")
    if not app:
        raise ValueError("App HICASPIAN_MIS not found")
    order = await product_service.charge_product(
        product_code,
        payload.phone,
        out_order_id=uuid4().hex,
        sale_price=0,
        source_identify="",
        notify_url="",
        from_app=app.id,  # 传递 app ID 而不是整个对象
        op_uid=mis_user.uid,  # type: ignore
        op_description=payload.description,
    )
    logger.info(f"Manual charge benefits, product_order[{order.id}] success.")  # type: ignore
    return ProductOrderResponse(data=order)  # type: ignore
