# encoding: utf-8
# src/infrastructures/logger/formatters.py
# created: 2025-08-14 14:55:00

import json
from typing import Any, Callable, Dict

from .settings import FormatterConfig


def text_formatter(record: Dict[str, Any], config: FormatterConfig) -> str:
    """文本格式化器

    Args:
        record: loguru 日志记录
        config: 格式化器配置

    Returns:
        格式化后的日志字符串
    """
    # 基本信息
    level = f"{record['level'].name:<8}"
    time_str = record["time"].strftime(config.time_format)[:-3]  # 去掉微秒的后3位
    message = str(record["message"])

    # 文件位置
    file_line = f"{record['file'].name}:{record['line']}"

    # 额外字段
    extra_str = ""
    if config.include_context and record.get("extra"):
        extra_parts = [f"{k}[{v}]" for k, v in record["extra"].items()]
        if extra_parts:
            extra_str = " " + " ".join(extra_parts)

    # 异常信息
    exception_str = ""
    if config.include_exception and record.get("exception"):
        exception = record["exception"]
        if exception:
            exception_str = f"\n{exception}"

    # 组装最终格式
    if config.template:
        # 使用自定义模板
        return config.template.format(
            level=level,
            time=time_str,
            message=message,
            file=file_line,
            extra=extra_str,
            exception=exception_str,
            **record,
        )
    else:
        # 默认格式
        return f"{level} [{time_str}] {message}{extra_str} file[{file_line}]{exception_str}"


def json_formatter(record: Dict[str, Any], config: FormatterConfig) -> str:
    """JSON格式化器

    Args:
        record: loguru 日志记录
        config: 格式化器配置

    Returns:
        JSON格式的日志字符串
    """
    data = {
        "time": record["time"].strftime(config.time_format),
        "level": record["level"].name,
        "message": str(record["message"]),
        "file": record["file"].path,
        "line": record["line"],
        "function": record["function"],
    }

    # 添加上下文
    if config.include_context and record.get("extra"):
        data["context"] = record["extra"]

    # 添加异常
    if config.include_exception and record.get("exception"):
        exception = record["exception"]
        if exception:
            data["exception"] = str(exception)

    return json.dumps(data, ensure_ascii=False)


def custom_formatter(record: Dict[str, Any], config: FormatterConfig) -> str:
    """自定义格式化器

    使用配置中的模板进行格式化

    Args:
        record: loguru 日志记录
        config: 格式化器配置

    Returns:
        格式化后的日志字符串
    """
    if not config.template:
        # 如果没有模板，退回到文本格式
        return text_formatter(record, config)

    # 准备模板变量
    template_vars = {
        "time": record["time"].strftime(config.time_format),
        "level": record["level"].name,
        "message": str(record["message"]),
        "file": record["file"].path,
        "line": record["line"],
        "function": record["function"],
    }

    # 添加额外字段
    if record.get("extra"):
        template_vars.update(record["extra"])

    # 添加异常
    if record.get("exception"):
        exception = record["exception"]
        if exception:
            template_vars["exception"] = str(exception)

    return config.template.format(**template_vars)


def create_formatter(config: FormatterConfig) -> Callable:
    """创建格式化器

    Args:
        config: 格式化器配置

    Returns:
        格式化器函数
    """
    formatters = {
        "text": text_formatter,
        "json": json_formatter,
        "custom": custom_formatter,
    }

    formatter_func = formatters.get(config.type, text_formatter)

    # 返回一个绑定了配置的格式化器
    def formatter(record: Dict[str, Any]) -> str:
        return formatter_func(record, config)

    return formatter


# Loguru 使用的格式化函数
def loguru_formatter(record: Dict[str, Any]) -> str:
    """供 loguru 使用的格式化函数

    这个函数会被 loguru 的 format 参数使用

    Args:
        record: loguru 日志记录

    Returns:
        格式化后的字符串（带颜色标记）
    """
    level = f"{record['level'].name:<8}"
    message = str(record["message"]).replace("{", "{{").replace("}", "}}")
    time_str = record["time"].strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    # 格式化额外字段
    extra = record.get("extra", {})
    extra_str = ""
    if extra:
        extra_parts = [f"{k}[{v}]" for k, v in extra.items()]
        extra_str = " " + " ".join(extra_parts)

    file_line = f"file[{record['file'].name}:{record['line']}]"

    # 返回带颜色标记的格式
    return f"<level>{level} [{time_str}] {message}{extra_str} {file_line}</level>\n"


__all__ = [
    "create_formatter",
    "text_formatter",
    "json_formatter",
    "custom_formatter",
    "loguru_formatter",
]
