# encoding: utf-8
# tests/unit/applications/openapi/test_base.py
# created: 2025-08-02 14:55:00

"""OpenAPI 测试基础架构验证测试"""

import pytest

from .base import BaseOpenapiUnitTest


@pytest.mark.openapi
@pytest.mark.unit
class TestBaseOpenapiUnitTest(BaseOpenapiUnitTest):
    """测试 OpenAPI 基础测试类的功能"""

    def test_mock_customer_repository_fixture(self, mock_customer_repository):
        """测试客户仓储 mock fixture"""
        assert mock_customer_repository is not None
        assert hasattr(mock_customer_repository, 'get_by_id')
        assert hasattr(mock_customer_repository, 'get_by_token')
        assert hasattr(mock_customer_repository, 'get_by_ak')

    def test_mock_redis_fixture(self, mock_redis):
        """测试 Redis mock fixture"""
        assert mock_redis is not None
        assert hasattr(mock_redis, 'get')
        assert hasattr(mock_redis, 'set')
        assert hasattr(mock_redis, 'delete')

    def test_mock_bifrost_gateway_fixture(self, mock_bifrost_gateway):
        """测试 Bifrost 网关 mock fixture"""
        assert mock_bifrost_gateway is not None
        assert hasattr(mock_bifrost_gateway, 'get_activities')
        assert hasattr(mock_bifrost_gateway, 'get_activity_detail')

    def test_sample_customer_data_fixture(self, sample_customer_data):
        """测试示例客户数据 fixture"""
        assert sample_customer_data is not None
        assert sample_customer_data["id"] == 1
        assert sample_customer_data["code"] == "test_customer_001"
        assert sample_customer_data["name"] == "测试客户"

    def test_sample_benefit_product_data_fixture(self, sample_benefit_product_data):
        """测试示例权益产品数据 fixture"""
        assert sample_benefit_product_data is not None
        assert sample_benefit_product_data["id"] == 1
        assert sample_benefit_product_data["code"] == "benefit_001"
        assert sample_benefit_product_data["sale_price"] == 1000

    def test_mock_config_fixture(self, mock_config):
        """测试配置 mock fixture"""
        assert mock_config is not None
        assert "openapi" in mock_config
        assert "redis" in mock_config
        assert mock_config["openapi"]["token_expire_seconds"] == 3600

    @pytest.mark.asyncio
    async def test_mock_customer_repository_async_methods(self, mock_customer_repository):
        """测试客户仓储异步方法 mock"""
        # 测试异步方法调用
        result = await mock_customer_repository.get_by_id(1)
        assert result is None
        
        # 验证方法被调用
        mock_customer_repository.get_by_id.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_mock_redis_async_methods(self, mock_redis):
        """测试 Redis 异步方法 mock"""
        # 测试异步方法调用
        result = await mock_redis.get("test_key")
        assert result is None
        
        # 测试设置值
        await mock_redis.set("test_key", "test_value")
        
        # 验证方法被调用
        mock_redis.get.assert_called_once_with("test_key")
        mock_redis.set.assert_called_once_with("test_key", "test_value")