# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/schemas.py
# created: 2024-12-08 01:57:27
# updated: 2025-05-26 11:02:07

from datetime import datetime
from typing import Generic, List, Optional, Sequence, TypeVar

from pydantic import BaseModel, Field, field_validator

from src.domains.benefits.dto import (
    BenefitsProductDetailDTO,
    BenefitsProductDTO,
    BenefitsProductOrderDTO,
    BenefitsProductOrderWithCustomerDTO,
    BenefitsSkuChargeRecordDetailDTO,
    BenefitsSkuChargeRecordDTO,
    BenefitsSkuDetailDTO,
    BenefitsSkuDTO,
    SupplierDetailDTO,
    SupplierDTO,
)
from src.domains.benefits.services.product_order import ProductOrderExportRecord
from src.domains.customer.dto import (
    CustomerBenefitsDetailDTO,
    CustomerBenefitsDTO,
    CustomerChannelsDTO,
    CustomerDTO,
    CustomerListDTO,
    CustomerRechargeRecordDTO,
    CustomerSecretDTO,
)
from src.domains.passport.dto import PassportAppDTO, PassportTenantDTO
from src.infrastructures.fastapi.response import BaseResponse

T = TypeVar("T")


class ListData(BaseModel, Generic[T]):
    total: int
    data: List[T]


class ChannelListData(BaseModel):
    total: int
    data: List[CustomerChannelsDTO]


class CreateOrUpdateChannelPayload(BaseModel):
    name: str = Field(..., description="渠道名称")
    identify: str = Field(..., description="渠道标识")
    description: Optional[str] = Field(None, description="渠道描述")
    comment: Optional[str] = Field(None, description="渠道备注")


PassportAppResponse = BaseResponse[PassportAppDTO]
PassportAppsResponse = BaseResponse[List[PassportAppDTO]]
PassportTenantsResponse = BaseResponse[List[PassportTenantDTO]]
ProductListResponse = BaseResponse[ListData[BenefitsProductDTO]]
ProductResponse = BaseResponse[BenefitsProductDetailDTO]
ProductOrderListResponse = BaseResponse[ListData[BenefitsProductOrderDTO]]
ProductOrderListWithCustomerResponse = BaseResponse[ListData[BenefitsProductOrderWithCustomerDTO]]
ProductOrderResponse = BaseResponse[BenefitsProductOrderDTO]
SkuListResponse = BaseResponse[ListData[BenefitsSkuDTO]]
SkuResponse = BaseResponse[BenefitsSkuDetailDTO]
SkuRecordsResponse = BaseResponse[ListData[BenefitsSkuChargeRecordDTO]]
SkuRecordResponse = BaseResponse[BenefitsSkuChargeRecordDetailDTO]
ChannelListResponse = BaseResponse[ChannelListData]
ChannelResponse = BaseResponse[CustomerChannelsDTO]
CustomerBenefitListResponse = BaseResponse[ListData[CustomerBenefitsDTO]]
CustomerBenefitResponse = BaseResponse[CustomerBenefitsDetailDTO]
SupplierResponse = BaseResponse[SupplierDetailDTO]
SupplierListResponse = BaseResponse[ListData[SupplierDTO]]
CustomerResponse = BaseResponse[CustomerDTO]
CustomerSecretResponse = BaseResponse[Sequence[CustomerSecretDTO]]
CustomerListResponse = BaseResponse[ListData[CustomerDTO]]
CustomerRechargeRecordListResponse = BaseResponse[List[CustomerRechargeRecordDTO]]


class CreateAppPayload(BaseModel):
    name: str = Field(..., description="应用名称")
    app_id: str = Field(..., description="应用ID")


class UpdateAppPayload(BaseModel):
    name: Optional[str] = Field(None, description="应用名称")
    app_id: Optional[str] = Field(None, description="应用ID")


class ChargeBenefitsPayload(BaseModel):
    phone: str = Field(..., pattern=r"^1[3-9]\d{9}$", description="充值手机号")
    description: str = Field(..., description="充值描述")


class CreateProductSkuPayload(BaseModel):
    count: int = Field(..., gt=0, lt=100, description="数量")


class RechargePayload(BaseModel):
    amount: int = Field(..., gt=0, description="充值金额(单位分)")
    description: Optional[str] = Field(None, description="充值说明")


class ProductOrderExportRequest(BaseModel):
    """产品订单导出请求"""

    product_code: Optional[str] = Field(None, description="产品编码")
    order_id: Optional[str] = Field(None, description="订单ID")
    out_order_id: Optional[str] = Field(None, description="外部订单ID")
    product_name: Optional[str] = Field(None, description="产品名称")
    account: Optional[str] = Field(None, description="充值账号")
    start_at: Optional[datetime] = Field(None, description="开始时间")
    end_at: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[int] = Field(None, description="订单状态")
    customer_id: Optional[int] = Field(None, description="客户ID")


# 导出相关响应
ProductOrderExportResponse = BaseResponse[ProductOrderExportRecord]
ProductOrderExportRecordListResponse = BaseResponse[List[ProductOrderExportRecord]]


class DeliveryPublishWashOrderTasksPayload(BaseModel):
    export_id: str = Field(..., description="导出ID")
