# encoding: utf-8
# docs/settings-refactor/README.md
# created: 2025-08-18 18:00:00

# Settings 配置系统重构文档

## 📋 文档索引

本文件夹包含了配置系统重构的完整文档，包括设计方案、实施报告、迁移指南等。

### 核心文档

| 文档 | 说明 | 适用对象 |
|------|------|----------|
| [📊 最终总结](./settings-optimization-final.md) | 重构项目的完整总结报告 | 所有人 |
| [🚀 迁移指南](./configuration-migration.md) | 详细的配置迁移步骤和方法 | 开发人员 |
| [🔒 安全实践](./config-security-best-practices.md) | 配置安全最佳实践和规范 | 所有人 |

### 实施报告

| 文档 | 说明 | 阶段 |
|------|------|------|
| [优化总结](./settings-optimization-summary.md) | 配置优化的详细总结 | 优化阶段 |
| [优化报告](./settings-optimization-report.md) | 配置优化的执行报告 | 优化阶段 |
| [清理计划](./settings-cleanup-plan.md) | 废弃代码清理计划 | 清理阶段 |
| [清理报告](./settings-cleanup-report.md) | 清理工作的执行报告 | 清理阶段 |

## 🎯 重构目标与成果

### 主要目标
1. ✅ 消除代码重复，创建统一的基础配置类
2. ✅ 统一配置加载机制和优先级
3. ✅ 移除所有硬编码的敏感信息
4. ✅ 清理废弃的配置代码
5. ✅ 提供完整的文档和迁移指南

### 关键成果
- **代码减少**: 90%（~1500行 → ~150行）
- **安全提升**: 100%（移除所有硬编码密钥）
- **一致性**: 所有服务配置行为统一
- **可维护性**: 清晰的继承结构
- **零中断**: 兼容层确保平滑迁移

## 🏗️ 新架构概览

```
配置系统架构
│
├── 基础设施层 (infrastructures/)
│   ├── settings/
│   │   ├── base.py          # BaseServiceSettings 基类
│   │   └── __init__.py      
│   ├── config_compat.py     # 兼容层（临时）
│   └── 各模块配置 (BaseModel)
│       ├── databases/settings.py
│       ├── rabbitmq/settings.py
│       ├── fastapi/settings.py
│       └── ...
│
├── 服务层 (deploys/)
│   ├── baseapi/settings.py     # 继承 BaseServiceSettings
│   ├── consumer/settings.py    # 继承 BaseServiceSettings
│   ├── openapi/settings.py     # 继承 BaseServiceSettings
│   ├── scheduler/settings.py   # 继承 BaseServiceSettings
│   └── growth_hacker/settings.py # 继承 BaseServiceSettings
│
└── 配置文件
    ├── .env.example         # 环境变量示例
    ├── .env.toml.example    # TOML配置示例
    └── .env / .env.toml     # 实际配置（不提交）
```

## 📝 快速开始

### 1. 创建配置文件
```bash
# 从项目根目录
cp .env.example .env
cp .env.toml.example .env.toml
```

### 2. 配置数据库和服务
```bash
# 编辑 .env 文件
vim .env

# 设置必要的环境变量
DATABASE__MYSQL_URI=mysql://user:pass@host/db
DATABASE__REDIS_URI=redis://user:pass@host
RABBITMQ__URL=amqp://user:pass@host/vhost
```

### 3. 验证配置
```bash
# 运行配置检查脚本
python scripts/check_settings.py
```

### 4. 启动服务
```bash
# 启动具体服务
python deploys/baseapi/main.py
python deploys/consumer/main.py
# ...
```

## 🔄 配置加载优先级

配置系统支持三层配置源，优先级从高到低：

1. **环境变量** (最高优先级)
   - 适用于生产环境、容器化部署
   - 使用双下划线表示嵌套：`DATABASE__MYSQL_URI`

2. **.env 文件**
   - 适用于本地开发
   - 不应提交到版本控制

3. **.env.toml 文件** (最低优先级)
   - 可作为默认配置
   - 可以提交（不含敏感信息）

## 🔒 安全注意事项

### ⚠️ 重要提醒
- **绝不**在代码中硬编码密码、密钥或其他敏感信息
- **绝不**将包含真实凭证的 `.env` 文件提交到版本控制
- **始终**使用环境变量或密钥管理服务管理生产环境的敏感配置
- **定期**轮换密钥和凭证

### 敏感信息清单
以下信息必须通过环境变量或配置文件管理：
- 数据库连接字符串（包含密码）
- Redis连接信息
- RabbitMQ凭证
- API密钥和密钥
- OAuth令牌
- 加密密钥

## 📊 配置字段说明

### 通用配置（所有服务）
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `database.mysql_uri` | string | MySQL连接字符串 | `mysql://user:pass@host/db` |
| `database.redis_uri` | string | Redis连接字符串 | `redis://user:pass@host` |
| `rabbitmq.url` | string | RabbitMQ连接URL | `amqp://user:pass@host/vhost` |
| `bifrost.*` | object | Bifrost服务配置 | 见配置文件 |
| `eleme_union.*` | object | 饿了么联盟配置 | 见配置文件 |

### 服务特定配置
| 服务 | 额外配置 | 说明 |
|------|----------|------|
| baseapi | `fastapi`, `sms` | API服务和短信功能 |
| consumer | `fastapi`, `sms` | 消费者服务 |
| openapi | `fastapi` | 开放API服务 |
| scheduler | - | 定时任务（无额外配置） |
| growth_hacker | `browser`, `ip_proxy`, `logger` | 增长黑客服务 |

## 🛠️ 开发指南

### 添加新配置字段

1. **确定配置层级**
   - 通用配置：添加到 `BaseServiceSettings`
   - 服务特定：添加到对应服务的 Settings 类

2. **更新配置类**
```python
# 例：添加新的通用配置
# src/infrastructures/settings/base.py
class BaseServiceSettings(BaseSettings):
    # 现有字段...
    new_service: NewServiceSettings = Field(default_factory=NewServiceSettings)
```

3. **更新示例文件**
   - 更新 `.env.example`
   - 更新 `.env.toml.example`

4. **更新文档**
   - 在本 README 中添加字段说明

### 创建新服务配置

```python
# deploys/new_service/settings.py
from pathlib import Path
from pydantic_settings import SettingsConfigDict
from src.infrastructures.settings import BaseServiceSettings

class NewServiceSettings(BaseServiceSettings):
    """新服务配置"""
    
    # 添加服务特定配置
    # special_config: SpecialSettings = Field(default_factory=SpecialSettings)
    
    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",
    )

config = NewServiceSettings()
```

## 📋 检查清单

### 部署前检查
- [ ] 所有必需的环境变量已设置
- [ ] 数据库连接可用
- [ ] RabbitMQ服务可访问
- [ ] 第三方服务凭证有效
- [ ] 运行 `check_settings.py` 无错误

### 代码提交前检查
- [ ] 无硬编码的敏感信息
- [ ] `.env` 文件未被跟踪
- [ ] 更新了配置示例文件
- [ ] 更新了相关文档

## 🔧 故障排查

### 常见问题

**Q: 配置没有生效**
- 检查环境变量是否正确设置
- 确认配置文件路径正确
- 查看配置加载优先级

**Q: 服务启动失败**
- 运行 `python scripts/check_settings.py` 检查配置
- 查看错误日志
- 确认所有依赖服务可用

**Q: 如何调试配置加载**
```python
# 打印当前配置
from deploys.baseapi.settings import config
print(config.model_dump())
```

## 📚 相关资源

### 项目文件
- [配置检查脚本](../../scripts/check_settings.py)
- [环境变量示例](../../.env.example)
- [TOML配置示例](../../.env.toml.example)

### 外部文档
- [Pydantic Settings 文档](https://docs.pydantic.dev/latest/usage/pydantic_settings/)
- [Python-dotenv 文档](https://github.com/theskumar/python-dotenv)
- [TOML 规范](https://toml.io/)

## 🤝 贡献指南

1. 修改配置时请更新相应的示例文件
2. 添加新配置项时请添加说明文档
3. 提交前运行配置检查脚本
4. 遵循安全最佳实践

## 📞 联系支持

- 技术问题：在项目 Issue 中提出
- 安全问题：联系安全团队
- 紧急问题：联系 DevOps 团队

---

**最后更新**: 2025-08-18  
**维护者**: Development Team  
**版本**: 1.0.0