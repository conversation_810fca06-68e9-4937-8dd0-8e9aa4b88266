from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbItemPromotionRequest(BaseRequest):

    def __init__(
        self,
        page_number: int = None,
        sort_type: str = None,
        page_size: int = None,
        pid: str = None,
        session_id: str = None,
        settle_type: int = None,
        filter_category_ids: str = None,
        filter_city_ids: str = None,
        search_keyword: str = None,
        hit_item_ids: str = None,
        sid: str = None,
        item_type: int = None,
    ):
        """
        页码，默认第一页，取值范围1~50
        """
        self._page_number = page_number
        """
            排序类型 normal-默认排序 reservePrice-折后价从高到低  commission-佣金从高到低 totalSales-月销量从高到低
        """
        self._sort_type = sort_type
        """
            每页返回数据大小，默认20，最大返回20
        """
        self._page_size = page_size
        """
            推广参数
        """
        self._pid = pid
        """
            用来分页，翻页时将上一次结果的sessionId带下来
        """
        self._session_id = session_id
        """
            推广物料结算模型 1-cpa 2-cps，3spu
        """
        self._settle_type = settle_type
        """
            类目筛选，多个类目逗号分隔（通过alibaba.alsc.union.kb.item.promotion.filter.list获取）
        """
        self._filter_category_ids = filter_category_ids
        """
            城市id(国标)筛选，多个城市逗号分隔（通过alibaba.alsc.union.kb.item.promotion.filter.list获取）
        """
        self._filter_city_ids = filter_city_ids
        """
            关键词搜索，多个词逗号分割
        """
        self._search_keyword = search_keyword
        """
            指定itemId查询推广信息，多个逗号分割
        """
        self._hit_item_ids = hit_item_ids
        """
            第三方会员id扩展
        """
        self._sid = sid
        """
            商品可售卖的端类型。1支付宝端商品，2微信端商品，3全部
        """
        self._item_type = item_type

    @property
    def page_number(self):
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        if isinstance(page_number, int):
            self._page_number = page_number
        else:
            raise TypeError("page_number must be int")

    @property
    def sort_type(self):
        return self._sort_type

    @sort_type.setter
    def sort_type(self, sort_type):
        if isinstance(sort_type, str):
            self._sort_type = sort_type
        else:
            raise TypeError("sort_type must be str")

    @property
    def page_size(self):
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        if isinstance(page_size, int):
            self._page_size = page_size
        else:
            raise TypeError("page_size must be int")

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def session_id(self):
        return self._session_id

    @session_id.setter
    def session_id(self, session_id):
        if isinstance(session_id, str):
            self._session_id = session_id
        else:
            raise TypeError("session_id must be str")

    @property
    def settle_type(self):
        return self._settle_type

    @settle_type.setter
    def settle_type(self, settle_type):
        if isinstance(settle_type, int):
            self._settle_type = settle_type
        else:
            raise TypeError("settle_type must be int")

    @property
    def filter_category_ids(self):
        return self._filter_category_ids

    @filter_category_ids.setter
    def filter_category_ids(self, filter_category_ids):
        if isinstance(filter_category_ids, str):
            self._filter_category_ids = filter_category_ids
        else:
            raise TypeError("filter_category_ids must be str")

    @property
    def filter_city_ids(self):
        return self._filter_city_ids

    @filter_city_ids.setter
    def filter_city_ids(self, filter_city_ids):
        if isinstance(filter_city_ids, str):
            self._filter_city_ids = filter_city_ids
        else:
            raise TypeError("filter_city_ids must be str")

    @property
    def search_keyword(self):
        return self._search_keyword

    @search_keyword.setter
    def search_keyword(self, search_keyword):
        if isinstance(search_keyword, str):
            self._search_keyword = search_keyword
        else:
            raise TypeError("search_keyword must be str")

    @property
    def hit_item_ids(self):
        return self._hit_item_ids

    @hit_item_ids.setter
    def hit_item_ids(self, hit_item_ids):
        if isinstance(hit_item_ids, str):
            self._hit_item_ids = hit_item_ids
        else:
            raise TypeError("hit_item_ids must be str")

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, sid):
        if isinstance(sid, str):
            self._sid = sid
        else:
            raise TypeError("sid must be str")

    @property
    def item_type(self):
        return self._item_type

    @item_type.setter
    def item_type(self, item_type):
        if isinstance(item_type, int):
            self._item_type = item_type
        else:
            raise TypeError("item_type must be int")

    def get_api_name(self):
        return "alibaba.alsc.union.kb.item.promotion"

    def to_dict(self):
        request_dict = {}
        if self._page_number is not None:
            request_dict["page_number"] = convert_basic(self._page_number)

        if self._sort_type is not None:
            request_dict["sort_type"] = convert_basic(self._sort_type)

        if self._page_size is not None:
            request_dict["page_size"] = convert_basic(self._page_size)

        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._session_id is not None:
            request_dict["session_id"] = convert_basic(self._session_id)

        if self._settle_type is not None:
            request_dict["settle_type"] = convert_basic(self._settle_type)

        if self._filter_category_ids is not None:
            request_dict["filter_category_ids"] = convert_basic(self._filter_category_ids)

        if self._filter_city_ids is not None:
            request_dict["filter_city_ids"] = convert_basic(self._filter_city_ids)

        if self._search_keyword is not None:
            request_dict["search_keyword"] = convert_basic(self._search_keyword)

        if self._hit_item_ids is not None:
            request_dict["hit_item_ids"] = convert_basic(self._hit_item_ids)

        if self._sid is not None:
            request_dict["sid"] = convert_basic(self._sid)

        if self._item_type is not None:
            request_dict["item_type"] = convert_basic(self._item_type)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
