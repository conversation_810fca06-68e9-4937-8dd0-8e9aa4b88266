from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class TaobaoTmcAuthGetRequest(BaseRequest):

    def __init__(self, group: str = None):
        """
        tmc组名
        """
        self._group = group

    @property
    def group(self):
        return self._group

    @group.setter
    def group(self, group):
        if isinstance(group, str):
            self._group = group
        else:
            raise TypeError("group must be str")

    def get_api_name(self):
        return "taobao.tmc.auth.get"

    def to_dict(self):
        request_dict = {}
        if self._group is not None:
            request_dict["group"] = convert_basic(self._group)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
