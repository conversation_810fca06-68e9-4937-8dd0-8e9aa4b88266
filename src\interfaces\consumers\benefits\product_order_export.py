# encoding: utf-8
# src/interfaces/consumers/benefits/product_order_export.py
# created: 2025-01-12 12:00:00

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.domains.benefits.messages import ProductOrderExportMessage, ProductOrderExportMessageContent
from src.infrastructures.rabbitmq.consumers import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import ProductOrderExportCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class ProductOrderExportConsumer(BaseConsumer):
    """产品订单导出消费者"""

    exchange_name = "benefits.topic"
    queue_name = "benefits_product_order_export"
    routing_key = "benefits.product_order_export"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        export_service: "ProductOrderExportCommandService",
    ):
        self.export_service = export_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = ProductOrderExportMessage.model_validate_json(message.body)
        payload: ProductOrderExportMessageContent = msg.payload
        logger.info(f"ProductOrderExportConsumer 收到消息: export_id={payload.export_id}")

        await self.export_service.export_orders(
            export_id=payload.export_id,
            user_id=payload.user_id,
            user_name=payload.user_name,
            filters_dict=payload.filters,
        )

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 导出失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "ProductOrderExportConsumer 回滚: 消息[{message_id}] 导出失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
