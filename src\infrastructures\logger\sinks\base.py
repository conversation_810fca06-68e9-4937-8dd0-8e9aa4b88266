# encoding: utf-8
# src/infrastructures/logger/sinks/base.py
# created: 2025-08-14 14:40:00

from abc import ABC, abstractmethod
from typing import Optional, Union

from ..settings import ConsoleSinkConfig, FileSinkConfig, SLSSinkConfig
from ..types import FormatterType, LogMessage


class BaseSink(ABC):
    """输出目标基类

    所有输出目标的抽象基类
    定义了输出目标的基本接口
    """

    def __init__(self, config: Union[ConsoleSinkConfig, FileSinkConfig, SLSSinkConfig], app_name: str):
        """初始化输出目标

        Args:
            config: 输出目标配置
            app_name: 应用名称
        """
        self.config = config
        self.app_name = app_name
        self.formatter = self._create_formatter()

    @abstractmethod
    def write(self, message: LogMessage) -> None:
        """写入日志消息

        Args:
            message: loguru Message 对象
        """
        pass

    def __call__(self, message: LogMessage) -> None:
        """处理日志消息

        loguru 会调用这个方法

        Args:
            message: loguru Message 对象
        """
        self.write(message)

    def _create_formatter(self) -> Optional[FormatterType]:
        """创建格式化器

        Returns:
            格式化器实例或None
        """
        # 某些输出目标可能不需要格式化器
        if hasattr(self.config, "formatter"):
            from ..formatters import create_formatter

            return create_formatter(self.config.formatter)
        return None

    def close(self) -> None:
        """关闭输出目标

        清理资源，刷新缓冲区等
        子类可以重写此方法
        """
        return

    async def async_close(self) -> None:
        """异步关闭输出目标

        异步清理资源
        子类可以重写此方法
        """
        return
