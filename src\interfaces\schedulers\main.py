# encoding: utf-8
# src/interfaces/schedulers/main.py
# created: 2025-07-24 00:05:37

import asyncio
import contextlib
from typing import TYPE_CHECKING

from src.infrastructures.logger import setup

from .manager import SchedulerManager

if TYPE_CHECKING:
    from src.containers import Container


@contextlib.asynccontextmanager
async def lifespan(container: "Container"):
    """上下文管理器，用于初始化和清理资源"""
    # 初始化日志系统
    setup(
        {
            "app_name": "scheduler",
            "sinks": [
                {"type": "console", "level": "INFO"},
                {"type": "file", "level": "DEBUG", "path": "logs/scheduler.log"},
            ],
        }
    )

    # 初始化资源
    await container.infrastructures.init_tortoise()

    yield container

    # 清理资源
    from tortoise import Tortoise

    await Tortoise.close_connections()


async def main(container: "Container"):
    """主函数，接收容器参数"""
    scheduler_manager = SchedulerManager()
    scheduler_manager.setup_signal_handlers(asyncio.get_running_loop())  # 设置信号处理

    async with lifespan(container) as active_container:
        active_container.wire(packages=["src.interfaces.schedulers.tasks"])  # 注册依赖

        from src.interfaces.schedulers.tasks import (
            eleme_user_profile,
            sync_union_messages,
            sync_union_order_by_1d,
            sync_union_order_by_3m,
            sync_union_purchases,
        )

        # 注册任务
        scheduler_manager.add_job(sync_union_messages, "interval", seconds=10, name="sync_union_messages")
        scheduler_manager.add_job(sync_union_order_by_3m, "interval", seconds=30, name="sync_union_order_by_3m")
        scheduler_manager.add_job(sync_union_purchases, "interval", minutes=10, name="sync_union_purchases")
        scheduler_manager.add_job(sync_union_order_by_1d, "cron", hour=1, minute=30, name="sync_union_order_by_1d")
        scheduler_manager.add_job(eleme_user_profile, "cron", hour=0, minute=30, name="eleme_user_profile")

        scheduler_manager.start()  # 启动调度器
        await scheduler_manager.wait_for_stop()  # 等待停止信号


if __name__ == "__main__":
    # 本地测试时使用
    from src.containers import Container
    from src.infrastructures.settings import BaseServiceSettings

    container = Container()
    container.config.from_pydantic(BaseServiceSettings())
    asyncio.run(main(container))
