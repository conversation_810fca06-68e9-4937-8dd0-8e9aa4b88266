# encoding: utf-8
# deploys/consumer/main.py
# created: 2025-07-23 17:30:00

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from deploys.consumer.settings import config


def setup_container():
    """设置依赖注入容器"""
    from src.containers import Container
    
    container = Container()
    container.config.from_pydantic(config)
    
    # 进行依赖注入的线路连接
    container.wire(packages=[
        "src.interfaces.consumers",
        "src.domains.benefits.services.charge_strategy",
        "src.applications.common.commands.benefits",
    ])
    
    return container


if __name__ == "__main__":
    import asyncio
    
    from src.interfaces.consumers.main import main
    
    # 设置容器并运行主程序
    container = setup_container()
    asyncio.run(main(container))