# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/delivery.py
# created: 2025-03-24 22:37:09
# updated: 2025-06-08 10:40:06

from enum import IntEnum, StrEnum
from typing import Any

import nanoid
from tortoise import Model, fields


def generate_code():
    return "DP" + nanoid.generate(size=6, alphabet="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")


class DeliveryPageTypeEnum(StrEnum):
    SPEC_UNION = "union_special"
    UNION = "union"


class DeliveryPage(Model):

    id = fields.BigIntField(primary_key=True)
    type = fields.CharEnumField(
        DeliveryPageTypeEnum, null=False, default=DeliveryPageTypeEnum.UNION, description="页面类型"
    )
    code = fields.CharField(max_length=255, null=False, description="页面code", unique=True, default=generate_code)
    name = fields.Char<PERSON>ield(max_length=255, null=False, default="", description="页面名称")
    url = fields.CharField(max_length=1024, null=False, default="", description="页面URL")
    description = fields.CharField(max_length=255, null=False, default="", description="页面说明")
    comment = fields.CharField(max_length=255, null=False, default="", description="页面备注")
    union_active_id = fields.CharField(max_length=255, null=False, default="", description="联盟活动ID")
    union_zone_pid = fields.CharField(max_length=255, null=True, default="", description="联盟资源位PID")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    custom_behavior = fields.CharField(max_length=255, null=True, default="", description="取链行为")

    class Meta:
        table = "delivery_page"
        table_description = "外卖可投放页面"


class OrderStateEnum(IntEnum):
    INVALID = 0
    ORDERED = 1
    PAID = 2
    RECEIVED = 4


class SettleStateEnum(IntEnum):
    # 1-已结算； 2-未结算；-99无需结算
    SETTLED = 1
    UNSETTLED = 2
    NO_SETTLEMENT_REQUIRED = -99


class DeliveryUnionOrder(Model):
    id = fields.BigIntField(primary_key=True)

    # order id info
    order_id = fields.BigIntField(default=0, description="订单ID", unique=True)
    biz_order_id = fields.BigIntField(default=0, description="淘宝子单号。biz_order_id+biz_unit是联合主键")

    # order state info
    order_state = fields.IntEnumField(OrderStateEnum, description="订单状态, 0-已失效 1-已下单 2-已付款 4-已收货")
    settle_state = fields.IntEnumField(SettleStateEnum, description="结算状态, 1-已结算； 2-未结算；-99无需结算")

    # order detail info
    shop_name = fields.CharField(max_length=255, null=False, default="", description="店铺名称")
    title = fields.CharField(max_length=255, default="", description="商品标题")
    pic_url = fields.CharField(max_length=255, default="", description="图片URL")
    item_id = fields.CharField(max_length=255, default="", description="商品ID")
    product_num = fields.IntField(default=0, description="商品数量")
    unit_price = fields.IntField(default=0, description="单价（单位分）")
    category_name = fields.CharField(max_length=255, default="", description="类别名称")

    # media info
    ad_zone_id = fields.CharField(max_length=255, default="", description="广告位ID")
    ad_zone_name = fields.CharField(max_length=255, default="", description="广告位名称")
    media_id = fields.CharField(max_length=255, default="", description="媒体ID")
    media_name = fields.CharField(max_length=255, default="", description="媒体名称")
    flow_type = fields.IntField(default=0, description="流量类型")
    platform_type = fields.IntField(default=0, description="平台类型")
    pid = fields.CharField(max_length=255, default="", description="推广位ID")

    # order amount info
    pay_amount = fields.IntField(default=0, description="用户付款金额(单位:分)")
    settle_amount = fields.IntField(default=0, description="结算金额(单位:分)")
    income = fields.IntField(
        default=0,
        description="预估收入(单位:分), 预估到手的佣金,包含技术服务费。付款之后有值。没结算之前只有income有值",
    )
    settle_income = fields.IntField(
        default=0, description="结算预估收入(单位:分), 最终到手的佣金，没有扣除技术服务；结算之后有值"
    )
    full_settle_amount = fields.IntField(default=0, description="结算基数(单位:分), 针对CPS订单, 等于付款金额+平台补贴")
    channel_fee = fields.IntField(default=0, description="渠道费用（单位分）")
    channel_rate = fields.FloatField(default=0.0, description="渠道费率")
    commission_fee = fields.IntField(default=0, description="佣金费用（单位分）")
    commission_rate = fields.FloatField(default=0.0, description="佣金费率")
    platform_commission_fee = fields.IntField(default=0, description="平台佣金费用（单位分）")
    platform_commission_rate = fields.FloatField(default=0.0, description="平台佣金费率")
    subsidy_fee = fields.IntField(default=0, description="补贴费用（单位分）")
    subsidy_rate = fields.FloatField(default=0.0, description="补贴费率")
    settle = fields.IntField(default=0, description="结算金额（单位分）")

    # source union active info
    sid = fields.CharField(max_length=50, default="", description="活动ID")
    ext_info: Any = fields.JSONField(description="联盟扩展信息")
    activity_info_remark_list = fields.CharField(max_length=255, default="", description="活动信息备注列表")
    source_info: Any = fields.JSONField(null=True, description="海狸扩展信息, 用于记录海狸来源相关扩展信息")

    # time info
    trace_time = fields.DatetimeField(description="点击时间", null=True, blank=True)
    tk_create_time = fields.DatetimeField(description="创建时间", null=True, blank=True)
    pay_time = fields.DatetimeField(description="付款时间", null=True, blank=True)
    receive_time = fields.DatetimeField(description="收货时间", null=True, blank=True)
    settle_time = fields.DatetimeField(description="结算时间", null=True, blank=True)
    gmt_modified = fields.DatetimeField(description="修改时间", null=True, blank=True)
    source = fields.CharField(max_length=255, default="hicaspian", description="来源")

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "delivery_union_order"


class DeliveryZone(Model):

    id = fields.BigIntField(primary_key=True)
    name = fields.CharField(max_length=255, null=False, default="", description="资源位名称")
    union_name = fields.CharField(max_length=255, null=False, default="", description="资源位名称", unique=True)
    zone_pid = fields.CharField(max_length=255, null=False, default="", description="资源位PID", unique=True)
    app_id = fields.CharField(max_length=128, null=False, default="", description="应用ID")
    tenant_id = fields.CharField(max_length=128, null=False, default="", description="租户ID")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "delivery_zone"
        table_description = "资源位"


class ElemeAddress(Model):

    id = fields.BigIntField(pk=True)
    address = fields.CharField(max_length=255, default="", description="完整地址")
    address_detail = fields.CharField(max_length=255, default="", description="详细地址")
    address_id = fields.CharField(max_length=255, default="", description="地址ID", unique=True)
    alsc_complete_address = fields.CharField(max_length=255, default="", description="饿了么完整地址")
    alsc_district_id = fields.IntField(default=0, description="饿了么区域ID")
    alsc_full_name = fields.CharField(max_length=255, default="", description="收货人姓名")
    alsc_phone = fields.CharField(max_length=255, default="", description="收货人电话")
    alsc_province_id = fields.CharField(max_length=255, default="", description="饿了么省份ID")
    city_id = fields.IntField(default=0, description="城市ID")
    city_name = fields.CharField(max_length=255, default="", description="城市名称")
    coordinates_source = fields.CharField(max_length=255, default="", description="坐标来源")
    delivery_address_type = fields.CharField(max_length=255, default="", description="配送地址类型")
    district_name = fields.CharField(max_length=255, default="", description="区域名称")
    need_global_expand = fields.BooleanField(default=False, description="是否需要全局扩展")
    poi_name = fields.CharField(max_length=255, default="", description="POI名称")
    province_name = fields.CharField(max_length=255, default="", description="省份名称")
    receive_lat = fields.CharField(max_length=255, default="", description="收货纬度")
    receive_lng = fields.CharField(max_length=255, default="", description="收货经度")
    select_collection_poi = fields.BooleanField(default=False, description="是否选择集合POI")
    user_address_poi = fields.JSONField(null=True, description="用户地址POI信息")  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "eleme_address"
        table_description = "饿了么地址信息"
        indexes = (
            ("alsc_phone",),  # 用户地址查询
            ("city_id", "alsc_district_id"),  # 区域地址查询
            ("alsc_full_name",),  # 收货人查询
            ("receive_lat", "receive_lng"),  # 地理位置查询
            ("delivery_address_type",),  # 地址类型统计
        )


class ElemeOrder(Model):

    id = fields.BigIntField(pk=True)
    order_id = fields.CharField(max_length=255, unique=True, description="订单ID")
    biz_scene = fields.CharField(max_length=255, description="业务场景")
    buyer_uid = fields.CharField(max_length=255, description="买家ID")
    buyer_phone = fields.CharField(max_length=255, description="买家手机号")
    total_amount = fields.IntField(description="订单总金额")
    merchant_subsidy_amount = fields.IntField(description="商家补贴金额")
    agent_subsidy_amount = fields.IntField(description="代理补贴金额")
    real_amount = fields.IntField(description="实际支付金额")
    payment_amount = fields.IntField(description="支付金额")
    trade_type = fields.CharField(max_length=255, description="交易类型")
    pay_time = fields.DatetimeField(description="支付时间")
    payee_account_no = fields.CharField(max_length=255, description="收款账号")
    payer_account_no = fields.CharField(max_length=255, description="付款账号")
    address = fields.ForeignKeyField(  # type: ignore
        "models.ElemeAddress", related_name="orders", description="地址信息", db_constraint=False
    )
    user_tag = fields.CharField(max_length=255, description="用户标签")
    source = fields.CharField(max_length=255, default="", description="来源")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    subjects = fields.JSONField(description="商品信息")  # type: ignore

    class Meta:
        table = "eleme_order"
        table_description = "饿了么订单信息"
        indexes = (
            ("buyer_phone", "pay_time"),  # 用户订单历史查询
            ("pay_time",),  # 时间范围查询
            ("trade_type", "pay_time"),  # 特定类型订单查询
            ("biz_scene", "pay_time"),  # 业务场景统计
        )


class GenderEnum(StrEnum):
    MALE = "男"
    FEMALE = "女"
    UNKNOWN = "未知"


class LifecycleStage(StrEnum):
    NEW = "新客"
    ACTIVE = "活跃"
    SILENT = "沉默"
    LOST = "流失"


class ElemeUserProfile(Model):
    id = fields.BigIntField(pk=True, description="主键ID")

    # 基础身份
    buyer_phone = fields.CharField(max_length=20, unique=True, description="用户手机号")
    eleme_uid = fields.CharField(max_length=64, default="", description="饿了么UID")
    haili_uid = fields.CharField(max_length=64, default="", description="海狸UID")
    gender = fields.CharEnumField(GenderEnum, default=GenderEnum.UNKNOWN, description="性别")
    main_city = fields.CharField(max_length=64, default="", description="常驻城市")
    main_district = fields.CharField(max_length=64, default="", description="常驻区域")
    main_address = fields.CharField(max_length=255, default="", description="最常用地址")
    device_type = fields.CharField(max_length=64, default="", description="设备类型")
    source = fields.CharField(max_length=64, default="hicaspian", description="来源标记")

    # 行为特征
    order_count = fields.IntField(default=0, description="总下单次数")
    order_count_7d = fields.IntField(default=0, description="近7天下单数")
    order_count_30d = fields.IntField(default=0, description="近30天下单数")
    order_count_90d = fields.IntField(default=0, description="近90天下单数")
    first_order_time = fields.DatetimeField(null=True, description="首单时间")
    last_order_time = fields.DatetimeField(null=True, description="最近下单时间")
    avg_order_interval_days = fields.FloatField(default=0.0, description="平均下单间隔")

    hourly_order_distribution = fields.JSONField(null=True, description='小时下单分布，如{"0":3,"22":5}')  # type: ignore
    preferred_hours = fields.JSONField(null=True, description="高频活跃时间段，如[22,23,0]")  # type: ignore
    weekday_order_count = fields.IntField(default=0, description="工作日订单数")
    weekend_order_count = fields.IntField(default=0, description="周末订单数")
    holiday_order_count = fields.IntField(default=0, description="节假日订单数")
    weekday_order_rate = fields.FloatField(default=0.0, description="工作日订单占比")
    holiday_order_rate = fields.FloatField(default=0.0, description="节假日订单占比")

    # 消费能力
    total_amount = fields.IntField(default=0, description="累计支付金额（分）")
    avg_order_amount = fields.IntField(default=0, description="平均客单价（分）")
    max_order_amount = fields.IntField(default=0, description="最大订单金额（分）")
    subsidy_ratio = fields.FloatField(default=0.0, description="补贴比例")

    # 偏好兴趣
    favorite_category = fields.CharField(max_length=64, default="", description="偏好品类")
    favorite_shop = fields.CharField(max_length=128, default="", description="偏好商家")
    address_poi = fields.CharField(max_length=255, default="", description="活跃POI")
    top_keywords = fields.JSONField(null=True, description="关键词统计")  # type: ignore

    # 渠道来源
    source_pid = fields.CharField(max_length=128, default="", description="来源推广位PID")
    ad_zone_id = fields.CharField(max_length=128, default="", description="广告位ID")
    flow_type = fields.CharField(max_length=32, default="", description="投放类型")
    trace_time = fields.DatetimeField(null=True, description="首次点击时间")

    # 生命周期 & 标签
    lifecycle_stage = fields.CharEnumField(LifecycleStage, default=LifecycleStage.NEW, description="生命周期阶段")
    is_returned_user = fields.BooleanField(default=False, description="是否回流")
    user_tags = fields.JSONField(null=True, description="用户标签结构体，如['夜猫子','高价值']")  # type: ignore
    # 注意：user_tags_text 字段是数据库Generated Column，由数据库自动生成，不在ORM中定义
    # 数据库定义：GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`user_tags`, '$'))) STORED
    last_tagged_at = fields.DatetimeField(auto_now_add=True, description="标签更新时间")

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "eleme_user_profile"
        table_description = "用户画像主表"
        unique_together = ("buyer_phone",)
