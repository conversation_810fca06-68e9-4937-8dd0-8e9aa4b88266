# encoding: utf-8
# src/applications/common/commands/benefits/union_purchase_sync.py
# created: 2025-08-19 10:00:00

from typing import TYPE_CHECKING

from loguru import logger
from tortoise.transactions import atomic

from src.domains.benefits.messages import (
    PurchaseTicketOpType,
    PurchaseTicketSubType,
)

if TYPE_CHECKING:
    from src.domains.benefits.services import (
        BenefitsProductService,
        BenefitsSkuService,
        BenefitsSupplierService,
    )
    from src.repositories.benefits import SupplierRepository


ELEME_UNION_IDENTIFY = "eleme_union"


class UnionPurchaseSyncCommandService:
    """联盟采购票据同步命令服务"""

    def __init__(
        self,
        supplier_repo: "SupplierRepository",
        benefits_supplier_service: "BenefitsSupplierService",
        sku_service: "BenefitsSkuService",
        benefits_product_service: "BenefitsProductService",
    ):
        self.supplier_repo = supplier_repo
        self.benefits_supplier_service = benefits_supplier_service
        self.sku_service = sku_service
        self.benefits_product_service = benefits_product_service

    @atomic()
    async def sync_purchase_ticket(
        self,
        ticket_id: str,
        purchase_id: str,
        op_type: PurchaseTicketOpType,
        sub_type: PurchaseTicketSubType,
    ) -> None:
        """
        同步采购票据

        Args:
            ticket_id: 票据ID
            purchase_id: 采购ID
            op_type: 操作类型
            sub_type: 子类型
        """
        logger.info(
            f"开始处理联盟采购票据同步 - "
            f"ticket_id={ticket_id}, purchase_id={purchase_id}, "
            f"op_type={op_type}, sub_type={sub_type}"
        )

        # 获取饿了么联盟供应商
        supplier = await self.supplier_repo.get_by_identify(ELEME_UNION_IDENTIFY)
        if not supplier:
            logger.error(f"饿了么联盟供应商不存在，停止同步 - " f"supplier_identify: {ELEME_UNION_IDENTIFY}")
            return

        # 检查是否需要处理
        if not self._should_process_callback(op_type, sub_type):
            logger.info(f"跳过处理 - op_type={op_type}, sub_type={sub_type}")
            return

        logger.info(
            f"处理联盟回调 - "
            f"ticket_id={ticket_id}, purchase_id={purchase_id}, "
            f"op_type={op_type}, sub_type={sub_type}"
        )

        try:
            # 1. 同步采购单信息
            await self.benefits_supplier_service.sync_eleme_union_purchase(purchase_id, supplier.id)

            # 2. 处理充值记录状态和库存同步
            await self.sku_service.sync_stock_from_purchase(
                ticket_id=ticket_id,
                purchase_id=purchase_id,
                op_type=op_type.value,
                sub_type=sub_type.value,
                product_service=self.benefits_product_service,
            )

            logger.info(f"联盟采购票据同步完成 - ticket_id={ticket_id}")

        except Exception as e:
            logger.error(f"处理联盟回调失败: {str(e)}")
            raise

    def _should_process_callback(self, op_type: PurchaseTicketOpType, sub_type: PurchaseTicketSubType) -> bool:
        """
        判断是否需要处理当前回调

        Args:
            op_type: 操作类型
            sub_type: 子类型

        Returns:
            bool: 是否需要处理
        """
        if op_type == PurchaseTicketOpType.DELIVER:  # 发放相关的回调
            return sub_type in [PurchaseTicketSubType.DELIVER_SUCCESS, PurchaseTicketSubType.DELIVER_FAIL]
        elif op_type == PurchaseTicketOpType.REFUND:  # 退款相关的回调
            return sub_type in [PurchaseTicketSubType.REFUND_SUCCESS, PurchaseTicketSubType.REFUND_FAIL]
        else:
            logger.warning(f"未知的操作类型: {op_type}")
            return False
