# 饿了么地址国标信息重构

## 概述

本次重构主要解决饿了么地址数据中存在的脏数据问题，通过集成 RegionGateway 获取标准的国标地址信息，提升地址数据的准确性和标准化程度。

## 问题背景

### 原有问题

1. **脏数据问题**: `raw_address_data_from_dto` 中的地址字段存在大量非标准化数据
2. **非国标信息**: 地址信息不符合国家标准的行政区划规范
3. **数据不一致**: 相同地区可能存在多种不同的地址表示方式

### 解决方案

通过 `alsc_district_id`（饿了么地区服务ID）与 RegionGateway 中的标准地区数据进行映射，获取对应的国标信息。

## 技术实现

### 1. 数据模型扩展

#### ElemeAddress 模型新增字段

```python
class ElemeAddress(Model):
    # ... 现有字段 ...
    gb_district_id = fields.CharField(max_length=20, default="", description="国标区县ID")
    gb_city_name = fields.CharField(max_length=100, default="", description="国标城市名称")
```

> **注意**: ElemeAddressDTO 中不需要添加国标信息字段，国标信息只在数据库层面存储

### 2. RegionGateway 功能扩展

#### 新增方法: `get_gb_info_by_alsc_district_id`

```python
def get_gb_info_by_alsc_district_id(self, alsc_district_id: int) -> Optional[dict]:
    """
    根据饿了么区县ID获取国标信息
    
    Args:
        alsc_district_id: 饿了么区县ID
        
    Returns:
        包含国标信息的字典，如果找不到返回None
    """
```

### 2.1. 直接导入使用

由于 RegionGateway 是单例模式，可以直接导入使用，无需依赖注入：

```python
# src/repositories/delivery/eleme_orders.py
from src.infrastructures.gateways.regions.gateway import region_gateway
```

**返回数据结构**:

```python
{
    "gb_district_id": "440300",  # 国标区县代码
    "gb_city_name": "深圳市",     # 国标城市名称
    "district_name": "南山区",    # 区县名称
    "city_name": "深圳市",        # 城市名称
    "province_name": "广东省",    # 省份名称
    "found": True
}
```

### 3. 地址创建逻辑重构

#### 原有逻辑

```python
# 直接使用原始地址数据
raw_address_data_from_dto = address_info_dto.model_dump()
defaults_for_address_creation = {
    k: v for k, v in raw_address_data_from_dto.items() 
    if v is not None and k != "address_id"
}
```

#### 重构后逻辑

```python
# 1. 获取原始地址数据
raw_address_data_from_dto = address_info_dto.model_dump()

# 2. 通过RegionGateway获取国标信息
if address_info_dto.alsc_district_id:
    gb_info = region_gateway.get_gb_info_by_alsc_district_id(
        address_info_dto.alsc_district_id
    )
    if gb_info:
        # 3. 更新地址数据，添加国标信息
        raw_address_data_from_dto["gb_district_id"] = gb_info["gb_district_id"]
        raw_address_data_from_dto["gb_city_name"] = gb_info["gb_city_name"]

# 4. 准备创建参数
defaults_for_address_creation = {
    k: v for k, v in raw_address_data_from_dto.items() 
    if v is not None and k != "address_id"
}
```

## 数据映射关系

### 饿了么地区ID → 国标信息映射

- `alsc_district_id` (饿了么区县ID) → `gb_district_id` (国标区县代码)
- 通过 RegionGateway 的 `_district_map` 索引进行查找
- 匹配成功后获取对应的 `districtCode` 和城市信息

### 示例映射

```
alsc_district_id: 440305 (南山区)
↓
gb_district_id: "440305" (国标区县代码)
gb_city_name: "深圳市" (国标城市名称)
```

## 优势与收益

### 1. 数据标准化

- 地址信息符合国家行政区划标准
- 统一的数据格式和命名规范
- 便于后续数据分析和处理

### 2. 数据质量提升

- 减少脏数据和重复数据
- 提高地址信息的准确性
- 支持更精确的地理位置分析

### 3. 系统兼容性

- 向后兼容现有数据
- 渐进式数据迁移
- 不影响现有业务逻辑

## 部署注意事项

### 1. 数据库迁移

需要执行数据库迁移脚本，为 `eleme_address` 表添加新字段：

```sql
ALTER TABLE eleme_address 
ADD COLUMN gb_district_id VARCHAR(20) DEFAULT '',
ADD COLUMN gb_city_name VARCHAR(100) DEFAULT '';
```

### 2. 数据回填

对于已存在的地址数据，可以通过以下方式回填国标信息：

```python
# 批量更新现有数据
async def backfill_gb_info():
    addresses = await ElemeAddress.filter(gb_district_id="")
    for address in addresses:
        if address.alsc_district_id:
            gb_info = region_gateway.get_gb_info_by_alsc_district_id(address.alsc_district_id)
            if gb_info:
                address.gb_district_id = gb_info["gb_district_id"]
                address.gb_city_name = gb_info["gb_city_name"]
                await address.save()
```

### 3. 监控与日志

- 添加国标信息获取成功率的监控
- 记录未找到对应国标信息的 alsc_district_id
- 定期分析数据质量指标

## 测试验证

### 1. 单元测试

```python
def test_get_gb_info_by_alsc_district_id():
    # 测试已知的alsc_district_id
    gb_info = region_gateway.get_gb_info_by_alsc_district_id(440305)
    assert gb_info is not None
    assert gb_info["gb_district_id"] == "440305"
    assert gb_info["gb_city_name"] == "深圳市"
```

### 2. 集成测试

```python
async def test_order_parsing_with_gb_info():
    # 测试订单解析时自动获取国标信息
    mock_order_data = {
        "alscOrderModelInfo": {
            "orderInfo": {
                "extInfo": {
                    "alscAddressInfo": json.dumps({
                        "alscDistrictId": 5001,  # 北京市东城区
                        # ... 其他地址字段
                    })
                }
            }
        }
    }
    
    # 通过 OrderSyncCommandService 解析订单
    order_sync_service = OrderSyncCommandService(
        # ... 依赖注入
        region_gateway=region_gateway,
    )
    
    order_dto = order_sync_service._parse_eleme_order("test_order_001", mock_order_data)
    assert order_dto.address_info.gb_district_id == "110101"
    assert order_dto.address_info.gb_city_name == "北京"
```

## 后续优化方向

### 1. 缓存优化

- 对频繁查询的 alsc_district_id 进行缓存
- 减少重复的 RegionGateway 查询

### 2. 数据同步

- 定期同步 RegionGateway 的地区数据
- 确保国标信息的时效性

### 3. 扩展功能

- 支持更多地址标准化功能
- 集成其他地址验证服务

---

**创建时间**: 2025-01-14  
**负责人**: AI Assistant  
**版本**: v1.0
