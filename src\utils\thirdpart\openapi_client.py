# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/26 01:53

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple

import aiohttp
import requests  # type: ignore
from loguru import logger

logger = logger.bind(module_name="openapi_client")


class OpenApiClientBase(ABC):

    def __init__(self, base_url: str):
        self.base_url = base_url

    @abstractmethod
    def _prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        pass

    @abstractmethod
    async def _async_prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        pass

    def _request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        payload: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        url = f"{self.base_url}{endpoint}"
        params, payload = self._prepare_params(method, params, payload)
        try:
            logger.debug(f"Requesting {method} {url} with params: {params} and payload: {payload}")
            response = requests.request(method, url, params=params, data=payload)
            logger.debug(f"Response status code: {response.status_code}, response text: {response.text}")
            response.raise_for_status()
        except requests.RequestException as exp:
            logger.error(f"Request {method} {url} failed with error: {exp}")
            raise
        return response.json()

    async def _async_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        payload: Optional[Dict[str, Any]] = None,
        without_auth: bool = False,
    ) -> Dict[str, Any]:
        url = f"{self.base_url}{endpoint}"
        if not without_auth:
            params, payload = self._prepare_params(method, params, payload)
            params, payload = await self._async_prepare_params(method, params, payload)  # type: ignore
        try:
            async with aiohttp.ClientSession() as session:
                logger.debug(f"Requesting {method} {url} with params: {params} and payload: {payload}")
                async with session.request(method, url, params=params, json=payload) as response:
                    text = await response.text()
                    logger.debug(f"Response status code: {response.status}, response text: {text}")
                    response.raise_for_status()
                    return await response.json()
        except aiohttp.ClientError as exp:
            logger.error(f"Request {method} {url} failed with error: {exp}")
            raise
