# encoding: utf-8
# src/domains/customer/entities/customer.py
# created: 2025-07-29 08:37:11

from typing import TYPE_CHECKING, Optional

import jwt
from loguru import logger
from pydantic import BaseModel

from .secrect import CustomerSecretEntity

if TYPE_CHECKING:
    from src.databases.models.customer import Customer, CustomerSecret

    from .benefit_product import BenefitProductEntity


class CustomerEntity(BaseModel):

    id: int
    code: str
    name: str
    description: str
    app_id: str
    tenant_id: str

    current_secrect: Optional["CustomerSecretEntity"] = None

    balance: int  # 账户余额

    @property
    def source_identify(self) -> str:
        return f"{self.app_id}.{self.tenant_id}.benefits"

    def payment_benefit(self, product: "BenefitProductEntity") -> int:
        self.balance -= product.sale_price
        logger.info(
            f"客户[{self.code}-{self.name}]支付权益产品: [{product.code}]{product.name}, 金额: {product.sale_price}"
        )
        return self.balance

    def validate_benefit(self, product: "BenefitProductEntity") -> bool:
        """验证客户是否可以购买权益产品"""
        if not product.enabled or product.customer_id != self.id:
            return False
        if product.sale_mode == "finacial" and self.balance < product.sale_price:
            logger.warning(
                "客户[{customer}]余额不足, 无法购买权益产品: {product_code}, 余额: {balance}, 需要: {need}",
                customer=f"{self.code}-{self.name}",
                product_code=product.code,
                balance=self.balance,
                need=product.sale_price,
            )
            return False
        if product.sale_mode == "stock" and product.stock <= 0:
            logger.warning(
                "客户[{customer}]权益产品库存不足, 无法购买权益产品: {product_code}, 库存: {stock}",
                customer=f"{self.code}-{self.name}",
                product_code=product.code,
                stock=product.stock,
            )
            return False
        return True

    def validate_source(self, app_id: int, tenant_id: str) -> bool:
        return self.app_id != app_id or self.tenant_id != tenant_id

    def parse_jwt(self, token: str) -> tuple[str, str]:
        from src.infrastructures.errors import InvalidTokenError

        if not self.current_secrect:
            raise InvalidTokenError
        try:
            payload = jwt.decode(token, key=self.current_secrect.jwt_secrect, algorithms=["HS256"])
            ori_open_id = payload.get("open_id")
            phone = payload.get("mobile")
            open_id = f"{self.id}_{ori_open_id}_{phone}"
            return open_id, phone
        except jwt.exceptions.InvalidTokenError as e:
            raise InvalidTokenError from e

    @classmethod
    async def from_model(cls, customer: "Customer") -> "CustomerEntity":
        return cls(
            id=customer.id,
            code=customer.code,
            name=customer.name,
            description=customer.description,
            app_id=str(customer.app_id),  # type: ignore
            tenant_id=str(customer.tenant_id),  # type: ignore
            balance=customer.balance,
        )

    @classmethod
    async def from_secret(cls, secret: "CustomerSecret") -> "CustomerEntity":
        await secret.fetch_related("customer")
        return cls(
            id=secret.customer.id,
            code=secret.customer.code,
            name=secret.customer.name,
            description=secret.customer.description,
            app_id=str(secret.customer.app_id),  # type: ignore
            tenant_id=str(secret.customer.tenant_id),  # type: ignore
            balance=secret.customer.balance,
            current_secrect=await CustomerSecretEntity.from_model(secret),
        )
