# 部署配置说明

本目录包含了基于"模块化单体架构"的独立服务部署配置，支持 HTTP API 服务的独立部署与管理。

## 📁 目录结构

```
deploys/
├── baseapi/                # baseapi 服务部署配置
│   ├── main.py            # 服务启动入口
│   ├── docker-compose.yml # Docker Compose 配置
│   ├── deploy.sh          # 部署脚本
│   └── .gitlab-ci.yml     # CI/CD 配置
├── openapi/               # openapi 服务部署配置  
│   ├── main.py            # 服务启动入口
│   ├── docker-compose.yml # Docker Compose 配置
│   ├── deploy.sh          # 部署脚本
│   └── .gitlab-ci.yml     # CI/CD 配置
├── consumer/              # consumer 服务部署配置
│   ├── main.py            # 服务启动入口
│   ├── docker-compose.yml # Docker Compose 配置
│   ├── deploy.sh          # 部署脚本
│   └── .gitlab-ci.yml     # CI/CD 配置
├── scheduler/             # scheduler 服务部署配置
│   ├── main.py            # 服务启动入口
│   ├── docker-compose.yml # Docker Compose 配置
│   ├── deploy.sh          # 部署脚本
│   └── .gitlab-ci.yml     # CI/CD 配置
└── README.md              # 本说明文档
```

## 🚀 快速开始

### 本地开发运行

```bash
# 运行 baseapi 服务
poetry run python deploys/baseapi/main.py

# 运行 openapi 服务  
poetry run python deploys/openapi/main.py

# 运行 consumer 服务
poetry run python deploys/consumer/main.py

# 运行 scheduler 服务
poetry run python deploys/scheduler/main.py
```

### Docker 容器化部署

```bash
# 部署 baseapi 服务
cd deploys/baseapi
./deploy.sh

# 部署 openapi 服务
cd deploys/openapi  
./deploy.sh

# 部署 consumer 服务
cd deploys/consumer
./deploy.sh

# 部署 scheduler 服务
cd deploys/scheduler
./deploy.sh
```

## 🔧 配置说明

### 服务端口配置

- **baseapi**: `5007:8000` (外部端口5007，内部端口8000)
- **openapi**: `5012:8000` (外部端口5012，内部端口8000)  
- **consumer**: 无对外端口（后台服务）
- **scheduler**: 无对外端口（后台服务）

### 环境变量

每个服务支持以下环境变量：

- `APP_NAME`: 应用名称 (baseapi/openapi/consumer/scheduler)
- `SERVICE_TYPE`: 服务类型 (baseapi/openapi/consumer/scheduler)
- `DOCKER_IMAGE_TAG`: Docker镜像标签
- `CI_COMMIT_SHA`: Git提交SHA
- `CI_COMMIT_BRANCH`: Git分支名

### Docker 镜像

默认使用镜像：`g-uzrj2377-docker.pkg.coding.net/beaver-backend/backend/backend-service:latest`

可通过 `BACKEND_IMAGE` 环境变量覆盖。

## 📋 GitLab CI/CD 部署流程

### CI/CD 架构说明

项目采用**模块化 CI/CD 配置**，主 CI 文件通过 `include` 引用各服务的独立配置：

```yaml
# .gitlab-ci.yml (主配置)
include:
  - local: 'deploys/baseapi/.gitlab-ci.yml'
  - local: 'deploys/openapi/.gitlab-ci.yml'
  - local: 'deploys/consumer/.gitlab-ci.yml'
  - local: 'deploys/scheduler/.gitlab-ci.yml'
```

### 部署任务分类

#### 🔧 独立微服务部署
**Alpha 环境**:
- `deploy-baseapi-alpha`: 部署 baseapi 服务到测试环境
- `deploy-openapi-alpha`: 部署 openapi 服务到测试环境
- `deploy-consumer-alpha`: 部署 consumer 服务到测试环境
- `deploy-scheduler-alpha`: 部署 scheduler 服务到测试环境

**Preview 环境**:  
- `deploy-baseapi-preview`: 部署 baseapi 服务到预览环境
- `deploy-openapi-preview`: 部署 openapi 服务到预览环境
- `deploy-consumer-preview`: 部署 consumer 服务到预览环境
- `deploy-scheduler-preview`: 部署 scheduler 服务到预览环境

**Production 环境**:
- `deploy-baseapi-prod`: 部署 baseapi 服务到生产环境  
- `deploy-openapi-prod`: 部署 openapi 服务到生产环境
- `deploy-consumer-prod`: 部署 consumer 服务到生产环境
- `deploy-scheduler-prod`: 部署 scheduler 服务到生产环境

### 部署依赖关系

```
build-image -> deploy-push (仅 master/release-*)
    ├── deploy-baseapi-alpha (manual, 所有分支)
    ├── deploy-openapi-alpha (manual, 所有分支)
    ├── deploy-consumer-alpha (manual, 所有分支)
    ├── deploy-scheduler-alpha (manual, 所有分支)
    ├── deploy-baseapi-preview (manual, master/release-*) -> deploy-baseapi-prod (manual)
    ├── deploy-openapi-preview (manual, master/release-*) -> deploy-openapi-prod (manual)
    ├── deploy-consumer-preview (manual, master/release-*) -> deploy-consumer-prod (manual)
    └── deploy-scheduler-preview (manual, master/release-*) -> deploy-scheduler-prod (manual)
```

### 服务部署架构

| 服务名称 | Alpha 环境 | Preview 环境 | Production 环境 | 配置文件位置 |
|---------|-----------|-------------|----------------|-------------|
| **baseapi** | `deploy-baseapi-alpha` | `deploy-baseapi-preview` | `deploy-baseapi-prod` | `deploys/baseapi/` |
| **openapi** | `deploy-openapi-alpha` | `deploy-openapi-preview` | `deploy-openapi-prod` | `deploys/openapi/` |
| **consumer** | `deploy-consumer-alpha` | `deploy-consumer-preview` | `deploy-consumer-prod` | `deploys/consumer/` |
| **scheduler** | `deploy-scheduler-alpha` | `deploy-scheduler-preview` | `deploy-scheduler-prod` | `deploys/scheduler/` |

**优势**:
- ✅ **防误操作**: 避免一键全部部署的误触风险
- ✅ **精确部署**: 按需部署单个服务，更加精准
- ✅ **独立管理**: 各服务配置独立，便于维护
- ✅ **资源优化**: 只启动需要的服务，节省资源
- ✅ **项目隔离**: 各服务使用独立的项目名称，避免相互干扰

### Docker Compose 项目命名

为避免服务间相互影响，各部署脚本使用不同的项目名称：

```bash
# 根目录部署脚本（传统完整部署）
PROJECT_NAME=${PROJECT_NAME:-"beaver_base"}

# baseapi 独立部署脚本
PROJECT_NAME="baseapi"

# openapi 独立部署脚本  
PROJECT_NAME="openapi"

# consumer 独立部署脚本
PROJECT_NAME="consumer"

# scheduler 独立部署脚本
PROJECT_NAME="scheduler"
```

**命名策略说明**:
- `beaver_base`: 传统完整服务部署
- `baseapi`: baseapi 独立服务部署
- `openapi`: openapi 独立服务部署
- `consumer`: consumer 独立服务部署
- `scheduler`: scheduler 独立服务部署

这确保了：
- 🔒 **服务隔离**: 部署单个服务不会影响其他服务
- 🎯 **精确控制**: 可以独立管理每个服务的生命周期
- 🛡️ **故障隔离**: 单服务重启不会影响整体系统

## 🛠️ 部署脚本参数

各服务的 `deploy.sh` 脚本支持以下参数：

```bash
./deploy.sh [选项]

选项:
  --image-tag TAG    指定Docker镜像标签（默认从配置文件读取）
  --help, -h         显示帮助信息
  --version, -v      显示脚本版本信息

示例:
  ./deploy.sh                     # 使用默认配置部署
  ./deploy.sh --image-tag abc123  # 使用指定镜像标签部署
```

## 📝 日志与监控

### 日志位置

- 容器日志：`/app/logs` (挂载到宿主机 `./logs`)
- 部署日志：`../../deployments/logs/deploy.log`

### 服务状态检查

```bash
# 查看 baseapi 服务状态
docker compose -p baseapi ps
docker compose -p baseapi logs -f baseapi

# 查看 openapi 服务状态  
docker compose -p openapi ps
docker compose -p openapi logs -f openapi

# 查看 consumer 服务状态
docker compose -p consumer ps  
docker compose -p consumer logs -f consumer

# 查看 scheduler 服务状态
docker compose -p scheduler ps
docker compose -p scheduler logs -f scheduler

# 查看所有服务状态（传统完整部署）
docker compose -p beaver_base ps
```

## ⚠️ 注意事项

1. **配置文件**: 确保项目根目录存在 `.env.toml` 配置文件
2. **项目命名**: 各服务使用独立的 Docker Compose 项目名称，避免相互影响
3. **数据持久化**: 日志目录已配置持久化挂载
4. **安全**: 生产环境部署时请确保镜像仓库访问权限正确配置
5. **CI/CD 架构**: 
   - 主 CI 文件 (`.gitlab-ci.yml`) 负责通用任务（构建、lint、代码推送）
   - 各服务 CI 文件 (`deploys/*/gitlab-ci.yml`) 负责独立服务部署
   - 通过 `include` 机制实现模块化管理
   - **已移除传统一键全部部署**，避免误操作风险

## 🔍 故障排查

### 常见问题

1. **镜像拉取失败**: 检查 Docker 镜像仓库连接和认证信息
2. **端口占用**: 确认服务端口没有被其他进程占用
3. **配置文件缺失**: 检查 `.env.toml` 文件是否存在且格式正确
4. **权限问题**: 确保部署脚本有执行权限 (`chmod +x deploy.sh`)
5. **服务互相干扰**: ✅ 已解决 - 各服务使用独立项目名称，避免相互停止

### 日志查看

```bash
# 查看部署日志
tail -f deployments/logs/deploy.log

# 查看服务运行日志  
docker logs baseapi    # baseapi 服务日志
docker logs openapi     # openapi 服务日志
docker logs consumer    # consumer 服务日志
docker logs scheduler   # scheduler 服务日志
```