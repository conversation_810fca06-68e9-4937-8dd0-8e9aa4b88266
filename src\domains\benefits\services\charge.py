# # encoding: utf-8
# <AUTHOR> <EMAIL>
# # domains/benefits/services/charge.py
# # created: 2024-12-04 02:14:34
# # updated: 2025-02-09 23:11:20

import time

from loguru import logger

# from src.infrastructures.rabbitmq import publish_message
from src.databases.models.benefits import (
    BenefitsProduct,
    BenefitsProductOrder,
    BenefitsProductOrderStatus,
    BenefitsSkuChargeRecordStatus,
)
from src.domains.benefits.messages import ChargeSkuMessage, NoticeChargeResultMessage
from src.infrastructures import errors
from src.repositories.benefits import ProductOrderRepository, SkuRepository
from src.repositories.benefits.sku_charge_record import SkuChargeRecordRepository

from ..dto import BenefitsProductOrderDTO
from .charge_strategy import ChargeStrategyFactory


class BenefitsChargeService:

    @classmethod
    # todo: wait for refactor
    async def recharge_order(cls, order_id: str, notify_url: str) -> BenefitsProductOrderDTO:  # type: ignore
        """重新充值订单"""
        order = await ProductOrderRepository.get_by_order_id(order_id)
        if not order:
            logger.error(f"重新充值的订单不存在: {order_id}")
            raise errors.ChargeOrderNotFoundError
        if order.status != BenefitsProductOrderStatus.FAILED:
            logger.error(f"重新充值的订单状态不是失败: {order_id}, {order.status}")
            raise errors.ChargeOrderNotFailedError

        order.status = BenefitsProductOrderStatus.PROCESSING
        order.notify_url = notify_url
        await order.save()
        logger.info(f"重新充值的订单状态更新为处理中: {order_id}, {notify_url}")

        # 重新生成skus
        skus = await SkuRepository.gets_by_product_code(order.product_code)
        for sku in skus:
            sku_charge_record = await SkuChargeRecordRepository.create_record(
                sku=sku,
                charge_order_id=str(order.order_id),
                account=order.account,
                source_identify=order.source_identify,
            )
            _ = ChargeSkuMessage(
                record_id=sku_charge_record.id,
                account=order.account,
            )
            # todo: 需要更新
            # await publish_message(
            #     message.model_dump_json(),
            #     routing_key="benefits.charge_sku",
            #     message_id=f"BENEFITS_RECHARGE_SKU_{sku_charge_record.id}_{int(time.time())}",
            # )
        return await BenefitsProductOrderDTO.from_tortoise_orm(order)
