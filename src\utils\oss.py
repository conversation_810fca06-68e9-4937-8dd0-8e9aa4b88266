# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# utils/oss.py
# created: 2025-01-12 12:00:00
# updated: 2025-01-12 12:00:00

import base64
import hashlib
import hmac
from datetime import datetime
from typing import Dict, Optional

import aiohttp
from loguru import logger

from src.infrastructures.config_compat import load_config


class OSSUploader:
    """阿里云OSS上传工具类"""

    def __init__(self):
        self.config = load_config()
        self.oss_config = self.config.thirdpart.aliyun.oss

    async def upload_file(
        self,
        bucket_name: str,
        endpoint: str,
        filename: str,
        file_content: bytes,
        content_type: str = "application/octet-stream",
        object_acl: str = "public-read",
        extra_headers: Optional[Dict[str, str]] = None,
    ) -> str:
        """
        上传文件到OSS

        Args:
            bucket_name: OSS bucket名称
            endpoint: OSS endpoint地址
            filename: 文件路径/名称
            file_content: 文件内容(字节)
            content_type: 文件MIME类型
            object_acl: 对象访问权限
            extra_headers: 额外的HTTP头部

        Returns:
            str: 文件的公开访问链接

        Raises:
            Exception: 上传失败时抛出异常
        """
        # 生成签名所需的时间和MD5
        date_str = datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")
        content_md5 = base64.b64encode(hashlib.md5(file_content).digest()).decode()

        # OSS特定头部
        oss_headers = {"x-oss-object-acl": object_acl}
        if extra_headers:
            # 只保留以x-oss-开头的头部用于签名
            for key, value in extra_headers.items():
                if key.lower().startswith("x-oss-"):
                    oss_headers[key.lower()] = value

        # 构建CanonicalizedOSSHeaders
        canonicalized_oss_headers = ""
        if oss_headers:
            sorted_headers = sorted(oss_headers.items())
            canonicalized_oss_headers = "\n".join([f"{k}:{v}" for k, v in sorted_headers]) + "\n"

        # 构建签名字符串
        string_to_sign = (
            f"PUT\n{content_md5}\n{content_type}\n{date_str}\n{canonicalized_oss_headers}/{bucket_name}/{filename}"
        )

        # 生成签名
        signature = base64.b64encode(
            hmac.new(self.oss_config.access_key_secret.encode(), string_to_sign.encode(), hashlib.sha1).digest()
        ).decode()

        # 构建Authorization头
        authorization = f"OSS {self.oss_config.access_key_id}:{signature}"

        # 准备请求头
        headers = {
            "Authorization": authorization,
            "Content-Type": content_type,
            "Content-MD5": content_md5,
            "Date": date_str,
            **oss_headers,
        }

        # 添加额外头部(非OSS特定头部)
        if extra_headers:
            for key, value in extra_headers.items():
                if not key.lower().startswith("x-oss-"):
                    headers[key] = value

        # 上传文件
        url = f"https://{bucket_name}.{endpoint}/{filename}"

        async with aiohttp.ClientSession() as session:
            async with session.put(url, data=file_content, headers=headers) as response:
                if response.status == 200:
                    file_url = f"https://{bucket_name}.{endpoint}/{filename}"
                    logger.info(f"OSS文件上传成功: {filename} -> {file_url}")
                    return file_url
                else:
                    error_text = await response.text()
                    logger.error(f"OSS文件上传失败: {response.status}, {error_text}")
                    raise Exception(f"OSS上传失败: HTTP {response.status} - {error_text}")


# 创建全局实例
oss_uploader = OSSUploader()


async def upload_to_oss(
    bucket_name: str,
    endpoint: str,
    filename: str,
    file_content: bytes,
    content_type: str = "application/octet-stream",
    object_acl: str = "public-read",
    extra_headers: Optional[Dict[str, str]] = None,
) -> str:
    """
    上传文件到OSS的便捷函数

    Args:
        bucket_name: OSS bucket名称
        endpoint: OSS endpoint地址
        filename: 文件路径/名称
        file_content: 文件内容(字节)
        content_type: 文件MIME类型
        object_acl: 对象访问权限
        extra_headers: 额外的HTTP头部

    Returns:
        str: 文件的公开访问链接

    Raises:
        Exception: 上传失败时抛出异常
    """
    return await oss_uploader.upload_file(
        bucket_name=bucket_name,
        endpoint=endpoint,
        filename=filename,
        file_content=file_content,
        content_type=content_type,
        object_acl=object_acl,
        extra_headers=extra_headers,
    )
