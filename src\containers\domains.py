# encoding: utf-8
# src/containers/domains.py
# created: 2025-07-27 16:08:58

from typing import TYPE_CHECKING

from dependency_injector import containers, providers

from src.domains.customer.services import CustomerBenefitService

from .infrastructures import Infrastructures

if TYPE_CHECKING:
    from .repositories import Repositories


class Domains(containers.DeclarativeContainer):

    repositories: "Repositories" = providers.DependenciesContainer()  # type: ignore
    infrastructures: "Infrastructures" = providers.DependenciesContainer()  # type: ignore

    # growth hacker domains
    from src.domains.growth_tasks.services import TaskDomainService, UserProfileDomainService

    task_domain_service = providers.Singleton(
        TaskDomainService,
        task_repository=repositories.gh_task_repository,
    )
    user_profile_domain_service = providers.Factory(
        UserProfileDomainService,
        user_repository=repositories.gh_user_repository,
    )

    # benefits domains
    from src.domains.benefits.services import (
        BenefitsProductService,
        BenefitsSkuService,
        BenefitsSupplierService,
        ProductOrderService,
    )

    benefits_sku_service = providers.Singleton(
        BenefitsSkuService,
        sku_repo=repositories.benefits_sku_repository,
        sku_charge_record_repo=repositories.benefits_sku_charge_record_repository,
        purchase_repo=repositories.benefits_purchase_repository,
        producer=infrastructures.rabbitmq_producer,
    )
    benefits_product_service = providers.Singleton(
        BenefitsProductService,
        product_repo=repositories.benefits_product_repository,
        pdorder_repo=repositories.benefits_pdorder_repository,
        sku_charge_record_repo=repositories.benefits_sku_charge_record_repository,
        sku_service=benefits_sku_service,
        producer=infrastructures.rabbitmq_producer,
    )
    benefits_product_order_service = providers.Singleton(
        ProductOrderService,
        redis_client=infrastructures.redis_manager,
        producer=infrastructures.rabbitmq_producer,
    )
    benefits_supplier_service = providers.Singleton(
        BenefitsSupplierService,
        supplier_repo=repositories.benefits_supplier_repository,
        sku_repo=repositories.benefits_sku_repository,
        purchase_repo=repositories.benefits_purchase_repository,
    )

    # customer domains
    customer_benefit_service = providers.Singleton(
        CustomerBenefitService,
        benefits_product_service=benefits_product_service,
        bsku_service=benefits_sku_service,
        benefits_repo=repositories.customer_benefits_repository,  # 修复名称不匹配
        customer_repo=repositories.customer_repository,  # 修复名称：应该是 customer_repository
    )

    # delivery domains
    from src.domains.delivery.services import DeliveryOrderService, DeliveryPageService, DeliveryUserService

    delivery_page_service = providers.Singleton(DeliveryPageService)
    delivery_user_service = providers.Singleton(
        DeliveryUserService, redis_manager=infrastructures.redis_manager, producer=infrastructures.rabbitmq_producer
    )
    delivery_order_service = providers.Singleton(
        DeliveryOrderService,
        redis_manager=infrastructures.redis_manager,
        producer=infrastructures.rabbitmq_producer,
    )

    # activity domains
    from src.domains.activity.services import ElemeActivityService

    eleme_activity_service = providers.Singleton(ElemeActivityService)

    # passport domains
    from src.domains.passport.services import UserService as PassportUserService

    user_service = providers.Singleton(
        PassportUserService,
        user_repository=repositories.passport_user_repository,
        app_repository=repositories.passport_app_repository,
        tenant_repository=repositories.passport_tenant_repository,
    )
