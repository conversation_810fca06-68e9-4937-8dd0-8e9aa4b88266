# encoding: utf-8
# src/infrastructures/exceptions/base.py
# created: 2025-07-27 09:40:31

from src.infrastructures.errors import BusinessError


class RetryableError(Exception):
    """可重试异常基类"""

    def __init__(self, message: str, retryable: bool = True):
        self.message = message
        self.retryable = retryable


class ApplicationError(BusinessError):
    """应用异常基类"""

    def __init__(self, message: str, code: int = 10000):
        super().__init__(message=message, code=code)
