# encoding: utf-8
# src/domains/passport/entities/app.py
# created: 2025-07-31 17:43:27

from typing import TYPE_CHECKING

from pydantic import BaseModel

if TYPE_CHECKING:
    from src.databases.models.passport import PassportApp


class AppEntity(BaseModel):

    id: int
    app_id: str
    app_name: str
    app_secret: str

    wechat_app_id: str
    wechat_app_secret: str
    dingtalk_app_id: str
    dingtalk_app_secret: str
    alipay_aes_key: str

    @classmethod
    async def from_model(cls, model: "PassportApp") -> "AppEntity":
        return cls(
            id=model.id,
            app_id=model.app_id,
            app_name=model.name,
            app_secret=model.app_secret,
            dingtalk_app_id=model.dingtalk_app_id,
            dingtalk_app_secret=model.dingtalk_app_secret,
            wechat_app_id=model.wechat_app_id,
            wechat_app_secret=model.wechat_app_secret,
            alipay_aes_key=model.alipay_aes_key,
        )
