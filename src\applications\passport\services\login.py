# encoding: utf-8
# src/applications/passport/services/login.py
# created: 2025-08-01 07:47:03

import time
from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.domains.passport.dto import AlipayDecryptContentDTO, DingtalkUserInfoDTO, WechatUserInfoDTO
from src.infrastructures.gateways.alipay import AlipayMpGateway
from src.infrastructures.gateways.aliyun import AliyunSmsRateLimitError
from src.infrastructures.gateways.dingtalk import DingtalkGateway
from src.infrastructures.gateways.wechat import WechatError, WechatMpGateway

from ..dto import UserInfoDTO
from ..errors import DingtalkLoginError, SmsSendTooFrequentError, SmsVerificationCodeIncorrectError

if TYPE_CHECKING:
    from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
    from src.domains.passport.services import UserService
    from src.infrastructures.databases import RedisManager
    from src.infrastructures.gateways.aliyun import AliyunSmsGateway
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class LoginService:

    def __init__(
        self,
        user_repository: "UserRepository",
        app_repository: "AppRepository",
        tenant_repository: "TenantRepository",
        redis: "RedisManager",
        user_domain_service: "UserService",
        sms_gateway: "AliyunSmsGateway",
    ):
        self.user_repository = user_repository
        self.app_repository = app_repository
        self.tenant_repository = tenant_repository
        self.redis = redis.client
        self.user_domain_service = user_domain_service
        self.sms_gateway = sms_gateway

    async def check_login(
        self, current_user: "UserEntity", app: "AppEntity", tenant: Optional["TenantEntity"]
    ) -> "UserInfoDTO":
        return UserInfoDTO.from_user_entity(current_user)

    async def send_sms_code(self, app: "AppEntity", phone: str, repeat_interval: int = 60) -> int:
        resend_limit = repeat_interval
        existing_code = await self.redis.get(f"verification_code:{app.app_id}:{phone}")

        # 如果验证码存在且距离上次发送时间小于重复发送间隔，则抛出错误
        if existing_code and int(existing_code.split(":")[1]) + resend_limit > int(time.time()):
            logger.warning(f"sms send too frequent: {existing_code}, app: [{app.app_id}]{app.app_name}, phone: {phone}")
            raise SmsSendTooFrequentError

        verification_code = self.sms_gateway.generate_verification_code()
        try:
            await self.sms_gateway.send_verification_code(phone, verification_code)
            await self.redis.set(
                f"verification_code:{app.app_id}:{phone}",
                f"{verification_code}:{int(time.time())}",
                ex=60 * 5,
            )
            return int(time.time() + 60 * 5)
        except AliyunSmsRateLimitError as e:
            raise SmsSendTooFrequentError from e
        except Exception as e:
            logger.error(f"发送短信失败: {e}")
            raise e

    async def sms_login(
        self, app: "AppEntity", tenant: Optional["TenantEntity"], phone: str, verify_code: str
    ) -> "UserInfoDTO":
        """短信登录策略"""
        stored_code = await self.redis.get(f"verification_code:{app.app_id}:{phone}")
        if not stored_code:
            logger.warning(f"sms login failed, verify code not exists: {phone}, {verify_code}")
            raise SmsVerificationCodeIncorrectError
        stored_code = stored_code.decode("utf-8")
        if not stored_code or stored_code.split(":")[0] != verify_code:
            logger.warning(f"sms login failed: {stored_code.split(':')[0]}, {verify_code}")
            raise SmsVerificationCodeIncorrectError

        user_entity = await self.user_domain_service.get_or_create_phone_user(phone, app, tenant)
        await self.redis.delete(f"verification_code:{app.app_id}:{phone}")
        logger.info(f"sms login success: {user_entity.nickname}-{user_entity.phone}-{user_entity.uid}")
        return UserInfoDTO.from_user_entity(user_entity)

    async def dingtalk_login(self, app: "AppEntity", tenant: Optional["TenantEntity"], auth_code: str) -> "UserInfoDTO":
        """钉钉登录策略"""
        dingtalk_gateway = DingtalkGateway(app_id=app.dingtalk_app_id, app_secret=app.dingtalk_app_secret)
        access_token = await dingtalk_gateway.get_user_token(auth_code)
        if not access_token:
            logger.error(f"Get dingtalk user token failed: can't get access token, auth_code: {auth_code}")
            raise DingtalkLoginError

        dingtalk_user_info = await dingtalk_gateway.get_userinfo(access_token)

        dingtalk_user = DingtalkUserInfoDTO(
            union_id=dingtalk_user_info.union_id,
            open_id=dingtalk_user_info.open_id,
            nickname=dingtalk_user_info.nick,
            avatar_url=dingtalk_user_info.avatar_url,
            mobile=dingtalk_user_info.mobile,
            email=dingtalk_user_info.email,
            state_code=dingtalk_user_info.state_code,
        )
        logger.info(f"Get dingtalk user info: {dingtalk_user.model_dump()}")

        user_entity = await self.user_domain_service.get_or_create_dingtalk_user(dingtalk_user, app, tenant)
        return UserInfoDTO.from_user_entity(user_entity)

    async def wechat_mp_login(
        self,
        app: "AppEntity",
        code: str,
        phone_code: Optional[str] = None,
        tenant: Optional["TenantEntity"] = None,
    ) -> "UserInfoDTO":
        """微信小程序登录策略"""
        """
        微信小程序登录

        :param code: 微信小程序登录code
        :param app: 应用
        :param tenant: 租户, 可选
        :param phone_code: 手机号授权code, 可选
        :return: 返回token、session_key、用户信息和是否为新用户的元组
        """

        wechat_mp_gateway = WechatMpGateway(app_id=app.wechat_app_id, app_secret=app.wechat_app_secret)
        # 通过login_code获取微信用户基本信息
        try:
            login_result = await wechat_mp_gateway.code2session(code)
            wechat_openid = login_result.openid
            wechat_unionid = login_result.unionid
            logger.info(f"微信小程序登录成功获取session: openid={wechat_openid}")
        except WechatError as e:
            logger.error(f"微信小程序登录失败: {e}")
            raise ValueError(f"微信小程序登录失败: {e.errmsg}") from e

        # 如果提供了phone_code，获取用户手机号
        phone_number = None
        country_code = None
        if phone_code:
            try:
                phone_result = await wechat_mp_gateway.get_phone_number(phone_code)
                phone_number = phone_result.pure_phone_number
                country_code = phone_result.country_code
                logger.info(f"获取用户手机号成功: {phone_number}")
            except WechatError as e:
                logger.error(f"获取用户手机号失败: {e}")
                raise ValueError(f"获取用户手机号失败: {e.errmsg}") from e

        # 如果没有手机号，抛出异常
        if not phone_number:
            raise ValueError("手机号是必需的")

        # 创建微信用户信息DTO
        wechat_user_info = WechatUserInfoDTO(
            open_id=wechat_openid,
            union_id=wechat_unionid,
            phone=phone_number,
            country_code=country_code,
            nickname=f"{phone_number[:3]}****{phone_number[7:]}",  # 使用隐藏中间4位的手机号作为昵称
            avatar_url=None,
        )

        user_entity = await self.user_domain_service.get_or_create_wechat_user(
            wechat_user_info, app, tenant
        )  # 获取或创建用户
        return UserInfoDTO.from_user_entity(user_entity)

    async def alipay_mp_login(
        self,
        app: "AppEntity",
        encrypted_data: str,
        tenant: Optional["TenantEntity"] = None,
    ) -> "UserInfoDTO":
        """支付宝小程序登录策略"""
        alipay_mp_gateway = AlipayMpGateway(alipay_aes_key=app.alipay_aes_key)
        # 解密支付宝加密数据
        decrypted_content = alipay_mp_gateway.alipay_aes_decrypt_base64(encrypted_data, app.alipay_aes_key, "utf-8")

        # 将解密后的内容转换为DTO对象
        content_dto = AlipayDecryptContentDTO.model_validate(decrypted_content)

        # 使用手机号获取或创建用户
        user_entity = await self.user_domain_service.get_or_create_phone_user(content_dto.mobile, app, tenant)
        return UserInfoDTO.from_user_entity(user_entity)
