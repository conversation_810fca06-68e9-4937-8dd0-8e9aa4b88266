# encoding: utf-8

"""
脚本管理器
统一管理JavaScript脚本文件的加载和渲染
"""

import json
from pathlib import Path
from typing import Dict

from jinja2 import Template
from loguru import logger

# 脚本文件目录
SCRIPTS_DIR = Path(__file__).parent / "scripts"


class ScriptManager:
    """JavaScript脚本管理器"""

    def __init__(self) -> None:
        self.scripts_cache: Dict[str, str] = {}
        self._load_scripts()

    def _load_scripts(self) -> None:
        """加载所有脚本文件到缓存"""
        script_files = {
            "init_template": "init.template.js",
        }

        for name, filename in script_files.items():
            script_path = SCRIPTS_DIR / filename
            if script_path.exists():
                with open(script_path, "r", encoding="utf-8") as f:
                    self.scripts_cache[name] = f.read()
                # logger.debug(f"已加载脚本: {filename}")
            else:
                logger.info(f"脚本文件不存在: {script_path}")

    def get_init_script(
        self,
        screen_width: int,
        screen_height: int,
        local_storage: Dict[str, str],
        session_storage: Dict[str, str],
    ) -> str:
        """
        获取渲染后的初始化脚本

        Args:
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            local_storage: localStorage数据
            session_storage: sessionStorage数据

        Returns:
            渲染后的JavaScript代码
        """
        template_content = self.scripts_cache.get("init_template", "")
        if not template_content:
            logger.info("init_template脚本未找到")
            return ""

        try:
            template = Template(template_content)
            return template.render(
                screen_width=screen_width,
                screen_height=screen_height,
                local_storage=json.dumps(local_storage, ensure_ascii=False),
                session_storage=json.dumps(session_storage, ensure_ascii=False),
            )
        except Exception as e:
            logger.info(f"渲染初始化脚本失败: {e}")
            return ""

    def reload_scripts(self) -> None:
        """重新加载所有脚本"""
        self.scripts_cache.clear()
        self._load_scripts()
        logger.info("脚本已重新加载")

    def list_available_scripts(self) -> list[str]:
        """列出所有可用的脚本"""
        return list(self.scripts_cache.keys())


# 创建全局脚本管理器实例
script_manager = ScriptManager()
