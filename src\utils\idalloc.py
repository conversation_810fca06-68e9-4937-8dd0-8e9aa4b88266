# encoding: utf-8
# <AUTHOR> <EMAIL>
# utils/idalloc.py
# created: 2025-03-21 13:35:49
# updated: 2025-05-04 20:39:48

from typing import Callable

import nanoid


def id_alloc_factor(
    length: int = 16,
    prefix: str | Callable = "",
    alphabet: str = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
) -> Callable[[], str]:
    """
    ID分配器工厂函数,返回一个生成唯一ID的函数

    Args:
        length: ID长度(不包括前缀),默认为16
        prefix: ID前缀,默认为空字符串
        alphabet: 用于生成ID的字符集,默认为数字和字母

    Returns:
        返回一个无参数函数,调用时生成带有指定前缀的唯一ID
    """

    def id_generator() -> str:
        """生成带有指定前缀的唯一ID"""
        random_id = nanoid.generate(size=length, alphabet=alphabet)
        if callable(prefix):
            return f"{prefix()}{random_id}"
        return f"{prefix}{random_id}"

    return id_generator


# 使用示例
# 生成一个10位数字ID分配器
# numeric_id_generator = id_alloc_factor(10, alphabet="0123456789")
#
# # 生成一个带有ORD前缀的16位订单ID分配器
# order_id_generator = id_alloc_factor(16, prefix="ORD-")
