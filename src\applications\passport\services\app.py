# encoding: utf-8
# src/applications/passport/services/app.py
# created: 2025-07-31 17:10:00

from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.domains.passport.entities import AppEntity, TenantEntity

from ..dto import AppInfoDTO, TenantInfoDTO
from ..errors import AppNotFoundError, DuplicateAppError

if TYPE_CHECKING:
    from src.repositories.passport import AppRepository, TenantRepository


class AppService:
    """应用命令服务"""

    def __init__(self, app_repository: "AppRepository", tenant_repository: "TenantRepository"):
        self.app_repository = app_repository
        self.tenant_repository = tenant_repository

    async def create_tenant_for_app(self, app: "AppEntity", tenant_name: str) -> TenantInfoDTO:
        """为应用创建租户"""

        tenant_entity = TenantEntity.create_tenant(tenant_name, app)

        await self.tenant_repository.save(tenant_entity)
        logger.info(f"为应用 {app.app_id} 创建租户: {tenant_name}")
        return TenantInfoDTO(
            tenant_id=tenant_entity.tenant_id,
            tenant_name=tenant_entity.tenant_name,
            app_id=app.app_id,
            app_name=app.app_name,
        )
