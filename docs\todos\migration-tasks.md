# 模块化单体架构迁移任务清单

## 概述
本文档定义了将现有系统迁移到模块化单体架构的详细任务清单。任务按优先级和依赖关系排序，适合AI Agent逐步执行。

## 任务分类

### 第一阶段：评估与准备（高优先级）

#### 1. 评估现有系统结构与目标架构的差异
- [ ] 分析现有目录结构
- [ ] 识别需要迁移的模块
- [ ] 评估代码复杂度和依赖关系
- [ ] 生成差异报告

#### 2. 创建新架构的基础目录结构
- [ ] 创建 src/ 目录
- [ ] 创建 src/interfaces/http/ 目录
- [ ] 创建 src/interfaces/consumers/ 目录
- [ ] 创建 src/interfaces/schedulers/ 目录
- [ ] 创建 src/applications/ 目录
- [ ] 创建 src/domains/ 目录
- [ ] 创建 src/repositories/ 目录
- [ ] 创建 src/databases/ 目录
- [ ] 创建 src/infra/ 目录
- [ ] 创建 deploys/ 目录
- [ ] 创建 tests/integration/ 目录
- [ ] 创建 tests/unit/ 目录

### 第二阶段：核心层迁移（高优先级）

#### 3. 迁移 infra 层 - 共享基础设施
- [ ] 迁移 core/config.py 到 src/infra/config/
- [ ] 迁移 core/logger/ 到 src/infra/logger/
- [ ] 迁移 core/middleware/ 到 src/infra/middleware/
- [ ] 迁移 core/errors.py 到 src/infra/exceptions/
- [ ] 迁移 core/containers.py 到 src/infra/containers/
- [ ] 创建基础类和接口定义

#### 4. 迁移 databases 层 - 数据模型
- [ ] 迁移 Tortoise ORM 模型到 src/databases/models/
- [ ] 迁移数据库迁移脚本到 src/databases/migrations/
- [ ] 更新数据库配置
- [ ] 验证模型关系和约束

#### 5. 迁移 repositories 层 - 数据访问
- [ ] 为每个领域创建 repositories 目录
- [ ] 定义数据访问接口 (interfaces.py)
- [ ] 实现具体的数据访问类 (implementations.py)
- [ ] 迁移现有的 repository 代码
- [ ] 添加单元测试

### 第三阶段：业务层迁移（高优先级）

#### 6. 重构 domains 层 - 领域逻辑
- [ ] 迁移 domains/benefits/ 到新结构
- [ ] 迁移 domains/passport/ 到新结构
- [ ] 迁移 domains/delivery/ 到新结构
- [ ] 迁移 domains/customer/ 到新结构
- [ ] 迁移 domains/aiagent/ 到新结构
- [ ] 迁移 domains/activity/ 到新结构
- [ ] 移除对外部协议的依赖
- [ ] 确保领域层纯净性

#### 7. 创建 applications 层 - 应用服务
- [ ] 为每个领域创建 applications 子目录
- [ ] 实现 commands/ 目录（写操作）
- [ ] 实现 queries/ 目录（读操作）
- [ ] 实现 handlers/ 目录（事件处理）
- [ ] 添加事务管理
- [ ] 添加业务编排逻辑

### 第四阶段：接口层实现（中优先级）

#### 8. 创建 interfaces 层 - HTTP API服务
- [ ] 创建 benefits_api 服务
- [ ] 创建 delivery_api 服务
- [ ] 创建 mis_api 服务
- [ ] 迁移现有 API 路由
- [ ] 实现协议转换（HTTP -> Command/Query）
- [ ] 添加鉴权和参数验证
- [ ] 统一错误响应格式

#### 9. 创建 interfaces 层 - 消息消费者服务
- [ ] 迁移 RabbitMQ 消费者
- [ ] 实现消息到命令的转换
- [ ] 添加错误处理和重试机制
- [ ] 配置消息路由

#### 10. 创建 interfaces 层 - 定时任务服务
- [ ] 迁移 APScheduler 任务
- [ ] 实现任务调度逻辑
- [ ] 添加任务监控和日志

### 第五阶段：部署与运维（中优先级）

#### 11. 创建独立部署配置 - deploys目录
- [ ] 为 benefits_api 创建部署配置
  - [ ] main.py（服务入口）
  - [ ] config.py（服务配置）
  - [ ] Dockerfile
  - [ ] .gitlab-ci.yml
- [ ] 为 delivery_api 创建部署配置
- [ ] 为 mis_api 创建部署配置
- [ ] 为 consumers 创建部署配置
- [ ] 为 schedulers 创建部署配置

#### 12. 配置CI/CD流水线 - GitLab CI
- [ ] 创建主 .gitlab-ci.yml
- [ ] 配置基于路径的触发规则
- [ ] 配置服务独立构建流程
- [ ] 配置自动化测试
- [ ] 配置部署流程

### 第六阶段：质量保证（低优先级）

#### 13. 迁移测试用例到新结构
- [ ] 迁移单元测试到 tests/unit/
- [ ] 迁移集成测试到 tests/integration/
- [ ] 更新测试配置和 fixtures
- [ ] 确保测试覆盖率达到60%

#### 14. 更新文档和README
- [ ] 更新项目 README
- [ ] 创建迁移指南
- [ ] 更新 API 文档
- [ ] 创建开发者指南

## 执行指南

### 前置条件
1. 确保所有依赖已安装（poetry install）
2. 备份现有代码和数据库
3. 创建功能分支进行迁移

### 执行顺序
1. 按照任务编号顺序执行
2. 每完成一个阶段进行测试验证
3. 确保每个任务的测试通过后再进行下一个

### 验证标准
- 所有测试通过
- 代码符合 Black 和 Ruff 规范
- API 兼容性保持
- 性能不降低

### 回滚计划
- 保留原有代码结构直到迁移完成
- 使用功能开关逐步切换到新架构
- 准备快速回滚脚本

## 注意事项

1. **保持向后兼容**：在迁移过程中确保现有API的兼容性
2. **逐步迁移**：不要试图一次性迁移所有内容
3. **充分测试**：每个迁移步骤都需要充分的测试
4. **文档先行**：在实施前先更新相关文档
5. **监控先行**：在切换前确保监控和日志就绪

## 成功标准

- [ ] 所有服务可独立部署
- [ ] 代码结构符合模块化单体架构
- [ ] 测试覆盖率达到60%以上
- [ ] CI/CD 流水线正常工作
- [ ] 性能指标达到或超过现有系统
- [ ] 团队可以独立负责不同服务