import json
import os
from typing import Dict, List, Tuple

import numpy as np
from dotenv import load_dotenv

from .embedding import EmbeddingClient


class ActivitySearcher:
    def __init__(self, activity_dir: str):
        """
        初始化活动搜索器

        Args:
            activity_dir: 活动JSON文件目录
        """
        # 加载环境变量
        load_dotenv()

        # 初始化embedding客户端
        self.embedding_client = EmbeddingClient()

        # 加载所有活动数据
        self.activities: List[Dict] = []
        self.vectors: List[List[float]] = []
        self._load_activities(activity_dir)

    def _load_activities(self, activity_dir: str):
        """加载所有活动数据到内存"""
        print("开始加载活动数据...")

        for filename in os.listdir(activity_dir):
            if filename.endswith(".json"):
                file_path = os.path.join(activity_dir, filename)
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        activity_data = json.load(f)

                    # 确保数据包含必要的字段
                    if "data" in activity_data and "vector" in activity_data["data"]:
                        self.activities.append(activity_data)
                        self.vectors.append(activity_data["data"]["vector"])
                except Exception as e:
                    print(f"加载文件 {filename} 时出错: {e}")

        print(f"成功加载 {len(self.activities)} 个活动")

    def _cosine_similarity(self, v1: List[float], v2: List[float]) -> float:
        """计算两个向量的余弦相似度"""
        v1_array = np.array(v1)
        v2_array = np.array(v2)

        dot_product = np.dot(v1_array, v2_array)
        norm_v1 = np.linalg.norm(v1_array)
        norm_v2 = np.linalg.norm(v2_array)

        return dot_product / (norm_v1 * norm_v2)

    def search(self, query: str, top_k: int = 1) -> List[Tuple[Dict, float]]:
        """
        搜索最相似的活动

        Args:
            query: 搜索查询
            top_k: 返回结果数量

        Returns:
            List[Tuple[Dict, float]]: 按相似度排序的(活动数据, 相似度分数)列表
        """
        # 获取查询的向量表示
        try:
            query_vector = self.embedding_client.get_embedding(query)[0]
        except Exception as e:
            print(f"获取查询向量时出错: {e}")
            return []

        # 计算所有活动的相似度
        similarities = []
        for idx, activity_vector in enumerate(self.vectors):
            similarity = self._cosine_similarity(query_vector, activity_vector)
            similarities.append((self.activities[idx], similarity))

        # 按相似度降序排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        # 过滤标题中含有“天天领红包”的优惠券
        similarities = [
            (activity, similarity)
            for activity, similarity in similarities
            if "天天领红包" not in activity["data"]["title"]
        ]
        return similarities[:top_k]


def format_activity(activity: Dict) -> str:
    """格式化活动信息为可读字符串"""
    data = activity.get("data", {})
    return f"""
标题: {data.get('title', 'N/A')}
描述: {data.get('description', 'N/A')}
"""


def search_activity(query: str, top_k: int = 1) -> List[Tuple[Dict, float]]:
    activity_searcher = ActivitySearcher("apis/ai/activitylist")
    return activity_searcher.search(query, top_k)
