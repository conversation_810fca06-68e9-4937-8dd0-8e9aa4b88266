# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/sku.py
# created: 2025-03-10 00:55:16
# updated: 2025-04-06 14:10:51

from typing import List, Optional, Sequence, Tuple

from pydantic import BaseModel, Field

from src.databases.models.benefits import BenefitsProductSku, BenefitsSku, BenefitsSupplier
from src.domains.benefits.dto import BenefitsSkuCreateFields, BenefitsSkuUpdateDTO


class SkuFilters(BaseModel):
    name: Optional[str] = Field(None, description="SKU名称")
    code: Optional[str] = Field(None, description="SKU编码")
    supplier_id: Optional[int] = Field(None, description="供应商ID")
    enabled: Optional[bool] = Field(None, description="是否启用")
    page: int = Field(default=1, ge=1, description="页码, 最小值为1")
    page_size: int = Field(default=10, ge=1, le=100, description="每页条数, 最小值为1")


class SkuRepository:

    @classmethod
    async def gets_by_codes(cls, sku_codes: List[str]) -> Sequence[BenefitsSku]:
        """根据SKU编码列表获取SKU列表"""
        return await BenefitsSku.filter(code__in=sku_codes).all()

    @classmethod
    async def get_by_third_code(cls, third_part_code: str) -> Optional[BenefitsSku]:
        return await BenefitsSku.filter(third_part_code=third_part_code).first()

    @classmethod
    async def get_by_code(cls, sku_code: str) -> Optional[BenefitsSku]:
        return await BenefitsSku.get_or_none(code=sku_code)

    @classmethod
    async def create_sku(cls, sku: BenefitsSkuCreateFields) -> BenefitsSku:
        sku = await BenefitsSku.create(**sku.model_dump())
        return sku

    @classmethod
    async def update_sku(cls, sku_code: str, update_fields: BenefitsSkuUpdateDTO) -> BenefitsSku | None:
        sku = await BenefitsSku.get_or_none(code=sku_code)
        if not sku:
            return None
        for field, value in update_fields.model_dump().items():
            if value is not None:
                setattr(sku, field, value)
        await sku.save()
        return sku

    @classmethod
    async def get_sku_by_code(cls, sku_code: str) -> Optional[BenefitsSku]:
        return await BenefitsSku.get_or_none(code=sku_code)

    @classmethod
    async def get_skus_by_params(
        cls,
        filters: SkuFilters,
    ) -> Tuple[int, Sequence[BenefitsSku]]:
        """根据参数获取SKU列表"""
        query = BenefitsSku.all()
        if filters.name:
            query = query.filter(name__icontains=filters.name)
        if filters.code:
            query = query.filter(code=filters.code)
        if filters.supplier_id:
            query = query.filter(supplier_id=filters.supplier_id)
        if filters.enabled is not None:
            query = query.filter(enabled=filters.enabled)
        count = await query.count()
        skus = await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).all()
        return count, skus

    @classmethod
    async def delete_by_code(cls, sku_code: str):
        """删除SKU"""
        await BenefitsSku.filter(code=sku_code).delete()

    @classmethod
    async def set_enabled(cls, sku_code: str, enabled: bool) -> BenefitsSku | None:
        """设置SKU是否启用"""
        sku = await BenefitsSku.get_or_none(code=sku_code)
        if sku:
            sku.enabled = enabled
            await sku.save()
        return sku

    @classmethod
    async def gets_by_product_code(cls, product_code: str) -> Sequence[BenefitsSku]:
        """根据产品代码获取关联的SKU清单"""
        product_skus = await BenefitsProductSku.filter(product__code=product_code).prefetch_related("sku").all()
        return [ps.sku for ps in product_skus]

    @classmethod
    async def get_by_third_part_code(cls, third_part_code: str) -> Optional[BenefitsSku]:
        return await BenefitsSku.get_or_none(third_part_code=third_part_code)

    @classmethod
    async def create_or_update_sku(
        cls,
        third_part_code: str,
        name: str,
        stock: int,
        detail: dict,
        charge_func: str,
        supplier: BenefitsSupplier,
    ) -> BenefitsSku:
        sku, created = await BenefitsSku.update_or_create(
            third_part_code=third_part_code,
            defaults={
                "name": name,
                "stock": stock,
                "detail": detail,
                "charge_func": charge_func,
                "supplier": supplier,
            },
        )
        return sku

    @classmethod
    async def update_stock_by_id(cls, sku_id: int, stock: int) -> BenefitsSku | None:
        """更新SKU库存"""
        sku = await BenefitsSku.get_or_none(id=sku_id)
        if sku:
            sku.stock = stock
            await sku.save()
        return sku
