# encoding: utf-8
# src/applications/passport/errors.py
# created: 2025-07-31 17:10:00

from src.infrastructures.exceptions import ApplicationError


class PassportApplicationError(ApplicationError):
    """Passport应用层基础异常"""

    pass


class UserNotFoundError(PassportApplicationError):
    """用户不存在异常"""

    def __init__(self, detail: str = "用户不存在"):
        super().__init__(detail)
        self.code = "USER_NOT_FOUND"


class AppNotFoundError(PassportApplicationError):
    """应用不存在异常"""

    def __init__(self, detail: str = "应用不存在"):
        super().__init__(detail)
        self.code = "APP_NOT_FOUND"


class TenantNotFoundError(PassportApplicationError):
    """租户不存在异常"""

    def __init__(self, detail: str = "租户不存在"):
        super().__init__(detail)
        self.code = "TENANT_NOT_FOUND"


class AuthenticationFailedError(PassportApplicationError):
    """认证失败异常"""

    def __init__(self, detail: str = "认证失败"):
        super().__init__(detail)
        self.code = "AUTHENTICATION_FAILED"


class InvalidTokenError(PassportApplicationError):
    """无效令牌异常"""

    def __init__(self, detail: str = "无效的访问令牌"):
        super().__init__(detail)
        self.code = "INVALID_TOKEN"


class TokenExpiredError(PassportApplicationError):
    """令牌过期异常"""

    def __init__(self, detail: str = "访问令牌已过期"):
        super().__init__(detail)
        self.code = "TOKEN_EXPIRED"


class UserTenantRelationNotFoundError(PassportApplicationError):
    """用户租户关系不存在异常"""

    def __init__(self, detail: str = "用户与租户关系不存在"):
        super().__init__(detail)
        self.code = "USER_TENANT_RELATION_NOT_FOUND"


class DuplicateAppError(PassportApplicationError):
    """重复应用异常"""

    def __init__(self, detail: str = "应用已存在"):
        super().__init__(detail)
        self.code = "DUPLICATE_APP"


class DuplicateTenantError(PassportApplicationError):
    """重复租户异常"""

    def __init__(self, detail: str = "租户已存在"):
        super().__init__(detail)
        self.code = "DUPLICATE_TENANT"


class InvalidLoginStrategyError(PassportApplicationError):
    """无效登录策略异常"""

    def __init__(self, detail: str = "无效的登录策略"):
        super().__init__(detail)
        self.code = "INVALID_LOGIN_STRATEGY"


class PhoneAlreadyExistsError(PassportApplicationError):
    """手机号已存在异常"""

    def __init__(self, detail: str = "手机号已存在"):
        super().__init__(detail)
        self.code = "PHONE_ALREADY_EXISTS"


class UserUidMismatchError(PassportApplicationError):
    """用户UID与手机号不匹配异常"""

    def __init__(self, detail: str = "用户UID与手机号不匹配"):
        super().__init__(detail)
        self.code = "USER_UID_MISMATCH"


class SmsVerificationCodeIncorrectError(PassportApplicationError):
    """短信验证码不正确异常"""

    def __init__(self, detail: str = "短信验证码不正确"):
        super().__init__(detail)
        self.code = "SMS_VERIFICATION_CODE_INCORRECT"


class SmsSendTooFrequentError(PassportApplicationError):
    """短信发送过于频繁异常"""

    def __init__(self, detail: str = "短信发送过于频繁"):
        super().__init__(detail)
        self.code = "SMS_SEND_TOO_FREQUENT"


class DingtalkLoginError(PassportApplicationError):
    """钉钉登录异常"""

    def __init__(self, detail: str = "钉钉登录异常"):
        super().__init__(detail)
        self.code = "DINGTALK_LOGIN_ERROR"
