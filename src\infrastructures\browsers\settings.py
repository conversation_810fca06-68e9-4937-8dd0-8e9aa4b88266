# encoding: utf-8
# src/infrastructures/browsers/settings.py
# created: 2025-07-26 11:24:30

import json
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class BrowserPoolConfig(BaseSettings):
    """浏览器池配置"""

    # 池管理配置
    browser_count: int = Field(default=2, description="浏览器实例数量")
    min_browsers: int = Field(default=1, description="最小浏览器数量")
    max_browsers: int = Field(default=3, description="最大浏览器数量")

    # 浏览器生命周期
    browser_max_usage: int = Field(default=20, description="单个浏览器最大使用次数")
    browser_max_lifetime: int = Field(default=1200, description="浏览器最大存活时间(秒)")
    browser_timeout: int = Field(default=30000, description="浏览器超时时间(毫秒)")

    # 健康检查
    enable_health_check: bool = Field(default=True, description="启用健康检查")
    health_check_interval: int = Field(default=60, description="健康检查间隔(秒)")


class BrowserLaunchConfig(BaseSettings):
    """浏览器启动配置"""

    headless: bool = Field(default=True, description="无头模式")
    browser_args: list[str] = Field(
        default_factory=lambda: [
            "--no-sandbox",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--disable-blink-features=AutomationControlled",
            "--memory-pressure-off",
            "--max_old_space_size=256",
            "--disable-background-networking",
            "--disable-background-timer-throttling",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-default-apps",
            "--mute-audio",
        ],
        description="浏览器启动参数",
    )

    @field_validator("browser_args", mode="before")
    @classmethod
    def parse_browser_args(cls, v):  # type: ignore
        if isinstance(v, str):
            v = v.strip()
            if v.startswith("[") and v.endswith("]"):
                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    pass
            return [arg.strip() for arg in v.split(",") if arg.strip()]
        return v


class BrowserContextConfig(BaseSettings):
    """浏览器上下文默认配置"""

    locale: str = Field(default="zh-CN", description="语言设置")
    timezone_id: str = Field(default="Asia/Shanghai", description="时区")
    permissions: list[str] = Field(
        default_factory=lambda: ["geolocation", "notifications"],
        description="权限列表",
    )
    is_mobile: bool = Field(default=True, description="移动设备模式")
    has_touch: bool = Field(default=True, description="触屏支持")

    # 视口设置
    viewport_width: int = Field(default=375, description="视口宽度")
    viewport_height: int = Field(default=812, description="视口高度")

    # 用户代理
    user_agent: Optional[str] = Field(default=None, description="自定义用户代理")

    @field_validator("permissions", mode="before")
    @classmethod
    def parse_permissions(cls, v):  # type: ignore
        if isinstance(v, str):
            return [p.strip() for p in v.split(",") if p.strip()]
        return v


class ResourceCacheConfig(BaseSettings):
    """资源缓存配置"""

    enable_cache: bool = Field(default=True, description="启用资源缓存")
    cache_static_resources: bool = Field(default=True, description="缓存静态资源")
    cache_api_responses: bool = Field(default=False, description="缓存API响应")
    max_cache_size_mb: int = Field(default=100, description="最大缓存大小(MB)")
    cache_ttl_seconds: int = Field(default=3600, description="缓存过期时间(秒)")

    # 缓存资源类型
    cached_resource_types: list[str] = Field(
        default_factory=lambda: ["image", "stylesheet", "font", "script"],
        description="缓存的资源类型",
    )


class BrowserConfig(BaseSettings):
    """浏览器完整配置 - 整合所有配置项"""

    # 子配置
    pool: BrowserPoolConfig = Field(default_factory=BrowserPoolConfig)
    launch: BrowserLaunchConfig = Field(default_factory=BrowserLaunchConfig)
    context: BrowserContextConfig = Field(default_factory=BrowserContextConfig)
    cache: ResourceCacheConfig = Field(default_factory=ResourceCacheConfig)

    # 向后兼容的快捷属性
    @property
    def headless(self) -> bool:
        return self.launch.headless

    @property
    def max_browsers(self) -> int:
        return self.pool.max_browsers

    @property
    def min_browsers(self) -> int:
        return self.pool.min_browsers

    @property
    def browser_timeout(self) -> int:
        return self.pool.browser_timeout

    @property
    def max_usage_per_browser(self) -> int:
        return self.pool.browser_max_usage

    @property
    def max_lifetime_minutes(self) -> int:
        return self.pool.browser_max_lifetime // 60

    @property
    def enable_health_check(self) -> bool:
        return self.pool.enable_health_check

    @property
    def browser_args(self) -> list[str]:
        return self.launch.browser_args


# 向后兼容的别名
BrowserSettings = BrowserConfig
