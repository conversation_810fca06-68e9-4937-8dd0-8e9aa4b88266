# encoding: utf-8
# deploys/openapi/settings.py
# created: 2025-07-30 12:13:11

from pathlib import Path

from pydantic import Field
from pydantic_settings import SettingsConfigDict

from src.infrastructures.fastapi import FastapiSettings
from src.infrastructures.settings import BaseServiceSettings


class OpenapiSettings(BaseServiceSettings):
    """OpenAPI 服务配置"""

    # OpenAPI 服务特有的配置
    fastapi: FastapiSettings = Field(default_factory=FastapiSettings)

    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",  # 方便嵌套字段注入
    )


config = OpenapiSettings()
