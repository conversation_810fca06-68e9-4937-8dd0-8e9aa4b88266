# encoding: utf-8
# src/interfaces/consumers/benefits/sku_charge.py
# created: 2025-04-06 16:37:36

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.domains.benefits.messages import SkuChargeCheckMessage, SkuChargeMessage, SkuChargeMessageContent
from src.infrastructures.rabbitmq import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import SkuChargeCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class SkuChargeConsumer(BaseConsumer):
    """权益SKU充值消费者"""

    exchange_name = "benefits.topic"
    queue_name = "benefits_sku_charge"
    routing_key = "benefits.sku_charge"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        charge_service: "SkuChargeCommandService",
    ):
        self.charge_service = charge_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = SkuChargeMessage.model_validate_json(message.body)
        payload: SkuChargeMessageContent = msg.payload
        logger.info(f"SkuChargeConsumer 收到消息: record_id={payload.record_id}")

        # 执行充值
        status, need_check = await self.charge_service.execute_charge(payload.record_id)

        if status == RecordStatus.PENDING:
            # 待处理状态，重新发送充值消息
            await self.producer.publish_message(
                SkuChargeMessage(payload=SkuChargeMessageContent(record_id=payload.record_id)),
                exchange_name="benefits.topic",
                delay=1000,
            )
        elif need_check:
            # 需要检查状态
            await self.producer.publish_message(
                SkuChargeCheckMessage(payload=SkuChargeMessageContent(record_id=payload.record_id)),
                exchange_name="benefits.topic",
                delay=1000,
            )

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 充值失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "SkuChargeConsumer 回滚: 消息[{message_id}] 充值失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
