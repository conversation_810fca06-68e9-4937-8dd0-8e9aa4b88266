# 迁移执行计划

## 🎯 执行策略

**核心理念**: 一步到位，直接替换
- API层面：直接平移URL路径，保持完全兼容
- Services层：在新架构基础上重写，不迁移老逻辑
- 数据层：直接使用新的Tortoise模型，简单处理兼容性

## ⏱️ 详细时间表

### Week 1: API接口直接平移 (4天)

#### Day 1-2: 权益充值接口
```python
# 目标: apis/internal/routers/benefits.py
POST /internal/benefit/product/{product_code}/charge
```
- 创建接口文件和数据模型
- 实现充值核心逻辑
- 基础功能测试

#### Day 3: 权益详情接口  
```python
GET /internal/benefit/product/{product_code}
```
- 实现产品查询逻辑
- Channel header兼容处理
- 权限检查机制

#### Day 4: 回调接口
```python
GET /internal/benefit/callback/shinesun/order
```
- 实现回调处理逻辑
- 参数兼容验证
- 集成测试

### Week 2: Services层重写 (5天)

#### Day 1-2: BenefitsProductService 核心方法
- `charge_product()` - 权益充值逻辑
- 集成现有策略工厂和消息队列
- 单元测试覆盖

#### Day 3: 产品查询和权限控制
- `get_product_by_code()` - 产品详情查询
- Channel到Customer映射逻辑
- 权限检查实现

#### Day 4: 回调处理和SDK整合
- `handle_shinesun_callback()` - 回调处理
- SDK迁移整合 (TaoPiaoPiao, ElemeUnion)
- 统一SDK工厂模式

#### Day 5: 数据兼容和异常处理
- 价格单位转换逻辑
- 统一异常处理机制
- 完善日志记录

### Week 3: 集成测试和上线 (2天)

#### Day 1: 集成测试
- Node服务端API调用测试
- 性能基准测试
- 错误场景测试

#### Day 2: 路由切换和清理
- nginx路由配置切换
- Django路由注释清理
- 代码库清理 (`apps/` 目录删除)

## 🔧 技术实施要点

### API兼容性保证

#### URL路径映射
```
老接口: /internal/benefit/product/{code}/charge
新接口: /internal/benefit/product/{code}/charge  (完全一致)
```

#### 请求参数兼容
```python
class ChargeRequestPayload(BaseModel):
    out_order_id: str
    account: str
```

#### 响应格式兼容
```python
class BenefitChargeResultDTO(BaseModel):
    order_id: str
    status: str
    amount: int
```

### Services层重写原则

1. **完全基于新架构**
   - 使用 `domains/benefits/repositories/`
   - 集成现有充值策略工厂
   - 使用现有消息队列机制

2. **不兼容老逻辑**
   - 不使用Django ORM
   - 不调用 `apps/benefits/services/`
   - 业务逻辑完全重写

3. **数据处理简化**
   - 简单的价格单位转换
   - Channel到Customer概念映射
   - 基础的权限检查

## 🛡️ 风险控制

### 最小风险策略
```nginx
# nginx配置简单切换
location /internal/benefit/ {
    proxy_pass http://new_fastapi_backend;  # 新后端
    # proxy_pass http://old_django_backend;  # 出问题立即回滚
}
```

### 快速回滚机制
- 保留老的Django服务运行
- nginx配置一键切换
- 数据库保持原状，无迁移风险

### 测试验证
```bash
# API兼容性测试脚本
curl -X POST /internal/benefit/product/TEST001/charge \
     -H "Channel: test_channel" \
     -d '{"out_order_id": "test123", "account": "<EMAIL>"}'
```

## 📊 工作量评估

### 人员配置
- **技术负责人**: 1人 (架构设计、风险控制)
- **后端开发**: 2人 (API实现、Services重写)
- **测试工程师**: 1人 (回归测试、性能测试)

### 关键里程碑
- [ ] **Day 4**: API接口层完成，基础功能可用
- [ ] **Day 9**: Services层重写完成，业务逻辑完整
- [ ] **Day 11**: 集成测试通过，正式上线

## ✅ 验收标准

### 功能验收
- [ ] Node服务调用0错误
- [ ] 所有API接口功能正常
- [ ] 充值成功率≥99.9%

### 性能验收
- [ ] 响应时间不超过原系统110%
- [ ] 并发能力满足现有业务量
- [ ] 错误率<0.1%

### 代码质量验收
- [ ] 测试覆盖率>80%
- [ ] 通过Ruff代码检查
- [ ] 所有public方法有完整文档

## 🎉 预期收益

### 即时收益
- **代码简化**: 删除整个 `apps/` 目录
- **架构统一**: 全部使用FastAPI + DDD架构
- **维护性**: 单一代码库，无历史包袱

### 中长期收益
- **开发效率**: 新功能开发速度提升50%
- **性能提升**: 异步架构性能更好
- **扩展性**: 基于DDD的模块化设计