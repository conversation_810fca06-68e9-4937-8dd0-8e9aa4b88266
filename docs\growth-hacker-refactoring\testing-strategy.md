# 测试策略方案

## 📋 测试策略概述

### 目标
建立完整的测试体系，确保重构过程的安全性和质量，为系统长期稳定发展提供保障。

### 测试金字塔
```mermaid
graph TB
    subgraph "测试金字塔"
        E2E[E2E测试 10%<br/>关键业务流程测试]
        Integration[集成测试 30%<br/>模块间交互测试]
        Unit[单元测试 60%<br/>组件逻辑测试]
    end
    
    subgraph "测试类型分布"
        Unit --> U1[领域逻辑测试]
        Unit --> U2[业务规则测试]
        Unit --> U3[异常处理测试]
        
        Integration --> I1[API接口测试]
        Integration --> I2[数据库集成测试]
        Integration --> I3[外部服务集成测试]
        
        E2E --> E1[完整任务流程测试]
        E2E --> E2[异常恢复测试]
        E2E --> E3[性能回归测试]
    end
```

### 质量门禁标准
| 测试类型 | 覆盖率要求 | 通过率要求 | 执行时间限制 |
|----------|------------|------------|--------------|
| **单元测试** | ≥90% | 100% | <5分钟 |
| **集成测试** | ≥80% | ≥95% | <15分钟 |
| **E2E测试** | 核心流程100% | ≥90% | <30分钟 |

## 🧪 分层测试策略

### 1. 单元测试策略 (60%)

#### 1.1 重构后组件单元测试

##### TaskOrchestrator 测试
```python
# tests/unit/applications/growth_hacker/services/test_task_orchestrator.py
import pytest
from unittest.mock import AsyncMock, Mock
from src.applications.growth_hacker.services import TaskOrchestrator
from src.domains.growth_tasks.exceptions import AlreadyClaimedError

class TestTaskOrchestrator:
    """TaskOrchestrator单元测试 - 专注于业务编排逻辑"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """创建模拟依赖"""
        return Mock(
            environment_preparer=AsyncMock(),
            task_executor=AsyncMock(), 
            lifecycle_manager=AsyncMock(),
            profile_updater=AsyncMock()
        )
    
    @pytest.fixture
    def orchestrator(self, mock_dependencies):
        """创建TaskOrchestrator实例"""
        return TaskOrchestrator(
            environment_preparer=mock_dependencies.environment_preparer,
            task_executor=mock_dependencies.task_executor,
            lifecycle_manager=mock_dependencies.lifecycle_manager,
            profile_updater=mock_dependencies.profile_updater
        )
    
    @pytest.mark.unit
    async def test_execute_task_success_flow(self, orchestrator, mock_dependencies, sample_task):
        """测试任务执行成功流程"""
        # Given: 准备测试数据和模拟行为
        task = sample_task()
        environment = Mock()
        result = Mock(success=True)
        
        mock_dependencies.environment_preparer.prepare.return_value = environment
        mock_dependencies.task_executor.execute.return_value = result
        
        # When: 执行任务
        await orchestrator.execute_task(task)
        
        # Then: 验证调用顺序和参数
        mock_dependencies.environment_preparer.prepare.assert_called_once_with(task, False)
        mock_dependencies.lifecycle_manager.start_task.assert_called_once_with(task.task_id)
        mock_dependencies.task_executor.execute.assert_called_once_with(task, environment)
        mock_dependencies.profile_updater.update_profile.assert_called_once()
        mock_dependencies.environment_preparer.cleanup.assert_called_once_with(environment, True)
    
    @pytest.mark.unit
    async def test_execute_task_handles_already_claimed_error(
        self, orchestrator, mock_dependencies, sample_task
    ):
        """测试处理已领取异常"""
        # Given: 模拟已领取异常
        task = sample_task()
        mock_dependencies.task_executor.execute.side_effect = AlreadyClaimedError("测试已领取")
        
        # When & Then: 验证异常处理
        with pytest.raises(AlreadyClaimedError):
            await orchestrator.execute_task(task)
        
        # 验证清理逻辑仍被执行
        mock_dependencies.environment_preparer.cleanup.assert_called_once()
        mock_dependencies.lifecycle_manager.complete_task.assert_called_once()
    
    @pytest.mark.unit  
    async def test_execute_task_with_none_proxy_enabled(
        self, orchestrator, mock_dependencies, sample_task
    ):
        """测试启用无代理模式"""
        # Given: 启用无代理模式
        task = sample_task()
        
        # When: 执行任务
        await orchestrator.execute_task(task, enable_none_proxy=True)
        
        # Then: 验证传递了正确的参数
        mock_dependencies.environment_preparer.prepare.assert_called_once_with(task, True)
```

##### PageDetector 测试
```python
# tests/unit/domains/growth_tasks/interactors/eleme10883/test_page_detector.py
import pytest
from unittest.mock import AsyncMock, Mock, patch
from src.domains.growth_tasks.interactors.eleme10883.page_detector import PageDetector
from src.domains.growth_tasks.exceptions import PageContentError, AlreadyClaimedError

class TestPageDetector:
    """PageDetector单元测试 - 专注于页面检测逻辑"""
    
    @pytest.fixture
    def mock_page(self):
        """模拟Page对象"""
        page = AsyncMock()
        page.query_selector.return_value = Mock()  # 模拟找到body元素
        page.content.return_value = "<html><body>正常页面内容超过100字符的测试内容...</body></html>"
        return page
    
    @pytest.fixture
    def page_detector(self, mock_page):
        """创建PageDetector实例"""
        return PageDetector(mock_page)
    
    @pytest.mark.unit
    async def test_detect_page_readiness_success(self, page_detector, mock_page):
        """测试页面检测成功"""
        # Given: 模拟正常页面
        body_element = Mock()
        body_element.inner_text.return_value = "足够长的页面内容" * 10
        mock_page.query_selector.return_value = body_element
        
        # When: 执行页面检测
        result = await page_detector.detect_page_readiness()
        
        # Then: 验证结果
        assert result.success is True
        assert "页面检测通过" in result.message
    
    @pytest.mark.unit
    async def test_detect_page_readiness_content_too_short(self, page_detector, mock_page):
        """测试页面内容过短"""
        # Given: 模拟内容过短的页面
        body_element = Mock()
        body_element.inner_text.return_value = "短内容"  # 少于100字符
        mock_page.query_selector.return_value = body_element
        mock_page.content.return_value = "<html><body>短内容</body></html>"
        
        # When & Then: 验证抛出异常
        with pytest.raises(PageContentError) as exc_info:
            await page_detector.detect_page_readiness()
        
        assert "页面内容过少" in str(exc_info.value)
    
    @pytest.mark.unit
    async def test_detect_already_claimed(self, page_detector, mock_page):
        """测试检测已领取状态"""
        # Given: 模拟已领取页面内容
        mock_page.content.return_value = """
        <html><body>
            <div>你已领取3个红包</div>
            <div>其他内容</div>
        </body></html>
        """
        
        # When & Then: 验证抛出已领取异常
        with pytest.raises(AlreadyClaimedError) as exc_info:
            await page_detector.content_checker.check_already_claimed()
        
        assert "你已领取3个红包" in str(exc_info.value)
```

#### 1.2 异常处理测试
```python
# tests/unit/domains/growth_tasks/exceptions/test_exceptions.py
import pytest
from src.domains.growth_tasks.exceptions import (
    GrowthTaskError, AlreadyClaimedError, ElementNotFoundError
)

class TestGrowthTaskExceptions:
    """异常体系测试"""
    
    @pytest.mark.unit
    def test_base_exception_properties(self):
        """测试基础异常属性"""
        error = GrowthTaskError(
            message="测试错误",
            retryable=True,
            error_code="TEST_ERROR"
        )
        
        assert error.message == "测试错误"
        assert error.retryable is True
        assert error.error_code == "TEST_ERROR"
        assert str(error) == "测试错误"
    
    @pytest.mark.unit
    def test_already_claimed_error(self):
        """测试已领取异常"""
        error = AlreadyClaimedError("3个红包")
        
        assert error.retryable is False
        assert error.error_code == "ALREADY_CLAIMED"
        assert "红包已领取: 3个红包" in error.message
    
    @pytest.mark.unit
    def test_element_not_found_error(self):
        """测试元素未找到异常"""
        error = ElementNotFoundError("红包按钮")
        
        assert error.retryable is True
        assert error.error_code == "ELEMENT_NOT_FOUND"
        assert "元素未找到: 红包按钮" in error.message
    
    @pytest.mark.unit
    def test_exception_with_context(self):
        """测试异常上下文功能"""
        error = GrowthTaskError("测试").with_context(
            task_id="test_123",
            phone="13800138000"
        )
        
        assert error.context["task_id"] == "test_123"
        assert error.context["phone"] == "13800138000"
```

### 2. 集成测试策略 (30%)

#### 2.1 模块间集成测试
```python
# tests/integration/applications/growth_hacker/test_task_execution_integration.py
import pytest
from src.applications.growth_hacker.services import TaskOrchestrator
from src.domains.growth_tasks.services import TaskDomainService, UserProfileDomainService

class TestTaskExecutionIntegration:
    """任务执行集成测试 - 测试真实组件间协作"""
    
    @pytest.fixture
    async def real_services(self, test_database, test_redis):
        """创建真实服务实例"""
        # 使用真实的数据库和Redis连接，但数据隔离
        task_service = TaskDomainService(test_database.task_repository)
        user_service = UserProfileDomainService(test_database.user_repository)
        
        return {
            'task_service': task_service,
            'user_service': user_service
        }
    
    @pytest.mark.integration
    async def test_full_task_lifecycle(self, real_services, sample_task):
        """测试完整的任务生命周期"""
        # Given: 准备测试数据
        task = sample_task()
        
        # When: 执行完整流程
        orchestrator = self._create_orchestrator(real_services)
        await orchestrator.execute_task(task)
        
        # Then: 验证数据库状态
        saved_task = await real_services['task_service'].get_by_task_id(task.task_id)
        assert saved_task is not None
        assert saved_task.status in [TaskStatus.SUCCESS, TaskStatus.ALREADY_CLAIMED]
    
    @pytest.mark.integration
    async def test_user_profile_update_integration(self, real_services, sample_task):
        """测试用户档案更新集成"""
        # Given: 有用户档案的任务
        task = sample_task()
        initial_profile = await real_services['user_service'].get_user_profile(task.phone)
        
        # When: 执行任务 (模拟成功场景)
        # ... 执行逻辑
        
        # Then: 验证用户档案被更新
        updated_profile = await real_services['user_service'].get_user_profile(task.phone)
        assert updated_profile.updated_at > initial_profile.updated_at
```

#### 2.2 浏览器交互集成测试
```python
# tests/integration/domains/growth_tasks/interactors/test_browser_integration.py
import pytest
from playwright.async_api import async_playwright
from src.domains.growth_tasks.interactors.eleme10883 import Eleme10083Interactor

class TestBrowserIntegration:
    """浏览器交互集成测试 - 使用真实浏览器环境"""
    
    @pytest.fixture
    async def browser_context(self):
        """创建真实浏览器上下文"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context(
            viewport={'width': 375, 'height': 667},  # 移动端视口
            user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'
        )
        
        yield context
        
        await context.close()
        await browser.close()
        await playwright.stop()
    
    @pytest.mark.integration
    @pytest.mark.browser
    async def test_page_detection_with_real_browser(self, browser_context):
        """使用真实浏览器测试页面检测"""
        # Given: 创建测试页面
        page = await browser_context.new_page()
        await page.goto("data:text/html,<html><body><div>测试页面内容超过100字符的详细内容...</div></body></html>")
        
        # When: 执行页面检测
        interactor = Eleme10083Interactor(page)
        result = await interactor.detect_page()
        
        # Then: 验证检测结果
        assert result.success is True
    
    @pytest.mark.integration
    @pytest.mark.browser  
    @pytest.mark.slow
    async def test_scroll_simulation_integration(self, browser_context):
        """测试滚动模拟集成"""
        # Given: 创建长页面
        long_content = "<div style='height:200px'>内容块</div>" * 20
        page = await browser_context.new_page()
        await page.goto(f"data:text/html,<html><body>{long_content}</body></html>")
        
        # When: 执行滚动模拟
        interactor = Eleme10083Interactor(page)
        await interactor.behavior_simulator.simulate_browsing()
        
        # Then: 验证页面确实发生了滚动
        scroll_position = await page.evaluate("window.pageYOffset")
        assert scroll_position > 0
```

### 3. 端到端测试策略 (10%)

#### 3.1 关键业务流程测试
```python
# tests/e2e/growth_hacker/test_complete_task_flow.py
import pytest
from tests.e2e.fixtures import mock_eleme_server, test_task_queue

class TestCompleteTaskFlow:
    """端到端任务流程测试"""
    
    @pytest.mark.e2e
    @pytest.mark.slow
    async def test_successful_task_completion(self, mock_eleme_server, test_task_queue):
        """测试成功的任务完成流程"""
        # Given: 模拟饿了么服务器和任务队列
        mock_eleme_server.setup_successful_scenario()
        task = test_task_queue.create_test_task()
        
        # When: 发送任务到队列
        await test_task_queue.publish_task(task)
        
        # Then: 等待任务完成并验证结果
        result = await test_task_queue.wait_for_completion(task.task_id, timeout=60)
        assert result.status == TaskStatus.SUCCESS
        assert result.execution_time > 0
    
    @pytest.mark.e2e
    async def test_already_claimed_scenario(self, mock_eleme_server, test_task_queue):
        """测试已领取场景的端到端流程"""
        # Given: 模拟已领取场景
        mock_eleme_server.setup_already_claimed_scenario()
        task = test_task_queue.create_test_task()
        
        # When: 执行任务
        await test_task_queue.publish_task(task)
        
        # Then: 验证正确处理已领取状态
        result = await test_task_queue.wait_for_completion(task.task_id, timeout=30)
        assert result.status == TaskStatus.ALREADY_CLAIMED
    
    @pytest.mark.e2e
    async def test_risk_detection_scenario(self, mock_eleme_server, test_task_queue):
        """测试风控检测场景"""
        # Given: 模拟风控页面
        mock_eleme_server.setup_risk_detection_scenario()
        task = test_task_queue.create_test_task()
        
        # When: 执行任务
        await test_task_queue.publish_task(task)
        
        # Then: 验证正确处理风控状态
        result = await test_task_queue.wait_for_completion(task.task_id, timeout=30)
        assert result.status == TaskStatus.RISK_DETECTED
```

#### 3.2 异常恢复测试
```python
# tests/e2e/growth_hacker/test_error_recovery.py
import pytest
from tests.e2e.fixtures import unstable_network, resource_limited_env

class TestErrorRecovery:
    """异常恢复端到端测试"""
    
    @pytest.mark.e2e
    async def test_network_interruption_recovery(self, unstable_network, test_task_queue):
        """测试网络中断恢复"""
        # Given: 不稳定的网络环境
        task = test_task_queue.create_test_task()
        
        # When: 在网络不稳定的情况下执行任务
        unstable_network.set_failure_rate(0.3)  # 30%失败率
        await test_task_queue.publish_task(task)
        
        # Then: 验证系统能够恢复并完成任务
        result = await test_task_queue.wait_for_completion(task.task_id, timeout=120)
        assert result.status in [TaskStatus.SUCCESS, TaskStatus.ALREADY_CLAIMED]
    
    @pytest.mark.e2e  
    async def test_resource_exhaustion_handling(self, resource_limited_env, test_task_queue):
        """测试资源耗尽处理"""
        # Given: 资源受限环境
        resource_limited_env.set_browser_limit(1)  # 限制只能1个浏览器实例
        
        # When: 同时发送多个任务
        tasks = [test_task_queue.create_test_task() for _ in range(5)]
        for task in tasks:
            await test_task_queue.publish_task(task)
        
        # Then: 验证所有任务最终都能完成
        results = []
        for task in tasks:
            result = await test_task_queue.wait_for_completion(task.task_id, timeout=300)
            results.append(result)
        
        # 至少80%的任务应该成功
        success_count = sum(1 for r in results if r.status == TaskStatus.SUCCESS)
        assert success_count >= len(tasks) * 0.8
```

## 🚀 性能测试策略

### 性能回归测试
```python
# tests/performance/test_performance_regression.py
import pytest
import time
from src.applications.growth_hacker.services import TaskOrchestrator

class TestPerformanceRegression:
    """性能回归测试 - 确保重构不影响性能"""
    
    @pytest.mark.performance
    async def test_task_execution_performance(self, performance_baseline):
        """测试任务执行性能"""
        # Given: 性能基线数据
        baseline_time = performance_baseline.get('task_execution_time', 10.0)
        
        # When: 执行任务并测量时间
        start_time = time.time()
        
        # 执行多个任务以获得更准确的平均值
        for _ in range(10):
            await self._execute_test_task()
        
        execution_time = (time.time() - start_time) / 10
        
        # Then: 验证性能未显著下降
        assert execution_time <= baseline_time * 1.2  # 允许20%的性能浮动
    
    @pytest.mark.performance
    async def test_memory_usage_regression(self, memory_monitor):
        """测试内存使用回归"""
        # Given: 内存监控器
        memory_monitor.start_monitoring()
        
        # When: 执行一批任务
        tasks = [self._create_test_task() for _ in range(50)]
        for task in tasks:
            await self._execute_test_task(task)
        
        # Then: 验证内存使用在合理范围内
        peak_memory = memory_monitor.get_peak_memory_usage()
        assert peak_memory < 500 * 1024 * 1024  # 小于500MB
        
        # 验证没有内存泄漏
        memory_monitor.trigger_gc()
        final_memory = memory_monitor.get_current_memory_usage()
        assert final_memory < peak_memory * 0.8  # 垃圾回收后应释放大部分内存
```

## 🔧 测试基础设施

### 测试数据工厂
```python
# tests/fixtures/data_factory.py
import factory
from src.interfaces.growth_hacker.schemas import TaskMessageContent
from src.databases.models.growth_hacker import GrowthHackerTask

class TaskMessageContentFactory(factory.Factory):
    """任务消息内容工厂"""
    class Meta:
        model = TaskMessageContent
    
    phone = factory.Sequence(lambda n: f"1380013{n:04d}")
    city = factory.Faker('city', locale='zh_CN')
    lat = factory.Faker('latitude')
    lng = factory.Faker('longitude') 
    access_url = factory.Faker('url')
    task_id = factory.Faker('uuid4')
    batch_name = factory.Faker('word')

class GrowthHackerTaskFactory(factory.Factory):
    """增长任务模型工厂"""
    class Meta:
        model = GrowthHackerTask
    
    task_id = factory.Faker('uuid4')
    phone = factory.Sequence(lambda n: f"1380013{n:04d}")
    city = factory.Faker('city', locale='zh_CN')
    status = TaskStatus.PENDING
    # ... 其他字段
```

### 测试环境配置
```yaml
# tests/config/test_settings.yaml
database:
  url: "sqlite+aiosqlite:///test.db"
  pool_size: 1
  max_overflow: 0

redis:
  host: "localhost"
  port: 6379
  db: 15  # 使用独立的测试数据库

rabbitmq:
  url: "amqp://guest:guest@localhost:5672/test"
  
browser:
  headless: true
  timeout: 10000
  pool_size: 2

proxy:
  enabled: false  # 测试环境禁用代理

logging:
  level: "WARNING"  # 减少测试日志输出
```

### CI/CD集成
```yaml
# .github/workflows/test.yml
name: 测试流水线

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
          
      - name: 安装依赖
        run: |
          pip install poetry
          poetry install
          
      - name: 运行单元测试
        run: |
          poetry run pytest tests/unit/ -v --cov=src --cov-report=xml
          
      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          
  integration-tests:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:latest
        ports:
          - 6379:6379
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test
        ports:
          - 5432:5432
          
    steps:
      - uses: actions/checkout@v3
      
      - name: 运行集成测试
        run: |
          poetry run pytest tests/integration/ -v --maxfail=1
          
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 安装浏览器
        run: |
          poetry run playwright install
          
      - name: 运行E2E测试
        run: |
          poetry run pytest tests/e2e/ -v --maxfail=3
```

## 📊 测试监控和报告

### 测试指标收集
```python
# tests/monitoring/test_metrics_collector.py
class TestMetricsCollector:
    """测试指标收集器"""
    
    def __init__(self):
        self.metrics = {
            'test_count': 0,
            'failure_count': 0,
            'execution_time': 0,
            'coverage_percentage': 0
        }
    
    def record_test_result(self, test_name: str, passed: bool, duration: float):
        """记录测试结果"""
        self.metrics['test_count'] += 1
        if not passed:
            self.metrics['failure_count'] += 1
        self.metrics['execution_time'] += duration
    
    def generate_report(self) -> dict:
        """生成测试报告"""
        return {
            'success_rate': (self.metrics['test_count'] - self.metrics['failure_count']) / self.metrics['test_count'],
            'average_execution_time': self.metrics['execution_time'] / self.metrics['test_count'],
            'total_tests': self.metrics['test_count'],
            'failed_tests': self.metrics['failure_count']
        }
```

### 质量趋势分析
```python
# scripts/test_quality_analyzer.py
def analyze_test_quality_trend():
    """分析测试质量趋势"""
    # 分析最近30天的测试数据
    # 生成质量趋势报告
    # 识别质量下降的模块
    # 提供改进建议
    pass
```

## 🎯 重构过程中的测试策略

### 重构安全网
1. **重构前**：确保现有测试全部通过
2. **重构中**：每个组件重构完成后立即运行相关测试
3. **重构后**：运行完整测试套件确保无回归

### 测试驱动重构 (TDR)
```python
# 重构步骤示例：
# 1. 为现有代码编写特性测试（如果没有）
def test_existing_task_service_behavior():
    """测试现有TaskService行为 - 重构安全网"""
    pass

# 2. 为新设计编写测试
def test_task_orchestrator_design():
    """测试新TaskOrchestrator设计"""
    pass

# 3. 重构实现
# 4. 确保所有测试通过
# 5. 清理旧测试，保留有价值的测试用例
```

---

*本测试策略为重构过程提供了全面的质量保障，通过分层测试、持续集成和监控报告，确保重构的安全性和效果的可验证性。*