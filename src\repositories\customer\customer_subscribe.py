# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/customer/repositories/customer_subscribe.py
# created: 2025-07-01 23:38:05
# updated: 2025-07-01 23:38:38

from typing import Optional

from src.databases.models.customer import CustomerSubscribe, CustomerSubscribeType


class CustomerSubscribeRepository:
    @classmethod
    async def get_customer_subscribe(
        cls, customer_id: int, subscribe_type: CustomerSubscribeType
    ) -> Optional[CustomerSubscribe]:
        return await CustomerSubscribe.filter(customer_id=customer_id, subscribe_type=subscribe_type).first()
