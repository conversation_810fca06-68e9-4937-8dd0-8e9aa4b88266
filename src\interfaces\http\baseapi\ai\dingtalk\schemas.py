# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/ai/dingtalk/schemas.py
# created: 2025-04-18 00:21:35
# updated: 2025-04-18 03:57:21

from enum import IntEnum
from typing import Optional

from pydantic import BaseModel, Field

from src.domains.benefits.dto import BenefitsProductDTO
from src.infrastructures.fastapi.response import BaseResponse


class SubscribeStateEnum(IntEnum):
    SUBSCRIBED = 1
    UNSUBSCRIBED = 0
    ALREADY_APPLIED = 2


class SubscribePayload(BaseModel):
    agent_id: str = Field(..., description="AI agent ID")
    agent_user_id: str = Field(..., description="AI agent user ID")


class SubscribeResult(BaseModel):
    benefit: Optional[BenefitsProductDTO] = Field(None, description="产品")
    subscribe_state: SubscribeStateEnum = Field(..., description="订阅状态")


class SubscribeState(BaseModel):
    subscribe_state: SubscribeStateEnum = Field(..., description="订阅状态")


QuerySubscribeStateResponse = BaseResponse[SubscribeState]
SubcribeResponse = BaseResponse[SubscribeResult]
