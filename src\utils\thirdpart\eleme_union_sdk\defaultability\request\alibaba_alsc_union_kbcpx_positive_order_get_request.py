from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbcpxPositiveOrderGetRequest(BaseRequest):

    def __init__(
        self,
        date_type: int = None,
        settle_state: int = None,
        end_date: str = None,
        biz_unit: int = None,
        page_size: int = None,
        page_number: int = None,
        start_date: str = None,
        order_state: int = None,
        flow_type: str = None,
        pid: str = None,
        order_id: str = None,
        include_used_store_id: bool = None,
    ):
        """
        时间维度，1-付款时间 2-创建时间 3-结算时间 4-更新时间
        """
        self._date_type = date_type
        """
            结算状态，1-已结算 2-未结算 不传-全部状态
        """
        self._settle_state = settle_state
        """
            查询截止时间，精确到时分秒。开始和结束时间不能超过31天
        """
        self._end_date = end_date
        """
            1-CPA 2-CPS
        """
        self._biz_unit = biz_unit
        """
            每页返回数据大小，默认10，最大返回50
        """
        self._page_size = page_size
        """
            页码，默认第一页
        """
        self._page_number = page_number
        """
            查询起始时间，精确到时分秒。开始和结束时间不能超过31天
        """
        self._start_date = start_date
        """
            订单状态，0-已失效 1-已下单 2-已付款 4-已收货 不传-全部状态
        """
        self._order_state = order_state
        """
            场景值，支持多场景（英文逗号分隔）查询7卡券订单，8卡券核销订单，10-媒体出资CPS红包，11-媒体出资霸王餐加码红包，26-评价有礼订单
        """
        self._flow_type = flow_type
        """
            推广位pid
        """
        self._pid = pid
        """
            淘宝子订单号或饿了么订单号
        """
        self._order_id = order_id
        """
            是否包含核销门店
        """
        self._include_used_store_id = include_used_store_id

    @property
    def date_type(self):
        return self._date_type

    @date_type.setter
    def date_type(self, date_type):
        if isinstance(date_type, int):
            self._date_type = date_type
        else:
            raise TypeError("date_type must be int")

    @property
    def settle_state(self):
        return self._settle_state

    @settle_state.setter
    def settle_state(self, settle_state):
        if isinstance(settle_state, int):
            self._settle_state = settle_state
        else:
            raise TypeError("settle_state must be int")

    @property
    def end_date(self):
        return self._end_date

    @end_date.setter
    def end_date(self, end_date):
        if isinstance(end_date, str):
            self._end_date = end_date
        else:
            raise TypeError("end_date must be str")

    @property
    def biz_unit(self):
        return self._biz_unit

    @biz_unit.setter
    def biz_unit(self, biz_unit):
        if isinstance(biz_unit, int):
            self._biz_unit = biz_unit
        else:
            raise TypeError("biz_unit must be int")

    @property
    def page_size(self):
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        if isinstance(page_size, int):
            self._page_size = page_size
        else:
            raise TypeError("page_size must be int")

    @property
    def page_number(self):
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        if isinstance(page_number, int):
            self._page_number = page_number
        else:
            raise TypeError("page_number must be int")

    @property
    def start_date(self):
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        if isinstance(start_date, str):
            self._start_date = start_date
        else:
            raise TypeError("start_date must be str")

    @property
    def order_state(self):
        return self._order_state

    @order_state.setter
    def order_state(self, order_state):
        if isinstance(order_state, int):
            self._order_state = order_state
        else:
            raise TypeError("order_state must be int")

    @property
    def flow_type(self):
        return self._flow_type

    @flow_type.setter
    def flow_type(self, flow_type):
        if isinstance(flow_type, str):
            self._flow_type = flow_type
        else:
            raise TypeError("flow_type must be str")

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def order_id(self):
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        if isinstance(order_id, str):
            self._order_id = order_id
        else:
            raise TypeError("order_id must be str")

    @property
    def include_used_store_id(self):
        return self._include_used_store_id

    @include_used_store_id.setter
    def include_used_store_id(self, include_used_store_id):
        if isinstance(include_used_store_id, bool):
            self._include_used_store_id = include_used_store_id
        else:
            raise TypeError("include_used_store_id must be bool")

    def get_api_name(self):
        return "alibaba.alsc.union.kbcpx.positive.order.get"

    def to_dict(self):
        request_dict = {}
        if self._date_type is not None:
            request_dict["date_type"] = convert_basic(self._date_type)

        if self._settle_state is not None:
            request_dict["settle_state"] = convert_basic(self._settle_state)

        if self._end_date is not None:
            request_dict["end_date"] = convert_basic(self._end_date)

        if self._biz_unit is not None:
            request_dict["biz_unit"] = convert_basic(self._biz_unit)

        if self._page_size is not None:
            request_dict["page_size"] = convert_basic(self._page_size)

        if self._page_number is not None:
            request_dict["page_number"] = convert_basic(self._page_number)

        if self._start_date is not None:
            request_dict["start_date"] = convert_basic(self._start_date)

        if self._order_state is not None:
            request_dict["order_state"] = convert_basic(self._order_state)

        if self._flow_type is not None:
            request_dict["flow_type"] = convert_basic(self._flow_type)

        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._order_id is not None:
            request_dict["order_id"] = convert_basic(self._order_id)

        if self._include_used_store_id is not None:
            request_dict["include_used_store_id"] = convert_basic(self._include_used_store_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
