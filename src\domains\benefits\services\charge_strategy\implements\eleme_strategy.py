# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/charge_strategy/implements/eleme_strategy.py
# created: 2025-04-06 20:32:28
# updated: 2025-04-13 16:07:44

from datetime import datetime
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger
from tortoise.transactions import atomic

from src.containers import Container
from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.databases.models.benefits import ChargeFuncEnum
from src.domains.benefits.dto import ChargeRecordDetailSchema
from src.infrastructures.errors import ElemeBenefitChargeNotFoundError, SKUNotSupportRefundError

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsSkuChargeRecord
    from src.infrastructures.gateways.bifrost import BifrostGateway
    from src.infrastructures.gateways.bifrost.schemas import RightsApplyResult, RightsQueryResult

from ..strategy import ChargeStrategy
from ..strategy_factor import register_strategy

# 常量定义
CHARGE_RESP_CODE_SUCCESS = ["0000", "3101"]
CHARGE_RESP_CODE_SYS_ERROR = ["4000", "4001"]
CHARGE_RESP_CODE_NO_ORDER = "3102"

GRANT_RESULT_MAP = {
    "granted": RecordStatus.SUCCESS,
    "grantFaild": RecordStatus.FAILED,
    "ungranted": RecordStatus.PROCESSING,
}


@register_strategy(ChargeFuncEnum.ELEME_BENEFITS)
class ElemeStrategy(ChargeStrategy):
    """饿了么权益中心策略, 业务对接人 @linlong"""

    def _log_result(self, record_id, action, resp_code, resp_msg, result_data):
        """统一的日志记录函数"""
        logger.info(f"{action}[{record_id}], 响应码: [{resp_code}] {resp_msg}, 结果: {result_data}")

    async def _process_charge_result(
        self,
        record: "BenefitsSkuChargeRecord",
        result: "RightsApplyResult",
        bifrost_client: "BifrostGateway",
    ) -> "BenefitsSkuChargeRecord":
        """处理充值返回结果, 比较复杂

        1. 如果resp_code为0000, 则表示请求接收成功, 下游异步发放权益, 不代表最终发放成功
        2. 如果resp_code为3101, 则表示请求接收成功, 下游异步发放权益, 代表最终发放成功
        3. 如果resp_code为4000, 则表示系统异常, 幂等重试发放(相同流水号)
        4. 如果resp_code为4001, 则表示系统异常, 幂等重试发放(相同流水号)
        5. 如果resp_code为其他, 则表示发放失败, 更新状态并记录错误
        """

        resp_code = result.resp_code
        resp_msg = bifrost_client.get_error_message(resp_code)  # 获取结果信息
        self._log_result(record.id, "申请饿了么权益", resp_code, resp_msg, result.model_dump_json())

        # 根据返回的不同resp_code,进行不同的处理
        if resp_code in CHARGE_RESP_CODE_SUCCESS:
            record.status = RecordStatus.PROCESSING
            logger.info(f"饿了么权益发放请求成功[{record.id}], 等待检查发放结果")
        elif resp_code in CHARGE_RESP_CODE_SYS_ERROR:  # 系统异常, 幂等重试发放(相同流水号)
            logger.warning(f"饿了么权益发放失败[{record.id}], 系统异常, 幂等重试发放")
        else:  # 发放失败,更新状态并记录错误
            record.status = RecordStatus.FAILED
            logger.error(f"饿了么权益发放请求失败[{record.id}], 发放失败, 错误信息: {resp_msg}")

        if result.decrypt_data:
            record.supplier_order_id = result.decrypt_data.trade_no
        return record

    async def _process_check_result(
        self,
        record: "BenefitsSkuChargeRecord",
        result: "RightsQueryResult",
        bifrost_client: "BifrostGateway",
    ) -> "BenefitsSkuChargeRecord":
        """处理检查返回结果"""
        resp_code = result.resp_code
        resp_msg = bifrost_client.get_error_message(resp_code)  # 获取结果信息
        self._log_result(record.id, "饿了么权益发放状态查询", resp_code, resp_msg, result.model_dump_json())

        if resp_code == CHARGE_RESP_CODE_NO_ORDER:  # 无此外部流水号，表示进件未成功，可用原流水号幂等进件
            raise ElemeBenefitChargeNotFoundError
        elif resp_code in CHARGE_RESP_CODE_SUCCESS:  # 查询请求处理成功
            if result.decrypt_data and result.decrypt_data.apply_result == "processed":
                grant_result = result.decrypt_data.detail[0].grant_result
                record.status = GRANT_RESULT_MAP[grant_result]
                record.charged_at = (  # type: ignore
                    datetime.strptime(result.decrypt_data.detail[0].grant_time, "%Y-%m-%d %H:%M:%S")
                    if result.decrypt_data.detail[0].grant_time
                    else None
                )
                record.supplier_order_id = result.decrypt_data.trade_no
        else:
            logger.error(f"饿了么权益状态查询失败[{record.id}], 稍后重试查询. 未知响应: [{resp_code}] {resp_msg}")

        return record

    @atomic()
    @inject
    async def charge(
        self,
        record: "BenefitsSkuChargeRecord",
        bifrost_client: "BifrostGateway" = Provide[Container.infrastructures.bifrost_gateway],
    ) -> "BenefitsSkuChargeRecord":
        """执行充值方法"""
        ori_status = record.status
        detail = ChargeRecordDetailSchema.model_validate(record.detail)
        request = {
            "out_trade_no": f"{record.charge_order_id}_{record.id}",
            "mobile": record.account,
            "welfare_case_no": record.sku.third_part_code,
            "with_prefix": True,
        }
        detail.deliver_request = {**request, "timestamp": datetime.now().isoformat()}

        try:
            result = await bifrost_client.apply_welfare(**request)
            detail.deliver_response = {
                **result.model_dump(),
                "timestamp": datetime.now().isoformat(),
            }

            # 如果结果为空, 则表示申请失败
            if not result.decrypt_data:
                logger.error(f"申请饿了么权益异常[{record.id}], 结果信息: {result.model_dump_json()}")
                record.status = RecordStatus.FAILED
                await record.save()
                return record

            record = await self._process_charge_result(record, result, bifrost_client)
        except Exception as e:
            logger.error(f"申请饿了么权益失败[{record.id}], 错误信息: {e}")
            detail.deliver_response = {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }
            record.status = RecordStatus.FAILED
        finally:
            record.detail = detail.model_dump()
            logger.info(
                "请求饿了么权益充值完成, 变更 charge_record[{record_id}] 的 status: {ori_status} --> {to_status}, Detail: {detail}",
                record_id=record.id,
                ori_status=ori_status,
                to_status=record.status,
                detail=detail.model_dump_json(),
            )
            await record.save()

        return record

    @atomic()
    @inject
    async def check_charge_status(
        self,
        record: "BenefitsSkuChargeRecord",
        bifrost_client: "BifrostGateway" = Provide[Container.infrastructures.bifrost_gateway],
    ) -> "BenefitsSkuChargeRecord":
        """检查充值状态"""
        detail = ChargeRecordDetailSchema.model_validate(record.detail)
        request = {
            "out_trade_no": f"{record.charge_order_id}_{record.id}",
            "with_prefix": True,
        }
        request_time = datetime.now().isoformat()

        try:
            result = await bifrost_client.query_welfare(**request)
            logger.info(f"饿了么权益发放状态查询结果[{record.id}]: {result.model_dump()}")

            detail.check_list.append(
                {
                    "check_request": {**request, "timestamp": request_time},
                    "check_response": {**result.model_dump(), "timestamp": datetime.now().isoformat()},
                }
            )

            # 如果结果为空, 则表示申请失败
            if not result.decrypt_data:
                logger.warning(f"查询饿了么权益异常[{record.id}], 结果信息: {result.model_dump_json()}")
                await record.save()
                return record

            record = await self._process_check_result(record, result, bifrost_client)
        except Exception as e:
            detail.check_list.append(
                {
                    "check_request": {**request, "timestamp": request_time},
                    "check_response": {"error": str(e), "timestamp": datetime.now().isoformat()},
                }
            )
            logger.error(f"饿了么权益卡券状态查询失败[{record.id}], 异常错误: {str(e)}")
        finally:
            record.detail = detail.model_dump()
            await record.save()

        return record

    async def refund(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """执行退款方法"""
        raise SKUNotSupportRefundError
