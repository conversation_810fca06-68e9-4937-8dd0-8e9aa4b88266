# encoding: utf-8
# src/applications/common/dto.py
# created: 2025-08-02 10:39:09

from typing import Optional

from pydantic import BaseModel

from src.domains.activity.schemas import ActivityLinks

ActivityLinksDTO = ActivityLinks


class ShopItemDTO(BaseModel):
    title: str
    picture: str
    price: int
    origin_price: int

    class Config:
        from_attributes = True


class ShopLinksDTO(BaseModel):
    wechat_appid: Optional[str] = None
    wechat_path: Optional[str] = None
    h5_url: Optional[str] = None


class ShopInfoDTO(BaseModel):
    shop_id: str
    title: str
    logo: str
    category: str
    category_id: str
    recommend_reasons: Optional[str] = ""
    items: list[ShopItemDTO]
