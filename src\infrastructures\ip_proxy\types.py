# encoding: utf-8
# src/infrastructures/ip_proxy/types.py
# created: 2025-08-07 20:00:00

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel


class IpProxy(BaseModel):
    """IP代理模型"""

    identify: str
    server: str
    username: str
    password: str
    city: str
    expired_at: datetime


class ProxyType(str, Enum):
    """代理类型枚举"""

    SHORT_TERM = "short_term"


class ProxyProvider(ABC):
    """代理提供者抽象基类"""

    @abstractmethod
    async def get_proxy(self, city: str) -> Optional[IpProxy]:
        """获取指定城市的代理"""
        pass

    @abstractmethod
    async def refresh_proxies(self, city: str) -> list[IpProxy]:
        """刷新指定城市的代理列表"""
        pass

    @abstractmethod
    def get_proxy_type(self) -> ProxyType:
        """获取此提供者提供的代理类型"""
        pass
