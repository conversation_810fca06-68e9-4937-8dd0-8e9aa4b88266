from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest, TopApiClient


class Ability305:

    def __init__(self, client: TopApiClient):
        self._client = client

    """
        获取消息队列积压情况
    """

    def taobao_tmc_queue_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())

    """
        获取用户开通的topic列表
    """

    def taobao_tmc_user_topics_get(self, request: BaseRequest):
        return self._client.execute(request.get_api_name(), request.to_dict(), request.get_file_param_dict())
