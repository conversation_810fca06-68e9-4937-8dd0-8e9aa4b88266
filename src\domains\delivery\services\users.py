import uuid
from datetime import datetime
from typing import List

from loguru import logger
from redis.asyncio import Redis

from src.domains.delivery.dto import (
    DeliveryWashOrderExportRecord,
    DeliveryWashOrderExportStatus,
    DeliveryWashOrderRunStatus,
)
from src.domains.delivery.messages import (
    DeliveryWashOrderExportMessage,
    DeliveryWashOrderExportMessageContent,
    DeliveryWashOrderRunMessage,
    DeliveryWashOrderRunMessageContent,
)
from src.infrastructures import errors
from src.infrastructures.databases import RedisManager
from src.infrastructures.rabbitmq import RabbitMQProducer
from src.repositories.delivery.users import DeliveryUserRepository, GenerateWashOrderTasksPayload


class DeliveryUserService:
    def __init__(self, redis_manager: RedisManager, producer: RabbitMQProducer):
        self.redis_client = redis_manager.client
        self.producer = producer

    async def generate_wash_order_tasks(self, payload: GenerateWashOrderTasksPayload) -> DeliveryWashOrderExportRecord:
        # 生成导出任务ID
        export_id = str(uuid.uuid4())
        logger.info(f"生成洗单任务导出任务: {export_id}")

        # 创建导出记录
        export_record = DeliveryWashOrderExportRecord(
            export_id=export_id,
            status=DeliveryWashOrderExportStatus.PENDING.value,
            created_at=datetime.now(),
            completed_at=None,
            file_url=None,
            error_message=None,
            filters=payload.model_dump(exclude_none=True),
            batch_name=None,
            run_date=None,
            run_status=DeliveryWashOrderRunStatus.PENDING.value,
            run_error_message=None,
        )

        # 保存导出记录到Redis
        redis_key = f"delivery_wash_order_export:{export_id}"
        await self.redis_client.set(redis_key, export_record.model_dump_json())

        # 发送导出消息
        export_message = DeliveryWashOrderExportMessage(
            payload=DeliveryWashOrderExportMessageContent(
                export_id=export_id,
                filters=payload.model_dump(exclude_none=True),
                created_at=datetime.now(),
            )
        )
        logger.info(f"发送导出消息: {export_message.model_dump()}")
        await self.producer.publish_message(export_message)

        return export_record

    async def get_export_records(self) -> List[DeliveryWashOrderExportRecord]:
        pattern = "delivery_wash_order_export:*"
        keys = await self.redis_client.keys(pattern)

        records = []
        for key in keys:
            record_data = await self.redis_client.get(key)
            if record_data:
                records.append(DeliveryWashOrderExportRecord.model_validate_json(record_data))
        # 按创建时间排序
        records.sort(key=lambda x: x.created_at, reverse=True)
        return records

    async def get_users_by_city(self, city: str) -> dict:
        return await DeliveryUserRepository.get_users_by_city(city)

    async def publish_wash_order_tasks(self, export_id: str) -> None:
        # 获取导出记录
        redis_key = f"delivery_wash_order_export:{export_id}"
        export_record_data = await self.redis_client.get(redis_key)

        # 检查导出记录是否存在
        if not export_record_data:
            raise errors.WashOrderExportRecordNotFoundError
        export_record = DeliveryWashOrderExportRecord.model_validate_json(export_record_data)

        # 检查导出记录是否已完成
        if export_record.status != DeliveryWashOrderExportStatus.COMPLETED.value:
            raise errors.WashOrderNotReadyError

        # 发送运行消息
        run_message = DeliveryWashOrderRunMessage(
            payload=DeliveryWashOrderRunMessageContent(
                export_id=export_id,
                created_at=datetime.now(),
            )
        )
        await self.producer.publish_message(run_message)
