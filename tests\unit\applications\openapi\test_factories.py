# encoding: utf-8
# tests/unit/applications/openapi/test_factories.py
# created: 2025-08-02 15:10:00

"""OpenAPI 测试数据工厂测试"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from .factories import OpenAPITestDataFactory, OpenAPIMockFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.unit
class TestOpenAPITestDataFactory:
    """测试 OpenAPI 测试数据工厂"""

    def test_create_customer_data(self):
        """测试创建客户数据"""
        data = OpenAPITestDataFactory.create_customer_data()
        
        assert "id" in data
        assert "code" in data
        assert "name" in data
        assert "description" in data
        assert "app_id" in data
        assert "tenant_id" in data
        assert "balance" in data
        
        assert isinstance(data["id"], int)
        assert isinstance(data["balance"], int)
        assert data["balance"] >= 0

    def test_create_customer_data_with_custom_id(self):
        """测试使用自定义ID创建客户数据"""
        custom_id = "custom_customer_001"
        data = OpenAPITestDataFactory.create_customer_data(customer_id=custom_id)
        
        assert data["code"] == custom_id

    def test_create_customer_data_with_kwargs(self):
        """测试使用kwargs创建客户数据"""
        custom_balance = 50000
        data = OpenAPITestDataFactory.create_customer_data(balance=custom_balance)
        
        assert data["balance"] == custom_balance

    def test_create_customer_secret_data(self):
        """测试创建客户密钥数据"""
        data = OpenAPITestDataFactory.create_customer_secret_data()
        
        required_fields = ["id", "customer_id", "access_key", "secret_key", "jwt_secret", "token", "enabled", "expires_at"]
        for field in required_fields:
            assert field in data
        
        assert data["enabled"] is True
        assert len(data["access_key"]) == 64  # SHA256 hex length
        assert len(data["secret_key"]) == 64
        assert len(data["jwt_secret"]) == 64

    def test_create_benefit_product_data(self):
        """测试创建权益产品数据"""
        data = OpenAPITestDataFactory.create_benefit_product_data()
        
        required_fields = ["id", "code", "name", "type", "description", "detail", "enabled", "sale_price", "stock", "customer_id"]
        for field in required_fields:
            assert field in data
        
        assert data["enabled"] is True
        assert data["type"] in ("coupon", "discount", "gift", "cash")
        assert isinstance(data["detail"], dict)
        assert data["sale_price"] > 0
        assert data["stock"] >= 0

    def test_create_activity_data(self):
        """测试创建活动数据"""
        data = OpenAPITestDataFactory.create_activity_data()
        
        required_fields = ["id", "name", "description", "code", "access_url", "union_active_id", "union_zone_pid", "created_at", "updated_at"]
        for field in required_fields:
            assert field in data
        
        assert data["access_url"].startswith("http")
        assert "union_" in data["union_active_id"]
        assert "zone_" in data["union_zone_pid"]

    def test_create_order_data(self):
        """测试创建订单数据"""
        data = OpenAPITestDataFactory.create_order_data()
        
        required_fields = ["id", "order_id", "out_order_id", "customer_id", "product_id", "product_code", "quantity", "price", "total_amount", "status", "app_id", "tenant_id", "created_at", "updated_at"]
        for field in required_fields:
            assert field in data
        
        assert data["order_id"].startswith("ORDER_")
        assert data["out_order_id"].startswith("THIRD_")
        assert data["status"] in ("pending", "paid", "shipped", "completed", "cancelled")
        assert data["quantity"] > 0
        assert data["total_amount"] > 0


@pytest.mark.openapi
@pytest.mark.unit
class TestOpenAPIMockFactory:
    """测试 OpenAPI Mock 对象工厂"""

    def test_create_mock_request(self):
        """测试创建 Mock Request"""
        headers = {"Authorization": "Bearer test_token"}
        query_params = {"page": "1", "size": "10"}
        path_params = {"id": "123"}
        
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers=headers,
            query_params=query_params,
            path_params=path_params
        )
        
        assert mock_request.headers == headers
        assert mock_request.query_params == query_params
        assert mock_request.path_params == path_params

    def test_create_mock_customer_repository(self):
        """测试创建 Mock CustomerRepository"""
        mock_repo = OpenAPIMockFactory.create_mock_customer_repository()
        
        assert isinstance(mock_repo, AsyncMock)
        assert hasattr(mock_repo, 'get_by_id')
        assert hasattr(mock_repo, 'get_by_token')
        assert hasattr(mock_repo, 'create')
        
        # 测试默认返回值
        assert mock_repo.get_by_id.return_value is None

    def test_create_mock_customer_repository_with_custom_returns(self):
        """测试使用自定义返回值创建 Mock CustomerRepository"""
        custom_customer = MagicMock(id=999, code="custom_customer")
        mock_repo = OpenAPIMockFactory.create_mock_customer_repository(
            get_by_id=custom_customer
        )
        
        assert mock_repo.get_by_id.return_value == custom_customer

    def test_create_mock_gateway(self):
        """测试创建 Mock Gateway"""
        mock_gateway = OpenAPIMockFactory.create_mock_gateway("test_gateway")
        
        assert isinstance(mock_gateway, AsyncMock)
        assert hasattr(mock_gateway, 'get_activities')
        assert hasattr(mock_gateway, 'get_data')

    def test_create_mock_redis(self):
        """测试创建 Mock Redis"""
        mock_redis = OpenAPIMockFactory.create_mock_redis()
        
        assert isinstance(mock_redis, AsyncMock)
        assert hasattr(mock_redis, 'get')
        assert hasattr(mock_redis, 'set')
        assert hasattr(mock_redis, 'delete')
        
        # 测试异步方法
        assert isinstance(mock_redis.get, AsyncMock)
        assert isinstance(mock_redis.set, AsyncMock)

    def test_create_mock_redis_with_custom_returns(self):
        """测试使用自定义返回值创建 Mock Redis"""
        custom_value = "cached_data"
        mock_redis = OpenAPIMockFactory.create_mock_redis(get=custom_value)
        
        assert mock_redis.get.return_value == custom_value

    def test_create_mock_service(self):
        """测试创建 Mock Service"""
        mock_service = OpenAPIMockFactory.create_mock_service("test_service")
        
        assert isinstance(mock_service, AsyncMock)
        assert hasattr(mock_service, 'get_by_id')
        assert hasattr(mock_service, 'create')
        assert hasattr(mock_service, 'list')


@pytest.mark.openapi  
@pytest.mark.unit
class TestOpenAPIFixtureFactory:
    """测试 OpenAPI 固件工厂"""

    def test_create_authentication_fixtures(self):
        """测试创建认证固件"""
        fixtures = OpenAPIFixtureFactory.create_authentication_fixtures()
        
        required_keys = ["valid_token", "invalid_token", "expired_token", "valid_access_key", "valid_secret_key", "invalid_access_key", "invalid_secret_key"]
        for key in required_keys:
            assert key in fixtures
        
        assert len(fixtures["valid_access_key"]) == 32
        assert len(fixtures["valid_secret_key"]) == 64

    def test_create_error_scenarios(self):
        """测试创建错误场景"""
        scenarios = OpenAPIFixtureFactory.create_error_scenarios()
        
        required_scenarios = ["network_timeout", "database_error", "validation_error", "authentication_error", "authorization_error"]
        for scenario in required_scenarios:
            assert scenario in scenarios
            assert "exception" in scenarios[scenario]
            assert "message" in scenarios[scenario]

    def test_create_performance_test_data(self):
        """测试创建性能测试数据"""
        perf_data = OpenAPIFixtureFactory.create_performance_test_data()
        
        required_keys = ["concurrent_users", "request_rates", "test_duration", "expected_response_time", "expected_success_rate"]
        for key in required_keys:
            assert key in perf_data
        
        assert isinstance(perf_data["concurrent_users"], list)
        assert isinstance(perf_data["request_rates"], list)
        assert perf_data["expected_success_rate"] <= 1.0

    def test_create_test_config(self):
        """测试创建测试配置"""
        config = OpenAPIFixtureFactory.create_test_config()
        
        required_sections = ["openapi", "redis", "database", "gateways"]
        for section in required_sections:
            assert section in config
        
        assert "token_expire_seconds" in config["openapi"]
        assert "cache_prefix" in config["redis"]
        assert "pool_size" in config["database"]
        assert "eleme" in config["gateways"]


@pytest.mark.openapi
@pytest.mark.integration
class TestFactoriesIntegration:
    """测试工厂的集成使用"""

    @pytest.mark.asyncio
    async def test_mock_repository_integration(self):
        """测试 Mock Repository 的集成使用"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        
        # 创建 mock repository
        mock_repo = OpenAPIMockFactory.create_mock_customer_repository(
            get_by_id=MagicMock(**customer_data)
        )
        
        # 测试异步调用
        result = await mock_repo.get_by_id(customer_data["id"])
        assert result.id == customer_data["id"]
        assert result.code == customer_data["code"]
        
        # 验证方法被调用
        mock_repo.get_by_id.assert_called_once_with(customer_data["id"])

    @pytest.mark.asyncio
    async def test_mock_redis_integration(self):
        """测试 Mock Redis 的集成使用"""
        # 创建 mock redis
        test_value = "test_cached_value"
        mock_redis = OpenAPIMockFactory.create_mock_redis(get=test_value)
        
        # 测试缓存操作
        cached_result = await mock_redis.get("test_key")
        assert cached_result == test_value
        
        await mock_redis.set("test_key", "new_value")
        mock_redis.set.assert_called_once_with("test_key", "new_value")

    def test_complete_test_setup(self):
        """测试完整的测试设置"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        product_data = OpenAPITestDataFactory.create_benefit_product_data(customer_id=customer_data["id"])
        
        # 创建认证固件
        auth_fixtures = OpenAPIFixtureFactory.create_authentication_fixtures()
        
        # 创建配置
        config = OpenAPIFixtureFactory.create_test_config()
        
        # 创建 mock 对象
        mock_request = OpenAPIMockFactory.create_mock_request(
            headers={"Authorization": f"Bearer {auth_fixtures['valid_token']}"}
        )
        
        # 验证所有组件都正确创建
        assert customer_data["id"] == product_data["customer_id"]
        assert mock_request.headers["Authorization"].endswith(auth_fixtures["valid_token"])
        assert config["openapi"]["token_expire_seconds"] > 0
        
        # 验证数据完整性
        assert all(field in customer_data for field in ["id", "code", "name", "balance"])
        assert all(field in product_data for field in ["id", "code", "name", "sale_price"])