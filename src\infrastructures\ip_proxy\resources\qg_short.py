# encoding: utf-8

from datetime import datetime
from typing import Optional

import aiohttp
from loguru import logger

from src.infrastructures.gateways import region_gateway

from ..settings import QGShortConfig
from ..types import IpProxy, ProxyProvider, ProxyType


class QingguoShortResources(ProxyProvider):
    """Qingguo short-term proxy provider"""

    def __init__(self, config: QGShortConfig) -> None:
        self.config = config

    async def get_proxy(self, city: str) -> Optional[IpProxy]:
        """get http proxy"""
        url = f"{self.config.endpoint}/get"

        # 使用 RegionGateway 获取城市区划代码
        area_code = region_gateway.get_district_code_by_pinyin(city)
        if not area_code:
            logger.warning(f"无法获取城市 {city} 的区划代码，跳过代理请求")
            return None

        params = {"key": self.config.key, "area": area_code, "distinct": "true"}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:  # type: ignore
                result = await response.json()
                if response.status != 200:
                    code = result.get("code", "unknown")
                    message = result.get("message", "unknown")
                    logger.error(f"Failed to get short-term proxies for {city}: {response.status} {code} {message}")
                    return None

                data = result.get("data", [])
                if len(data) == 0:
                    return None
                return IpProxy(
                    identify=data[0]["proxy_ip"],
                    server=data[0]["server"],
                    city=city,
                    username=self.config.key,
                    password=self.config.password,
                    expired_at=datetime.strptime(data[0]["deadline"], "%Y-%m-%d %H:%M:%S"),
                )

    async def refresh_proxies(self, city: str) -> list[IpProxy]:
        """Refresh short-term proxies - same as get_proxy since they're one-time use"""
        proxy = await self.get_proxy(city)
        return [proxy] if proxy else []

    def get_proxy_type(self) -> ProxyType:
        return ProxyType.SHORT_TERM
