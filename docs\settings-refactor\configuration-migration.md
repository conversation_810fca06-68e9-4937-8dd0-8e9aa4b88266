# encoding: utf-8
# docs/configuration-migration.md
# created: 2025-08-18 15:50:00

# 配置系统迁移指南

## 概述

本次重构对项目的配置系统进行了全面优化，主要改进包括：

1. **创建基础配置类** - 抽象公共配置逻辑，减少代码重复
2. **统一配置类型** - infrastructures 层使用 BaseModel，服务层使用 BaseSettings
3. **移除敏感信息** - 清理硬编码的密码和密钥
4. **标准化配置加载** - 统一环境变量、.env、TOML 文件的优先级

## 主要变更

### 1. 新增基础配置类

创建了 `src/infrastructures/settings/base.py`，提供：
- `BaseServiceSettings` - 所有服务配置的基类
- `BaseApiSettings` - API 服务配置的基类

### 2. 服务配置简化

各服务的 settings.py 现在继承自 BaseServiceSettings，只需定义服务特有的配置：

```python
from src.infrastructures.settings import BaseServiceSettings

class YourServiceSettings(BaseServiceSettings):
    """服务配置"""
    # 只需定义服务特有的字段
    # 通用字段已在基类中定义
```

### 3. 配置优先级统一

所有服务现在使用相同的配置源优先级：
1. 环境变量（最高优先级）
2. .env 文件
3. .env.toml 文件（最低优先级）

### 4. 敏感信息安全

移除了所有硬编码的敏感信息，包括：
- 数据库连接字符串
- RabbitMQ 连接 URL
- API 密钥和密码

## 迁移步骤

### 步骤 1：备份现有配置

```bash
# 备份现有的配置文件
cp deploys/*/.env* backup/
cp deploys/*/.env.toml backup/
```

### 步骤 2：创建新配置文件

```bash
# 从示例文件创建配置
cp .env.example .env
cp .env.toml.example .env.toml

# 或为每个服务创建独立配置
cp .env.example deploys/baseapi/.env
cp .env.toml.example deploys/baseapi/.env.toml
```

### 步骤 3：填入实际配置值

编辑 .env 或 .env.toml 文件，填入实际的配置值：

```toml
[database]
mysql_uri = "mysql://实际的数据库连接"
redis_uri = "redis://实际的Redis连接"

[rabbitmq]
url = "amqp://实际的RabbitMQ连接"
```

### 步骤 4：验证配置加载

```python
# 测试配置是否正确加载
from deploys.baseapi.settings import config

print(config.database.mysql_uri)  # 应显示配置的值
```

## 环境变量配置

### 使用环境变量

环境变量使用双下划线表示嵌套：

```bash
export DATABASE__MYSQL_URI="mysql://..."
export RABBITMQ__URL="amqp://..."
export FASTAPI__PORT=8080
```

### 在 Docker 中使用

```dockerfile
ENV DATABASE__MYSQL_URI="mysql://..."
ENV RABBITMQ__URL="amqp://..."
```

### 在 Kubernetes 中使用

```yaml
env:
  - name: DATABASE__MYSQL_URI
    valueFrom:
      secretKeyRef:
        name: db-secret
        key: mysql-uri
```

## 配置示例

### 开发环境 (.env.development)

```env
DATABASE__MYSQL_URI=mysql://dev_user:dev_pass@localhost:3306/dev_db
DATABASE__REDIS_URI=redis://localhost:6379
RABBITMQ__URL=amqp://guest:guest@localhost:5672/
DEBUG=true
```

### 生产环境 (.env.production)

```env
DATABASE__MYSQL_URI=${SECRET_MYSQL_URI}
DATABASE__REDIS_URI=${SECRET_REDIS_URI}
RABBITMQ__URL=${SECRET_RABBITMQ_URL}
DEBUG=false
```

## 注意事项

1. **不要提交敏感信息** - .env 文件应该加入 .gitignore
2. **使用密钥管理服务** - 生产环境建议使用 KMS 或 Vault
3. **验证默认值** - 确保生产环境不使用默认配置
4. **配置验证** - 启动时检查必需的配置项是否存在

## 常见问题

### Q: 为什么 openapi 和 scheduler 的环境变量没有生效？

A: 这个问题已在本次重构中修复。所有服务现在都正确加载环境变量。

### Q: 如何添加新的配置字段？

A: 
1. 如果是通用配置，添加到 BaseServiceSettings
2. 如果是服务特有配置，添加到对应服务的 Settings 类
3. 更新 .env.example 和 .env.toml.example

### Q: 如何处理不同环境的配置？

A: 推荐方案：
1. 使用不同的 .env 文件（.env.dev, .env.prod）
2. 通过环境变量覆盖特定配置
3. 使用配置管理工具（如 Consul、etcd）

## 回滚方案

如果需要回滚到旧的配置系统：

```bash
# 恢复备份的配置文件
git checkout HEAD~1 -- deploys/*/settings.py
git checkout HEAD~1 -- src/infrastructures/*/settings.py

# 删除新增的文件
rm -rf src/infrastructures/settings/
rm .env.example .env.toml.example
```

## 联系支持

如有问题，请联系：
- 技术负责人
- DevOps 团队
- 或在项目 Issue 中提问