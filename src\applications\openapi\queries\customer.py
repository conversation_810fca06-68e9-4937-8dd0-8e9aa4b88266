# encoding: utf-8
# src/applications/openapi/queries/customer.py
# created: 2025-07-29 08:36:16

from typing import TYPE_CHECKING, Optional

from src.databases.models.customer import CustomerSubscribeType
from src.domains.customer.entities import CustomerEntity

from ..dto import AccountInfoDTO

if TYPE_CHECKING:
    from src.databases.models.customer import CustomerSubscribe
    from src.repositories.customer import CustomerRepository, CustomerSubscribeRepository


class CustomerQueryService:

    def __init__(self, customer_repo: "CustomerRepository", customer_subscribe_repo: "CustomerSubscribeRepository"):
        self.customer_repo = customer_repo
        self.customer_subscribe_repo = customer_subscribe_repo

    async def get_account_info(self, customer: "CustomerEntity") -> AccountInfoDTO:
        return AccountInfoDTO(balance=customer.balance, code=customer.code)

    async def get_customer_subscribe(
        self, customer: "CustomerEntity", subscribe_type: "CustomerSubscribeType"
    ) -> Optional["CustomerSubscribe"]:
        return await self.customer_subscribe_repo.get_customer_subscribe(customer.id, subscribe_type)

    async def get_customer_by_id(self, customer_id: int) -> Optional["CustomerEntity"]:
        customer_model = await self.customer_repo.get_by_id(customer_id)
        return await CustomerEntity.from_model(customer_model) if customer_model else None
