from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionMediaZoneGetRequest(BaseRequest):

    def __init__(self, page: int = None, limit: int = None):
        """
        页码，从1开始
        """
        self._page = page
        """
            每页展示条数
        """
        self._limit = limit

    @property
    def page(self):
        return self._page

    @page.setter
    def page(self, page):
        if isinstance(page, int):
            self._page = page
        else:
            raise TypeError("page must be int")

    @property
    def limit(self):
        return self._limit

    @limit.setter
    def limit(self, limit):
        if isinstance(limit, int):
            self._limit = limit
        else:
            raise TypeError("limit must be int")

    def get_api_name(self):
        return "alibaba.alsc.union.media.zone.get"

    def to_dict(self):
        request_dict = {}
        if self._page is not None:
            request_dict["page"] = convert_basic(self._page)

        if self._limit is not None:
            request_dict["limit"] = convert_basic(self._limit)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
