# encoding: utf-8
# scripts/example_logger.py
# created: 2025-08-18 11:02:29

"""
日志系统使用示例

演示如何使用 infrastructures.logger 模块进行日志记录
包括初始化、配置、上下文管理、异常处理等功能
"""

import asyncio
import json

# 添加项目根目录到 Python 路径
import sys
import time
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructures.logger import (
    LogContext,
    LoggerSettings,
    default_logger,
    get_logger,
    setup,
)
from src.infrastructures.logger.settings import (
    ConsoleSinkConfig,
    FileSinkConfig,
)


def example_basic_usage():
    """基础使用示例"""
    print("\n" + "=" * 60)
    print("1. 基础使用示例")
    print("=" * 60)

    # 使用默认配置创建日志器
    logger = default_logger("example_app")

    # 不同级别的日志
    logger.debug("这是 DEBUG 级别的日志（默认不显示）")
    logger.info("应用启动成功")
    logger.warning("这是一个警告信息")
    logger.error("发生了一个错误")

    # 带参数的日志
    user_id = 12345
    logger.info(f"用户 {user_id} 登录成功")

    # 使用占位符
    logger.info("处理订单: order_id={}, amount={}", "ORD-001", 99.99)


def example_custom_config():
    """自定义配置示例"""
    print("\n" + "=" * 60)
    print("2. 自定义配置示例")
    print("=" * 60)

    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 配置多个输出目标
    config = {
        "app_name": "custom_app",
        "sinks": [
            # 控制台输出（彩色格式）
            {
                "type": "console",
                "level": "INFO",
                "format": "simple",  # 或 "json"
            },
            # 文件输出（JSON 格式）
            {
                "type": "file",
                "level": "DEBUG",
                "path": "logs/{app_name}.log",
                "rotation": "100 MB",  # 按大小轮转
                "retention": "7 days",  # 保留 7 天
                "compression": "gz",  # 压缩旧文件
                "format": "json",
            },
        ],
        "context_fields": {"environment": "development", "version": "1.0.0"},
    }

    logger = setup(config)

    logger.info("使用自定义配置的日志器")
    logger.debug("这条 DEBUG 日志只会写入文件")

    # 查看生成的日志文件
    log_files = list(log_dir.glob("*.log"))
    if log_files:
        print(f"\n生成的日志文件: {log_files[0]}")
        with open(log_files[0], "r") as f:
            first_line = f.readline()
            print(f"日志内容示例: {first_line[:100]}...")


def example_context_management():
    """上下文管理示例"""
    print("\n" + "=" * 60)
    print("3. 上下文管理示例")
    print("=" * 60)

    logger = default_logger("context_app")

    # 设置全局上下文
    LogContext.set("server_id", "server-01")
    logger.info("服务器初始化")  # 自动包含 server_id

    # 设置请求级别的上下文
    def process_request(request_id: str, user_id: int):
        # 设置请求上下文
        LogContext.set("request_id", request_id)
        LogContext.set("user_id", user_id)

        logger.info("开始处理请求")

        # 模拟业务处理
        time.sleep(0.1)
        logger.info("查询用户数据")

        time.sleep(0.1)
        logger.info("验证权限")

        time.sleep(0.1)
        logger.info("请求处理完成")

        # 清理请求上下文
        LogContext.clear("request_id")
        LogContext.clear("user_id")

    # 处理多个请求
    process_request("req-001", 10001)
    process_request("req-002", 10002)

    # 批量设置上下文
    LogContext.update(task_id="task-123", step="validation")
    logger.info("执行验证步骤")
    logger.info("验证完成")
    # 清理任务上下文
    LogContext.clear("task_id")
    LogContext.clear("step")

    logger.info("上下文已自动清理")


def example_exception_handling():
    """异常处理示例"""
    print("\n" + "=" * 60)
    print("4. 异常处理示例")
    print("=" * 60)

    logger = setup(
        {
            "app_name": "exception_app",
            "sinks": [
                {
                    "type": "console",
                    "level": "INFO",
                    "backtrace": True,  # 显示完整堆栈
                    "diagnose": True,  # 显示变量值
                }
            ],
        }
    )

    def divide(a: int, b: int) -> float:
        """除法函数"""
        try:
            result = a / b
            logger.info(f"计算结果: {a} / {b} = {result}")
            return result
        except ZeroDivisionError as e:
            logger.error(f"除零错误: {e}")
            raise
        except Exception as e:
            logger.exception(f"未知错误: {e}")
            raise

    # 正常情况
    divide(10, 2)

    # 异常情况
    try:
        divide(10, 0)
    except ZeroDivisionError:
        pass

    # 使用 catch 装饰器自动捕获异常
    from loguru import logger as loguru_logger

    @loguru_logger.catch
    def risky_operation():
        numbers = [1, 2, 3]
        return numbers[10]  # IndexError

    try:
        risky_operation()
    except IndexError:
        logger.info("已捕获并记录异常")


def example_performance_logging():
    """性能日志示例"""
    print("\n" + "=" * 60)
    print("5. 性能日志示例")
    print("=" * 60)

    logger = default_logger("performance_app")

    # 使用上下文管理器记录执行时间
    class Timer:
        def __init__(self, operation: str):
            self.operation = operation
            self.start_time = None

        def __enter__(self):
            self.start_time = time.time()
            logger.info(f"开始: {self.operation}")
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            elapsed = time.time() - self.start_time  # type: ignore
            logger.info(f"完成: {self.operation}, 耗时: {elapsed:.3f}秒")

    # 使用计时器
    with Timer("数据库查询"):
        time.sleep(0.5)  # 模拟数据库查询

    with Timer("API 调用"):
        time.sleep(0.3)  # 模拟 API 调用

    # 批量操作的性能日志
    def process_batch(batch_id: str, items: list):
        LogContext.set("batch_id", batch_id)
        logger.info(f"开始处理批次, 项目数: {len(items)}")

        start = time.time()
        processed = 0
        errors = 0

        for item in items:
            try:
                # 模拟处理
                time.sleep(0.01)
                processed += 1
            except Exception as e:
                errors += 1
                logger.error(f"处理失败: {item}, 错误: {e}")

        elapsed = time.time() - start
        logger.info(
            f"批次处理完成: 成功={processed}, 失败={errors}, "
            f"耗时={elapsed:.2f}秒, 速率={len(items)/elapsed:.1f}项/秒"
        )
        LogContext.clear("batch_id")

    # 处理批次
    process_batch("batch-001", list(range(50)))


async def example_async_logging():
    """异步日志示例"""
    print("\n" + "=" * 60)
    print("6. 异步日志示例")
    print("=" * 60)

    # 配置异步日志
    logger = setup(
        {
            "app_name": "async_app",
            "sinks": [
                {
                    "type": "console",
                    "level": "INFO",
                    "enqueue": True,  # 启用异步队列
                },
                {
                    "type": "file",
                    "level": "DEBUG",
                    "path": "logs/async.log",
                    "enqueue": True,  # 异步写入文件
                },
            ],
        }
    )

    async def async_task(task_id: int):
        """异步任务"""
        LogContext.set("task_id", f"task-{task_id}")
        logger.info(f"异步任务 {task_id} 开始")

        await asyncio.sleep(0.1)
        logger.info(f"异步任务 {task_id} 进行中")

        await asyncio.sleep(0.1)
        logger.info(f"异步任务 {task_id} 完成")

        LogContext.clear("task_id")

    # 并发执行多个异步任务
    tasks = [async_task(i) for i in range(5)]
    await asyncio.gather(*tasks)

    logger.info("所有异步任务完成")


def example_structured_logging():
    """结构化日志示例"""
    print("\n" + "=" * 60)
    print("7. 结构化日志示例")
    print("=" * 60)

    # 配置 JSON 格式输出
    logger = setup(
        {
            "app_name": "structured_app",
            "sinks": [
                {
                    "type": "console",
                    "level": "INFO",
                    "format": "json",  # JSON 格式
                }
            ],
        }
    )

    # 记录结构化数据
    logger.info(
        "用户操作",
        action="login",
        user_id=12345,
        ip="*************",
        user_agent="Mozilla/5.0",
    )

    # 记录复杂对象
    order = {
        "order_id": "ORD-2024-001",
        "user_id": 12345,
        "items": [
            {"product_id": "P001", "quantity": 2, "price": 29.99},
            {"product_id": "P002", "quantity": 1, "price": 49.99},
        ],
        "total": 109.97,
        "status": "pending",
    }

    logger.info(
        "创建订单", order=order, payment_method="credit_card", shipping_address={"city": "北京", "district": "朝阳区"}
    )

    # 记录指标数据
    metrics = {
        "cpu_usage": 45.2,
        "memory_usage": 78.5,
        "disk_usage": 62.3,
        "active_connections": 152,
        "request_rate": 1250.5,
    }

    logger.info("系统指标", **metrics)


def example_log_filtering():
    """日志级别过滤示例"""
    print("\n" + "=" * 60)
    print("8. 日志级别过滤示例")
    print("=" * 60)

    # 配置不同级别的日志输出
    logger = setup(
        {
            "app_name": "filter_app",
            "sinks": [
                {
                    "type": "console",
                    "level": "INFO",  # 控制台只显示 INFO 及以上
                },
                {
                    "type": "file",
                    "level": "WARNING",  # 文件只记录 WARNING 及以上
                    "path": "logs/important.log",
                },
                {
                    "type": "file",
                    "level": "DEBUG",  # 另一个文件记录所有日志
                    "path": "logs/debug.log",
                },
            ],
        }
    )

    # 测试不同级别的日志
    logger.debug("这是调试信息（只在 debug.log）")
    logger.info("这是信息日志（控制台和 debug.log）")
    logger.warning("这是警告信息（所有输出）")
    logger.error("这是错误信息（所有输出）")

    # 展示结果
    print("\n日志已写入:")
    print("- 控制台: INFO 及以上级别")
    print("- logs/important.log: WARNING 及以上级别")
    print("- logs/debug.log: 所有级别")


def main():
    """运行所有示例"""
    print("\n" + "#" * 60)
    print("#" + " " * 18 + "日志系统使用示例" + " " * 18 + "#")
    print("#" * 60)

    # 运行各个示例
    example_basic_usage()
    example_custom_config()
    example_context_management()
    example_exception_handling()
    example_performance_logging()

    # 运行异步示例
    print("\n运行异步示例...")
    asyncio.run(example_async_logging())

    example_structured_logging()
    example_log_filtering()

    print("\n" + "#" * 60)
    print("#" + " " * 20 + "示例运行完成" + " " * 20 + "#")
    print("#" * 60)
    print("\n提示:")
    print("1. 查看 logs/ 目录下生成的日志文件")
    print("2. 尝试修改配置参数，观察不同的输出效果")
    print("3. 在实际项目中，建议将配置写入 YAML 或环境变量")


if __name__ == "__main__":
    main()
