# encoding: utf-8
# src/interfaces/schedulers/tasks/eleme_user_profile.py
# created: 2025-06-08 08:55:21
# updated: 2025-06-10 08:56:27

import asyncio
import gc
from collections import Counter
from datetime import datetime, timedelta
from functools import wraps
from typing import List

import psutil
import pytz
from loguru import logger

from src.databases.models.delivery import ElemeOrder, ElemeUserProfile, LifecycleStage

# 北京时区
BEIJING_TZ = pytz.timezone("Asia/Shanghai")

# 性能优化配置
BATCH_SIZE = 100  # 批处理大小
PROGRESS_LOG_INTERVAL = 100  # 进度日志间隔
MAX_EARLY_FAILURES = 10  # 早期失败最大数量
EARLY_FAILURE_THRESHOLD = 20  # 早期失败检测阈值
MAX_FAILURE_RATE = 0.1  # 最大失败率
MIN_FAILURES_FOR_RATE_CHECK = 50  # 失败率检查的最小失败数量

# 并行处理配置
CONCURRENT_USERS = 20  # 并发处理用户数
MAX_DB_CONNECTIONS = 50  # 最大数据库连接数
SEMAPHORE_LIMIT = 25  # 信号量限制


def log_memory_usage(prefix: str = ""):
    """记录当前内存使用情况"""
    try:
        if psutil is None:
            logger.debug(f"[内存监控] {prefix} psutil不可用，跳过内存监控")
            return 0.0

        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        logger.info(f"[内存监控] {prefix} 当前内存使用: {memory_mb:.1f}MB")
        return memory_mb
    except Exception as e:
        logger.debug(f"[内存监控] {prefix} 获取内存信息失败: {str(e)}")
        return 0.0


# 全局信号量，控制并发数量
db_semaphore = None


async def get_db_semaphore():
    """获取数据库信号量"""
    global db_semaphore
    if db_semaphore is None:
        db_semaphore = asyncio.Semaphore(SEMAPHORE_LIMIT)
    return db_semaphore


async def process_user_batch(batch_users: List[tuple], batch_index: int) -> tuple:
    """并行处理一批用户"""
    semaphore = await get_db_semaphore()

    async def process_single_user(user_data):
        buyer_phone, buyer_uid = user_data[0], user_data[1]
        async with semaphore:  # 控制并发数量
            try:
                await update_eleme_user_profile(buyer_phone, buyer_uid)
                return True, buyer_phone, None
            except Exception as e:
                return False, buyer_phone, str(e)

    # 并行处理这一批用户
    results = await asyncio.gather(*[process_single_user(user) for user in batch_users], return_exceptions=True)

    # 统计结果
    success_count = 0
    error_count = 0
    errors = []

    for result in results:
        if isinstance(result, Exception):
            error_count += 1
            errors.append(f"异常: {str(result)}")
        else:
            success, phone, error = result  # type: ignore
            if success:
                success_count += 1
            else:
                error_count += 1
                errors.append(f"{phone}: {error}")

    logger.info(f"[批次 {batch_index}] 完成处理 {len(batch_users)} 个用户 - 成功: {success_count}, 失败: {error_count}")

    return success_count, error_count, errors


# 用户标签阈值配置
class TagThresholds:
    HIGH_VALUE_AMOUNT = 5000  # 高价值用户金额阈值（分）
    MEDIUM_VALUE_AMOUNT = 3000  # 中等消费金额阈值（分）
    HIGH_FREQUENCY_7D = 3  # 高频用户7天订单数
    ACTIVE_USER_30D = 5  # 活跃用户30天订单数
    WEEKDAY_USER_RATE = 0.7  # 工作日用户比例
    WEEKEND_USER_RATE = 0.3  # 周末用户比例
    LOYAL_USER_ORDERS = 20  # 忠实用户订单数
    REGULAR_USER_ORDERS = 10  # 常客订单数
    RETURN_GAP_DAYS = 30  # 回流用户间隔天数


def normalize_datetime(dt):
    """标准化datetime对象，确保时区一致性"""
    if dt is None:
        return None

    if dt.tzinfo is None:
        # 如果是naive datetime，假设它是北京时间
        return BEIJING_TZ.localize(dt)
    else:
        # 如果已经有时区信息，转换到北京时区
        return dt.astimezone(BEIJING_TZ)


def handle_step_errors(step_name: str):
    """装饰器：统一处理步骤执行错误"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                logger.debug(f"{step_name}完成")
                return result
            except Exception as e:
                logger.error(f"{step_name}失败: {str(e)}")
                raise

        return wrapper

    return decorator


async def update_eleme_user_profile(buyer_phone: str, eleme_uid: str):
    """更新用户画像数据"""
    if not buyer_phone or not eleme_uid:
        logger.warning(f"参数不完整: buyer_phone={buyer_phone}, eleme_uid={eleme_uid}")
        return

    try:
        logger.debug(f"开始处理用户: {buyer_phone}, UID: {eleme_uid}")

        # step 1: 获取或创建用户画像记录
        try:
            # 先尝试获取现有记录
            user_profile = await ElemeUserProfile.filter(buyer_phone=buyer_phone).first()
            # created = False

            if not user_profile:
                # 如果不存在，创建新记录时只设置最基本的字段
                user_profile = await ElemeUserProfile.create(
                    buyer_phone=buyer_phone,
                    eleme_uid=eleme_uid,
                    # 不设置其他有默认值的字段，让数据库自动处理
                )
                # created = True
                logger.debug(f"用户画像记录创建成功: {buyer_phone}")
            else:
                # 更新 eleme_uid（可能会变化）
                user_profile.eleme_uid = eleme_uid
                logger.debug(f"用户画像记录获取成功: {buyer_phone}")

        except Exception as e:
            logger.error(f"获取或创建用户画像记录失败 {buyer_phone}: {str(e)}")
            raise

        # step 2: 获取用户所有订单数据
        try:
            orders = await ElemeOrder.filter(buyer_phone=buyer_phone).prefetch_related("address").order_by("pay_time")
            logger.debug(f"用户 {buyer_phone} 共有 {len(orders)} 个订单")
        except Exception as e:
            logger.error(f"获取用户订单数据失败 {buyer_phone}: {str(e)}")
            raise

        if not orders:
            logger.warning(f"用户 {buyer_phone} 没有订单数据，跳过处理")
            return

        # step 3: 计算时间窗口 - 使用北京时间
        now = datetime.now(BEIJING_TZ)
        time_7d = now - timedelta(days=7)
        time_30d = now - timedelta(days=30)
        time_90d = now - timedelta(days=90)

        # step 4-10: 执行用户画像计算步骤（并行优化）
        # 第一批：独立计算步骤，可以并行执行
        await asyncio.gather(
            _calculate_basic_metrics(user_profile, orders, time_7d, time_30d, time_90d),
            _calculate_location_info(user_profile, orders),
            _calculate_time_patterns(user_profile, orders),
            _calculate_consumption_metrics(user_profile, orders),
            _calculate_preferences(user_profile, orders),
        )

        # 第二批：依赖前面结果的步骤，必须串行
        await _update_lifecycle_stage(user_profile, orders, now)
        await _generate_user_tags(user_profile)

        # 保存更新 - 确保不保存Generated Column字段
        try:
            # 明确指定要更新的字段，排除Generated Column和自动管理字段
            update_fields = [
                # 基础信息
                "eleme_uid",
                "main_city",
                "main_district",
                "main_address",
                # 行为特征
                "order_count",
                "order_count_7d",
                "order_count_30d",
                "order_count_90d",
                "first_order_time",
                "last_order_time",
                "avg_order_interval_days",
                "hourly_order_distribution",
                "preferred_hours",
                "weekday_order_count",
                "weekend_order_count",
                "weekday_order_rate",
                # 消费能力
                "total_amount",
                "avg_order_amount",
                "max_order_amount",
                "subsidy_ratio",
                # 偏好信息
                "favorite_category",
                "favorite_shop",
                "top_keywords",
                # 生命周期和标签
                "lifecycle_stage",
                "is_returned_user",
                "user_tags",
                "last_tagged_at",
            ]

            await user_profile.save(update_fields=update_fields)
            logger.debug(f"用户画像保存成功: {buyer_phone}")
        except Exception as e:
            logger.error(f"保存用户画像失败 {buyer_phone}: {str(e)}")
            logger.error(
                f"用户画像数据摘要: 订单数={user_profile.order_count}, 总金额={user_profile.total_amount}, 标签数={len(user_profile.user_tags) if user_profile.user_tags else 0}"
            )
            raise

        logger.info(f"用户画像更新完成: {buyer_phone}, 订单数: {len(orders)}")

    except Exception as e:
        logger.error(f"更新用户画像失败 {buyer_phone}: {str(e)}")
        logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
        raise


@handle_step_errors("基础指标计算")
async def _calculate_basic_metrics(
    profile: ElemeUserProfile, orders: List[ElemeOrder], time_7d: datetime, time_30d: datetime, time_90d: datetime
):
    """计算基础行为指标"""
    profile.order_count = len(orders)

    # 使用标准化时间比较，处理可能的时区差异
    order_count_7d = 0
    order_count_30d = 0
    order_count_90d = 0

    for order in orders:
        normalized_pay_time = normalize_datetime(order.pay_time)
        if normalized_pay_time is not None:
            if normalized_pay_time >= time_7d:
                order_count_7d += 1
            if normalized_pay_time >= time_30d:
                order_count_30d += 1
            if normalized_pay_time >= time_90d:
                order_count_90d += 1

    profile.order_count_7d = order_count_7d
    profile.order_count_30d = order_count_30d
    profile.order_count_90d = order_count_90d

    if orders:
        profile.first_order_time = orders[0].pay_time
        profile.last_order_time = orders[-1].pay_time

        # 计算平均下单间隔
        if len(orders) > 1:
            first_time = normalize_datetime(profile.first_order_time)
            last_time = normalize_datetime(profile.last_order_time)
            if first_time and last_time:
                total_days = (last_time - first_time).days
                profile.avg_order_interval_days = total_days / (len(orders) - 1) if total_days > 0 else 0.0
            else:
                profile.avg_order_interval_days = 0.0


@handle_step_errors("地理位置信息计算")
async def _calculate_location_info(profile: ElemeUserProfile, orders: List[ElemeOrder]):
    """计算地理位置信息"""
    # 统计最常用的城市、区域、地址
    cities = []
    districts = []
    addresses = []

    for order in orders:
        if order.address:
            if order.address.city_name:
                cities.append(order.address.city_name)
            if order.address.district_name:
                districts.append(order.address.district_name)
            if order.address.address:
                addresses.append(order.address.address)

    if cities:
        profile.main_city = Counter(cities).most_common(1)[0][0]
    if districts:
        profile.main_district = Counter(districts).most_common(1)[0][0]
    if addresses:
        profile.main_address = Counter(addresses).most_common(1)[0][0]


@handle_step_errors("时间行为特征计算")
async def _calculate_time_patterns(profile: ElemeUserProfile, orders: List[ElemeOrder]):
    """计算时间行为模式"""
    # 小时分布统计
    hourly_dist: dict[str, int] = {}
    for order in orders:
        hour = order.pay_time.hour
        hourly_dist[str(hour)] = hourly_dist.get(str(hour), 0) + 1

    profile.hourly_order_distribution = hourly_dist

    # 找出高频时间段（订单数量 >= 平均数的1.5倍）
    if hourly_dist:
        avg_count = sum(hourly_dist.values()) / len(hourly_dist)
        preferred_hours = [int(h) for h, count in hourly_dist.items() if count >= avg_count * 1.5]
        profile.preferred_hours = sorted(preferred_hours)

    # 工作日/周末统计
    weekday_count = 0
    weekend_count = 0

    for order in orders:
        if order.pay_time.weekday() < 5:  # 0-4 是工作日
            weekday_count += 1
        else:
            weekend_count += 1

    profile.weekday_order_count = weekday_count
    profile.weekend_order_count = weekend_count

    if profile.order_count > 0:
        profile.weekday_order_rate = weekday_count / profile.order_count


@handle_step_errors("消费能力指标计算")
async def _calculate_consumption_metrics(profile: ElemeUserProfile, orders: List[ElemeOrder]):
    """计算消费能力指标"""
    if not orders:
        return

    # 安全的数值计算，过滤异常数据
    payment_amounts = [order.payment_amount for order in orders if order.payment_amount and order.payment_amount > 0]
    if not payment_amounts:
        logger.warning("所有订单的payment_amount都为空或无效")
        return

    total_amount = sum(payment_amounts)
    total_subsidy = sum(
        (order.merchant_subsidy_amount or 0) + (order.agent_subsidy_amount or 0)
        for order in orders
        if order.merchant_subsidy_amount is not None or order.agent_subsidy_amount is not None
    )

    profile.total_amount = total_amount
    profile.avg_order_amount = total_amount // len(payment_amounts)  # 使用有效订单数量
    profile.max_order_amount = max(payment_amounts)

    # 补贴比例 - 添加安全检查
    if total_amount > 0:
        profile.subsidy_ratio = min(total_subsidy / total_amount, 1.0)  # 限制最大比例为100%
    else:
        profile.subsidy_ratio = 0.0


@handle_step_errors("偏好信息计算")
async def _calculate_preferences(profile: ElemeUserProfile, orders: List[ElemeOrder]):
    """计算偏好信息"""
    # 商家偏好
    shops = []
    categories = []
    keywords = []

    for order in orders:
        # 从订单的 subjects 字段提取商品信息
        if order.subjects:
            for subject in order.subjects:
                if isinstance(subject, dict):
                    # 提取商家名称
                    if "shop_name" in subject:
                        shops.append(subject["shop_name"])
                    # 提取品类信息
                    if "category" in subject:
                        categories.append(subject["category"])
                    # 提取关键词
                    if "name" in subject:
                        keywords.extend(subject["name"].split())

    if shops:
        profile.favorite_shop = Counter(shops).most_common(1)[0][0]
    if categories:
        profile.favorite_category = Counter(categories).most_common(1)[0][0]
    if keywords:
        # 保存前10个高频关键词
        top_keywords = dict(Counter(keywords).most_common(10))
        profile.top_keywords = top_keywords


@handle_step_errors("生命周期阶段更新")
async def _update_lifecycle_stage(profile: ElemeUserProfile, orders: List[ElemeOrder], now: datetime):
    """更新生命周期阶段"""
    if not orders:
        profile.lifecycle_stage = LifecycleStage.NEW
        return

    last_order_time = normalize_datetime(profile.last_order_time)
    if last_order_time is None:
        profile.lifecycle_stage = LifecycleStage.NEW
        return

    last_order_days = (now - last_order_time).days

    # 生命周期判断逻辑
    if profile.order_count == 1:
        profile.lifecycle_stage = LifecycleStage.NEW
    elif last_order_days <= 7:
        profile.lifecycle_stage = LifecycleStage.ACTIVE
    elif last_order_days <= 30:
        profile.lifecycle_stage = LifecycleStage.SILENT
    else:
        profile.lifecycle_stage = LifecycleStage.LOST

    # 判断是否回流用户（超过指定天数没下单后又下单）
    profile.is_returned_user = False  # 默认为False
    if len(orders) >= 2:
        # 检查是否有超过指定天数的间隔后又下单
        for i in range(1, len(orders)):
            prev_time = normalize_datetime(orders[i - 1].pay_time)
            curr_time = normalize_datetime(orders[i].pay_time)
            if prev_time and curr_time:
                gap_days = (curr_time - prev_time).days
                if gap_days > TagThresholds.RETURN_GAP_DAYS:
                    profile.is_returned_user = True
                    break


@handle_step_errors("用户标签生成")
async def _generate_user_tags(profile: ElemeUserProfile):
    """生成用户标签"""
    tags = []

    # 消费水平标签
    if profile.avg_order_amount >= TagThresholds.HIGH_VALUE_AMOUNT:  # 50元以上
        tags.append("高价值用户")
    elif profile.avg_order_amount >= TagThresholds.MEDIUM_VALUE_AMOUNT:  # 30元以上
        tags.append("中等消费")
    else:
        tags.append("节俭型用户")

    # 活跃度标签
    if profile.order_count_7d >= TagThresholds.HIGH_FREQUENCY_7D:
        tags.append("高频用户")
    elif profile.order_count_30d >= TagThresholds.ACTIVE_USER_30D:
        tags.append("活跃用户")

    # 时间偏好标签
    night_hours = set([22, 23, 0, 1, 2])
    if profile.preferred_hours and set(profile.preferred_hours) & night_hours:
        tags.append("夜猫子")

    if profile.weekday_order_rate > TagThresholds.WEEKDAY_USER_RATE:
        tags.append("工作日用户")
    elif profile.weekday_order_rate < TagThresholds.WEEKEND_USER_RATE:
        tags.append("周末用户")

    # 忠诚度标签
    if profile.order_count >= TagThresholds.LOYAL_USER_ORDERS:
        tags.append("忠实用户")
    elif profile.order_count >= TagThresholds.REGULAR_USER_ORDERS:
        tags.append("常客")

    # 生命周期标签
    tags.append(f"生命周期-{profile.lifecycle_stage}")

    if profile.is_returned_user:
        tags.append("回流用户")

    # 确保标签列表格式正确（JSON数组格式）
    # 数据库的Generated Column会用JSON_UNQUOTE(JSON_EXTRACT(user_tags, '$'))来生成user_tags_text
    profile.user_tags = tags  # 这将被序列化为JSON数组格式，如: ["标签1", "标签2"]
    profile.last_tagged_at = datetime.now(BEIJING_TZ)

    logger.debug(f"生成用户标签: {len(tags)}个标签 - {tags[:3]}{'...' if len(tags) > 3 else ''}")


async def eleme_user_profile():
    start_time = datetime.now()
    logger.info(f"[任务开始] 正在增量更新用户画像... 🚀 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 系统健康检查
        logger.info("[系统检查] 开始执行系统健康检查...")
        await _system_health_check()

        # 并发优化
        logger.info("[系统优化] 开始并发参数优化...")
        await optimize_concurrency()

        etime = datetime.now(BEIJING_TZ).replace(hour=0, minute=0, second=0, microsecond=0)
        stime_90d = etime - timedelta(days=90)

        logger.info(f"[查询条件] 统计时间范围: {stime_90d.strftime('%Y-%m-%d')} ~ {etime.strftime('%Y-%m-%d')}")

        # 查询需要更新的买家数据
        logger.info("[数据查询] 正在查询需要更新的买家数据...")
        buyers = await ElemeOrder.filter(pay_time__gte=stime_90d).distinct().values_list("buyer_phone", "buyer_uid")

        total_buyers = len(buyers)
        logger.info(f"[数据统计] 查询完成，共找到 {total_buyers} 个活跃买家需要更新画像")

        if total_buyers == 0:
            logger.warning("⚠️  没有找到需要更新的买家数据")
            return

        # 测试单个用户更新（先测试第一个用户）
        if total_buyers > 0:
            test_buyer = buyers[0]
            logger.info(f"[测试处理] 先测试处理单个用户: {test_buyer[0]}")
            try:
                await update_eleme_user_profile(test_buyer[0], test_buyer[1])
                logger.info("✅ [测试处理] 单个用户测试成功，开始批量处理")
            except Exception as e:
                logger.error(f"❌ [测试处理] 单个用户测试失败: {str(e)}")
                logger.error(f"错误类型: {type(e).__name__}")
                # 如果测试失败，不继续批量处理
                raise

        # 批量并行处理买家画像更新
        success_count = 0
        error_count = 0
        all_errors = []

        logger.info(f"[并行处理] 开始并行更新用户画像... 并发度: {CONCURRENT_USERS}")

        # 记录初始内存使用
        log_memory_usage("批处理开始")

        # 将用户分批，每批CONCURRENT_USERS个用户并行处理
        batch_size = CONCURRENT_USERS
        total_batches = (total_buyers + batch_size - 1) // batch_size

        for batch_index in range(total_batches):
            start_idx = batch_index * batch_size
            end_idx = min(start_idx + batch_size, total_buyers)
            batch_users = buyers[start_idx:end_idx]

            logger.info(f"[批次 {batch_index + 1}/{total_batches}] 开始处理 {len(batch_users)} 个用户...")

            try:
                # 并行处理这一批用户
                batch_success, batch_errors, batch_error_details = await process_user_batch(
                    batch_users, batch_index + 1
                )

                success_count += batch_success
                error_count += batch_errors
                all_errors.extend(batch_error_details)

                # 进度报告
                processed_count = end_idx
                progress = (processed_count / total_buyers) * 100

                logger.info(
                    f"[总体进度] {processed_count}/{total_buyers} ({progress:.1f}%) - 成功: {success_count}, 失败: {error_count}"
                )

                # 内存监控和清理
                if batch_index % 5 == 0:  # 每5批次监控一次内存
                    log_memory_usage(f"批次 {batch_index + 1} 完成")

                if batch_index % 10 == 0:  # 每10批次执行一次垃圾回收
                    gc.collect()
                    logger.debug(f"[内存清理] 执行垃圾回收 - 已处理 {batch_index + 1} 个批次")

                # 早期失败率检查
                if batch_index < 5 and error_count > success_count:  # 前5个批次
                    logger.error(f"⚠️  初期失败率过高 ({error_count}/{processed_count}), 可能存在系统性问题，终止处理")
                    break
                elif processed_count > 100 and error_count / processed_count > MAX_FAILURE_RATE:
                    logger.error(f"⚠️  失败率过高 ({error_count}/{processed_count}), 建议检查系统状态")

            except Exception as e:
                logger.error(f"[批次异常] 批次 {batch_index + 1} 处理失败: {str(e)}")
                # 批次失败时，继续处理下一批次
                continue

        # 输出最终统计结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        logger.info("[任务完成] 用户画像更新任务执行完毕 ✅")
        logger.info(f"[执行时间] 开始: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"[执行时间] 结束: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"[执行时长] 总耗时: {duration}")
        logger.info(f"[处理统计] 总用户数: {total_buyers}")
        logger.info(f"[处理统计] 成功更新: {success_count}")
        logger.info(f"[处理统计] 更新失败: {error_count}")
        if total_buyers > 0:
            logger.info(f"[成功率] {(success_count/total_buyers*100):.2f}%")

        # 性能统计
        if total_buyers > 0:
            avg_time_per_user = duration.total_seconds() / total_buyers
            users_per_minute = 60 / avg_time_per_user if avg_time_per_user > 0 else 0
            logger.info(f"[性能统计] 平均每用户处理时间: {avg_time_per_user:.2f}秒")
            logger.info(f"[性能统计] 处理速度: {users_per_minute:.1f}用户/分钟")

            # 并行效率估算
            estimated_serial_time = avg_time_per_user * total_buyers
            parallel_efficiency = (
                (estimated_serial_time / duration.total_seconds()) if duration.total_seconds() > 0 else 1
            )
            logger.info(f"[并行效率] 相比串行处理提升: {parallel_efficiency:.1f}倍")

        if error_count > 0:
            logger.warning(f"⚠️  有 {error_count} 个用户更新失败，请检查相关日志")
            # 输出前几个失败示例
            if all_errors:
                logger.warning("失败示例:")
                for i, error in enumerate(all_errors[:5], 1):
                    logger.warning(f"  {i}. {error}")
                if len(all_errors) > 5:
                    logger.warning(f"  ... 还有 {len(all_errors) - 5} 个失败记录")
        else:
            logger.info("🎉 所有用户画像更新成功！")

        logger.info("=" * 60)

    except Exception as e:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.error(f"[任务异常] 用户画像更新任务执行失败: {str(e)}")
        logger.error(f"[异常时间] 开始: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.error(f"[异常时间] 异常: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.error(f"[执行时长] 异常前耗时: {duration}")
        raise


async def _system_health_check():
    """系统健康检查"""
    try:
        # 1. 检查数据库连接
        logger.info("[健康检查] 测试数据库连接...")
        order_count = await ElemeOrder.all().count()
        logger.info(f"✅ [数据库连接] ElemeOrder表连接正常，共有 {order_count} 条记录")

        profile_count = await ElemeUserProfile.all().count()
        logger.info(f"✅ [数据库连接] ElemeUserProfile表连接正常，共有 {profile_count} 条记录")

        # 2. 测试模型操作
        logger.info("[健康检查] 测试模型基础操作...")

        # 测试查询操作
        recent_orders = await ElemeOrder.all().limit(1)
        if recent_orders:
            logger.info("✅ [模型操作] ElemeOrder查询操作正常")
        else:
            logger.warning("⚠️  [模型操作] ElemeOrder表为空")

        # 测试用户画像表操作
        try:
            # 尝试创建一个测试记录（但不保存）
            test_profile = ElemeUserProfile(
                buyer_phone="test_phone_12345", eleme_uid="test_uid_12345", source="health_check"
            )
            logger.info("✅ [模型操作] ElemeUserProfile模型实例创建正常")

            # 测试字段访问
            test_profile.order_count = 0
            test_profile.total_amount = 0
            test_profile.user_tags = ["测试标签"]
            logger.info("✅ [模型操作] ElemeUserProfile字段赋值正常")

        except Exception as e:
            logger.error(f"❌ [模型操作] ElemeUserProfile模型操作失败: {str(e)}")
            raise

        # 3. 检查关键字段
        logger.info("[健康检查] 检查模型字段定义...")
        profile_fields = ElemeUserProfile._meta.fields_map.keys()
        required_fields = [
            "buyer_phone",
            "eleme_uid",
            "order_count",
            "total_amount",
            "user_tags",
            "lifecycle_stage",
            "created_at",
            "updated_at",
        ]

        missing_fields = [field for field in required_fields if field not in profile_fields]
        if missing_fields:
            logger.error(f"❌ [字段检查] 缺少必要字段: {missing_fields}")
            raise Exception(f"ElemeUserProfile模型缺少字段: {missing_fields}")
        else:
            logger.info("✅ [字段检查] 所有必要字段存在")

        # 验证Generated Column不在ORM字段中
        if "user_tags_text" in profile_fields:
            logger.warning("⚠️  [字段检查] user_tags_text不应该在ORM模型中定义（它是Generated Column）")
        else:
            logger.info("✅ [字段检查] user_tags_text正确地不在ORM模型中（Generated Column）")

        logger.info("✅ [系统检查] 所有健康检查通过")

    except Exception as e:
        logger.error(f"❌ [系统检查] 健康检查失败: {str(e)}")
        logger.error(f"详细错误: {type(e).__name__}: {str(e)}")
        raise


async def test_single_user():
    """测试单个用户画像更新（用于调试）"""
    # 这个函数可以用来测试单个用户的更新过程
    test_phone = "13800138000"  # 替换为实际的测试手机号
    test_uid = "test_uid_001"  # 替换为实际的测试UID

    logger.info(f"🧪 [测试模式] 开始测试单个用户: {test_phone}")

    try:
        await update_eleme_user_profile(test_phone, test_uid)
        logger.info("✅ [测试模式] 单个用户测试成功")
    except Exception as e:
        logger.error(f"❌ [测试模式] 单个用户测试失败: {str(e)}")
        raise


async def optimize_concurrency():
    """动态优化并发参数"""
    global CONCURRENT_USERS, SEMAPHORE_LIMIT

    try:
        # 获取系统信息
        if psutil:
            cpu_count = psutil.cpu_count()
            memory_total = psutil.virtual_memory().total / (1024**3)  # GB

            # 基于系统资源动态调整并发数
            optimal_concurrent = min(cpu_count * 4, int(memory_total * 2), 50)
            optimal_semaphore = min(optimal_concurrent + 5, 50)

            if optimal_concurrent != CONCURRENT_USERS:
                logger.info(f"[并发优化] 根据系统资源调整并发数: {CONCURRENT_USERS} -> {optimal_concurrent}")
                logger.info(f"[并发优化] 系统信息: CPU={cpu_count}核, 内存={memory_total:.1f}GB")
                CONCURRENT_USERS = optimal_concurrent
                SEMAPHORE_LIMIT = optimal_semaphore
        else:
            logger.info(f"[并发优化] 使用默认并发配置: 并发数={CONCURRENT_USERS}, 信号量={SEMAPHORE_LIMIT}")

    except Exception as e:
        logger.warning(f"[并发优化] 获取系统信息失败，使用默认配置: {str(e)}")

    logger.info(f"[并发配置] 最终配置: 并发用户数={CONCURRENT_USERS}, 信号量限制={SEMAPHORE_LIMIT}")


async def main():
    logger.info("🔧 [系统初始化] 正在启动用户画像更新服务...")

    try:
        # 在函数内导入以避免循环导入
        from src.containers import Container
        from src.infrastructures.settings import BaseServiceSettings
        from src.interfaces.schedulers.main import lifespan

        # 创建容器实例
        container = Container()
        container.config.from_pydantic(BaseServiceSettings())

        async with lifespan(container) as context:
            logger.info("✅ [系统初始化] 依赖注入容器启动成功")
            context.wire(modules=[__name__])
            logger.info("✅ [系统初始化] 模块装配完成")

            await eleme_user_profile()

    except Exception as e:
        logger.error(f"❌ [系统错误] 服务启动或运行失败: {str(e)}")
        raise
    finally:
        logger.info("🏁 [系统关闭] 用户画像更新服务已关闭")


if __name__ == "__main__":
    logger.info("🚀 [程序启动] 饿了么用户画像更新脚本开始执行")
    try:
        asyncio.run(main())
        logger.info("✅ [程序完成] 脚本执行成功")
    except Exception as e:
        logger.error(f"❌ [程序异常] 脚本执行失败: {str(e)}")
        exit(1)
