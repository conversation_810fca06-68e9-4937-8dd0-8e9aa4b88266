# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/customer/dto.py
# created: 2024-12-04 03:25:08
# updated: 2025-07-01 23:37:05

import enum
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from tortoise import Tortoise
from tortoise.contrib.pydantic import PydanticModel, pydantic_model_creator

from src.databases.models.customer import (
    Customer,
    CustomerBenefits,
    CustomerChannels,
    CustomerRechargeRecord,
    CustomerSecret,
    CustomerSubscribe,
    CustomerType,
)
from src.domains.benefits.dto import BenefitsProductDTO

Tortoise.init_models(
    [
        "src.databases.models.benefits",
        "src.databases.models.customer",
        "src.databases.models.delivery",
        "src.databases.models.passport",
        "src.databases.models.datas",
    ],
    "models",
)


CustomerDTO = pydantic_model_creator(Customer, name="CustomerDTO")
CustomerSubscribeDTO = pydantic_model_creator(CustomerSubscribe, name="CustomerSubscribeDTO")


class CustomerUpdateFields(BaseModel):
    name: Optional[str] = Field(None, min_length=1, description="客户名称")
    description: Optional[str] = Field(None, description="客户描述")
    type: Optional[CustomerType] = Field(None, description="客户类型")


class CustomerCreateFields(BaseModel):
    name: str = Field(..., min_length=1, description="客户名称")
    description: str = Field(..., description="客户描述")
    type: CustomerType = Field(..., description="客户类型")
    app_id: Optional[str] = Field(None, description="应用ID")
    tenant_id: Optional[str] = Field(None, description="租户ID")


class BenefitsProductOrderStatus(enum.IntEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2
    PROCESSING = 3


class CustomerSecretDTO(PydanticModel):
    id: int = Field(..., description="密钥ID")
    name: str = Field(..., description="密钥名称")
    app_key: str = Field(..., description="APP_KEY", serialization_alias="jwt_secret")
    app_secret: str = Field(..., description="APP_SECRET", serialization_alias="access_token")
    access_key: str = Field(..., description="ACCESS_KEY", serialization_alias="access_key")
    access_secret: str = Field(..., description="ACCESS_SECRET", serialization_alias="access_secret")

    class Config:
        from_attributes = True
        orig_model = CustomerSecret


class CustomerSecretCreateDTO(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="密钥名称")


CustomerBenefitsDTO = pydantic_model_creator(
    CustomerBenefits,
    exclude=("customer", "product.charge_records", "product.product_skus", "product.skus"),
)


class CustomerBenefitsCreateDTO(BaseModel):
    customer_id: Optional[int] = Field(None, description="客户ID")
    product_id: Optional[int] = Field(None, description="产品ID")
    product_code: str = Field(..., description="产品Code")
    sale_price: int = Field(default=0, description="售价(单位分)")
    stock: int = Field(default=-1, description="库存")

    @field_validator("sale_price")
    @classmethod
    def validate_sale_price(cls, v):
        if v < 0:
            raise ValueError("售价不能为负数")
        return v

    @field_validator("stock")
    @classmethod
    def validate_stock(cls, v):
        if v < -1:
            raise ValueError("库存不能小于-1, -1表示无限库存")
        return v


class CustomerBenefitsUpdateDTO(BaseModel):
    sale_price: Optional[int] = Field(None, description="售价(单位分)")
    stock: Optional[int] = Field(None, description="库存")


CustomerBenefitsDetailDTO = pydantic_model_creator(
    CustomerBenefits,
    exclude=(
        "customer",
        "product.charge_records",
        "product.product_skus",
        "product.skus.sku_products",
        "product.skus.purchase_records",
        "product.skus.benefits_sku_charge_records",
    ),
)
CustomerChannelsDTO = pydantic_model_creator(CustomerChannels, exclude=("customer",))


class CustomerBenefitDTO(BaseModel):
    product: BenefitsProductDTO = Field(..., description="产品")


class CustomerBenefitsListDTO(BaseModel):
    total: int = Field(..., description="总数")
    benefits: list[CustomerBenefitsDetailDTO] = Field(..., description="客户权益列表")  # type: ignore


class CustomerListDTO(BaseModel):
    total: int = Field(..., description="总数")
    customers: list[CustomerDTO] = Field(..., description="客户列表")  # type: ignore


class OpenPlatformCustomerBenefitsDTO(BaseModel):
    id: int = Field(..., description="产品ID")
    code: str = Field(..., description="产品编码")
    name: str = Field(..., description="产品名称")
    price: int = Field(..., description="商品标价(单位分)")
    stock: int = Field(..., description="库存")


CustomerRechargeRecordDTO = pydantic_model_creator(CustomerRechargeRecord, name="CustomerRechargeRecordDTO")

# class CustomerSubscribeDTO(BaseModel):
#     id: int = Field(..., description="订阅ID")
#     type: CustomerSubscribeType = Field(..., description="订阅类型")
#     url: str = Field(..., description="订阅URL")
#     created_at: datetime = Field(..., description="创建时间")
#     updated_at: datetime = Field(..., description="更新时间")
