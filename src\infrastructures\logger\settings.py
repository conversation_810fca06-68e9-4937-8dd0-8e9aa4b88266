# encoding: utf-8
# src/infrastructures/logger/settings.py
# created: 2025-08-14 14:30:00

from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field


class FormatterConfig(BaseModel):
    """格式化器配置"""

    type: Literal["json", "text", "custom"] = "text"
    template: Optional[str] = None
    time_format: str = "%Y-%m-%d %H:%M:%S.%f"
    include_context: bool = True
    include_exception: bool = True


class SinkConfig(BaseModel):
    """输出目标配置基类"""

    type: str
    level: str = "INFO"
    formatter: FormatterConfig = Field(default_factory=FormatterConfig)
    filter: Optional[Dict[str, Any]] = None
    enqueue: bool = False  # 是否异步写入
    backtrace: bool = True
    diagnose: bool = False


class ConsoleSinkConfig(SinkConfig):
    """控制台输出配置"""

    type: Literal["console"] = "console"
    colorize: bool = True
    serialize: bool = False


class FileSinkConfig(SinkConfig):
    """文件输出配置"""

    type: Literal["file"] = "file"
    path: str = "logs/{app_name}.log"
    rotation: Union[str, int] = "10 MB"
    retention: Union[str, int] = "7 days"
    compression: Optional[str] = "gz"
    encoding: str = "utf-8"
    mode: str = "a"


class SLSSinkConfig(SinkConfig):
    """阿里云SLS输出配置"""

    type: Literal["sls"] = "sls"
    endpoint: str
    project: str
    logstore: str
    access_key_id: str
    access_key_secret: str
    batch_size: int = 100
    flush_interval: int = 5
    max_retry: int = 3


class LoggerSettings(BaseModel):
    """日志器主配置"""

    app_name: str = "default"
    sinks: List[Union[ConsoleSinkConfig, FileSinkConfig, SLSSinkConfig]] = Field(
        default_factory=lambda: [ConsoleSinkConfig()]  # type: ignore
    )
    context_fields: Dict[str, Any] = Field(default_factory=dict)
    intercept_stdlib: bool = True
    async_mode: bool = True

    class Config:
        extra = "allow"  # 允许额外字段
