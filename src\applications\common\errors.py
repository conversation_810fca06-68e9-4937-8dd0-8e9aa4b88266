# encoding: utf-8
# src/applications/common/errors.py
# created: 2025-08-02 10:39:14

from typing import Optional

from src.infrastructures.errors import BusinessError


class ActivityNotFoundError(BusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "活动不存在"
        super().__init__(message=message, code=404)


class ShopNotFoundError(BusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "店铺不存在"
        super().__init__(message=message, code=404)
