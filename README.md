# Beaver-Service-Backend

## 1. 项目概述

Beaver-Service-Backend 是一个基于 Python FastAPI 和 Django 开发的后端服务项目，主要为电影应用提供多种 API 和业务支持。该项目采用了模块化架构设计，清晰地划分了不同的业务领域和技术层次。

### 1.1 项目演进说明

**重要**：本项目最初是基于 Django 框架搭建的，所有应用层代码在 `apps/` 目录中，旧项目的配置在 `common/` 目录中。随着项目发展，系统正在逐步迁移至 FastAPI 框架，因此存在新旧框架共存的过渡状态。

**当前状态**：
- `apps/passport` 和 `apps/base` 模块基本已经废弃
- `apps/movie` 应用处于前端无人使用状态
- `apps/benefits` 和 `apps/channels` 模块仍在为权益中心产品提供内部接口供 Node 服务调用，尚未迁移到新架构

**API 路由说明**：
- `apis/base` 目前一部分能力在代理旧的 Django API
- 新开发的 API 路径以 `/v0` 开头，走新的 FastAPI 架构
- 团队在开发新功能时，应优先考虑使用新架构

## 2. 技术栈

- **后端框架**：FastAPI、Django
- **数据库**：MySQL、MongoDB
- **异步任务**：Celery
- **消息队列**：RabbitMQ、RocketMQ
- **缓存**：Redis
- **容器化**：Docker
- **依赖管理**：Poetry
- **ORM**：Tortoise-ORM、Beanie

## 3. 项目结构

```
.
├── admin/          # 管理后台相关代码
├── apis/           # API 接口层
│   ├── base/       # 基础 API
│   ├── internal/   # 内部服务 API
│   ├── mis/        # 管理系统 API
│   └── openapi/    # 开放 API
├── apps/           # 应用模块
│   ├── base/       # 基础应用
│   ├── benefits/   # 权益应用
│   ├── channels/   # 渠道应用
│   ├── movie/      # 电影应用
│   └── passport/   # 认证应用
├── common/         # 公共组件
├── core/           # 核心功能模块
│   ├── app.py      # 应用程序入口
│   ├── database.py # 数据库配置
│   ├── celery.py   # Celery配置
│   ├── errors.py   # 错误处理
│   ├── log.py      # 日志配置
│   ├── openapi.py  # OpenAPI配置
│   ├── rabbitmq/   # RabbitMQ配置
│   └── schedules.py # 定时任务
├── databases/      # 数据库相关代码
│   ├── models/     # 数据模型定义
│   └── documents/  # 文档数据模型
├── domains/        # 业务领域模块
│   ├── benefits/   # 权益相关
│   ├── customer/   # 客户相关
│   ├── delivery/   # 配送相关
│   ├── datas/      # 数据相关
│   └── passport/   # 认证相关
├── migrations/     # 数据库迁移文件
├── packages/       # 自定义包
├── scripts/        # 脚本文件
├── tests/          # 测试用例
└── utils/          # 工具类
```

## 4. 核心模块详解

### 4.1 API 层 (apis/)

API 层负责暴露各种 HTTP 接口，分为多个子模块：

- **base**: 基础 API，提供基础功能接口（**注意**：目前同时包含旧 Django API 的代理和新的 `/v0` 路径 API）
- **internal**: 内部服务 API，仅供系统内部调用
- **mis**: 管理系统 API，提供后台管理功能
- **openapi**: 开放 API，供第三方应用调用

#### APIs/openapi 认证机制

该项目实现了多种认证机制，主要通过 `apis/openapi/authorization.py` 和 `apis/openapi/middleware.py` 实现：

**认证中间件 (AuthMiddleware)**：
- `require_auth()`: 要求进行认证的静态方法
- `bearer_only()`: 仅接受 Bearer Token 认证的静态方法
- `hmac_only()`: 仅接受 HMAC 认证的静态方法

**安全路由功能**：
- `secure_router()`: 为整个路由器添加认证依赖
- `AuthenticatedAPIRoute`: 支持认证的 API 路由类
- `AuthenticatedAPIRouter`: 支持认证的 API 路由器类，可以控制单个路由的认证需求

认证流程：
1. 从请求头中提取认证信息（Bearer Token 或 HMAC）
2. 通过 AuthenticationHandler.authenticate 验证有效性
3. 返回客户信息，或在认证失败时抛出 401 错误

### 4.2 领域模块 (domains/)

领域模块包含核心业务逻辑，按照不同业务领域划分：

- **benefits**: 权益相关业务逻辑
- **customer**: 客户相关业务逻辑
- **delivery**: 配送相关业务逻辑
- **datas**: 数据处理相关业务逻辑
- **passport**: 认证相关业务逻辑

每个领域模块通常包含：
- services.py: 业务服务
- repositories.py: 数据访问层
- schemas.py: 数据模型定义

### 4.3 应用模块 (apps/)

应用模块是面向具体功能的实现，主要为旧的 Django 架构所用：

- **base**: 基础应用功能（基本废弃）
- **benefits**: 权益应用功能（仍在使用，为权益中心提供内部接口）
- **channels**: 渠道应用功能（仍在使用，为权益中心提供内部接口）
- **movie**: 电影应用功能（前端无人使用）
- **passport**: 认证应用功能（基本废弃）

### 4.4 核心功能 (core/)

核心功能模块提供项目的基础设施：

- **app.py**: FastAPI 应用程序配置和初始化
- **database.py**: 数据库连接和配置
- **celery.py**: Celery 异步任务配置
- **errors.py**: 全局错误处理
- **log.py**: 日志系统配置
- **rabbitmq/**: RabbitMQ 消息队列配置
- **schedules.py**: 定时任务配置

#### 错误处理机制

项目实现了统一的错误处理机制，通过 `core/app.py` 中的 `business_error_handler` 函数捕获并处理异常，返回标准格式的响应:

```python
class CommonResponse(BaseModel, Generic[T]):
    code: int = Field(default=0, description="状态码")
    message: str = Field(default="success", description="消息")
    data: Optional[T] = Field(default=None, description="数据对象")
```

### 4.5 数据库模块 (databases/)

数据库模块处理所有数据存储相关的逻辑：

- **models/**: 关系型数据库模型 (MySQL)
- **documents/**: 文档型数据库模型 (MongoDB)

## 5. 主要功能模块

### 5.1 电影应用 (Movie App)

包含以下主要功能：

#### 前端 API (13个)
- 电影列表、城市列表
- 影院查询、场次查询
- 订单处理（提交、支付、取消）
- 座位图查询
- 订单列表

#### 回调 API (4个)
- 出票结果、票务变更
- 退票通知、影院场次更新

#### 内部服务 (2个)
- 订单支付结果回调
- 电影订单支付服务

#### 定时任务和异步任务 (3个)
- 每日同步基础数据
- 更新影院场次信息
- 关闭未确认订单

## 6. 开发环境设置

### 6.1 依赖管理

使用 Poetry 管理项目依赖：
```bash
poetry install
```

### 6.2 环境配置

- 复制 `env.toml.example` 到 `env.toml`
- 根据需要修改配置参数

### 6.3 运行服务

```bash
poetry run python manage.py runserver
```

### 6.4 Docker 部署

```bash
docker-compose up -d
```

### 6.5 服务说明

项目使用Docker Compose管理多个微服务，主要服务如下：

- **openapi**: 提供开放API接口服务
  ```yaml
  command: poetry run uvicorn apis.openapi:app --host 0.0.0.0 --port 8000 --workers 4
  ports:
    - "5012:8000"
  ```

- **baseapi**: 提供基础API接口服务（包含旧Django API代理和新的`/v0`路径API）
  ```yaml
  command: poetry run uvicorn apis.base:app --host 0.0.0.0 --port 8000 --workers 4
  ports:
    - "5007:8000"
  ```

- **internal-web**: 提供内部服务API，基于Django框架
  ```yaml
  command: poetry run uvicorn common.applications.internal.asgi:application --host 0.0.0.0 --port 8000 --workers 4
  ports:
    - "5008:8000"
  ```

- **consumer**: RabbitMQ消息消费者服务
  ```yaml
  command: poetry run python -m core.rabbitmq.command
  ```

- **apscheduler**: 定时任务调度服务
  ```yaml
  command: poetry run python -m core.schedules
  ```

- **celery-worker**: Celery异步任务处理服务（**可选服务**，仅用于同步电影相关信息，大多数情况下可不启动）
  ```yaml
  command: poetry run celery -A common worker --loglevel=info --concurrency=4
  ```

**服务启动方式**:
- 启动所有服务: `docker-compose up -d`
- 启动指定服务: `docker-compose up -d <service-name>`
- 查看服务日志: `docker-compose logs -f <service-name>`

**必要服务**:
- 对于基础API和开放API：`baseapi`, `openapi`, `consumer`, `apscheduler`
- 对于内部服务：`internal-web`

**环境变量**:
所有服务共享以下环境变量配置:
```yaml
environment:
  - PYTHONPATH=/app
  - TZ=Asia/Shanghai
  - LC_ALL=C.UTF-8
```

## 7. 测试

项目使用 pytest 进行测试，测试文件位于 `tests/` 目录下。运行测试：

```bash
poetry run pytest
```

## 8. 代码风格

项目使用 Black 和 isort 进行代码格式化，配置信息在 `pyproject.toml` 中定义：

```toml
[tool.black]
line-length = 120
target-version = ['py312']
```

## 9. 数据库迁移

项目使用 Aerich 进行数据库迁移管理，配置信息在 `pyproject.toml` 中定义：

```toml
[tool.aerich]
tortoise_orm = "core.database.tortoise_config"
location = "./migrations"
src_folder = "./."
```

## 10. 项目维护和扩展

- 新增 API 应在对应的 apis/ 目录下添加，**优先使用 `/v0` 路径的新架构**
- 新增业务逻辑应在对应的 domains/ 目录下实现
- 尽量避免在 apps/ 目录下添加新功能，除非是对现有未迁移模块的维护
- 公共组件和工具类应分别放在 common/ 和 utils/ 目录下

### 10.1 迁移指南

对于需要维护旧代码或将旧功能迁移到新架构的开发者：

1. 了解 Django 和 FastAPI 的架构差异
2. 旧项目的配置主要在 `common/` 目录中
3. 迁移过程中，可能需要在 `apis/base` 中添加代理，将请求转发到对应的 Django 视图
4. 新功能应直接使用 FastAPI 架构在 `domains/` 中实现业务逻辑，在 `apis/` 中暴露接口

通过这种模块化架构，项目可以轻松地进行维护和扩展，团队成员可以根据自己的职责专注于特定模块的开发。

# Loguru 日志系统重构

## 重构概述

根据PRD需求，我们对日志系统进行了重构，主要实现了以下功能：

- 支持多服务独立日志记录
- 集成阿里云SLS日志服务
- 分离Uvicorn日志与应用日志
- 支持请求追踪(request_id)以及来源app追踪
- 提供清晰的上下文信息
- 模块化设计，便于维护和扩展

## 目录结构

```
core/logger/
├── __init__.py          # 对外暴露简单接口
├── config.py            # 日志配置管理
├── context.py           # 上下文管理
├── formatters.py        # 日志格式化
├── handlers/
│   ├── __init__.py
│   ├── file.py          # 文件日志处理
│   ├── console.py       # 控制台日志处理
│   └── sls.py           # 阿里云SLS处理
├── interceptors.py      # 日志拦截器
└── initializer.py       # 日志初始化
```

## 使用方法

### 基本使用

```python
from core.logger import logger, initialize_logger

# 初始化日志系统
initialize_logger(app_name="my_service")

# 使用日志
logger.info("这是一条信息日志")
logger.error("这是一条错误日志", exc_info=True)
logger.warning("这是一条警告日志", user_id="123456")
```

### FastAPI 集成

```python
from fastapi import FastAPI
from core.logger import setup_fastapi_logging

app = FastAPI(title="MyApp")

# 设置FastAPI日志记录
setup_fastapi_logging(app)
```

### 上下文变量

```python
from src.infrastructures.logger import LogContext

# 设置上下文变量
LogContext.set("request_id", "req-12345")
LogContext.set("trace_id", "trace-abcdef")
LogContext.set("user_id", "user-123")

# 批量设置上下文
LogContext.update(
    request_id="req-12345",
    trace_id="trace-abcdef",
    user_id="user-123"
)

# 使用带上下文信息的日志
logger.info("用户操作记录", action="login")
```

## 配置示例

在 `env.toml` 文件中配置：

```toml
[log]
dir = "logs"

[log.config]
level = "INFO"
rotation = "00:00"
retention = "30 days"
compression = "zip"

[log.sls]
endpoint = "cn-shanghai.log.aliyuncs.com"
project = "my-project"
logstore = "default"
access_key_id = "YOUR_ACCESS_KEY_ID"
access_key_secret = "YOUR_ACCESS_KEY_SECRET"
```

## 主要特性

1. **多服务支持**：根据应用名称自动区分日志文件和SLS存储
2. **高性能批处理**：SLS日志使用批量发送减少API调用
3. **完整上下文追踪**：支持request_id, trace_id等上下文信息
4. **格式统一**：统一的日志格式便于解析和查询
5. **错误详情增强**：异常日志包含完整堆栈信息
6. **标准库拦截**：拦截并统一处理所有标准库日志
7. **Uvicorn日志集成**：将Uvicorn日志纳入统一管理

## 迁移指南

从旧版日志系统迁移到新版非常简单：

1. 将导入从 `from core.log import logger` 改为 `from core.logger import logger`
2. 在应用启动时调用 `initialize_logger()` 或 `setup_fastapi_logging(app)`
3. 日志调用方式保持不变: `logger.info()`, `logger.error()` 等

## 测试

可以运行 `test_logger.py` 测试日志系统的功能：

```bash
python test_logger.py
```