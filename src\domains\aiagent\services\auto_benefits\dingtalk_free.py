# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/aiagent/services/auto_benefits/dingtalk_free.py
# created: 2025-04-18 00:46:21
# updated: 2025-04-22 09:17:20

from datetime import datetime
from random import randrange
from typing import TYPE_CHECKING, Optional

from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.databases.models.benefits import BenefitsProductOrder
from src.domains.benefits.dto import BenefitsProductOrderDTO
from src.infrastructures import errors

from .auto_benefits_factor import AutoBenefitStrategy

if TYPE_CHECKING:
    from redis.asyncio import Redis

    from src.domains.benefits.services import BenefitsProductService
    from src.domains.customer.services.benefits_charge import CustomerBenefitService
    from src.repositories.customer import CustomerRepository
    from src.repositories.passport import AppRepository, TenantRepository


DINGTALK_CUSTOMER_CODE = "zjTWxm4z"
OPEN_PLATFORM_APP_ID = "OPEN_PLATFORM"
DINGTALK_TENANT_ID = "q7tkbeY4R0yxt6DV"
BENEFITS_PRODUCT_LIST = [
    "PHmRcQBUDyEus",  # 20-6元满减红包, 无商业化
    "PoFtXQNxyHfZK",  # 25-7元满减红包, 无商业化
    "PhKNJspVCQ7f2",  # 30-8元满减红包, 无商业化
    "PDlQRrg80ustw",  # 36-9元满减红包, 无商业化
    "PvdTYeHtzKIww",  # 35-8元满减红包, 无商业化
    "PTHSdqofzAgKV",  # 45-10元满减红包, 无商业化
]


class DingtalkFreeStrategy(AutoBenefitStrategy):

    @inject
    def __init__(
        self,
        redis: "Redis" = Provide["redis"],
        product_service: "BenefitsProductService" = Provide["benefits_product_service"],
        customer_repo: "CustomerRepository" = Provide["customer_repo"],
        customer_benefits_service: "CustomerBenefitService" = Provide["customer_benefit_service"],
    ):
        self.redis = redis
        self.product_service = product_service
        self.customer_repo = customer_repo
        self.customer_benefits_service = customer_benefits_service

    async def check_user_today_charge_order(
        self, phone: str, union_id: str, throw_error: bool = True
    ) -> Optional[BenefitsProductOrder]:
        """检查用户今日是否已领取权益"""
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        order = await BenefitsProductOrder.filter(
            account=phone,
            app__app_id=OPEN_PLATFORM_APP_ID,
            tenant_id=DINGTALK_TENANT_ID,
            created_at__gte=today,
            created_at__lte=tomorrow,
        ).first()

        if order:
            logger.warning(
                f"用户今日已领取权益: {union_id}, order_id: {order.order_id}, order_status: {order.status}, order_product_code: {order.product_code}."
            )
            if not throw_error:
                return order

            raise errors.AutoBenefitsApplyAlreadyAppliedError
        return None

    async def apply(
        self, phone: str, union_id: str, agent_id: str, agent_user_id: str, uid: str
    ) -> "BenefitsProductOrderDTO":
        # 检查用户今日是否已领取权益
        order = await self.check_user_today_charge_order(phone, union_id)
        if order:
            return await BenefitsProductOrderDTO.from_tortoise_orm(order)

        # 随机选择一个权益
        product_code = BENEFITS_PRODUCT_LIST[randrange(0, len(BENEFITS_PRODUCT_LIST), 1)]
        product = await self.product_service.get_product_by_code(product_code)
        if not product:
            logger.error("钉钉免费权益发放失败，权益产品不存在, product_code: {product_code}")
            raise errors.BenefitsProductNotFoundError

        # 发放权益
        customer = await self.customer_repo.get_by_code(DINGTALK_CUSTOMER_CODE)
        if not customer:
            logger.warning(f"客户不存在: {DINGTALK_CUSTOMER_CODE}")
            raise errors.CustomerNotFoundError

        logger.info(f"开始发放权益: {product.name}-{product.code}, 用户: {phone}")
        order_info = await self.customer_benefits_service.charge_benefit(
            customer,
            product_code,
            phone,
            f"dingtalk_{phone}_{product.code}_{datetime.now().strftime('%Y%m%d')}",
            "",
            f"{OPEN_PLATFORM_APP_ID}.dingtalk_ai.{union_id}",
        )
        logger.info(f"自动领取权益成功: {order_info.model_dump()}, 用户: {phone}, 权益: {product.name}-{product.code}")
        return order_info
