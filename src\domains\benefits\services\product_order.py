# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/product_order.py
# created: 2025-01-11 21:30:00
# updated: 2025-01-11 21:30:00

import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from loguru import logger

from src.domains.benefits.dto import ProductOrderExportRecord, ProductOrderExportStatus
from src.domains.benefits.messages import ProductOrderExportMessage, ProductOrderExportMessageContent
from src.infrastructures.databases import RedisManager
from src.infrastructures.rabbitmq import RabbitMQProducer
from src.repositories.benefits.product_order import ProductOrderExportFilters

if TYPE_CHECKING:
    from src.domains.passport.entities import UserEntity


class ProductOrderService:
    """产品订单服务"""

    def __init__(self, redis_client: RedisManager, producer: RabbitMQProducer):
        self.redis_client = redis_client.client
        self.producer = producer

    async def create_export_task(
        self, filters: ProductOrderExportFilters, user: "UserEntity"
    ) -> ProductOrderExportRecord:
        """创建产品订单导出任务"""
        # 生成导出任务ID
        export_id = str(uuid.uuid4())

        # 创建导出记录
        export_record = ProductOrderExportRecord(
            export_id=export_id,
            status=ProductOrderExportStatus.PENDING.value,
            file_url=None,
            error_message=None,
            created_at=datetime.now(),
            completed_at=None,
            user_id=user.uid,
            user_name=user.nickname,
            filters=filters.model_dump(exclude_none=True),
        )

        # 保存导出记录到Redis
        redis_key = f"product_order_export:{user.uid}:{export_id}"  # type: ignore
        await self.redis_client.set(redis_key, export_record.model_dump_json())

        # 发送异步导出消息
        export_message = ProductOrderExportMessage(
            payload=ProductOrderExportMessageContent(
                export_id=export_id,
                user_id=user.uid,  # type: ignore
                user_name=user.nickname,  # type: ignore
                filters=filters.model_dump(exclude_none=True),
                created_at=datetime.now(),
            )
        )
        await self.producer.publish_message(export_message)

        logger.info(f"创建产品订单导出任务: export_id={export_id}, user={user.uid}")  # type: ignore
        return export_record

    async def get_export_records(self, user: "UserEntity") -> list[ProductOrderExportRecord]:
        """获取用户的产品订单导出记录"""
        pattern = f"product_order_export:{user.uid}:*"  # type: ignore
        keys = await self.redis_client.keys(pattern)

        records = []
        for key in keys:
            record_data = await self.redis_client.get(key)
            if record_data:
                try:
                    record_json = record_data.decode("utf-8") if isinstance(record_data, bytes) else record_data
                    records.append(ProductOrderExportRecord.model_validate_json(record_json))
                except (ValueError, Exception) as e:
                    logger.warning(f"解析导出记录失败: {e}")
                    continue

        # 按创建时间倒序排列
        records.sort(key=lambda x: x.created_at, reverse=True)
        return records
