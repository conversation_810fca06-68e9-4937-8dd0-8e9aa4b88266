# encoding: utf-8
# src/applications/passport/dto.py
# created: 2025-07-31 17:10:00

from typing import TYPE_CHECKING, List, Optional

from pydantic import BaseModel, Field

if TYPE_CHECKING:
    from src.domains.passport.entities import UserEntity


class UserRelationDTO(BaseModel):
    uid: str = Field(..., description="用户ID")
    app_id: str = Field(..., description="应用ID")
    tenant_id: str = Field(..., description="租户ID")


class UserInfoDTO(BaseModel):
    """用户信息DTO"""

    uid: str = Field(..., description="用户ID")
    phone: Optional[str] = Field("", description="手机号")
    nickname: str = Field(..., description="用户昵称")
    avatar_url: Optional[str] = Field("", description="头像URL")
    email: Optional[str] = Field("", description="邮箱")
    app_id: Optional[str] = Field("", description="应用ID")
    app_name: Optional[str] = Field("", description="应用名称")
    tenant_id: Optional[str] = Field("", description="租户ID")
    tenant_name: Optional[str] = Field("", description="租户名称")
    token: Optional[str] = Field("", description="jwt token")

    @classmethod
    def from_user_entity(cls, user_entity: "UserEntity") -> "UserInfoDTO":
        return cls(
            uid=user_entity.uid,
            phone=user_entity.phone,
            nickname=user_entity.nickname,
            avatar_url=user_entity.avatar_url,
            email=user_entity.email,
            app_id=user_entity.current_app.app_id if user_entity.current_app else "",
            app_name=user_entity.current_app.app_name if user_entity.current_app else "",
            tenant_id=user_entity.current_tenant.tenant_id if user_entity.current_tenant else "",
            tenant_name=user_entity.current_tenant.tenant_name if user_entity.current_tenant else "",
            token=user_entity.jwt,
        )


class LoginRequestDTO(BaseModel):
    """登录请求DTO"""

    auth_code: str = Field(..., description="认证码")
    app_id: str = Field(..., description="应用ID")
    tenant_id: Optional[str] = Field(None, description="租户ID")
    phone_number: Optional[str] = Field(None, description="手机号")
    strategy: str = Field(..., description="登录策略")


class LoginResponseDTO(BaseModel):
    """登录响应DTO"""

    token: str = Field(..., description="访问令牌")
    user_info: "UserInfoDTO" = Field(..., description="用户信息")
    app_info: "AppInfoDTO" = Field(..., description="应用信息")
    tenant_info: Optional["TenantInfoDTO"] = Field(None, description="租户信息")


class AuthResultDTO(BaseModel):
    """认证结果DTO"""

    success: bool = Field(..., description="认证是否成功")
    user_info: Optional["UserInfoDTO"] = Field(None, description="用户信息")
    error_message: str = Field("", description="错误信息")


class AppInfoDTO(BaseModel):
    """应用信息DTO"""

    app_id: str = Field(..., description="应用ID")
    app_name: str = Field(..., description="应用名称")


class TenantInfoDTO(BaseModel):
    """租户信息DTO"""

    tenant_id: str = Field(..., description="租户ID")
    tenant_name: str = Field(..., description="租户名称")
    app_id: str = Field(..., description="所属应用ID")
    app_name: str = Field(..., description="所属应用名称")

    class Config:
        from_attributes = True


class UserTenantListDTO(BaseModel):
    """用户租户列表DTO"""

    tenants: List[TenantInfoDTO] = Field(..., description="租户列表")


class SwitchTenantRequestDTO(BaseModel):
    """切换租户请求DTO"""

    uid: str = Field(..., description="用户ID")
    app_id: str = Field(..., description="应用ID")
    tenant_id: str = Field(..., description="目标租户ID")


class SwitchTenantResponseDTO(BaseModel):
    """切换租户响应DTO"""

    token: str = Field(..., description="新的访问令牌")
    tenant_info: TenantInfoDTO = Field(..., description="租户信息")


class CreateUserRequestDTO(BaseModel):
    """创建用户请求DTO"""

    phone_number: Optional[str] = Field(None, description="手机号")
    nickname: Optional[str] = Field(None, description="昵称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    email: Optional[str] = Field(None, description="邮箱")


class UpdateUserRequestDTO(BaseModel):
    """更新用户请求DTO"""

    nickname: Optional[str] = Field(None, description="昵称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    email: Optional[str] = Field(None, description="邮箱")


class CreateAppRequestDTO(BaseModel):
    """创建应用请求DTO"""

    app_name: str = Field(..., description="应用名称")
    app_id: str = Field(..., description="应用ID")


class CreateTenantRequestDTO(BaseModel):
    """创建租户请求DTO"""

    app_id: str = Field(..., description="应用ID")
    tenant_name: str = Field(..., description="租户名称")


# 为了避免循环导入，使用字符串注解
LoginResponseDTO.model_rebuild()
AuthResultDTO.model_rebuild()
