# encoding: utf-8
# src/infrastructures/browsers/session.py
# created: 2025-08-08 00:02:12

import contextlib
from typing import TYPE_CHECKING, Any, AsyncIterator, Dict, List, Optional

from loguru import logger
from playwright.async_api import <PERSON>rowser<PERSON>ontex<PERSON>, <PERSON>ie, Geolocation, ProxySettings, Request, Route, StorageState

from .pools import get_browser_pool
from .resource_cache import resource_cache
from .script_manager import script_manager
from .types import DeviceInfo, WebViewInfo

if TYPE_CHECKING:
    from playwright.async_api import BrowserContext, Page


class PageSession:

    def __init__(
        self, context: "BrowserContext", proxy: Optional[ProxySettings] = None, enable_resource_cache: bool = True
    ):
        self.context = context
        self.proxy = proxy  # 保存代理信息
        self.page: Optional["Page"] = None
        self.enable_resource_cache = enable_resource_cache
        self._route_handler_set = False

    async def create_page(self) -> None:
        self.page = await self.context.new_page()

        # 设置初始化脚本
        await self._setup_init_script()

        # 授予地理位置权限
        await self._grant_permissions()

        # 设置资源缓存路由拦截
        if self.enable_resource_cache and not self._route_handler_set:
            await self._setup_resource_cache()

    async def get_cookies(self) -> List[Cookie]:
        """获取当前会话的 cookies"""
        return await self.context.cookies()

    async def get_local_storage(self) -> Dict[str, Any]:
        """获取当前页面的 localStorage"""
        if not self.page:
            return {}

        try:
            return await self.page.evaluate(
                """
                () => {
                    try {
                        const storage = {};
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            storage[key] = localStorage.getItem(key);
                        }
                        return storage;
                    } catch (e) {
                        // localStorage 不可访问（如 about:blank 或跨域）
                        return {};
                    }
                }
                """
            )
        except Exception as e:
            logger.debug(f"Failed to get localStorage: {str(e).replace('<', '').replace('>', '')}")
            return {}

    async def get_session_storage(self) -> Dict[str, Any]:
        """获取当前页面的 sessionStorage"""
        if not self.page:
            return {}

        try:
            return await self.page.evaluate(
                """
                () => {
                    try {
                        const storage = {};
                        for (let i = 0; i < sessionStorage.length; i++) {
                            const key = sessionStorage.key(i);
                            storage[key] = sessionStorage.getItem(key);
                        }
                        return storage;
                    } catch (e) {
                        // sessionStorage 不可访问（如 about:blank 或跨域）
                        return {};
                    }
                }
                """
            )
        except Exception as e:
            logger.debug(f"Failed to get sessionStorage: {str(e).replace('<', '').replace('>', '')}")
            return {}

    async def collect_browser_data(self) -> Dict[str, Any]:
        """收集所有浏览器数据（cookies、localStorage、sessionStorage）"""
        return {
            "cookies": await self.get_cookies(),
            "local_storage": await self.get_local_storage(),
            "session_storage": await self.get_session_storage(),
        }

    async def _setup_init_script(self) -> None:
        """设置初始化脚本（浏览器环境伪装）"""
        if not self.page:
            return

        # 获取屏幕尺寸（从 viewport 获取）
        viewport = self.page.viewport_size
        screen_width = viewport["width"] if viewport else 390
        screen_height = viewport["height"] if viewport else 844

        # 获取初始化脚本
        init_script = script_manager.get_init_script(
            screen_width=screen_width,
            screen_height=screen_height,
            local_storage={},  # localStorage 已经在 storage_state 中设置
            session_storage={},  # sessionStorage 已经在 storage_state 中设置
        )

        if init_script:
            await self.context.add_init_script(script=init_script)

    async def _grant_permissions(self) -> None:
        """授予必要的权限"""
        try:
            await self.context.grant_permissions(["geolocation"])
        except Exception:
            pass  # 忽略权限设置失败

    async def _setup_resource_cache(self) -> None:
        """设置资源缓存路由拦截"""
        await self.context.route("**/*", self._handle_route)
        self._route_handler_set = True

    async def _handle_route(self, route: Route, request: Request) -> None:
        """处理路由请求 - 委托给统一的缓存系统"""
        current_page_url = self.page.url if self.page else None
        await resource_cache.handle_route(route, request, self.enable_resource_cache, current_page_url)


@contextlib.asynccontextmanager
async def create_session(
    device: DeviceInfo,
    webview: WebViewInfo,
    state: StorageState,
    location: Geolocation,
    ip_proxy: Optional[ProxySettings] = None,
    enable_cache: bool = True,
) -> AsyncIterator[PageSession]:
    """
    创建一个浏览器会话

    Args:
        device: 设备信息
        webview: WebView信息
        state: 存储状态（cookies、localStorage等）
        location: 地理位置
        ip_proxy: 代理设置
        enable_cache: 是否启用资源缓存
    """
    browser_pool = get_browser_pool()
    if not browser_pool:
        raise RuntimeError("Browser pool not initialized. Call init_browser_pool() first.")

    context = await browser_pool.acquire(device, webview, state, location, ip_proxy)
    session = PageSession(context, proxy=ip_proxy, enable_resource_cache=enable_cache)

    try:
        await session.create_page()
        yield session
    finally:
        await browser_pool.release(context)
