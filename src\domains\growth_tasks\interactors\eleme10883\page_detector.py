# encoding: utf-8
# src/domains/growth_tasks/interactors/eleme10883/page_detector.py
# created: 2025-08-08 01:12:47

import re
from typing import TYPE_CHECKING, Optional

from loguru import logger
from playwright.async_api import ElementHandle

from src.infrastructures.exceptions.growth_hacker import (
    AlreadyClaimedError,
    ElementNotFoundError,
    PageContentError,
    RiskDetectedError,
)
from src.infrastructures.utils.decorator import retry

from .risk_detector import RiskDetector

if TYPE_CHECKING:
    from src.infrastructures.browsers import PageSession


class InteractionResult:
    """交互结果"""

    def __init__(self, success: bool, message: str = "", data: Optional[dict] = None):
        self.success = success
        self.message = message
        self.data = data or {}

    def __bool__(self) -> bool:
        return self.success


class PageDetector:
    """页面检测器"""

    def __init__(self, session: "PageSession"):
        self.session = session
        if not session.page:
            raise ValueError("PageSession must have a valid page")
        self.risk_detector = RiskDetector(session.page)
        self.hongbao_button: Optional[ElementHandle] = None

    async def _check_already_received(self) -> bool:
        """检查是否已领取红包"""
        if not self.session.page:
            raise PageContentError("页面未加载")

        pattern = r"你已领取.*?个红包"
        html_content = await self.session.page.content()
        text_content = re.sub(r"<[^>]*>", "", html_content).strip()

        match = re.search(pattern, text_content)
        if match:
            raise AlreadyClaimedError(f"红包已领取，领取个数: {match.group(0)}")

        return True

    async def _check_hongbao_button(self) -> None:
        """检查红包领取按钮"""

        if not self.session.page:
            raise PageContentError("页面未加载")

        # 根据实际HTML结构优化的CSS选择器
        button_selectors = [
            # 精确匹配实际结构
            "tiga-view.pack-btn tiga-view.btn-onekey",  # 嵌套结构
            "tiga-view.btn-onekey",  # 直接匹配btn-onekey类
            ".pack-btn .btn-onekey",  # 类选择器组合
            ".btn-onekey",  # 单独的btn-onekey类
            '[class*="btn-onekey"]',  # 包含btn-onekey的类
            '[class*="pack-btn"]',  # 包含pack-btn的类
            # 通过多个类名组合查找
            'tiga-view[class*="pack-btn"][class*="c7ffbdadfce28436b1f1b03686099463"]',
            'tiga-view[class*="btn-onekey"][class*="a0ab9426cac54f89b6865b2e37b57d88"]',
        ]

        # 使用 locator 检查标准选择器
        for selector in button_selectors:
            locator = self.session.page.locator(selector).first
            if await locator.count() > 0:
                # locator 不能直接赋值给 ElementHandle，需要获取元素
                self.hongbao_button = await locator.element_handle()
                break

        # 如果标准选择器没找到，使用文本内容查找
        if not self.hongbao_button:
            # 使用Playwright的文本选择器和 locator
            text_selectors = [
                ("text", "一键领取红包"),
                ("text", "领取红包"),
                ("text", "领取"),
                ("button", "一键领取红包"),
                ("button", "领取红包"),
                ("button", "领取"),
                ("[role=button]", "一键领取红包"),
                ("[role=button]", "领取红包"),
            ]

            for selector_type, text in text_selectors:
                try:
                    if selector_type == "text":
                        # 使用 getByText
                        locator = self.session.page.get_by_text(text, exact=False).first
                    else:
                        # 使用 locator 与 filter
                        locator = self.session.page.locator(selector_type).filter(has_text=text).first

                    if await locator.count() > 0:
                        self.hongbao_button = await locator.element_handle()
                        break
                except Exception:
                    # 某些选择器可能不支持，继续尝试下一个
                    continue

        # 最后尝试：通过JavaScript查找包含特定文本的元素
        if not self.hongbao_button:
            button = await self.session.page.evaluate(
                """
                () => {
                    const keywords = ['一键领取红包', '领取红包', '领取', '立即领取'];
                    
                    // 优先查找tiga-view结构的按钮
                    const prioritySelectors = [
                        'tiga-view[class*="pack-btn"]',
                        'tiga-view[class*="btn-onekey"]', 
                        'tiga-view[class*="btn"]',
                    ];
                    
                    // 首先尝试优先选择器
                    for (const selector of prioritySelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim() || '';
                            if (keywords.some(keyword => text.includes(keyword))) {
                                // 如果是嵌套结构，尝试找到最外层的可点击元素
                                let clickableEl = el;
                                while (clickableEl.parentElement) {
                                    const parent = clickableEl.parentElement;
                                    if (parent.tagName.toLowerCase() === 'tiga-view' && 
                                        (parent.className.includes('pack-btn') || parent.className.includes('btn'))) {
                                        clickableEl = parent;
                                    } else {
                                        break;
                                    }
                                }
                                return clickableEl;
                            }
                        }
                    }
                    
                    // 备选查找所有可能的按钮元素
                    const fallbackSelectors = [
                        'button', 'tiga-view', '.btn', '[class*="btn"]', 
                        '[role="button"]', 'a', 'div[onclick]'
                    ];
                    
                    for (const selector of fallbackSelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim() || '';
                            if (keywords.some(keyword => text.includes(keyword))) {
                                return el;
                            }
                        }
                    }
                    return null;
                }
            """
            )

            if button:
                # 将JavaScript返回的元素转换为ElementHandle
                self.hongbao_button = button

        if not self.hongbao_button:
            raise ElementNotFoundError("红包领取按钮")

    @retry(max_attempts=20, delay=3.0)
    async def detect_page(self) -> InteractionResult:
        """检测页面内容"""
        if not self.session.page:
            raise PageContentError("页面未加载")

        try:
            # 检查页面内容（添加超时保护）
            body = self.session.page.locator("body")
            await body.wait_for(state="attached", timeout=10000)

            await self.risk_detector.detect()  # 检查风控

            # 检查内容长度
            body_text = await body.inner_text()
            content_length = len(body_text.strip())

            # 如果内容过少，记录详细信息并抛出异常
            if content_length < 50:  # 降低阈值到50字符，更宽松
                logger.debug(f"页面内容: {body_text[:500]}...")  # 打印前500字符
                page_html = await self.session.page.content() if self.session.page else ""
                logger.debug(f"页面HTML长度: {len(page_html)}")
                raise PageContentError(
                    message=f"页面内容过少({content_length} 字符)，可能是页面加载失败或反爬虫",
                    content_length=content_length,
                    content_snippet=page_html[:1000],  # 只保存前1000字符
                )

            await self._check_already_received()  # 检查红包是否已领取
            await self._check_hongbao_button()  # 检查红包领取按钮

            return InteractionResult(True, "页面内容正常")
        except (AlreadyClaimedError, RiskDetectedError) as e:
            raise e
        except Exception as e:
            raise PageContentError(f"页面检测失败: {e}") from e
