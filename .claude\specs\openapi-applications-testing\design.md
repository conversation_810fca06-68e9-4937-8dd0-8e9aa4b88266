# encoding: utf-8
# .claude/specs/openapi-applications-testing/design_v9533.md
# created: 2025-08-02 14:45:00

# OpenAPI 应用测试设计文档

## 概述

本设计文档定义了 OpenAPI 应用层测试功能的技术架构和实现方案。该测试套件将为 `src/applications/openapi/` 模块提供全面的单元测试和集成测试覆盖，确保认证器、查询服务、业务服务和依赖注入容器的质量和可靠性。

### 设计目标

- 建立全面的测试覆盖，覆盖率达到 85% 以上
- 提供可靠的测试数据工厂和 Mock 机制
- 实现端到端的业务流程测试
- 确保错误处理和异常场景的测试覆盖
- 支持性能和安全性测试

### 范围

- OpenAPI 认证器（Token 和 AKSK 认证）
- OpenAPI 查询服务（活动、客户、权益查询）
- OpenAPI 业务服务（权益充值、爱心餐服务）
- 依赖注入容器配置和生命周期管理
- 错误处理和异常管理
- 测试数据工厂和 Mock 对象

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "测试层 Test Layer"
        UT[单元测试 Unit Tests]
        IT[集成测试 Integration Tests]
        PT[性能测试 Performance Tests]
        ST[安全测试 Security Tests]
    end
    
    subgraph "测试工具层 Test Tools"
        TF[测试工厂 Test Factories]
        MO[Mock 对象 Mock Objects]
        FX[测试固件 Test Fixtures]
        TU[测试工具 Test Utils]
    end
    
    subgraph "被测试对象 System Under Test"
        subgraph "OpenAPI 应用层"
            AUTH[认证器 Authenticators]
            QUERY[查询服务 Query Services]
            BIZ[业务服务 Business Services]
            CONTAINER[依赖注入容器 DI Container]
        end
        
        subgraph "依赖层 Dependencies"
            DOMAINS[领域服务 Domain Services]
            REPOS[仓储层 Repositories]
            INFRA[基础设施 Infrastructure]
        end
    end
    
    UT --> TF
    UT --> MO
    IT --> FX
    PT --> TU
    
    UT --> AUTH
    UT --> QUERY
    UT --> BIZ
    IT --> CONTAINER
    
    AUTH --> REPOS
    QUERY --> DOMAINS
    BIZ --> DOMAINS
    CONTAINER --> DOMAINS
    CONTAINER --> REPOS
    CONTAINER --> INFRA
```

### 数据流图

```mermaid
graph LR
    subgraph "测试执行流程"
        A[测试启动] --> B[加载测试配置]
        B --> C[初始化测试环境]
        C --> D[创建 Mock 对象]
        D --> E[设置测试数据]
        E --> F[执行测试用例]
        F --> G{测试结果}
        G -->|通过| H[生成覆盖率报告]
        G -->|失败| I[记录错误信息]
        H --> J[清理测试环境]
        I --> J
        J --> K[测试完成]
    end
    
    subgraph "测试数据管理"
        TD[测试数据] --> TF[测试工厂]
        TF --> TE[测试实体]
        TE --> TC[测试用例]
        TC --> TR[测试结果]
    end
```

## 组件设计

### 测试基础组件

#### BaseOpenapiUnitTest

```python
class BaseOpenapiUnitTest:
    """OpenAPI 模块单元测试基类"""
    
    @pytest.fixture
    def mock_customer_repository(self) -> AsyncMock:
        """Mock 客户仓储"""
        
    @pytest.fixture  
    def mock_benefits_repository(self) -> AsyncMock:
        """Mock 权益仓储"""
        
    @pytest.fixture
    def mock_redis_manager(self) -> AsyncMock:
        """Mock Redis 管理器"""
        
    @pytest.fixture
    def mock_external_gateways(self) -> Dict[str, AsyncMock]:
        """Mock 外部网关集合"""
```

#### OpenapiTestFactories

```python
class CustomerFactory:
    """客户测试数据工厂"""
    
    @staticmethod
    def create_customer_entity() -> CustomerEntity:
        """创建客户实体"""
        
    @staticmethod
    def create_customer_secret() -> CustomerSecret:
        """创建客户密钥"""

class BenefitsFactory:
    """权益测试数据工厂"""
    
    @staticmethod
    def create_benefits_product() -> BenefitsProduct:
        """创建权益产品"""
        
    @staticmethod
    def create_benefits_order() -> BenefitsOrder:
        """创建权益订单"""
```

### 认证器测试组件

#### OpenAPITokenAuthenticatorTest

**职责:**
- 测试 Bearer Token 认证逻辑
- 验证 Token 有效性检查
- 测试客户信息获取
- 测试异常情况处理

**接口:**
```python
class TestOpenAPITokenAuthenticator(BaseOpenapiUnitTest):
    async def test_authenticate_with_valid_token(self)
    async def test_authenticate_with_invalid_token(self)
    async def test_authenticate_with_missing_token(self)
    async def test_authenticate_with_repository_error(self)
```

**依赖:**
- CustomerRepository (Mock)
- CustomerEntity (Factory)

#### OpenAPIAKSKAuthenticatorTest

**职责:**
- 测试 Access Key Secret Key 认证
- 验证签名计算和验证
- 测试客户信息获取
- 测试认证失败场景

**接口:**
```python
class TestOpenAPIAKSKAuthenticator(BaseOpenapiUnitTest):
    async def test_authenticate_with_valid_aksk(self)
    async def test_authenticate_with_invalid_signature(self)
    async def test_authenticate_with_missing_credentials(self)
    async def test_authenticate_signature_calculation(self)
```

### 查询服务测试组件

#### ActivitiesQueryServiceTest

**职责:**
- 测试活动查询功能
- 验证外部网关集成
- 测试缓存机制
- 测试错误处理

**接口:**
```python
class TestActivitiesQueryService(BaseOpenapiUnitTest):
    async def test_query_activities_success(self)
    async def test_query_activities_with_cache(self)
    async def test_query_activities_gateway_error(self)
    async def test_query_activities_cache_fallback(self)
```

**依赖:**
- DeliveryPageService (Mock)
- BifrostGateway (Mock)
- ElemeUnionGateway (Mock)
- RedisManager (Mock)

#### BenefitsQueryServiceTest

**职责:**
- 测试权益查询功能
- 验证订单查询逻辑
- 测试权限验证
- 测试数据转换

**接口:**
```python
class TestBenefitsQueryService(BaseOpenapiUnitTest):
    async def test_get_order_by_order_id(self)
    async def test_get_order_by_out_order_id(self)
    async def test_get_products_by_customer(self)
    async def test_query_with_invalid_source(self)
```

### 业务服务测试组件

#### BenefitsChargeServiceTest

**职责:**
- 测试权益充值业务逻辑
- 验证事务处理
- 测试库存管理
- 测试订单创建

**接口:**
```python
class TestBenefitsChargeService(BaseOpenapiUnitTest):
    async def test_charge_benefit_success(self)
    async def test_charge_benefit_insufficient_stock(self)
    async def test_charge_benefit_invalid_customer(self)
    async def test_charge_benefit_transaction_rollback(self)
    async def test_charge_benefit_again(self)
```

**依赖:**
- BenefitsRepository (Mock)
- BenefitsProductService (Mock)
- ProductOrderRepository (Mock)

#### ElemeAixincanServiceTest

**职责:**
- 测试爱心餐服务集成
- 验证外部网关调用
- 测试异常处理
- 测试响应数据处理

**接口:**
```python
class TestElemeAixincanService(BaseOpenapiUnitTest):
    async def test_aixincan_service_success(self)
    async def test_aixincan_service_gateway_error(self)
    async def test_aixincan_service_timeout(self)
    async def test_aixincan_service_invalid_response(self)
```

### 容器测试组件

#### OpenapiApplicationsContainerTest

**职责:**
- 测试依赖注入配置
- 验证服务实例化
- 测试容器生命周期
- 验证依赖关系

**接口:**
```python
class TestOpenapiApplicationsContainer(BaseOpenapiUnitTest):
    def test_container_configuration(self)
    def test_authenticator_providers(self)
    def test_query_service_providers(self)
    def test_business_service_providers(self)
    def test_container_dependencies_validation(self)
```

## 数据模型

### 核心数据结构定义

```python
# 测试配置数据结构
@dataclass
class TestConfig:
    """测试配置"""
    database_url: str
    redis_url: str
    test_timeout: int
    mock_external_services: bool

# 测试结果数据结构
@dataclass 
class TestResult:
    """测试结果"""
    test_name: str
    status: TestStatus
    execution_time: float
    error_message: Optional[str]
    coverage_percentage: float

# Mock 配置数据结构
@dataclass
class MockConfig:
    """Mock 配置"""
    service_name: str
    mock_responses: Dict[str, Any]
    error_scenarios: List[str]
```

### 数据模型图

```mermaid
erDiagram
    TestConfig {
        string database_url
        string redis_url
        int test_timeout
        bool mock_external_services
    }
    
    TestResult {
        string test_name
        enum status
        float execution_time
        string error_message
        float coverage_percentage
    }
    
    MockConfig {
        string service_name
        json mock_responses
        array error_scenarios
    }
    
    TestSuite {
        string name
        array test_cases
        TestConfig config
    }
    
    TestCase {
        string name
        string description
        array fixtures
        TestResult result
    }
    
    TestSuite ||--o{ TestCase : contains
    TestCase ||--|| TestResult : produces
    TestSuite ||--|| TestConfig : uses
    TestCase ||--o{ MockConfig : configures
```

## 业务流程

### 流程 1：认证器测试流程

```mermaid
flowchart TD
    A[开始认证器测试] --> B[创建测试环境]
    B --> C[初始化 Mock 对象]
    C --> D[mock_customer_repository.configure]
    D --> E[设置测试数据]
    E --> F[CustomerFactory.create_customer_entity]
    F --> G[执行认证测试]
    G --> H{认证类型?}
    H -->|Token| I[测试 Bearer Token 认证]
    H -->|AKSK| J[测试 AKSK 认证]
    I --> K[验证 token 解析]
    J --> L[验证签名计算]
    K --> M[检查客户信息获取]
    L --> M
    M --> N[验证错误处理]
    N --> O[生成测试报告]
    O --> P[清理测试环境]
    P --> Q[测试完成]
```

### 流程 2：查询服务测试流程

```mermaid
sequenceDiagram
    participant TF as TestFixture
    participant QS as QueryService
    participant MR as MockRepository
    participant MG as MockGateway
    participant MC as MockCache
    
    TF->>QS: 初始化查询服务
    TF->>MR: 配置 Mock 仓储
    TF->>MG: 配置 Mock 网关
    TF->>MC: 配置 Mock 缓存
    
    TF->>QS: 执行查询请求
    QS->>MC: 检查缓存
    MC-->>QS: 返回缓存结果或空
    
    alt 缓存未命中
        QS->>MG: 调用外部网关
        MG-->>QS: 返回数据
        QS->>MC: 更新缓存
    end
    
    QS->>MR: 查询数据库
    MR-->>QS: 返回查询结果
    
    QS-->>TF: 返回处理结果
    TF->>TF: 验证结果正确性
    TF->>TF: 检查 Mock 调用次数
```

### 流程 3：业务服务测试流程

```mermaid
flowchart TD
    A[开始业务服务测试] --> B[初始化测试环境]
    B --> C[配置事务管理器]
    C --> D[设置业务数据]
    D --> E[BenefitsFactory.create_benefits_product]
    E --> F[开始事务测试]
    F --> G[执行业务操作]
    G --> H[benefits_charge_service.charge_benefit]
    H --> I{操作成功?}
    I -->|是| J[验证数据变更]
    I -->|否| K[验证事务回滚]
    J --> L[检查订单创建]
    K --> M[验证错误处理]
    L --> N[验证库存扣减]
    M --> O[验证异常日志]
    N --> P[提交事务]
    O --> P
    P --> Q[生成测试报告]
    Q --> R[清理测试数据]
    R --> S[测试完成]
```

### 流程 4：集成测试流程

```mermaid
sequenceDiagram
    participant IT as IntegrationTest
    participant DC as DIContainer
    participant AS as AuthService
    participant QS as QueryService
    participant BS as BusinessService
    participant DB as Database
    participant RC as RedisCache
    participant EG as ExternalGateway
    
    IT->>DC: 初始化容器
    DC->>AS: 注入认证服务
    DC->>QS: 注入查询服务
    DC->>BS: 注入业务服务
    
    IT->>AS: 执行认证
    AS->>DB: 查询客户信息
    DB-->>AS: 返回客户数据
    AS-->>IT: 返回认证结果
    
    IT->>QS: 执行查询
    QS->>RC: 检查缓存
    RC-->>QS: 返回缓存或空
    QS->>EG: 调用外部服务
    EG-->>QS: 返回数据
    QS-->>IT: 返回查询结果
    
    IT->>BS: 执行业务操作
    BS->>DB: 开始事务
    BS->>DB: 执行业务逻辑
    BS->>DB: 提交事务
    BS-->>IT: 返回操作结果
    
    IT->>IT: 验证端到端流程
```

## 错误处理策略

### 错误分类和处理机制

```mermaid
graph TD
    A[异常发生] --> B{异常类型}
    B -->|业务异常| C[OpenapiBusinessError]
    B -->|系统异常| D[SystemError]
    B -->|网络异常| E[NetworkError]
    B -->|数据异常| F[DataError]
    
    C --> G[记录业务日志]
    D --> H[记录系统日志]
    E --> I[记录网络日志]
    F --> J[记录数据日志]
    
    G --> K[返回业务错误响应]
    H --> L[返回系统错误响应]
    I --> M[返回网络错误响应]
    J --> N[返回数据错误响应]
    
    K --> O[测试验证错误处理]
    L --> O
    M --> O
    N --> O
```

### 具体错误处理测试

1. **认证错误处理**
   - 无效 Token 错误
   - AKSK 签名验证失败
   - 客户信息不存在
   - 仓储连接异常

2. **查询错误处理**
   - 外部网关超时
   - 缓存服务不可用
   - 数据库连接失败
   - 数据格式错误

3. **业务错误处理**
   - 权益产品不存在
   - 库存不足
   - 客户权限不足
   - 事务回滚异常

4. **容器错误处理**
   - 依赖注入失败
   - 配置缺失
   - 服务实例化错误

## 测试策略

### 单元测试策略

```python
# 测试隔离原则
class TestIsolationPrinciples:
    """
    1. 每个测试用例独立运行
    2. 使用 Mock 对象隔离外部依赖
    3. 测试数据通过工厂模式生成
    4. 测试后自动清理环境
    """

# 测试覆盖目标
COVERAGE_TARGETS = {
    "line_coverage": 85,        # 行覆盖率
    "branch_coverage": 80,      # 分支覆盖率
    "function_coverage": 90,    # 函数覆盖率
}

# 测试分类标记
PYTEST_MARKS = {
    "@pytest.mark.unit",           # 单元测试
    "@pytest.mark.integration",    # 集成测试
    "@pytest.mark.performance",    # 性能测试
    "@pytest.mark.security",       # 安全测试
}
```

### 集成测试策略

```python
# 集成测试范围
class IntegrationTestScope:
    """
    1. 认证服务与数据库集成
    2. 查询服务与外部网关集成
    3. 业务服务与事务管理集成
    4. 容器与所有依赖集成
    """

# 外部服务模拟
EXTERNAL_SERVICE_MOCKS = {
    "bifrost_gateway": "Mock API responses",
    "eleme_union_gateway": "Mock union responses", 
    "eleme_aixincan_gateway": "Mock aixincan responses",
    "wifi_master_gateway": "Mock wifi responses",
}
```

### 性能测试策略

```python
# 性能测试指标
PERFORMANCE_METRICS = {
    "response_time": {
        "p50": "< 100ms",
        "p95": "< 500ms", 
        "p99": "< 1000ms"
    },
    "throughput": {
        "concurrent_users": 100,
        "requests_per_second": 1000
    },
    "resource_usage": {
        "memory_usage": "< 512MB",
        "cpu_usage": "< 80%"
    }
}
```

### 安全测试策略

```python
# 安全测试场景
SECURITY_TEST_SCENARIOS = {
    "authentication": [
        "无效认证凭据",
        "过期 Token",
        "恶意签名"
    ],
    "authorization": [
        "权限提升攻击",
        "跨租户访问",
        "未授权资源访问"
    ],
    "input_validation": [
        "SQL 注入",
        "XSS 攻击",
        "参数篡改"
    ]
}
```

## 实现计划

### 阶段 1：基础测试框架搭建（第 1-2 周）

1. **创建测试基础架构**
   - 建立 `tests/unit/applications/openapi/` 目录结构
   - 实现 `BaseOpenapiUnitTest` 基类
   - 配置 pytest 和覆盖率工具

2. **实现测试工厂类**
   - `CustomerFactory` - 客户数据工厂
   - `BenefitsFactory` - 权益数据工厂
   - `ActivityFactory` - 活动数据工厂

3. **建立 Mock 对象库**
   - Mock 仓储层接口
   - Mock 网关接口
   - Mock 缓存接口

### 阶段 2：认证器测试实现（第 3-4 周）

1. **OpenAPITokenAuthenticator 测试**
   - 有效 Token 认证测试
   - 无效 Token 处理测试
   - 异常情况测试

2. **OpenAPIAKSKAuthenticator 测试**
   - AKSK 签名验证测试
   - 认证失败场景测试
   - 性能测试

### 阶段 3：查询服务测试实现（第 5-6 周）

1. **ActivitiesQueryService 测试**
   - 活动查询功能测试
   - 缓存机制测试
   - 外部网关集成测试

2. **BenefitsQueryService 测试**
   - 权益查询测试
   - 订单查询测试
   - 权限验证测试

3. **CustomerQueryService 测试**
   - 客户信息查询测试

### 阶段 4：业务服务测试实现（第 7-8 周）

1. **BenefitsChargeService 测试**
   - 权益充值业务测试
   - 事务处理测试
   - 错误处理测试

2. **ElemeAixincanService 测试**
   - 爱心餐服务测试
   - 外部网关集成测试

### 阶段 5：容器和集成测试（第 9-10 周）

1. **OpenapiApplications 容器测试**
   - 依赖注入配置测试
   - 容器生命周期测试

2. **端到端集成测试**
   - 完整业务流程测试
   - 性能测试
   - 安全测试

### 阶段 6：测试完善和优化（第 11-12 周）

1. **测试覆盖率优化**
   - 达到 85% 以上覆盖率
   - 补充边界条件测试

2. **测试文档和维护**
   - 编写测试使用说明
   - 建立测试维护流程

这个设计文档为 OpenAPI 应用测试功能提供了全面的技术方案，确保测试的质量、覆盖率和可维护性。设计遵循项目的模块化单体架构原则，与现有的测试基础设施保持一致。