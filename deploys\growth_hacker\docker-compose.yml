version: '3.8'

services:
  growth-hacker:
    build:
      context: ../..
      dockerfile: deploys/growth_hacker/Dockerfile
    container_name: growth-hacker-service
    environment:
      # 覆盖配置文件路径
      - CONFIG_FILE=/app/config/.env.toml
    volumes:
      # 挂载配置文件
      - ./config/.env.toml:/app/config/.env.toml:ro
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载浏览器用户数据（可选）
      - ./browser_data:/app/browser_data
    networks:
      - haili-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
    depends_on:
      - rabbitmq
      - mysql

  # RabbitMQ 服务（本地开发用）
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: growth-hacker-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"    # AMQP 端口
      - "15672:15672"  # 管理界面端口
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - haili-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MySQL 服务（本地开发用）
  mysql:
    image: mysql:8.0
    container_name: growth-hacker-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=growth_hacker
      - MYSQL_USER=growth_user
      - MYSQL_PASSWORD=growth_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - haili-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

networks:
  haili-network:
    driver: bridge

volumes:
  rabbitmq_data:
  mysql_data: