# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/dt.py
# created: 2025-01-22 11:20:59
# updated: 2025-01-22 11:23:27

from tortoise import fields
from tortoise.models import Model


class DTBenefitsContract(Model):
    id = fields.BigIntField(primary_key=True)
    customer_name = fields.CharField(max_length=255, null=False, default="", description="客户名称")
    city_ent_name = fields.CharField(max_length=255, null=False, default="", description="城市合伙人名称")
    amount = fields.IntField(null=False, default=0, description="金额(单位分)")
    paid_at = fields.DatetimeField(null=True, description="支付时间")
    active_id = fields.CharField(max_length=255, null=False, default="", description="活动ID")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "dt_benefits_contract"
        table_description = "DT权益售卖合同表"
