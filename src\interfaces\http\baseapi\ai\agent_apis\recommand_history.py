import json
import os
import sqlite3
from datetime import datetime, timedelta
from typing import Any, Dict, Optional


class RecommendHistory:
    def __init__(self, db_path="recommend_history.db"):
        self.db_path = db_path
        self._init_db()

    def _init_db(self):
        """初始化数据库，创建必要的表"""
        # 如果数据库文件已存在，先删除它以确保表结构正确
        if os.path.exists(self.db_path):
            os.remove(self.db_path)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS recommend_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                restaurant_names TEXT NOT NULL,  -- JSON格式存储商铺名称列表
                tag TEXT NOT NULL,               -- 单个标签字符串
                latitude TEXT,                   -- 纬度
                longitude TEXT,                  -- 经度
                created_at TEXT NOT NULL         -- 精确到毫秒的时间戳
            )
        """
        )
        conn.commit()
        conn.close()

    def add_recommendation(
        self, user_id: str, restaurant_names: list, tag: str, latitude: str = None, longitude: str = None
    ) -> bool:
        """添加新的推荐记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            # 使用精确到毫秒的时间戳
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
            cursor.execute(
                "INSERT INTO recommend_history (user_id, restaurant_names, tag, latitude, longitude, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                (user_id, json.dumps(restaurant_names), tag, latitude, longitude, current_time),
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"添加推荐记录时出错: {e}")
            return False

    def get_user_recommendations(self, user_id: str) -> list:
        """获取用户的所有推荐记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT restaurant_names, tag, latitude, longitude, created_at FROM recommend_history WHERE user_id = ? ORDER BY created_at DESC",
                (user_id,),
            )
            results = cursor.fetchall()
            conn.close()

            # 将JSON字符串转换回Python对象
            formatted_results = []
            for result in results:
                formatted_results.append(
                    {
                        "restaurant_names": json.loads(result[0]),
                        "tag": result[1],
                        "latitude": result[2],
                        "longitude": result[3],
                        "created_at": result[4],
                    }
                )
            return formatted_results
        except Exception as e:
            print(f"获取推荐记录时出错: {e}")
            return []

    def delete_recommendation(self, user_id: str, created_at: str) -> bool:
        """删除特定的推荐记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM recommend_history WHERE user_id = ? AND created_at = ?", (user_id, created_at))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"删除推荐记录时出错: {e}")
            return False

    def update_recommendation(
        self,
        user_id: str,
        created_at: str,
        new_restaurant_names: list,
        new_tag: str,
        new_latitude: str = None,
        new_longitude: str = None,
    ) -> bool:
        """更新推荐记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE recommend_history SET restaurant_names = ?, tag = ?, latitude = ?, longitude = ? WHERE user_id = ? AND created_at = ?",
                (json.dumps(new_restaurant_names), new_tag, new_latitude, new_longitude, user_id, created_at),
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"更新推荐记录时出错: {e}")
            return False

    def get_latest_recommendation(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户最近1小时内相同标签的推荐记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最近1小时的时间点
            one_hour_ago = (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S.%f")

            # 首先获取最新的一条记录
            cursor.execute(
                "SELECT restaurant_names, tag, latitude, longitude, created_at FROM recommend_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",
                (user_id,),
            )
            latest_result = cursor.fetchone()

            if not latest_result:
                return None

            latest_tag = latest_result[1]
            latest_latitude = latest_result[2]
            latest_longitude = latest_result[3]

            # 获取最近1小时内相同标签的所有记录
            cursor.execute(
                """
                SELECT restaurant_names, created_at 
                FROM recommend_history 
                WHERE user_id = ? 
                AND tag = ? 
                AND created_at >= ? 
                ORDER BY created_at DESC
                """,
                (user_id, latest_tag, one_hour_ago),
            )
            results = cursor.fetchall()
            conn.close()

            # 合并所有餐厅名称
            all_restaurants = set()
            for result in results:
                restaurants = json.loads(result[0])
                all_restaurants.update(restaurants)

            return {
                "restaurant_names": list(all_restaurants),
                "tag": latest_tag,
                "latitude": latest_latitude,
                "longitude": latest_longitude,
                "created_at": latest_result[4],
                "count": len(results),
            }
        except Exception as e:
            print(f"获取最新推荐记录时出错: {e}")
            return None


# 使用示例
if __name__ == "__main__":
    # 创建推荐历史管理器实例
    history = RecommendHistory()

    # 添加推荐记录
    history.add_recommendation("user123", ["老王饭店", "小李面馆", "张记烧烤"], "川菜", "39.9042", "116.4074")
    history.add_recommendation("user123", ["老c王饭店", "3小李面馆", "x张记烧烤"], "川菜2", "39.9042", "116.4074")
    history.add_recommendation("user123", ["新川菜馆", "川味小馆"], "川菜", "39.9042", "116.4074")

    # 获取用户的最新推荐记录
    latest = history.get_latest_recommendation("user123")
    print("用户最近1小时内的川菜推荐:", latest)

    # 获取用户的所有推荐记录
    recommendations = history.get_user_recommendations("user123")
    print("用户的推荐记录:", recommendations)

    # 更新推荐记录
    if recommendations:
        first_recommendation = recommendations[0]
        history.update_recommendation(
            "user123", first_recommendation["created_at"], ["老王饭店", "小李面馆"], "面食", "39.9042", "116.4074"
        )

    # 删除推荐记录
    if recommendations:
        first_recommendation = recommendations[0]
        history.delete_recommendation("user123", first_recommendation["created_at"])
