import json
import os
import time
from typing import Any, Generator, List, Optional, Tuple, Union

from ..agent_apis.eleme_wxscheme import get_wx_scheme_url
from ..agent_apis.embedding import EmbeddingClient
from ..agent_apis.llm import LLMHandler
from ..utils.base import LLMClient


def expand_activity_content(client: LLMClient, title: str, description: str, max_retries: int = 3) -> Optional[str]:
    """
    使用LLM扩写活动内容，失败时会重试
    :param client: LLM客户端
    :param title: 活动标题
    :param description: 活动描述
    :param max_retries: 最大重试次数
    :return: 扩写内容，失败返回None
    """
    for attempt in range(max_retries):
        try:
            messages = [
                {
                    "role": "user",
                    "content": f"""根据以下是本地生活优惠券活动页面的标题和描述，扩写一些关键词
活动标题：{title}
活动描述：{description}
词汇纬度：外卖类型、餐品分类、可能含有的食材、这类型的其他品牌、其他相关标签
比如：
标题：小谷姐姐全国品牌日，可以扩写：麻辣烫、火锅、小吃、杨国福、张亮、牛肉丸、虾滑、蔬菜""",
                }
            ]
            response = client.chat(messages, stream=False)
            if isinstance(response, Generator):
                return "".join(response)
            return response
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"LLM调用出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                print("等待5秒后重试...")
                time.sleep(5)
            else:
                print(f"LLM调用最终失败: {e}")
                return None
    return None


def process_single_activity(
    title: str, description: str, expanded_content: str, embedding_client: EmbeddingClient, max_retries: int = 3
) -> Optional[List[float]]:
    """
    处理单个活动的向量化，失败时会重试
    :param title: 活动标题
    :param description: 活动描述
    :param expanded_content: 扩写内容
    :param embedding_client: Embedding客户端实例
    :param max_retries: 最大重试次数
    :return: 向量化结果，失败返回None
    """
    # 拼接所有文本
    combined_text = f"{title}\n{description}\n{expanded_content}"

    for attempt in range(max_retries):
        try:
            # 获取向量表示
            vectors = embedding_client.get_embedding(combined_text)
            if vectors and len(vectors) > 0:
                return vectors[0]  # 返回第一个向量，因为只有一个文本
            return None
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"向量化处理出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                print("等待5秒后重试...")
                time.sleep(5)
            else:
                print(f"向量化处理最终失败: {e}")
                return None
    return None  # 如果所有重试都失败，返回None


def process_wx_promotion(data: dict) -> None:
    """
    处理JSON数据中的wx_promotion链接，获取对应的wx_url_scheme
    :param data: JSON数据字典
    """
    if "link" in data and "wx_promotion" in data["link"]:
        try:
            activity_id = data["id"]
            time.sleep(3)
            wx_url_scheme = get_wx_scheme_url(activity_id)
            if wx_url_scheme:
                data["link"]["wx_url_scheme"] = wx_url_scheme
                print(f"成功获取wx_url_scheme: {wx_url_scheme}")
        except Exception as e:
            print(f"处理wx_promotion时出错: {e}")


def process_single_file(file_path: str, llm_client: LLMHandler, embedding_client: EmbeddingClient) -> bool:
    """
    处理单个文件，包含重试逻辑
    :return: 处理是否成功
    """
    try:
        # 读取JSON文件，使用 utf-8-sig 编码处理 BOM
        with open(file_path, "r", encoding="utf-8-sig") as f:
            json_data = json.load(f)

        # 获取活动标题和描述
        data = json_data.get("data", {})
        title = data.get("title", "")
        description = data.get("description", "")
        # expanded_content = data.get("expanded_content", "")

        print(f"标题: {title}")
        print(f"描述: {description}")

        # 处理wx_promotion链接
        process_wx_promotion(data)

        # 生成向量并添加到JSON中
        # vector = process_single_activity(
        #     title, description, expanded_content, embedding_client)
        # if not vector:
        #     return False

        # print("已生成向量表示")
        # data['vector'] = vector

        # 保存更新后的JSON文件，使用 utf-8 编码
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(json_data, f, ensure_ascii=False, indent=4)

        return True

    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False


def process_activity_files(activity_dir):
    """
    处理活动目录中的所有JSON文件
    :param activity_dir: 活动文件目录路径
    """
    # 初始化客户端
    llm_client = LLMHandler()
    embedding_client = EmbeddingClient(api_key=os.getenv("SILICONFLOW_APIKEY"))

    # 获取所有JSON文件列表
    json_files = [f for f in os.listdir(activity_dir) if f.endswith(".json")]
    total_files = len(json_files)

    print(f"\n开始处理，共发现 {total_files} 个JSON文件")

    # 遍历目录中的所有JSON文件
    for index, filename in enumerate(json_files, 1):
        file_path = os.path.join(activity_dir, filename)
        print(f"\n处理文件 [{index}/{total_files}] {filename}")

        success = process_single_file(file_path, llm_client, embedding_client)

        if success:
            print(f"✓ 文件处理完成 ({index}/{total_files})")
        else:
            print(f"✗ 文件处理失败 ({index}/{total_files})")

        print(f"进度: {index/total_files*100:.1f}%")

    print(f"\n所有文件处理完成！共处理 {total_files} 个文件")


# 修改主函数
if __name__ == "__main__":
    activity_dir = "../activitylist"
    process_activity_files(activity_dir)
