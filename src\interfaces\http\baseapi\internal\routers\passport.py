# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/internal/routers/passport.py
# created: 2025-05-19 11:50:15
# updated: 2025-05-29 14:03:49


from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

from src.interfaces.http.baseapi import Container

if TYPE_CHECKING:
    from src.applications.passport.services import AppService, AuthService, UserService
    from src.domains.passport.entities import AppEntity

from ..authorization import verify_internal_app
from ..schemas import (
    AuthorizeRequestPayload,
    AuthorizeResponse,
    BaseResponse,
    CreateTenantPayload,
    CreateUserPayload,
    TenantResponse,
    UserRelationResponse,
)

router = APIRouter(prefix="", tags=["passport"])


@router.post(
    "/{app_id}/authorize",
    summary="身份授权接口",
    description="身份授权接口, 传入前端的jwt token, 返回用户身份信息",
    response_model=AuthorizeResponse,
)
@inject
async def authorize(
    app_id: str,
    payload: AuthorizeRequestPayload,
    current_app: "AppEntity" = Depends(verify_internal_app),
    auth_service: "AuthService" = Depends(Provide[Container.applications.passport_auth_service]),
):
    # 解码JWT token获取用户信息
    user_info = await auth_service.jwt_auth(payload.token, current_app)
    return AuthorizeResponse(data=user_info)


@router.put(
    "/{app_id}/tenants",
    summary="创建租户接口",
    description="创建租户接口, 传入租户信息, 返回租户信息, 用tenant_id可以作为customer_code",
    response_model=TenantResponse,
)
@inject
async def create_tenant(
    app_id: str,
    payload: CreateTenantPayload,
    current_app: "AppEntity" = Depends(verify_internal_app),
    app_service: "AppService" = Depends(Provide[Container.applications.passport_app_service]),
):
    # 创建租户
    tenant = await app_service.create_tenant_for_app(current_app, payload.tenant_name)
    return TenantResponse(data=tenant)


@router.put(
    "/{app_id}/tenants/{tenant_id}/users",
    summary="创建用户接口",
    description="创建用户接口, 传入用户信息, 返回用户信息",
    response_model=UserRelationResponse,
)
@inject
async def create_user_relation(
    app_id: str,
    tenant_id: str,
    payload: CreateUserPayload,
    current_app: "AppEntity" = Depends(verify_internal_app),
    user_service: "UserService" = Depends(Provide[Container.applications.passport_user_service]),
):
    user_relation = await user_service.create_relation(payload.uid, current_app.app_id, tenant_id)
    return UserRelationResponse(data=user_relation)


@router.delete(
    "/{app_id}/tenants/{tenant_id}/users/{uid}",
    summary="删除租户用户接口",
    description="删除租户用户接口, 传入用户ID, 返回用户信息",
    response_model=BaseResponse,
)
@inject
async def delete_user_relation(
    app_id: str,
    tenant_id: str,
    uid: str,
    current_app: "AppEntity" = Depends(verify_internal_app),
    user_service: "UserService" = Depends(Provide[Container.applications.passport_user_service]),
):
    await user_service.remove_user_relation(uid, current_app.app_id, tenant_id)
    return BaseResponse(message="success")
