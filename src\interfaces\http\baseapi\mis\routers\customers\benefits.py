# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/customers/benefits.py
# created: 2025-02-06 15:06:32
# updated: 2025-05-26 11:03:13

from fastapi import APIRouter, Query

from src.domains.customer.dto import (
    CustomerBenefitsCreateDTO,
    CustomerBenefitsDetailDTO,
    CustomerBenefitsDTO,
    CustomerBenefitsUpdateDTO,
)
from src.infrastructures import errors
from src.repositories.benefits import ProductRepository
from src.repositories.customer import BenefitsFilters, BenefitsRepository, CustomerRepository

from ...schemas import BaseResponse, CustomerBenefitListResponse, CustomerBenefitResponse, ListData

router = APIRouter(tags=["customer", "customer.benefit"])


async def check_customer_exists(customer_id: int):
    customer = await CustomerRepository.get_by_id(customer_id)
    if not customer:
        raise errors.CustomerNotFoundError


@router.post("/{customer_id}/benefits", response_model=CustomerBenefitResponse)
async def create_customer_benefit(customer_id: int, create_fields: CustomerBenefitsCreateDTO):
    """创建客户权益"""
    await check_customer_exists(customer_id)
    benefit = await BenefitsRepository.get_by_customer_and_product_code(customer_id, create_fields.product_code)
    if benefit:
        raise errors.BenefitsProductAlreadyExistsError
    create_fields.customer_id = customer_id
    product = await ProductRepository.get_by_code(create_fields.product_code)
    if not product:
        raise errors.BenefitsProductNotFoundError
    create_fields.product_id = product.id
    benefit = await BenefitsRepository.create_customer_benefit(create_fields)
    return CustomerBenefitResponse(data=await CustomerBenefitsDetailDTO.from_tortoise_orm(benefit))


@router.delete("/{customer_id}/benefits/{product_code}", response_model=BaseResponse)
async def delete_customer_benefit(customer_id: int, product_code: str):
    """删除客户权益"""
    await check_customer_exists(customer_id)
    await BenefitsRepository.delete_customer_benefit_by_customer_and_product_code(customer_id, product_code)
    return BaseResponse(message="客户权益删除成功")


@router.put("/{customer_id}/benefits/{product_code}", response_model=CustomerBenefitResponse)
async def update_customer_benefit(customer_id: int, product_code: str, update_fields: CustomerBenefitsUpdateDTO):
    """更新客户权益"""
    await check_customer_exists(customer_id)
    benefit = await BenefitsRepository.update_customer_benefit_by_customer_and_product_code(
        customer_id=customer_id,
        product_code=product_code,
        update_fileds=update_fields,
    )
    if not benefit:
        raise errors.BenefitsProductNotFoundError
    return CustomerBenefitResponse(data=await CustomerBenefitsDetailDTO.from_tortoise_orm(benefit))


@router.get("/{customer_id}/benefits", response_model=CustomerBenefitListResponse)
async def get_customer_benefits(customer_id: int, filters: BenefitsFilters = Query(...)):
    """获取客户权益列表"""
    await check_customer_exists(customer_id)
    total, benefits = await BenefitsRepository.gets_by_customer_id(customer_id, filters)
    benefits = [await CustomerBenefitsDTO.from_tortoise_orm(benefit) for benefit in benefits]  # type: ignore
    return CustomerBenefitListResponse(data=ListData(total=total, data=benefits))


@router.get("/{customer_id}/benefits/{product_code}", response_model=CustomerBenefitResponse)
async def get_customer_benefit(customer_id: int, product_code: str):
    """获取客户权益详情"""
    await check_customer_exists(customer_id)
    benefit = await BenefitsRepository.get_by_customer_and_product_code(customer_id, product_code)
    if not benefit:
        raise errors.BenefitsProductNotFoundError
    return CustomerBenefitResponse(data=await CustomerBenefitsDetailDTO.from_tortoise_orm(benefit))
