# encoding: utf-8
# src/infrastructures/gateways/eleme/union/goods_gateway.py
# created: 2025-08-11 11:04:47

from typing import Optional, Union

from redis.asyncio import Redis

from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_detail_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_order_create_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_order_pay_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_order_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_coupon_store_query_request import *

from .dto import UnionGoodsDetailDTO, UnionGoodsListDTO, UnionGoodsStoresListDTO
from .settings import ElemeUnionSettings


class ElemeUnionGoodsGateway:
    """饿了么联盟商品网关"""

    def __init__(self, config: Union[ElemeUnionSettings, dict], redis: Redis):
        if isinstance(config, dict):
            config = ElemeUnionSettings(**config)

        self.config = config
        self.redis = redis

    def _get_client(self) -> Defaultability:
        """获取饿了么联盟SDK客户端"""
        client = TopApiClient(
            appkey=self.config.app_key,
            app_sercet=self.config.app_secret,
            top_gateway_url=self.config.top_gateway_url,
            verify_ssl=True,
        )
        return Defaultability(client=client)

    async def get_goods_list(
        self,
        page_size: int = 20,
        page_number: int = 1,
        item_type: int = 4,
        session_id: Optional[str] = None,
    ) -> UnionGoodsListDTO:
        """获取商品列表

        Args:
            page_size: 每页数量（1~20）
            page_number: 页码
            item_type: 商品类型（1-商品券；2-代金券；4-新天特）
            session_id: 会话ID（page_number>1时必填）
        """
        ability = self._get_client()
        request = AlibabaAlscUnionElemeCouponQueryRequest(
            AlibabaAlscUnionElemeCouponQueryCouponQueryRequest(
                session_id=session_id or "",
                page_number=page_number,
                page_size=page_size,
                item_type=str(item_type),  # 传入单个商品类型
            )
        )
        response = ability.alibaba_alsc_union_eleme_coupon_query(request)
        return UnionGoodsListDTO.model_validate(response["data"])

    async def get_goods_detail(self, item_id: str, item_type: int = 4) -> UnionGoodsDetailDTO:
        """获取商品详情

        Args:
            item_id: 商品ID
            item_type: 商品类型（4-新天特）
        """
        ability = self._get_client()
        request = AlibabaAlscUnionElemeCouponDetailGetRequest(
            AlibabaAlscUnionElemeCouponDetailGetCouponDetailRequest(item_id=item_id, item_type=item_type)
        )
        response = ability.alibaba_alsc_union_eleme_coupon_detail_get(request)
        return UnionGoodsDetailDTO.model_validate(response["data"])

    async def get_goods_stores(
        self,
        item_id: str,
        page_size: int = 20,
        page_number: int = 1,
        item_type: int = 4,
    ) -> UnionGoodsStoresListDTO:
        """获取商品门店列表

        Args:
            item_id: 商品ID
            page_size: 每页数量（1~20）
            page_number: 页码
            item_type: 商品类型（4-新天特），不传则默认为囤券券
        """
        ability = self._get_client()
        request = AlibabaAlscUnionElemeCouponStoreQueryRequest(
            AlibabaAlscUnionElemeCouponStoreQueryCouponShopRequest(
                item_id=item_id,
                page_number=page_number,
                page_size=page_size,
                item_type=item_type,
            )
        )
        response = ability.alibaba_alsc_union_eleme_coupon_store_query(request)
        return UnionGoodsStoresListDTO.model_validate(response["data"])

    async def create_order(
        self,
        outer_order_id: str,
        item_id: str,
        quantity: int,
        order_fee: int,
        title: str,
        phone: str,
        ext_info: Optional[str] = None,
        skip_pay: bool = False,
        item_type: int = 4,
    ) -> dict:
        """创建卡券订单

        Args:
            outer_order_id: 外部订单号
            item_id: 加密后的商品ID
            quantity: 订单份数
            order_fee: 订单金额（分）
            title: 商品名称
            phone: 手机号
            ext_info: 扩展字段，json格式。淘宝订单号tbOrderId
            skip_pay: true预下单不支付，false下单并支付
            item_type: 商品类型（4-新天特），如果是新天特的订单，必须传入

        Returns:
            dict: 包含 outer_order_id（外部订单号）和 biz_order_id（饿了么订单号）
        """
        ability = self._get_client()
        order_dto = AlibabaAlscUnionElemeCouponOrderCreateOrderDto(
            outer_order_id=outer_order_id,
            item_id=item_id,
            quantity=quantity,
            order_fee=order_fee,
            title=title,
            ext_info=ext_info or "{}",
            phone=phone,
            skip_pay=skip_pay,
            item_type=item_type,
        )
        request = AlibabaAlscUnionElemeCouponOrderCreateRequest(order_dto=order_dto)
        response = ability.alibaba_alsc_union_eleme_coupon_order_create(request)
        return response.get("data", {})

    async def query_order(self, outer_order_id: str) -> dict:
        """查询订单状态

        Args:
            outer_order_id: 外部订单号

        Returns:
            dict: 订单详情
        """
        ability = self._get_client()
        query_dto = AlibabaAlscUnionElemeCouponOrderQueryCouponOrderQueryDto(biz_order_id=outer_order_id)  # type: ignore
        request = AlibabaAlscUnionElemeCouponOrderQueryRequest(order_query_dto=query_dto)
        response = ability.alibaba_alsc_union_eleme_coupon_order_query(request)
        return response.get("data", {})

    async def pay_order(self, outer_order_id: str) -> dict:
        """支付订单

        Args:
            outer_order_id: 外部订单号

        Returns:
            dict: 支付结果
        """
        ability = self._get_client()
        pay_dto = AlibabaAlscUnionElemeCouponOrderPayCouponOrderPayDto(outer_order_id=outer_order_id)  # type: ignore
        request = AlibabaAlscUnionElemeCouponOrderPayRequest(order_pay_dto=pay_dto)
        response = ability.alibaba_alsc_union_eleme_coupon_order_pay(request)
        return response.get("data", {})
