# 第五阶段：仓储层迁移任务

## 任务1：创建仓储层基础架构

### 1.1 定义仓储接口
```python
# src/repositories/base.py
"""仓储层基础接口"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, List, Dict, Any
from datetime import datetime

T = TypeVar('T')
ID = TypeVar('ID')

class IRepository(ABC, Generic[T, ID]):
    """仓储接口基类"""
    
    @abstractmethod
    async def get_by_id(self, id: ID) -> Optional[T]:
        """根据ID获取实体"""
        pass
    
    @abstractmethod
    async def get_many_by_ids(self, ids: List[ID]) -> List[T]:
        """根据ID列表获取多个实体"""
        pass
    
    @abstractmethod
    async def save(self, entity: T) -> T:
        """保存实体（创建或更新）"""
        pass
    
    @abstractmethod
    async def save_many(self, entities: List[T]) -> List[T]:
        """批量保存实体"""
        pass
    
    @abstractmethod
    async def delete(self, id: ID) -> bool:
        """删除实体"""
        pass
    
    @abstractmethod
    async def delete_many(self, ids: List[ID]) -> int:
        """批量删除实体"""
        pass
    
    @abstractmethod
    async def exists(self, id: ID) -> bool:
        """检查实体是否存在"""
        pass
    
    @abstractmethod
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """统计实体数量"""
        pass

class IReadRepository(ABC, Generic[T]):
    """只读仓储接口"""
    
    @abstractmethod
    async def find_all(
        self,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[List[str]] = None,
        offset: int = 0,
        limit: Optional[int] = None
    ) -> List[T]:
        """查找所有实体"""
        pass
    
    @abstractmethod
    async def find_one(self, filters: Dict[str, Any]) -> Optional[T]:
        """查找单个实体"""
        pass

class ISpecificationRepository(ABC, Generic[T]):
    """支持规约模式的仓储接口"""
    
    @abstractmethod
    async def find_by_specification(self, specification: Any) -> List[T]:
        """根据规约查找实体"""
        pass
    
    @abstractmethod
    async def count_by_specification(self, specification: Any) -> int:
        """根据规约统计数量"""
        pass
```

### 1.2 创建仓储实现基类
```python
# src/repositories/tortoise_base.py
"""基于Tortoise ORM的仓储基类"""
from typing import TypeVar, Generic, Optional, List, Dict, Any, Type
from tortoise.models import Model
from tortoise.exceptions import DoesNotExist
from src.repositories.base import IRepository, IReadRepository

T = TypeVar('T')
M = TypeVar('M', bound=Model)

class TortoiseRepository(IRepository[T, int], IReadRepository[T], Generic[T, M]):
    """Tortoise ORM仓储基类"""
    
    def __init__(self, model_class: Type[M], entity_class: Type[T]):
        self.model_class = model_class
        self.entity_class = entity_class
    
    def _to_entity(self, model: M) -> T:
        """模型转实体"""
        # 子类需要实现具体的转换逻辑
        raise NotImplementedError
    
    def _to_model(self, entity: T) -> M:
        """实体转模型"""
        # 子类需要实现具体的转换逻辑
        raise NotImplementedError
    
    async def get_by_id(self, id: int) -> Optional[T]:
        """根据ID获取实体"""
        try:
            model = await self.model_class.get(id=id)
            return self._to_entity(model)
        except DoesNotExist:
            return None
    
    async def get_many_by_ids(self, ids: List[int]) -> List[T]:
        """根据ID列表获取多个实体"""
        models = await self.model_class.filter(id__in=ids).all()
        return [self._to_entity(model) for model in models]
    
    async def save(self, entity: T) -> T:
        """保存实体"""
        model = self._to_model(entity)
        await model.save()
        return self._to_entity(model)
    
    async def save_many(self, entities: List[T]) -> List[T]:
        """批量保存实体"""
        models = [self._to_model(entity) for entity in entities]
        await self.model_class.bulk_create(models)
        return [self._to_entity(model) for model in models]
    
    async def delete(self, id: int) -> bool:
        """删除实体"""
        deleted_count = await self.model_class.filter(id=id).delete()
        return deleted_count > 0
    
    async def delete_many(self, ids: List[int]) -> int:
        """批量删除实体"""
        return await self.model_class.filter(id__in=ids).delete()
    
    async def exists(self, id: int) -> bool:
        """检查实体是否存在"""
        return await self.model_class.filter(id=id).exists()
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """统计数量"""
        query = self.model_class.all()
        if filters:
            query = query.filter(**filters)
        return await query.count()
    
    async def find_all(
        self,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[List[str]] = None,
        offset: int = 0,
        limit: Optional[int] = None
    ) -> List[T]:
        """查找所有实体"""
        query = self.model_class.all()
        
        if filters:
            query = query.filter(**filters)
        
        if order_by:
            query = query.order_by(*order_by)
        
        if offset:
            query = query.offset(offset)
        
        if limit:
            query = query.limit(limit)
        
        models = await query
        return [self._to_entity(model) for model in models]
    
    async def find_one(self, filters: Dict[str, Any]) -> Optional[T]:
        """查找单个实体"""
        try:
            model = await self.model_class.get(**filters)
            return self._to_entity(model)
        except DoesNotExist:
            return None
```

## 任务2：迁移数据库模型

### 2.1 创建数据库模型基类
```python
# src/databases/models/base.py
"""数据库模型基类"""
from tortoise.models import Model
from tortoise import fields

class BaseModel(Model):
    """基础模型"""
    id = fields.IntField(pk=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    
    class Meta:
        abstract = True

class SoftDeleteModel(BaseModel):
    """支持软删除的模型"""
    deleted_at = fields.DatetimeField(null=True)
    
    class Meta:
        abstract = True
    
    async def soft_delete(self):
        """软删除"""
        from datetime import datetime
        self.deleted_at = datetime.utcnow()
        await self.save()
    
    @classmethod
    def active(cls):
        """获取未删除的记录"""
        return cls.filter(deleted_at__isnull=True)
```

### 2.2 迁移SKU数据库模型
```python
# src/databases/models/benefits/sku.py
"""SKU数据库模型"""
from decimal import Decimal
from tortoise import fields
from src.databases.models.base import BaseModel

class SkuModel(BaseModel):
    """SKU模型"""
    # 基本信息
    name = fields.CharField(max_length=200)
    code = fields.CharField(max_length=50, unique=True, index=True)
    category = fields.CharField(max_length=50, index=True)
    status = fields.CharField(max_length=20, default="active", index=True)
    
    # 价格信息
    price = fields.DecimalField(max_digits=10, decimal_places=2)
    cost = fields.DecimalField(max_digits=10, decimal_places=2)
    currency = fields.CharField(max_length=3, default="CNY")
    
    # 库存信息
    stock = fields.IntField(default=0)
    reserved = fields.IntField(default=0)
    
    # 规则配置
    min_purchase = fields.IntField(default=1)
    max_purchase = fields.IntField(null=True)
    
    # 时间信息
    valid_from = fields.DatetimeField(null=True)
    valid_to = fields.DatetimeField(null=True)
    
    class Meta:
        table = "skus"
        indexes = [
            ("category", "status"),
            ("valid_from", "valid_to"),
        ]

class SkuPriceHistoryModel(BaseModel):
    """SKU价格历史模型"""
    sku = fields.ForeignKeyField("models.SkuModel", related_name="price_history")
    price = fields.DecimalField(max_digits=10, decimal_places=2)
    previous_price = fields.DecimalField(max_digits=10, decimal_places=2)
    changed_by = fields.CharField(max_length=100, null=True)
    change_reason = fields.TextField(null=True)
    
    class Meta:
        table = "sku_price_history"
        ordering = ["-created_at"]

class SkuStockHistoryModel(BaseModel):
    """SKU库存历史模型"""
    sku = fields.ForeignKeyField("models.SkuModel", related_name="stock_history")
    stock_before = fields.IntField()
    stock_after = fields.IntField()
    change_quantity = fields.IntField()
    change_type = fields.CharField(max_length=20)  # reserve, release, deduct, replenish
    reference_type = fields.CharField(max_length=50, null=True)  # order, return, manual
    reference_id = fields.CharField(max_length=100, null=True)
    
    class Meta:
        table = "sku_stock_history"
        ordering = ["-created_at"]
```

## 任务3：实现权益领域仓储

### 3.1 定义SKU仓储接口
```python
# src/repositories/benefits/interfaces.py
"""权益领域仓储接口"""
from typing import Optional, List, Dict, Any
from decimal import Decimal
from src.repositories.base import IRepository, IReadRepository
from src.domains.benefits.entities.sku import Sku

class ISkuRepository(IRepository[Sku, int], IReadRepository[Sku]):
    """SKU仓储接口"""
    
    async def get_by_code(self, code: str) -> Optional[Sku]:
        """根据编码获取SKU"""
        pass
    
    async def get_by_codes(self, codes: List[str]) -> List[Sku]:
        """根据编码列表获取SKU"""
        pass
    
    async def search(
        self,
        filters: Dict[str, Any],
        offset: int = 0,
        limit: int = 20
    ) -> List[Sku]:
        """搜索SKU"""
        pass
    
    async def get_active_by_category(self, category: str) -> List[Sku]:
        """获取某类别的活跃SKU"""
        pass
    
    async def update_stock(self, sku_id: int, quantity: int, operation: str) -> bool:
        """更新库存"""
        pass
    
    async def batch_update_status(self, sku_ids: List[int], status: str) -> int:
        """批量更新状态"""
        pass

class ISkuPriceHistoryRepository(IRepository):
    """SKU价格历史仓储接口"""
    
    async def get_price_history(
        self,
        sku_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取价格历史"""
        pass

class ISkuStockHistoryRepository(IRepository):
    """SKU库存历史仓储接口"""
    
    async def get_stock_history(
        self,
        sku_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        change_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取库存历史"""
        pass
```

### 3.2 实现SKU仓储
```python
# src/repositories/benefits/implementations.py
"""权益领域仓储实现"""
from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from tortoise.expressions import Q
from src.repositories.tortoise_base import TortoiseRepository
from src.repositories.benefits.interfaces import (
    ISkuRepository,
    ISkuPriceHistoryRepository,
    ISkuStockHistoryRepository
)
from src.domains.benefits.entities.sku import Sku
from src.databases.models.benefits.sku import (
    SkuModel,
    SkuPriceHistoryModel,
    SkuStockHistoryModel
)

class SkuRepository(TortoiseRepository[Sku, SkuModel], ISkuRepository):
    """SKU仓储实现"""
    
    def __init__(self):
        super().__init__(SkuModel, Sku)
    
    def _to_entity(self, model: SkuModel) -> Sku:
        """模型转实体"""
        return Sku(
            id=model.id,
            name=model.name,
            code=model.code,
            category=model.category,
            status=model.status,
            price=model.price,
            cost=model.cost,
            currency=model.currency,
            stock=model.stock,
            reserved=model.reserved,
            min_purchase=model.min_purchase,
            max_purchase=model.max_purchase,
            valid_from=model.valid_from,
            valid_to=model.valid_to,
            created_at=model.created_at,
            updated_at=model.updated_at
        )
    
    def _to_model(self, entity: Sku) -> SkuModel:
        """实体转模型"""
        model_data = {
            'name': entity.name,
            'code': entity.code,
            'category': entity.category,
            'status': entity.status,
            'price': entity.price,
            'cost': entity.cost,
            'currency': entity.currency,
            'stock': entity.stock,
            'reserved': entity.reserved,
            'min_purchase': entity.min_purchase,
            'max_purchase': entity.max_purchase,
            'valid_from': entity.valid_from,
            'valid_to': entity.valid_to
        }
        
        if entity.id and entity.id > 0:
            model_data['id'] = entity.id
            return SkuModel(**model_data)
        else:
            return SkuModel(**model_data)
    
    async def get_by_code(self, code: str) -> Optional[Sku]:
        """根据编码获取SKU"""
        return await self.find_one({'code': code})
    
    async def get_by_codes(self, codes: List[str]) -> List[Sku]:
        """根据编码列表获取SKU"""
        models = await SkuModel.filter(code__in=codes).all()
        return [self._to_entity(model) for model in models]
    
    async def search(
        self,
        filters: Dict[str, Any],
        offset: int = 0,
        limit: int = 20
    ) -> List[Sku]:
        """搜索SKU"""
        query = SkuModel.all()
        
        # 构建查询条件
        if 'keyword' in filters:
            keyword = filters['keyword']
            query = query.filter(
                Q(name__icontains=keyword) | Q(code__icontains=keyword)
            )
        
        if 'category' in filters:
            query = query.filter(category=filters['category'])
        
        if 'status' in filters:
            query = query.filter(status=filters['status'])
        
        if 'min_price' in filters:
            query = query.filter(price__gte=filters['min_price'])
        
        if 'max_price' in filters:
            query = query.filter(price__lte=filters['max_price'])
        
        if 'min_stock' in filters:
            query = query.filter(stock__gte=filters['min_stock'])
        
        # 排序和分页
        query = query.order_by('-created_at').offset(offset).limit(limit)
        
        models = await query
        return [self._to_entity(model) for model in models]
    
    async def get_active_by_category(self, category: str) -> List[Sku]:
        """获取某类别的活跃SKU"""
        now = datetime.utcnow()
        models = await SkuModel.filter(
            category=category,
            status='active',
            valid_from__lte=now,
            valid_to__gte=now
        ).all()
        return [self._to_entity(model) for model in models]
    
    async def update_stock(self, sku_id: int, quantity: int, operation: str) -> bool:
        """更新库存"""
        sku = await SkuModel.get_or_none(id=sku_id)
        if not sku:
            return False
        
        if operation == 'increase':
            sku.stock += quantity
        elif operation == 'decrease':
            if sku.stock < quantity:
                return False
            sku.stock -= quantity
        elif operation == 'reserve':
            if sku.stock - sku.reserved < quantity:
                return False
            sku.reserved += quantity
        elif operation == 'release':
            if sku.reserved < quantity:
                return False
            sku.reserved -= quantity
        else:
            return False
        
        await sku.save()
        return True
    
    async def batch_update_status(self, sku_ids: List[int], status: str) -> int:
        """批量更新状态"""
        return await SkuModel.filter(id__in=sku_ids).update(status=status)

class SkuPriceHistoryRepository(TortoiseRepository, ISkuPriceHistoryRepository):
    """SKU价格历史仓储实现"""
    
    def __init__(self):
        super().__init__(SkuPriceHistoryModel, dict)
    
    async def get_price_history(
        self,
        sku_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取价格历史"""
        query = SkuPriceHistoryModel.filter(sku_id=sku_id)
        
        if start_date:
            query = query.filter(created_at__gte=start_date)
        
        if end_date:
            query = query.filter(created_at__lte=end_date)
        
        models = await query.order_by('-created_at').all()
        
        return [
            {
                'id': model.id,
                'price': float(model.price),
                'previous_price': float(model.previous_price),
                'changed_by': model.changed_by,
                'change_reason': model.change_reason,
                'created_at': model.created_at
            }
            for model in models
        ]
```

## 任务4：创建仓储工厂和容器

### 4.1 创建仓储工厂
```python
# src/repositories/factory.py
"""仓储工厂"""
from typing import Dict, Type, Any
from src.repositories.base import IRepository
from src.repositories.benefits.implementations import (
    SkuRepository,
    SkuPriceHistoryRepository,
    SkuStockHistoryRepository
)

class RepositoryFactory:
    """仓储工厂"""
    
    _repositories: Dict[str, Type[IRepository]] = {
        'sku': SkuRepository,
        'sku_price_history': SkuPriceHistoryRepository,
        'sku_stock_history': SkuStockHistoryRepository,
    }
    
    @classmethod
    def create(cls, name: str) -> IRepository:
        """创建仓储实例"""
        repository_class = cls._repositories.get(name)
        if not repository_class:
            raise ValueError(f"Repository '{name}' not found")
        
        return repository_class()
    
    @classmethod
    def register(cls, name: str, repository_class: Type[IRepository]):
        """注册仓储类"""
        cls._repositories[name] = repository_class
```

### 4.2 配置依赖注入
```python
# src/repositories/containers.py
"""仓储层依赖注入配置"""
from dependency_injector import containers, providers
from src.repositories.benefits.implementations import (
    SkuRepository,
    SkuPriceHistoryRepository,
    SkuStockHistoryRepository
)

class RepositoryContainer(containers.DeclarativeContainer):
    """仓储容器"""
    
    # 权益领域仓储
    sku_repository = providers.Singleton(SkuRepository)
    sku_price_history_repository = providers.Singleton(SkuPriceHistoryRepository)
    sku_stock_history_repository = providers.Singleton(SkuStockHistoryRepository)
    
    # 其他领域仓储...
```

## 任务5：创建数据迁移脚本

### 5.1 创建Aerich配置
```python
# src/databases/aerich_config.py
"""Aerich数据库迁移配置"""
from src.databases.config import TORTOISE_ORM

TORTOISE_ORM = {
    "connections": {
        "default": "mysql://user:password@localhost:3306/database"
    },
    "apps": {
        "models": {
            "models": [
                "src.databases.models.benefits.sku",
                "src.databases.models.passport.user",
                "src.databases.models.delivery.store",
                "src.databases.models.customer.customer",
                "src.databases.models.aiagent.agent",
                "src.databases.models.activity.activity",
                "aerich.models"
            ],
            "default_connection": "default",
        }
    }
}
```

### 5.2 创建迁移脚本
```bash
#!/bin/bash
# scripts/init_database.sh

echo "初始化数据库迁移..."

# 初始化 Aerich
poetry run aerich init -t src.databases.aerich_config.TORTOISE_ORM

# 创建初始迁移
poetry run aerich init-db

echo "✅ 数据库迁移初始化完成"
```

## 任务6：创建仓储层测试

### 6.1 创建测试配置
```python
# tests/integration/repositories/conftest.py
"""仓储层集成测试配置"""
import pytest
from tortoise import Tortoise
from src.databases.models.benefits.sku import SkuModel
from src.repositories.benefits.implementations import SkuRepository

@pytest.fixture(scope="function")
async def db():
    """测试数据库"""
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["src.databases.models.benefits.sku"]}
    )
    await Tortoise.generate_schemas()
    yield
    await Tortoise.close_connections()

@pytest.fixture
async def sku_repository(db):
    """SKU仓储"""
    return SkuRepository()

@pytest.fixture
async def sample_sku_model(db):
    """示例SKU模型"""
    return await SkuModel.create(
        name="Test SKU",
        code="TEST001",
        category="test",
        price=100.00,
        cost=60.00,
        stock=100
    )
```

### 6.2 创建仓储测试
```python
# tests/integration/repositories/benefits/test_sku_repository.py
"""SKU仓储集成测试"""
import pytest
from decimal import Decimal
from src.domains.benefits.entities.sku import Sku

class TestSkuRepository:
    """SKU仓储测试"""
    
    @pytest.mark.asyncio
    async def test_save_new_sku(self, sku_repository):
        """测试保存新SKU"""
        # 准备
        sku = Sku(
            id=0,
            name="New SKU",
            code="NEW001",
            category="test",
            price=Decimal("150.00"),
            cost=Decimal("90.00"),
            stock=50
        )
        
        # 执行
        saved_sku = await sku_repository.save(sku)
        
        # 验证
        assert saved_sku.id > 0
        assert saved_sku.name == "New SKU"
        assert saved_sku.code == "NEW001"
        
        # 验证持久化
        fetched_sku = await sku_repository.get_by_id(saved_sku.id)
        assert fetched_sku is not None
        assert fetched_sku.name == "New SKU"
    
    @pytest.mark.asyncio
    async def test_get_by_code(self, sku_repository, sample_sku_model):
        """测试根据编码获取SKU"""
        # 执行
        sku = await sku_repository.get_by_code("TEST001")
        
        # 验证
        assert sku is not None
        assert sku.code == "TEST001"
        assert sku.name == "Test SKU"
    
    @pytest.mark.asyncio
    async def test_search_skus(self, sku_repository, sample_sku_model):
        """测试搜索SKU"""
        # 创建更多测试数据
        await SkuModel.create(
            name="Another SKU",
            code="TEST002",
            category="test",
            price=200.00,
            cost=120.00,
            stock=30
        )
        
        # 执行搜索
        results = await sku_repository.search(
            filters={'category': 'test'},
            offset=0,
            limit=10
        )
        
        # 验证
        assert len(results) == 2
        assert all(sku.category == 'test' for sku in results)
    
    @pytest.mark.asyncio
    async def test_update_stock(self, sku_repository, sample_sku_model):
        """测试更新库存"""
        # 增加库存
        success = await sku_repository.update_stock(
            sample_sku_model.id,
            50,
            'increase'
        )
        assert success is True
        
        # 验证
        sku = await sku_repository.get_by_id(sample_sku_model.id)
        assert sku.stock == 150
        
        # 减少库存
        success = await sku_repository.update_stock(
            sample_sku_model.id,
            30,
            'decrease'
        )
        assert success is True
        
        # 验证
        sku = await sku_repository.get_by_id(sample_sku_model.id)
        assert sku.stock == 120
```

## 验证步骤

### 1. 检查仓储结构
```bash
find src/repositories -name "*.py" | grep -E "(interfaces|implementations)" | sort
```

### 2. 验证数据库模型
```python
# scripts/verify_models.py
from tortoise import Tortoise
import asyncio

async def verify_models():
    """验证数据库模型"""
    await Tortoise.init(
        db_url="sqlite://:memory:",
        modules={"models": ["src.databases.models"]}
    )
    
    # 生成模式
    await Tortoise.generate_schemas()
    
    # 获取所有模型
    from tortoise.models import Model
    models = Model.all_models()
    
    print("已注册的模型:")
    for model_name, model_class in models.items():
        print(f"  - {model_name}: {model_class._meta.table}")
    
    await Tortoise.close_connections()

asyncio.run(verify_models())
```

### 3. 运行集成测试
```bash
poetry run pytest tests/integration/repositories -v
```

## 完成标准

- [ ] 仓储接口定义完整
- [ ] Tortoise ORM基类实现
- [ ] 数据库模型迁移完成
- [ ] SKU仓储实现完整
- [ ] 其他领域仓储实现
- [ ] 仓储工厂和容器配置
- [ ] 数据库迁移脚本准备
- [ ] 集成测试覆盖主要场景
- [ ] 事务支持实现
- [ ] 性能优化（查询优化、缓存等）