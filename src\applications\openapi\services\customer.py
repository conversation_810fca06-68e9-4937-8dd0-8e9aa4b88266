# encoding: utf-8
# src/applications/openapi/services/customer.py
# created: 2025-08-06 19:10:11

from typing import TYPE_CHECKING, List

from loguru import logger
from tortoise.transactions import atomic

from src.databases.models.customer import CustomerRechargeRecord
from src.domains.customer.dto import CustomerCreateFields, CustomerDTO, CustomerRechargeRecordDTO
from src.domains.passport.entities import AppEntity, TenantEntity
from src.infrastructures import errors

if TYPE_CHECKING:
    from src.repositories.customer import CustomerRepository
    from src.repositories.passport.apps import AppRepository
    from src.repositories.passport.tenants import TenantRepository


class CustomerService:

    def __init__(self, customer_repo: "CustomerRepository", app_repo: "AppRepository", tenant_repo: "TenantRepository"):
        self.customer_repo = customer_repo
        self.app_repo = app_repo
        self.tenant_repo = tenant_repo

    @atomic()
    async def recharge(
        self, customer_id: int, amount: int, description: str, operator_id: str, operator_name: str
    ) -> CustomerDTO:
        # 获取客户信息
        customer = await self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise errors.CustomerNotFoundError

        # 记录充值前的余额
        balance_before = customer.balance
        new_balance = balance_before + amount

        # 先创建充值记录（记录充值后余额）
        await CustomerRechargeRecord.create(
            customer_id=customer.id,
            customer_code=customer.code,
            customer_name=customer.name,
            amount=amount,
            balance_after=new_balance,
            description=description or "",
            operator_id=operator_id,
            operator_name=operator_name,
        )

        # 更新客户余额
        customer.balance = new_balance
        await customer.save()

        return await CustomerDTO.from_tortoise_orm(customer)

    @atomic()
    async def create_customer(self, create_fields: CustomerCreateFields, app_id: str = "OPEN_PLATFORM") -> CustomerDTO:
        """创建客户"""
        # 获取应用信息
        app_model = await self.app_repo.get_by_appid(app_id)
        if not app_model:
            raise errors.PassportAppNotFoundError

        # 通过 Domain Entity 创建租户
        app_entity = await AppEntity.from_model(app_model)
        tenant_entity = TenantEntity.create_tenant(create_fields.name, app_entity)

        # 保存租户到数据库
        await self.tenant_repo.save(tenant_entity)

        # 设置应用和租户ID
        create_fields.app_id = str(app_model.id)
        create_fields.tenant_id = tenant_entity.tenant_id

        # 创建客户
        customer = await self.customer_repo.create_customer(create_fields)
        logger.info(f"为应用 {app_id} 创建客户 {create_fields.name}，租户ID: {tenant_entity.tenant_id}")
        return await CustomerDTO.from_tortoise_orm(customer)

    async def get_recharge_records(self, customer_id: int) -> List[CustomerRechargeRecordDTO]:
        # 验证客户是否存在
        customer = await self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise errors.CustomerNotFoundError

        # 获取充值记录
        records = await CustomerRechargeRecord.filter(customer_id=customer_id).order_by("-created_at")
        return [await CustomerRechargeRecordDTO.from_tortoise_orm(record) for record in records]
