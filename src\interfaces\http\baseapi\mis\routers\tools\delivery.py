import urllib.parse
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

from src.containers import Container
from src.repositories.delivery.users import GenerateWashOrderTasksPayload as DeliveryGenerateWashOrderTasksPayload

from ...schemas import BaseResponse, DeliveryPublishWashOrderTasksPayload

if TYPE_CHECKING:
    from src.domains.delivery.services import DeliveryUserService

router = APIRouter(tags=["delivery"])


@router.post("/delivery/generate_wash_order_tasks")
@inject
async def generate_wash_order_tasks(
    payload: DeliveryGenerateWashOrderTasksPayload,
    delivery_user_service: "DeliveryUserService" = Depends(Provide[Container.domains.delivery_user_service]),
):
    """生成洗单任务"""

    export_record = await delivery_user_service.generate_wash_order_tasks(payload=payload)

    return BaseResponse(data=export_record)


@router.get("/delivery/get_wash_order_export_records")
@inject
async def get_wash_order_export_record(
    delivery_user_service: "DeliveryUserService" = Depends(Provide[Container.domains.delivery_user_service]),
):
    """获取洗单任务导出记录"""
    export_records = await delivery_user_service.get_export_records()
    return BaseResponse(data=export_records)


@router.get("/delivery/get_users_by_city")
@inject
async def get_users_by_city(
    city: str = Query(..., description="城市"),
    delivery_user_service: "DeliveryUserService" = Depends(Provide[Container.domains.delivery_user_service]),
):
    """获取城市用户数量"""
    # 解码 URL 编码的城市名称
    decoded_city = urllib.parse.unquote(city)
    users_by_city = await delivery_user_service.get_users_by_city(decoded_city)
    return BaseResponse(data=users_by_city)


@router.post("/delivery/publish_wash_order_tasks")
@inject
async def publish_wash_order_tasks(
    payload: DeliveryPublishWashOrderTasksPayload,
    delivery_user_service: "DeliveryUserService" = Depends(Provide[Container.domains.delivery_user_service]),
):
    """发布洗单任务"""
    await delivery_user_service.publish_wash_order_tasks(export_id=payload.export_id)
    return BaseResponse(data=True)
