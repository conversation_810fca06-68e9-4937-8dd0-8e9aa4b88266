# encoding: utf-8
# docs/settings-refactor/02-architecture.md
# created: 2025-08-18 18:15:00

# 配置系统架构设计

## 整体架构

```mermaid
graph TB
    subgraph "配置源"
        ENV[环境变量]
        DOTENV[.env 文件]
        TOML[.env.toml 文件]
    end
    
    subgraph "基础设施层"
        BASE[BaseServiceSettings]
        DB[DatabaseSettings]
        RMQ[RabbitmqSettings]
        FS[FastapiSettings]
    end
    
    subgraph "服务层"
        API[BaseapiSettings]
        CONSUMER[ConsumerSettings]
        OPEN[OpenapiSettings]
        SCHED[SchedulerSettings]
        GH[GrowthHackerSettings]
    end
    
    ENV --> BASE
    DOTENV --> BASE
    TOML --> BASE
    
    BASE --> API
    BASE --> CONSUMER
    BASE --> OPEN
    BASE --> SCHED
    BASE --> GH
    
    DB --> BASE
    RMQ --> BASE
    FS --> BASE
```

## 核心组件

### 1. BaseServiceSettings（基类）

**位置**: `src/infrastructures/settings/base.py`

**职责**:
- 定义通用配置字段
- 实现配置加载逻辑
- 管理配置源优先级
- 提供扩展接口

**核心方法**:
```python
@classmethod
def settings_customise_sources(cls, ...) -> Tuple[...]:
    """配置源优先级管理"""
    # 1. 环境变量（最高）
    # 2. .env 文件
    # 3. .env.toml 文件（最低）
```

### 2. 服务配置类（子类）

**特点**:
- 继承 BaseServiceSettings
- 只定义服务特有配置
- 自动获得配置加载能力

**示例**:
```python
class BaseapiSettings(BaseServiceSettings):
    fastapi: FastapiSettings = Field(...)
    sms: AliyunSmsSettings = Field(...)
```

### 3. 模块配置类

**类型**: BaseModel（不是 BaseSettings）

**原因**:
- 不直接从环境变量加载
- 作为嵌套配置使用
- 由服务配置类管理

**示例**:
```python
class DatabaseSettings(BaseModel):
    mysql_uri: str = ""
    redis_uri: str = ""
```

## 配置加载流程

```mermaid
sequenceDiagram
    participant App as 应用启动
    participant Config as Settings类
    participant Env as 环境变量
    participant File as 配置文件
    participant Base as BaseSettings
    
    App->>Config: 初始化配置
    Config->>Base: 调用基类构造
    Base->>Base: settings_customise_sources
    Base->>Env: 读取环境变量
    Base->>File: 读取.env文件
    Base->>File: 读取.toml文件
    Base->>Base: 合并配置（按优先级）
    Base-->>Config: 返回配置实例
    Config-->>App: 配置就绪
```

## 配置优先级机制

### 优先级顺序（高到低）

1. **环境变量**
   - 来源：系统环境变量
   - 使用场景：生产环境、容器化部署
   - 格式：`DATABASE__MYSQL_URI=...`

2. **.env 文件**
   - 来源：项目根目录或服务目录
   - 使用场景：本地开发
   - 格式：KEY=VALUE

3. **.env.toml 文件**
   - 来源：项目根目录或服务目录
   - 使用场景：默认配置
   - 格式：TOML

### 合并策略

```python
# 伪代码表示
final_config = merge(
    toml_config,      # 基础
    dotenv_config,    # 覆盖
    env_config        # 最终覆盖
)
```

## 类型系统

### 继承关系

```
pydantic.BaseSettings
    └── BaseServiceSettings
        ├── BaseapiSettings
        ├── ConsumerSettings
        ├── OpenapiSettings
        ├── SchedulerSettings
        └── GrowthHackerSettings

pydantic.BaseModel
    ├── DatabaseSettings
    ├── FastapiSettings
    ├── RabbitmqSettings
    └── ...其他模块配置
```

### 字段定义

```python
# 必需字段
field: str  # 必须提供值

# 可选字段
field: str = ""  # 有默认值

# 复杂类型
field: ComplexType = Field(default_factory=ComplexType)

# 带验证
field: str = Field(..., min_length=1, max_length=100)
```

## 扩展机制

### 添加通用配置

```python
# 修改 BaseServiceSettings
class BaseServiceSettings(BaseSettings):
    # 现有字段...
    new_common: NewSettings = Field(default_factory=NewSettings)
```

### 添加服务特定配置

```python
# 修改具体服务的 Settings
class YourServiceSettings(BaseServiceSettings):
    special: SpecialSettings = Field(default_factory=SpecialSettings)
```

### 自定义配置源

```python
class CustomSettings(BaseServiceSettings):
    @classmethod
    def settings_customise_sources(cls, ...):
        # 自定义配置源逻辑
        sources = super().settings_customise_sources(...)
        # 添加自定义源
        return sources
```

## 安全设计

### 敏感信息管理

1. **不硬编码**：所有密钥、密码通过配置注入
2. **不提交**：.env 文件加入 .gitignore
3. **加密存储**：考虑使用密钥管理服务
4. **访问控制**：限制配置文件权限

### 配置验证

```python
class SecureSettings(BaseModel):
    password: str = Field(..., min_length=8)
    
    @validator('password')
    def validate_password(cls, v):
        if 'password' in v.lower():
            raise ValueError('Weak password')
        return v
```

## 性能优化

### 缓存机制

```python
@lru_cache(maxsize=1)
def get_config():
    return ServiceSettings()
```

### 懒加载

```python
class LazySettings:
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = ServiceSettings()
        return cls._instance
```

## 最佳实践

1. **分层配置**：基础设施配置用 BaseModel，服务配置用 BaseSettings
2. **最小权限**：只暴露必要的配置
3. **类型安全**：使用 Pydantic 类型验证
4. **文档完善**：每个配置字段都要有说明
5. **版本管理**：配置变更要有版本记录