# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/customer/repositories/customer.py
# created: 2025-02-06 12:12:13
# updated: 2025-07-01 23:51:09

from typing import Optional, Sequence, Tuple, Union

from pydantic import BaseModel, Field

from src.databases.models.customer import Customer, CustomerSecret, CustomerType
from src.databases.models.passport import PassportApp, PassportTenant
from src.domains.customer.dto import CustomerCreateFields, CustomerUpdateFields
from src.utils.idalloc import id_alloc_factor

gen_api_secret = id_alloc_factor(32)
gen_api_token = id_alloc_factor(64)
gen_access_key = id_alloc_factor(32)
gen_access_secret = id_alloc_factor(64)


class CustomerFilters(BaseModel):
    name: Optional[str] = Field(None, description="客户名称")
    code: Optional[str] = Field(None, description="客户编码")
    type: Optional[CustomerType] = Field(None, description="客户类型")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")


class CustomerRepository:

    @classmethod
    async def delete_by_code(cls, customer_code: str) -> Optional[bool]:
        customer = await Customer.get_or_none(code=customer_code)
        if not customer:
            return None
        await customer.delete()
        return True

    @classmethod
    async def create_customer(cls, create_fields: CustomerCreateFields) -> Customer:
        return await Customer.create(**create_fields.model_dump())

    @classmethod
    async def update_customer(cls, customer_id: int, update_fields: CustomerUpdateFields) -> Customer | None:
        customer = await Customer.get_or_none(id=customer_id)
        if not customer:
            return None
        for field, value in update_fields.model_dump().items():
            if value is not None:
                setattr(customer, field, value)
        await customer.save()
        return customer

    @classmethod
    async def get_customer_list(cls, filters: CustomerFilters) -> Tuple[Sequence[Customer], int]:
        query = Customer.all()
        if filters.name:
            query = query.filter(name__icontains=filters.name)
        if filters.code:
            query = query.filter(code=filters.code)
        if filters.type:
            query = query.filter(type=filters.type)
        total = await query.count()
        customers = (
            await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).order_by("-id").all()
        )
        return customers, total

    @classmethod
    async def get_by_id(cls, customer_id: int) -> Customer | None:
        return await Customer.get_or_none(id=customer_id).prefetch_related("app", "tenant")

    @classmethod
    async def get_by_code(cls, code: str) -> Customer | None:
        return await Customer.get_or_none(code=code).prefetch_related("app", "tenant")

    @classmethod
    async def get_by_api_token(cls, api_token: str) -> Customer | None:
        return await Customer.get_or_none(secrets__app_secret=api_token).prefetch_related("app", "tenant")

    @classmethod
    async def get_by_access_key(cls, access_key: str) -> Customer | None:
        return await Customer.get_or_none(secrets__access_key=access_key).prefetch_related("app", "tenant")

    @classmethod
    async def get_secret_by_access_key(cls, access_key: str) -> Union[CustomerSecret, None]:
        return await CustomerSecret.get_or_none(access_key=access_key).prefetch_related("customer")

    @classmethod
    async def delete_secret_by_customer_id(cls, customer_id: int, secret_id: int) -> Optional[bool]:
        secret = await CustomerSecret.get_or_none(id=secret_id, customer_id=customer_id)
        if not secret:
            return None
        await secret.delete()
        return True

    @classmethod
    async def get_secrets_by_customer_id(cls, customer_id: int) -> Sequence[CustomerSecret]:
        return await CustomerSecret.filter(customer_id=customer_id).all()

    @classmethod
    async def create_customer_secret(cls, customer: Customer, secret_name: str) -> CustomerSecret:
        return await CustomerSecret.create(
            customer=customer,
            name=secret_name,
            app_key=gen_api_secret(),
            app_secret=gen_api_token(),
            access_key=gen_access_key(),
            access_secret=gen_access_secret(),
        )

    @classmethod
    async def get_by_id_for_update(cls, customer_id: int) -> Customer | None:
        return await Customer.filter(id=customer_id).select_for_update().first()

    @classmethod
    async def get_secret_by_token(cls, token: str) -> Union[CustomerSecret, None]:
        return await CustomerSecret.get_or_none(app_secret=token).prefetch_related("customer")

    @classmethod
    async def get_by_app_tenant(cls, app: PassportApp, tenant: PassportTenant) -> Customer | None:
        return await Customer.get_or_none(app_id=app.app_id, tenant_id=tenant.tenant_id)
