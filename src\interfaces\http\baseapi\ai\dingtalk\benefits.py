# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/ai/dingtalk/benefits.py
# created: 2025-04-18 00:09:40
# updated: 2025-05-04 20:09:43

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query
from loguru import logger

from src.databases.models.passport import ThirdPlatformEnum
from src.infrastructures import errors

from .authorization import agent_api_authentication, base_api_authentication
from .schemas import (
    QuerySubscribeStateResponse,
    SubcribeResponse,
    SubscribePayload,
    SubscribeResult,
    SubscribeState,
    SubscribeStateEnum,
)

if TYPE_CHECKING:
    from redis.asyncio import Redis

    from src.databases.models.passport import PassportUser
    from src.domains.aiagent.services.auto_benefits.dingtalk_free import DingtalkFreeStrategy
    from src.domains.aiagent.services.auto_benefits.dingtalk_paid import DingtalkPaidStrategy
    from src.domains.benefits.services import BenefitsProductService
    from src.domains.passport.services import UserService

DINGTALK_CAMPUS_AGENT = "64598ba338694f529fa244627fd0d4c3"


router = APIRouter(tags=["dingtalk", "benefits"])


@router.get("/benefits/subscribe_state", name="查询订阅状态", response_model=SubcribeResponse)
@inject
async def query_subscribe_state(
    agent_id: str = Query(..., description="AI agent ID"),
    agent_user_id: str = Query(..., description="AI agent user ID"),
    agent_token: str = Depends(agent_api_authentication),
    redis: "Redis" = Depends(Provide["redis"]),
    product_service: "BenefitsProductService" = Depends(Provide["benefits_product_service"]),
    dingtalk_free_strategy: "DingtalkFreeStrategy" = Depends(Provide["dingtalk_free_strategy"]),
    dingtalk_paid_strategy: "DingtalkPaidStrategy" = Depends(Provide["dingtalk_paid_strategy"]),
):
    """查询用户是否已订阅自动申请权益
    如果用户已订阅且今日未领取，自动发放一张免费券
    如果用户已订阅且今日已领取，返回已领取的券信息
    如果用户未订阅，返回未订阅状态

    Returns:
        SubcribeResponse: 包含订阅状态的响应
        - subscribe_state: UNSUBSCRIBED-未订阅, SUBSCRIBED-已订阅且刚领取新券, ALREADY_APPLIED-已订阅且今日已领取
        - benefit: 权益信息，如果有的话
    """
    # 使用pattern匹配所有可能的key
    pattern = "dingtalk_auto_apply_blind:*"
    keys = await redis.keys(pattern)

    logger.info(f"DEBUGDINGTALKAI 开始查询订阅状态: agent_id={agent_id}, agent_user_id={agent_user_id}")
    logger.info(f"DEBUGDINGTALKAI 匹配到的keys: {keys}")

    # 遍历所有匹配的key，检查是否存在对应的agent_id和agent_user_id
    # message:DEBUGDINGTALKAI 当前key=dingtalk_auto_apply_blind:16619910574, hash_data={'uid': '00fcp2RUDriFYKLw', 'created_at': '1744979893.746312', 'union_id': 'k3X8BDSdjuiPrWbLQKUOhggiEiE', 'agent_id': '64598ba338694f529fa244627fd0d4c3', 'agent_user_id': '320404546129081338'}
    for key in keys:
        hash_data = await redis.hgetall(key)
        logger.info(f"DEBUGDINGTALKAI 当前key={key}, hash_data={hash_data}")
        if hash_data.get("agent_id") == agent_id and hash_data.get("agent_user_id") == agent_user_id:
            logger.info(f"DEBUGDINGTALKAI 找到匹配记录: {key}, {hash_data}")
            # 用户已订阅，检查是否今日已领取
            try:
                # 检查必要字段是否存在
                phone = key.split(":")[1]
                logger.info(f"DEBUGDINGTALKAI 当前phone={phone}")
                union_id = hash_data.get("union_id")
                if not phone or not union_id:
                    logger.warning(f"DEBUGDINGTALKAI 必要字段缺失: phone={phone}, union_id={union_id}")
                    return SubcribeResponse(
                        data=SubscribeResult(benefit=None, subscribe_state=SubscribeStateEnum.UNSUBSCRIBED)
                    )

                # 检查今日是否已领取
                product_order = await dingtalk_free_strategy.check_user_today_charge_order(
                    phone, union_id, throw_error=False
                )
                if product_order:
                    logger.info(
                        f"DEBUGDINGTALKAI 用户今日已领取权益: phone={phone}, product_code={product_order.product_code}"
                    )
                    # 今日已领取，返回已领取券信息
                    benefit = await product_service.get_product_by_code(product_order.product_code)
                    return SubcribeResponse(
                        data=SubscribeResult(benefit=benefit, subscribe_state=SubscribeStateEnum.ALREADY_APPLIED)
                    )

                # 今日未领取，自动发放一张新券
                logger.info(f"DEBUGDINGTALKAI 用户今日未领取权益，开始自动发券: phone={phone}")
                order_info = await dingtalk_free_strategy.apply(phone, union_id, agent_id, agent_user_id, "")
                benefit = await product_service.get_product_by_code(order_info.product_code)
                logger.info(f"DEBUGDINGTALKAI 自动发券成功: phone={phone}, product_code={order_info.product_code}")
                return SubcribeResponse(
                    data=SubscribeResult(benefit=benefit, subscribe_state=SubscribeStateEnum.SUBSCRIBED)
                )
            except Exception as e:
                logger.error(f"DEBUGDINGTALKAI 自动发券失败: {str(e)}")
                return SubcribeResponse(
                    data=SubscribeResult(benefit=None, subscribe_state=SubscribeStateEnum.SUBSCRIBED)
                )

    # 如果没有找到匹配的记录，返回未订阅状态
    logger.info("DEBUGDINGTALKAI 未找到匹配记录，返回未订阅状态")
    return SubcribeResponse(data=SubscribeResult(benefit=None, subscribe_state=SubscribeStateEnum.UNSUBSCRIBED))


@router.post("/benefits/subscribe_benefits_blind", name="订阅自动领盲盒", response_model=SubcribeResponse)
@inject
async def auto_apply_benefits(
    payload: SubscribePayload,
    current_user: "PassportUser" = Depends(base_api_authentication),
    redis: "Redis" = Depends(Provide["redis"]),
    product_service: "BenefitsProductService" = Depends(Provide["benefits_product_service"]),
    user_service: "UserService" = Depends(Provide["user_service"]),
    dingtalk_free_strategy: "DingtalkFreeStrategy" = Depends(Provide["dingtalk_free_strategy"]),
    dingtalk_paid_strategy: "DingtalkPaidStrategy" = Depends(Provide["dingtalk_paid_strategy"]),
):
    """钉钉AI agent自动领券能力

    处理逻辑：
    1. 检查用户是否已在自动领券名单中
    2. 如果是新用户：
       - 在2024年4月18日有机会获得无门槛券
       - 其他日期随机获得普通优惠券
    3. 如果是老用户，且今日已发过券，抛出错误
    4. 否则发送新券
    """
    logger.info(
        f"DEBUGDINGTALKAI 收到订阅自动领盲盒请求: {payload.model_dump()}, 用户ID: {current_user.uid}, 手机号: {current_user.phone}"
    )

    if payload.agent_id != DINGTALK_CAMPUS_AGENT:
        logger.error(f"DEBUGDINGTALKAI 无效的agent_id: {payload.agent_id}")
        raise errors.AgentNotFoundError

    dingtalk_user = await user_service.get_third_user_by_platform(ThirdPlatformEnum.DINGTALK, current_user.uid)
    if not dingtalk_user:
        logger.warning(f"DEBUGDINGTALKAI 钉钉用户不存在: {current_user.uid}, 手机号: {current_user.phone}")
        raise errors.DingtalkUserNotFoundError

    # 检查用户是否已在自动领券名单中
    is_old_user = await redis.sismember("dingtalk_ai_agent_auto_apply_users", current_user.phone)
    logger.info(
        f"DEBUGDINGTALKAI 用户是否在自动领券名单中: {is_old_user}, 用户ID: {current_user.uid}, union_id: {dingtalk_user.union_id}"
    )

    # 老用户林叔数据补充，查询dingtalk_auto_apply_blind:phone, 如果存在且无agent_user_id,则修改这条，添加agent_user_id
    if is_old_user:
        key = f"dingtalk_auto_apply_blind:{current_user.phone}"
        if await redis.exists(key):
            if not await redis.hexists(key, "agent_user_id"):
                await redis.hset(key, "agent_user_id", payload.agent_user_id)

    # 用户自动发券处理开始
    strategy = dingtalk_free_strategy if is_old_user else dingtalk_paid_strategy
    logger.info(
        f"DEBUGDINGTALKAI {'老' if is_old_user else '新'}用户自动发券处理开始: {current_user.phone}, union_id: {dingtalk_user.union_id}"
    )
    try:
        order_info = await strategy.apply(
            current_user.phone, dingtalk_user.union_id, payload.agent_id, payload.agent_user_id, current_user.uid
        )
        benefit = await product_service.get_product_by_code(order_info.product_code)
        subscribe_state = SubscribeStateEnum.SUBSCRIBED if is_old_user else SubscribeStateEnum.UNSUBSCRIBED
        logger.info(
            f"DEBUGDINGTALKAI {'老' if is_old_user else '新'}用户自动发券成功: {current_user.phone}, order_info: {order_info}"
        )
    except errors.AutoBenefitsApplyAlreadyAppliedError as e:
        logger.info(f"DEBUGDINGTALKAI 老用户今日已领取权益，查询已领取权益信息: {current_user.phone}")
        order_info = await dingtalk_free_strategy.check_user_today_charge_order(
            current_user.phone, dingtalk_user.union_id, throw_error=False
        )
        if not order_info:
            raise Exception(f"DEBUGDINGTALKAI 老用户今日已领取权益，查询已领取权益信息失败: {e}") from e

        benefit = await product_service.get_product_by_code(order_info.product_code)
        logger.info(
            "DEBUGDINGTALKAI 老用户今日已领取权益: union_id: {union_id}, product: {product}",
            union_id=dingtalk_user.union_id,
            product=f"{benefit.name}-{benefit.code}",
        )
        return SubcribeResponse(
            data=SubscribeResult(
                benefit=benefit,
                subscribe_state=SubscribeStateEnum.ALREADY_APPLIED,
            )
        )

    logger.info(
        f"DEBUGDINGTALKAI 用户订阅处理完成: phone: {current_user.phone}, union_id: {dingtalk_user.union_id}, "
        f"订阅产品: {benefit.name}-{benefit.code}, "
        f"订阅状态: {subscribe_state}"
    )
    return SubcribeResponse(
        data=SubscribeResult(
            benefit=benefit,
            subscribe_state=subscribe_state,
        )
    )


@router.get("/benefits/unsubscribe_agent", name="查询取消订阅状态", response_model=SubcribeResponse)
@inject
async def unsubscribe_agent(
    agent_id: str = Query(..., description="AI agent ID"),
    agent_user_id: str = Query(..., description="AI agent user ID"),
    agent_token: str = Depends(agent_api_authentication),
    redis: "Redis" = Depends(Provide["redis"]),
):
    """取消订阅状态

    处理逻辑：
    1. 检查agent_id是否合法
    2. 查找用户是否在dingtalk_auto_apply_blind:*中
    3. 如果找到用户，从dingtalk_ai_agent_auto_apply_users和dingtalk_auto_apply_blind:中删除
    4. 如果未找到用户，返回未订阅错误
    """
    if agent_id != DINGTALK_CAMPUS_AGENT:
        logger.error(f"DEBUGDINGTALKAI 无效的agent_id: {agent_id}")
        raise errors.AgentNotFoundError

    # 使用pattern匹配所有可能的key
    pattern = "dingtalk_auto_apply_blind:*"
    keys = await redis.keys(pattern)

    logger.info(f"DEBUGDINGTALKAI 开始查询取消订阅状态: agent_id={agent_id}, agent_user_id={agent_user_id}")
    logger.info(f"DEBUGDINGTALKAI 匹配到的keys: {keys}")

    # 遍历所有匹配的key，检查是否存在对应的agent_id和agent_user_id
    for key in keys:
        hash_data = await redis.hgetall(key)
        logger.info(f"DEBUGDINGTALKAI 当前key={key}, hash_data={hash_data}")
        if hash_data.get("agent_id") == agent_id and hash_data.get("agent_user_id") == agent_user_id:
            logger.info(f"DEBUGDINGTALKAI 找到匹配记录: {key}, {hash_data}")
            # 从dingtalk_ai_agent_auto_apply_users中删除用户
            phone = key.split(":")[1]
            await redis.srem("dingtalk_ai_agent_auto_apply_users", phone)
            # 删除dingtalk_auto_apply_blind:phone记录
            await redis.delete(key)
            logger.info(f"DEBUGDINGTALKAI 成功取消订阅: phone={phone}")
            return SubcribeResponse(
                data=SubscribeResult(
                    benefit=None,
                    subscribe_state=SubscribeStateEnum.UNSUBSCRIBED,
                )
            )

    # 如果没有找到匹配的记录，返回未订阅错误
    logger.info("DEBUGDINGTALKAI 未找到匹配记录，返回未订阅错误")
    raise errors.UserNotSubscribedError


@router.get("/benefits/query_subscribe_for_agent", name="通过查询订阅状态", response_model=QuerySubscribeStateResponse)
@inject
async def query_subscribe_for_agent(
    agent_id: str = Query(..., description="AI agent ID"),
    agent_user_id: str = Query(..., description="AI agent user ID"),
    agent_token: str = Depends(agent_api_authentication),
    redis: "Redis" = Depends(Provide["redis"]),
):
    """查询订阅状态

    处理逻辑：
    1. 检查agent_id是否合法
    2. 查找用户是否在dingtalk_auto_apply_blind:*中
    3. 如果找到用户，返回已订阅状态
    4. 如果未找到用户，返回未订阅状态
    """
    if agent_id != DINGTALK_CAMPUS_AGENT:
        logger.error(f"DEBUGDINGTALKAI 无效的agent_id: {agent_id}")
        raise errors.AgentNotFoundError

    # 使用pattern匹配所有可能的key
    pattern = "dingtalk_auto_apply_blind:*"
    keys = await redis.keys(pattern)

    logger.info(f"DEBUGDINGTALKAI 开始查询订阅状态: agent_id={agent_id}, agent_user_id={agent_user_id}")
    logger.info(f"DEBUGDINGTALKAI 匹配到的keys: {keys}")

    # 遍历所有匹配的key，检查是否存在对应的agent_id和agent_user_id
    for key in keys:
        hash_data = await redis.hgetall(key)
        logger.info(f"DEBUGDINGTALKAI 当前key={key}, hash_data={hash_data}")
        if hash_data.get("agent_id") == agent_id and hash_data.get("agent_user_id") == agent_user_id:
            return QuerySubscribeStateResponse(
                data=SubscribeState(
                    subscribe_state=SubscribeStateEnum.SUBSCRIBED,
                )
            )

    # 如果没有找到匹配的记录，返回未订阅状态
    return QuerySubscribeStateResponse(
        data=SubscribeState(
            subscribe_state=SubscribeStateEnum.UNSUBSCRIBED,
        )
    )


@router.post("/benefits/unsubscribe_benefits", name="取消自动申请权益", response_model=SubcribeResponse)
@inject
async def unsubscribe_benefits(
    current_user: "PassportUser" = Depends(base_api_authentication),
    redis: "Redis" = Depends(Provide["redis"]),
    user_service: "UserService" = Depends(Provide["user_service"]),
):
    """取消自动申请权益"""
    dingtalk_user = await user_service.get_third_user_by_platform(ThirdPlatformEnum.DINGTALK, current_user.uid)  # type: ignore
    if not dingtalk_user:
        logger.warning(f"钉钉用户不存在: {current_user.uid}")  # type: ignore
        raise errors.DingtalkUserNotFoundError

    # type: ignore
    await redis.srem("dingtalk_ai_agent_auto_apply_users", current_user.phone)
    await redis.hdel(f"dingtalk_auto_apply:{dingtalk_user.union_id}", "product_code")
    return SubcribeResponse(
        data=SubscribeResult(
            benefit=None,
            subscribe_state=SubscribeStateEnum.UNSUBSCRIBED,
        )
    )
