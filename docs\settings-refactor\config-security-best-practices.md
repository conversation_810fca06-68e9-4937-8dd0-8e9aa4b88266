# encoding: utf-8
# docs/config-security-best-practices.md
# created: 2025-08-18 16:30:00

# 配置文件安全最佳实践

## 重要提醒 ⚠️

当前 `.env.toml` 文件**没有**被 .gitignore 忽略，这意味着它会被提交到版本控制。
请确保 `.env.toml` 中**不包含任何敏感信息**！

## 配置文件分类

### 可以提交到版本控制的文件
- ✅ `.env.example` - 配置示例（无敏感信息）
- ✅ `.env.toml.example` - TOML示例（无敏感信息）  
- ⚠️ `.env.toml` - 默认配置（当前可提交，但需谨慎）

### 绝不能提交的文件
- ❌ `.env` - 包含实际密钥
- ❌ `.env.production` - 生产环境配置
- ❌ `.env.local` - 本地开发配置
- ❌ `**/secrets.*` - 任何密钥文件

## 安全配置策略

### 1. 分层配置管理

```
.env.toml (可提交)
├── 只包含默认值和非敏感配置
├── 数据库URI使用占位符
└── API密钥留空

.env (不可提交)
├── 包含实际的连接字符串
├── 包含真实的API密钥
└── 覆盖 .env.toml 的默认值
```

### 2. 敏感信息处理原则

#### ✅ 正确做法
```toml
# .env.toml (可提交)
[database]
mysql_uri = ""  # 在 .env 中设置实际值
redis_uri = ""  # 在 .env 中设置实际值

[rabbitmq]
url = ""  # 在 .env 中设置实际值
```

#### ❌ 错误做法
```toml
# .env.toml (不要这样做！)
[database]
mysql_uri = "mysql://user:password@host/db"  # 包含密码！
redis_uri = "redis://user:password@host"     # 包含密码！
```

### 3. 环境变量优先级

1. **生产环境**：只使用环境变量
2. **测试环境**：环境变量 + .env 文件
3. **开发环境**：.env 文件 + .env.toml 默认值

## 配置检查清单

### 提交代码前必须检查

- [ ] `.env` 文件在 .gitignore 中
- [ ] 没有硬编码的密码或密钥
- [ ] `.env.toml` 不包含敏感信息
- [ ] 示例文件已更新（.env.example）
- [ ] 运行 `git diff` 检查是否有敏感信息

### 快速检查命令

```bash
# 检查是否有密码泄露
grep -r "password\|secret\|key\|token" --include="*.toml" --include="*.env"

# 检查 git 状态
git status --porcelain | grep -E "\.env$|secrets"

# 运行配置检查脚本
python scripts/check_settings.py
```

## 不同环境的配置管理

### 本地开发
```bash
# 创建本地配置
cp .env.example .env
# 编辑并填入开发环境的值
vim .env
```

### CI/CD 环境
```yaml
# GitHub Actions 示例
env:
  DATABASE__MYSQL_URI: ${{ secrets.MYSQL_URI }}
  RABBITMQ__URL: ${{ secrets.RABBITMQ_URL }}
```

### Docker 部署
```dockerfile
# 使用构建参数
ARG DATABASE_URI
ENV DATABASE__MYSQL_URI=${DATABASE_URI}

# 或使用 docker-compose
environment:
  - DATABASE__MYSQL_URI=${MYSQL_URI}
```

### Kubernetes 部署
```yaml
# 使用 Secret
envFrom:
  - secretRef:
      name: app-secrets
```

## 紧急响应流程

### 如果不小心提交了敏感信息

1. **立即行动**
   ```bash
   # 从历史中删除文件
   git filter-branch --force --index-filter \
     'git rm --cached --ignore-unmatch path/to/sensitive-file' \
     --prune-empty --tag-name-filter cat -- --all
   ```

2. **轮换密钥**
   - 立即更换所有泄露的密码
   - 重新生成API密钥
   - 更新所有环境的配置

3. **通知相关人员**
   - 通知安全团队
   - 记录事件
   - 审查流程防止再次发生

## 推荐工具

### 密钥扫描工具
- **git-secrets**: 防止提交密钥
- **truffleHog**: 扫描历史提交
- **gitleaks**: CI/CD集成扫描

### 密钥管理服务
- **HashiCorp Vault**: 企业级密钥管理
- **AWS Secrets Manager**: AWS环境
- **Azure Key Vault**: Azure环境
- **Kubernetes Secrets**: K8s原生方案

## 团队规范

1. **新成员入职**
   - 培训配置安全意识
   - 提供配置模板
   - 审查首次提交

2. **代码审查**
   - 检查配置变更
   - 验证无敏感信息
   - 确认遵循规范

3. **定期审计**
   - 每月扫描代码库
   - 检查配置合规性
   - 更新安全策略

## 联系方式

- 安全问题：<EMAIL>
- 配置支持：<EMAIL>
- 紧急响应：<EMAIL>

---

**记住：安全是每个人的责任！**