# encoding: utf-8
# src/applications/common/commands/benefits/product_order_export.py
# created: 2025-08-19 10:00:00

import asyncio
import io
import json
from datetime import datetime
from typing import TYPE_CHECKING, Optional, Sequence

import pandas as pd
import redis.asyncio as redis
from loguru import logger

from src.databases.models.benefits import BenefitsProductOrderStatus
from src.domains.benefits.dto import BenefitsProductOrderWithCustomerDTO, ProductOrderExportStatus
from src.repositories.benefits.product_order import ProductOrderExportFilters
from src.utils.oss import upload_to_oss

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsProductOrder
    from src.databases.models.customer import Customer
    from src.repositories.benefits import ProductOrderRepository


# 订单状态映射
ORDER_STATUS_MAP = {
    BenefitsProductOrderStatus.PENDING: "待处理",
    BenefitsProductOrderStatus.SUCCESS: "成功",
    BenefitsProductOrderStatus.FAILED: "失败",
    BenefitsProductOrderStatus.PROCESSING: "处理中",
    BenefitsProductOrderStatus.EXCEPTION: "异常",
    BenefitsProductOrderStatus.REFUNDED: "已退款",
}


class ProductOrderExportCommandService:
    """产品订单导出命令服务"""

    def __init__(
        self,
        order_repo: "ProductOrderRepository",
        redis_client: redis.Redis,
    ):
        self.order_repo = order_repo
        self.redis_client = redis_client

    async def export_orders(
        self,
        export_id: str,
        user_id: str,
        user_name: str,
        filters_dict: dict,
    ) -> None:
        """
        导出订单数据

        Args:
            export_id: 导出任务ID
            user_id: 用户ID
            user_name: 用户名
            filters_dict: 过滤条件字典
        """
        logger.info(f"开始处理产品订单导出任务: export_id={export_id}, user_id={user_id}")

        try:
            # 更新状态为处理中
            await self._update_export_status(user_id, export_id, ProductOrderExportStatus.PROCESSING.value)

            # 构建过滤条件
            filters = ProductOrderExportFilters(**filters_dict)

            # 获取数据
            total, orders, customers = await self.order_repo.get_product_orders_for_export(filters)

            if total == 0:
                await self._update_export_status(
                    user_id, export_id, ProductOrderExportStatus.COMPLETED.value, completed_at=datetime.now()
                )
                logger.info(f"导出任务完成，无数据: {export_id}")
                return

            # 生成Excel
            excel_content = await self._generate_excel_content(orders, customers)

            # 上传到OSS
            try:
                file_url = await self._upload_to_oss(excel_content, export_id, user_name)

                # 更新状态为完成
                await self._update_export_status(
                    user_id,
                    export_id,
                    ProductOrderExportStatus.COMPLETED.value,
                    file_url=file_url,
                    completed_at=datetime.now(),
                )
                logger.info(f"产品订单导出任务完成: {export_id}, url: {file_url}")

            except Exception as upload_error:
                logger.error(f"文件上传失败: {upload_error}")
                await self._update_export_status(
                    user_id,
                    export_id,
                    ProductOrderExportStatus.FAILED.value,
                    error_message=f"文件上传失败: {str(upload_error)}",
                    completed_at=datetime.now(),
                )
                raise

        except Exception as e:
            logger.error(f"产品订单导出任务失败: {e}")
            await self._update_export_status(
                user_id,
                export_id,
                ProductOrderExportStatus.FAILED.value,
                error_message=str(e),
                completed_at=datetime.now(),
            )
            raise

    async def _generate_excel_content(
        self, orders: Sequence["BenefitsProductOrder"], customers: Sequence["Customer"]
    ) -> bytes:
        """
        生成Excel内容

        Args:
            orders: 订单列表
            customers: 客户列表

        Returns:
            bytes: Excel文件内容
        """

        # 批量转换为DTO列表，提升性能
        async def convert_order(order):
            return await BenefitsProductOrderWithCustomerDTO.from_order_with_customers(order, customers)

        # 使用asyncio.gather并发转换，显著提升大数据量时的性能
        orders_dto = await asyncio.gather(*[convert_order(order) for order in orders])

        logger.debug(f"成功转换 {len(orders_dto)} 个订单为DTO")

        # 使用列表推导式构建数据，提升性能
        data = [
            {
                "订单ID": str(order_dto.order_id),  # type: ignore
                "外部订单ID": order_dto.out_order_id,  # type: ignore
                "产品编码": order_dto.product_code,  # type: ignore
                "产品名称": order_dto.product_name,  # type: ignore
                "价格(分)": order_dto.price,  # type: ignore
                "充值账号": order_dto.account,  # type: ignore
                "订单状态": ORDER_STATUS_MAP.get(order_dto.status, f"未知状态({order_dto.status})"),  # type: ignore
                "客户ID": order_dto.customer_id or "",
                "客户名称": order_dto.customer_name or "",
                "来源标识": order_dto.source_identify,  # type: ignore
                "创建时间": order_dto.created_at.strftime("%Y-%m-%d %H:%M:%S") if order_dto.created_at else "",  # type: ignore
                "更新时间": order_dto.updated_at.strftime("%Y-%m-%d %H:%M:%S") if order_dto.updated_at else "",  # type: ignore
            }
            for order_dto in orders_dto
        ]

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 导出到Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="产品订单", index=False)

            # 优化列宽设置，避免对大数据集进行全量计算
            worksheet = writer.sheets["产品订单"]
            for idx, col in enumerate(df.columns):
                # 采样计算列宽，提升大数据量时的性能
                sample_size = min(100, len(df))
                if sample_size > 0:
                    sample_data = df[col].head(sample_size).astype(str)
                    max_length = max(sample_data.map(len).max(), len(col))
                else:
                    max_length = len(col)
                worksheet.column_dimensions[chr(65 + idx)].width = min(max_length + 2, 50)

        return output.getvalue()

    async def _upload_to_oss(self, excel_content: bytes, export_id: str, user_name: str) -> str:
        """
        上传文件到OSS

        Args:
            excel_content: Excel文件内容
            export_id: 导出ID
            user_name: 用户名

        Returns:
            str: 文件URL
        """
        # OSS配置
        bucket_name = "benefits-order-export"
        endpoint = "oss-cn-shanghai.aliyuncs.com"

        # 生成文件名
        filename = f"exports/product_orders/{export_id}_{user_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 使用公共上传方法
        return await upload_to_oss(
            bucket_name=bucket_name,
            endpoint=endpoint,
            filename=filename,
            file_content=excel_content,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            object_acl="public-read",
        )

    async def _update_export_status(
        self,
        user_id: str,
        export_id: str,
        status: str,
        file_url: Optional[str] = None,
        error_message: Optional[str] = None,
        completed_at: Optional[datetime] = None,
    ):
        """
        更新导出状态到Redis

        Args:
            user_id: 用户ID
            export_id: 导出ID
            status: 状态
            file_url: 文件URL
            error_message: 错误信息
            completed_at: 完成时间
        """
        try:
            redis_key = f"product_order_export:{user_id}:{export_id}"

            # 使用Pipeline批量操作，提升性能
            async with self.redis_client.pipeline() as pipe:
                record_data = await self.redis_client.get(redis_key)

                if record_data:
                    record_dict = json.loads(record_data)

                    # 更新字段
                    record_dict["status"] = status
                    if file_url is not None:
                        record_dict["file_url"] = file_url
                    if error_message is not None:
                        record_dict["error_message"] = error_message
                    if completed_at is not None:
                        record_dict["completed_at"] = completed_at.isoformat()

                    # 保存回Redis，设置过期时间避免内存泄漏
                    await pipe.set(redis_key, json.dumps(record_dict, default=str), ex=86400)  # 24小时过期
                    await pipe.execute()

                    logger.debug(f"成功更新导出状态: export_id={export_id}, status={status}")
        except Exception as e:
            logger.error(f"更新导出状态失败: export_id={export_id}, error={e}")
