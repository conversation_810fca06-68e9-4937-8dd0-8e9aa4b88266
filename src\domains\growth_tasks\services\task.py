# encoding: utf-8
# src/domains/growth_tasks/services/task.py
# created: 2025-07-27 09:16:44

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.databases.models.growth_hacker import TaskStatus
from src.infrastructures.exceptions.growth_hacker import TaskNotFoundError, TaskValidationError

if TYPE_CHECKING:
    from databases.models.growth_hacker import GrowthHackerTask
    from src.repositories.growth_hacker import TaskRepository


class TaskDomainService:

    def __init__(self, task_repository: "TaskRepository"):
        self.task_repository = task_repository

    async def validate_task(
        self, task_id: str, phone: str, lat: str, lng: str, city: str, access_url: str, batch_name: str
    ) -> bool:
        """验证任务"""

        allowed_statuses = [TaskStatus.FAILED, TaskStatus.PENDING, TaskStatus.RUNNING]

        # 判断task_id是否存在，如果存在且状态不在允许的状态列表中则不允许重复执行
        existing_task = await self.task_repository.get_by_task_id(task_id)
        if existing_task and existing_task.status not in allowed_statuses:
            logger.warning(f"task_id: {task_id} 已存在且状态为 {existing_task.status}，不允许重复执行")
            return False
        elif existing_task and existing_task.status in allowed_statuses:
            logger.info(f"task_id: {task_id} 存在但状态为 {existing_task.status}，允许重试")

        # 检查同一手机号今天是否已有成功任务
        today = datetime.now()
        today_success_task = await self.task_repository.get_by_phone_process_date(phone, today)
        if today_success_task:
            logger.warning(f"手机号 {phone} 今日已有成功任务, 任务ID: {today_success_task.task_id}")
            return False

        return True

    async def create_task(
        self, task_id: str, phone: str, lat: str, lng: str, city: str, access_url: str, batch_name: str
    ) -> "GrowthHackerTask":
        """创建任务或重置可重试任务"""
        allowed_statuses = [TaskStatus.FAILED, TaskStatus.PENDING, TaskStatus.RUNNING]

        # 先检查是否是可重试的任务
        existing_task = await self.task_repository.get_by_task_id(task_id)
        if existing_task and existing_task.status in allowed_statuses:
            # 如果任务状态在允许列表中，重置为 PENDING 准备重试
            logger.info(f"重置任务 {task_id} (原状态: {existing_task.status}) 为 PENDING")
            await self.task_repository.update_status(
                task_id=task_id,
                status=TaskStatus.PENDING,
                message=f"任务重试 (原状态: {existing_task.status})",
                execution_time=None,
            )
            return existing_task

        # 创建新任务
        task = await self.task_repository.create(
            task_id=task_id,
            phone=phone,
            city=city or None,  # 空字符串转为None
            lat=lat,
            lng=lng,
            access_url=access_url or None,
            batch_name=batch_name or None,
        )
        return task

    async def update_task_status(
        self,
        task_id: str,
        status: "TaskStatus",
        message: str = "",
        execution_time: Optional[float] = None,
        page_content_html: Optional[str] = None,
    ) -> bool:
        """更新任务状态"""

        # 检查任务是否存在
        task = await self.task_repository.get_by_task_id(task_id)
        if not task:
            raise TaskNotFoundError(f"任务 {task_id} 不存在")

        # 验证状态转换是否合法
        if not self._validate_status_transition(task.status, status):
            raise TaskValidationError(f"不允许从状态 {task.status} 转换到 {status}")

        # 更新状态
        return await self.task_repository.update_status(
            task_id=task_id,
            status=status,
            message=message,
            execution_time=execution_time,
            page_content_html=page_content_html,
        )

    def _validate_status_transition(self, current_status: "TaskStatus", new_status: "TaskStatus") -> bool:
        """验证状态转换是否合法"""
        allowed_transitions = {
            TaskStatus.PENDING: [TaskStatus.RUNNING, TaskStatus.FAILED],
            TaskStatus.RUNNING: [
                TaskStatus.SUCCESS,
                TaskStatus.FAILED,
                TaskStatus.RISK_DETECTED,
                TaskStatus.ALREADY_CLAIMED,
            ],
            TaskStatus.FAILED: [TaskStatus.PENDING, TaskStatus.RUNNING],  # 允许重试
            # 终态一般不允许转换，但可根据业务需求调整
            TaskStatus.SUCCESS: [],
            TaskStatus.ALREADY_CLAIMED: [],
            TaskStatus.RISK_DETECTED: [TaskStatus.PENDING],  # 允许重试
        }

        return new_status in allowed_transitions.get(current_status, [])
