# encoding: utf-8
# src/applications/passport/services/authorization.py
# created: 2025-07-31 17:10:00

import asyncio
from typing import TYPE_CHECKING, Optional

from loguru import logger

from src.domains.passport.entities import AppEntity, UserEntity
from src.infrastructures.errors import ApplicationNotFoundError

from ..dto import UserInfoDTO

if TYPE_CHECKING:
    from src.domains.passport.services import UserService
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class AuthService:
    """认证服务"""

    def __init__(
        self,
        user_domain_service: "UserService",
        app_repository: "AppRepository",
    ):
        self.user_domain_service = user_domain_service
        self.app_repository = app_repository

    async def jwt_auth(self, token: str, current_app: "AppEntity") -> UserInfoDTO:
        user_entity = await self.user_domain_service.get_user_by_jwt(token, current_app.app_secret)
        return UserInfoDTO.from_user_entity(user_entity)

    async def baseapi_authentication(self, token: str, app_id: str, tenant_id: Optional[str] = None) -> UserEntity:
        app = await self.app_repository.get_by_appid(app_id)
        if not app:
            raise ApplicationNotFoundError

        user_entity = await self.user_domain_service.get_user_by_jwt(token, app.app_secret)
        return user_entity
