# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/repositories/orders.py
# created: 2025-02-06 13:18:53
# updated: 2025-04-07 17:28:58

from datetime import datetime

from tortoise.expressions import RawSQL

from src.databases.models.delivery import DeliveryUnionOrder, OrderStateEnum, SettleStateEnum
from src.domains.delivery.dto import UnionOrderInfoDTO


class DeliveryOrderRepository:

    @classmethod
    async def get_orders_count(cls, start_time: datetime, end_time: datetime) -> int:
        return await DeliveryUnionOrder.filter(tk_create_time__range=(start_time, end_time)).count()

    @classmethod
    async def get_orders_list(
        cls, page: int, page_size: int, start_time: datetime, end_time: datetime
    ) -> list[DeliveryUnionOrder]:
        orders = (
            await DeliveryUnionOrder.filter(tk_create_time__range=(start_time, end_time))
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        return orders

    @classmethod
    async def get_order_by_hours_by_time_range(cls, start: datetime, end: datetime) -> list[dict]:
        # 按小时分组统计订单数量
        results = (
            await DeliveryUnionOrder.annotate(time=RawSQL("DATE_FORMAT(tk_create_time, '%%Y-%%m-%%d %%H')"))
            .filter(
                order_state__in=[OrderStateEnum.ORDERED, OrderStateEnum.PAID, OrderStateEnum.RECEIVED],
                tk_create_time__range=(start, end),
            )
            .group_by("time")
            .order_by("time")
            .annotate(
                count=RawSQL("COUNT(*)"),
                pay_amount=RawSQL("SUM(pay_amount)"),
                order_amount=RawSQL("SUM(unit_price)"),
                settle_amount=RawSQL("SUM(income)"),
            )
            .values("time", "count", "pay_amount", "order_amount", "income")
        )
        return results

    @classmethod
    async def create_or_update(cls, order_info: UnionOrderInfoDTO) -> DeliveryUnionOrder:
        union_order, _ = await DeliveryUnionOrder.update_or_create(
            order_id=order_info.parent_order_id,
            biz_order_id=order_info.biz_order_id,
            defaults=order_info.model_dump(by_alias=True),
        )
        return union_order

    @classmethod
    async def get_order_by_id_state(
        cls, order_id: str, order_state: OrderStateEnum, settle_state: SettleStateEnum
    ) -> DeliveryUnionOrder | None:
        return await DeliveryUnionOrder.filter(
            order_id=order_id, order_state=order_state, settle_state=settle_state
        ).first()

    @classmethod
    async def get_daily_task_orders_aggregated(cls, date: str) -> dict:
        """查询符合日常任务batch格式的订单数据并进行聚合"""
        # 使用更精确的过滤条件，直接在数据库层面过滤
        target_pattern = f"日常任务_{date}"

        # 使用 ORM 的 annotate 配合 RawSQL 在数据库层面过滤
        orders = (
            await DeliveryUnionOrder.annotate(batch_value=RawSQL("JSON_UNQUOTE(JSON_EXTRACT(source_info, '$.batch'))"))
            .filter(source_info__isnull=False, batch_value__icontains=target_pattern)
            .all()
        )

        # 解析并聚合数据
        aggregated = {}
        for order in orders:
            if order.source_info and isinstance(order.source_info, dict):
                batch = order.source_info.get("batch", "")
                if isinstance(batch, str) and target_pattern in batch:
                    parts = batch.split("_")
                    if len(parts) >= 4 and parts[-2] == "日常任务" and parts[-1] == date:
                        lifecycle = parts[0]
                        city = parts[1]

                        key = (city, lifecycle)
                        if key not in aggregated:
                            aggregated[key] = {
                                "lifecycle": lifecycle,
                                "count": 0,
                                "total_pay_amount": 0,
                                "total_settle_amount": 0,
                                "total_income": 0,
                            }

                        aggregated[key]["count"] += 1
                        aggregated[key]["total_pay_amount"] += order.pay_amount or 0
                        aggregated[key]["total_settle_amount"] += order.settle_amount or 0
                        aggregated[key]["total_income"] += order.income or 0

        # 按城市分组重新组织数据
        result = {}
        for (city, _), data in aggregated.items():
            if city not in result:
                result[city] = []
            result[city].append(data)

        # 对每个城市内的数据按生命周期排序
        for city in result:
            result[city].sort(key=lambda x: x["lifecycle"])

        return result

    @classmethod
    async def get_order_recovery_stats_by_lifecycle(cls, date: str) -> dict:
        """查询特定日期批次任务的七天内订单回收数量统计"""
        from datetime import datetime, timedelta

        # 构建batch过滤条件
        target_pattern = f"日常任务_{date}"

        # 查询符合batch条件的订单
        orders = (
            await DeliveryUnionOrder.annotate(batch_value=RawSQL("JSON_UNQUOTE(JSON_EXTRACT(source_info, '$.batch'))"))
            .filter(source_info__isnull=False, batch_value__icontains=target_pattern)
            .all()
        )

        # 生成七天的日期列表
        task_date = datetime.strptime(date, "%Y-%m-%d").date()
        seven_days = []
        for i in range(7):
            day_date = task_date + timedelta(days=i)
            seven_days.append(day_date.strftime("%Y-%m-%d"))

        # 按日期和生命周期分组统计
        date_lifecycle_stats = {}
        total_orders = 0

        for order in orders:
            if order.source_info and isinstance(order.source_info, dict):
                batch = order.source_info.get("batch", "")
                if isinstance(batch, str) and target_pattern in batch:
                    parts = batch.split("_")
                    if len(parts) >= 4 and parts[-2] == "日常任务" and parts[-1] == date:
                        lifecycle = parts[0]
                        total_orders += 1

                        # 计算订单创建时间与任务日期的天数差异
                        if order.tk_create_time:
                            order_date = order.tk_create_time.date()
                            day_diff = (order_date - task_date).days

                            # 统计七天内的订单数量（0-6天）
                            if 0 <= day_diff <= 6:
                                order_date_str = order_date.strftime("%Y-%m-%d")

                                if order_date_str not in date_lifecycle_stats:
                                    date_lifecycle_stats[order_date_str] = {}

                                if lifecycle not in date_lifecycle_stats[order_date_str]:
                                    date_lifecycle_stats[order_date_str][lifecycle] = 0

                                date_lifecycle_stats[order_date_str][lifecycle] += 1

        # 构建最终返回格式
        daily_stats = []
        for date_str in seven_days:
            day_data = {"date": date_str, "lifecycles": []}

            if date_str in date_lifecycle_stats:
                for lifecycle, count in date_lifecycle_stats[date_str].items():
                    day_data["lifecycles"].append({"lifecycle": lifecycle, "count": count})

            # 按生命周期排序
            day_data["lifecycles"].sort(key=lambda x: x["lifecycle"])
            daily_stats.append(day_data)

        return {"total_orders": total_orders, "daily_stats": daily_stats}
