# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/dto.py
# created: 2024-12-04 02:22:29
# updated: 2025-04-14 21:52:06

import enum
import json
from datetime import datetime
from enum import StrEnum
from typing import Any, Dict, List, Optional, Sequence

from pydantic import BaseModel, ConfigDict, Field
from tortoise import Tortoise
from tortoise.contrib.pydantic import pydantic_model_creator

from src.databases.models.benefits import (
    BenefitsProduct,
    BenefitsProductOrder,
    BenefitsSku,
    BenefitsSkuChargeRecord,
    BenefitsSkuChargeRecordStatus,
    BenefitsSKUPurchase,
    BenefitsSkuType,
    BenefitsSupplier,
    ChargeFuncEnum,
    ProductOrderStatus,
)

Tortoise.init_models(
    [
        "src.databases.models.benefits",
        "src.databases.models.customer",
        "src.databases.models.delivery",
        "src.databases.models.passport",
        "src.databases.models.datas",
    ],
    "models",
)


SupplierDTO = pydantic_model_creator(BenefitsSupplier)
SupplierDetailDTO = pydantic_model_creator(BenefitsSupplier)
SupplierUpdateDTO = pydantic_model_creator(
    BenefitsSupplier,
    exclude=("id", "created_at", "updated_at", "skus", "balance"),
    optional=("name", "identify", "contact", "phone", "extra", "settled_at"),
)
SupplierCreateDTO = pydantic_model_creator(
    BenefitsSupplier,
    exclude=("id", "created_at", "updated_at", "skus"),
    optional=("name", "identify", "contact", "phone", "extra", "settled_at", "balance"),
)


BenefitsProductOrderDTO = pydantic_model_creator(BenefitsProductOrder)
BenefitsProductOrderCreateFields = pydantic_model_creator(
    BenefitsProductOrder,
    name="BenefitsProductOrderCreateFields",
    include=(
        "price",
        "account",
        "out_order_id",
        "product_id",
        "product_name",
        "product_code",
        "source_identify",
        "notify_url",
        "detail",
        "app_id",
        "tenant_id",
        "op_uid",
        "op_description",
    ),
    optional=("detail", "app_id", "tenant_id", "op_uid", "op_description"),
)

BenefitsProductDTO = pydantic_model_creator(
    BenefitsProduct,
    exclude=("skus",),
)


class SupplierItemDTO(BaseModel):
    id: int = Field(..., description="供应商ID")
    name: str = Field(..., description="供应商名称")
    identify: str = Field(..., description="供应商标识")
    contact: str = Field(..., description="供应商联系人")
    phone: str = Field(..., description="供应商电话")


class BenefitsProductSkuDTO(BaseModel):
    name: str = Field(..., description="SKU 名称")
    code: str = Field(..., description="SKU 编码")
    cost: int = Field(..., description="SKU 成本")
    stock: int = Field(..., description="SKU 库存")
    detail: Optional[dict] = Field(None, description="SKU 详情")
    count: int = Field(..., description="SKU 数量")
    supplier: Optional[SupplierItemDTO] = Field(None, description="供应商")

    model_config = ConfigDict(from_attributes=True)


class BenefitsProductDetailDTO(BaseModel):
    name: str = Field(..., description="商品名称")
    code: str = Field(..., description="商品编码")
    price: int = Field(..., description="商品价格")
    sale_price: int = Field(..., description="商品销售价格")
    detail: Optional[dict] = Field(None, description="商品详情")
    description: str = Field(..., description="商品描述")
    skus: List[BenefitsProductSkuDTO] = Field([], description="商品SKU列表")


BenefitsProductUpdateDTO = pydantic_model_creator(
    BenefitsProduct,
    sort_alphabetically=True,
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "product_skus",
        "skus",
        "code",
        "customer_benefitss",
        "benefits_product_skuss",
        "benefits_skus",
    ),
    optional=("name", "detail"),
)
BenefitsProductCreateDTO = pydantic_model_creator(
    BenefitsProduct,
    sort_alphabetically=True,
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "product_skus",
        "skus",
        "code",
        "customer_benefitss",
        "benefits_product_skuss",
        "benefits_skus",
    ),
)

BenefitsSkuChargeRecordDetailDTO = pydantic_model_creator(
    BenefitsSkuChargeRecord,
    sort_alphabetically=True,
    exclude=("sku.products", "sku.benefits_sku_charge_records", "sku.sku_products", "sku.purchase_records"),
)
BenefitsSkuChargeRecordDTO = pydantic_model_creator(
    BenefitsSkuChargeRecord,
    sort_alphabetically=True,
    include=(
        "id",
        "sku",
        "sku.code",
        "sku.name",
        "status",
        "supplier_sku_code",
        "supplier_order_id",
        "account",
        "detail",
        "charge_order_id",
        "source_identify",
        "charged_at",
        "created_at",
        "updated_at",
    ),
)


BenefitsSkuDTO = pydantic_model_creator(BenefitsSku, sort_alphabetically=True, exclude=("products",))
BenefitsSkuDetailDTO = pydantic_model_creator(BenefitsSku, sort_alphabetically=True, name="BenefitsSkuDetailDTO")
BenefitsSkuCreatedDTO = pydantic_model_creator(  # benefits create model
    BenefitsSku,
    sort_alphabetically=True,
    include=(
        "name",
        "type",
        "third_part_code",
        "charge_func",
        "supplier_id",
        "price",
        "cost",
        "stock",
        "detail",
        "enabled",
    ),
    optional=("price", "cost", "stock", "detail", "enabled"),
)

BenefitsSkuCreateFields = pydantic_model_creator(
    BenefitsSku,
    name="BenefitsSkuCreateFields",
    include=("name", "type", "third_part_code", "supplier_id", "charge_func", "price", "cost", "stock", "detail"),
    optional=("price", "cost", "stock", "detail"),
)
BenefitsSkuUpdateDTO = pydantic_model_creator(  # benefits update model
    BenefitsSku,
    sort_alphabetically=True,
    exclude=(
        "supplier",
        "id",
        "code",
        "created_at",
        "updated_at",
        "products",
        "benefits_sku_charge_records",
        "sku_products",
        "purchase_records",
        "benefits_product_skuss",
        "benefits_products",
        "skus",
    ),
    optional=(
        "name",
        "type",
        "third_part_code",
        "charge_func",
        "price",
        "cost",
        "stock",
        "detail",
        "enabled",
        "supplier_id",
    ),
)


class SupplierListDTO(BaseModel):
    total: int = Field(..., description="总数")
    # type: ignore
    suppliers: List[SupplierDTO] = Field(..., description="供应商列表")


PurchaseDTO = pydantic_model_creator(BenefitsSKUPurchase, sort_alphabetically=True, name="PurchaseDTO")
PurchaseCreateFields = pydantic_model_creator(
    BenefitsSKUPurchase,
    name="PurchaseCreateFields",
    include=(
        "purchase_id",
        "purchase_name",
        "purchase_stock",
        "remain_stock",
        "sku_id",
        "unit_price",
        "total_price",
        "sales_start_time",
        "sales_end_time",
        "detail",
    ),
    optional=(
        "purchase_stock",
        "remain_stock",
        "unit_price",
        "total_price",
        "sales_start_time",
        "sales_end_time",
        "detail",
    ),
    model_config=ConfigDict(extra="ignore"),
)
PurchaseUpdateFields = pydantic_model_creator(
    BenefitsSKUPurchase,
    name="PurchaseUpdateFields",
    include=("purchase_stock", "remain_stock", "unit_price", "total_price", "sales_start_time", "sales_end_time"),
    optional=("purchase_stock", "remain_stock", "unit_price", "total_price", "sales_start_time", "sales_end_time"),
    model_config=ConfigDict(extra="ignore"),
)


class ChargeRecordDetailSchema(BaseModel):
    deliver_request: Optional[dict] = Field(None, description="发放请求")
    deliver_response: Optional[dict] = Field(None, description="发放结果")
    charge_result: Optional[dict] = Field(None, description="充值结果")
    refund_request: Optional[dict] = Field(None, description="退款请求")
    refund_response: Optional[dict] = Field(None, description="退款结果")
    check_list: list[dict] = Field([], description="检查列表")


class BenefitsProductOrderWithCustomerDTO(BenefitsProductOrderDTO):
    """权益产品订单DTO（包含客户信息）"""

    customer_id: Optional[int] = Field(None, description="客户ID")
    customer_name: str = Field("", description="客户名称")

    @classmethod
    async def from_order_with_customers(
        cls, order: BenefitsProductOrder, customers: Sequence[Any]
    ) -> "BenefitsProductOrderWithCustomerDTO":
        """从订单和客户列表创建DTO"""
        # 先转换为基础DTO
        base_dto = await BenefitsProductOrderDTO.from_tortoise_orm(order)

        # 查找对应的客户
        customer_id = None
        customer_name = ""
        for customer in customers:
            if customer.tenant_id == order.tenant_id:  # type: ignore
                customer_id = customer.id
                customer_name = customer.name
                break

        # 创建扩展DTO
        return cls(**base_dto.model_dump(), customer_id=customer_id, customer_name=customer_name)


class ProductOrderExportStatus(StrEnum):
    """产品订单导出状态"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProductOrderExportRecord(BaseModel):
    """产品订单导出记录"""

    export_id: str = Field(description="导出任务ID")
    user_id: str = Field(description="用户ID")
    user_name: str = Field(description="用户名称")
    status: str = Field(description="导出状态: pending, processing, completed, failed")
    created_at: datetime = Field(description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    file_url: Optional[str] = Field(None, description="文件下载链接")
    error_message: Optional[str] = Field(None, description="错误信息")
    filters: Dict[str, Any] = Field(description="过滤条件")
