# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# domains/delivery/repositories/eleme_orders.py
# created: 2025-05-20 17:24:27
# updated: 2025-06-08 02:03:50

from src.databases.models.delivery import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ElemeOrder
from src.domains.delivery.dto import ElemeAddressDTO, ElemeOrderDTO


class DeliveryElemeOrderRepository:

    @classmethod
    async def get_eleme_order_by_id(cls, order_id: str) -> ElemeOrder | None:
        return await ElemeOrder.filter(order_id=order_id).first()

    @classmethod
    async def create_or_update(cls, order_info: ElemeOrderDTO) -> ElemeOrder:
        address_info_dto = order_info.address_info

        # 从DTO中获取地址数据字典
        raw_address_data_from_dto = address_info_dto.model_dump()

        # 准备 'defaults' 参数给 get_or_create
        # defaults 不应包含查找键 (address_id)
        # 同时移除DTO中可能意外存在的 'id' (主键) 字段
        defaults_for_address_creation = {
            k: v for k, v in raw_address_data_from_dto.items() if v is not None and k != "address_id"
        }
        defaults_for_address_creation.pop("id", None)  # 确保 'id' 不在 defaults 中

        # 用于查找的 address_id
        lookup_address_id = address_info_dto.address_id

        print(f"Attempting get_or_create for ElemeAddress with address_id: '{lookup_address_id}'")
        print(f"Defaults for address creation: {defaults_for_address_creation}")

        address, created = await ElemeAddress.get_or_create(
            address_id=lookup_address_id, defaults=defaults_for_address_creation
        )

        if not created:
            # 地址已存在，使用 defaults_for_address_creation 中的字段更新它
            print(f"ElemeAddress found (id: {address.id}), updating with: {defaults_for_address_creation}")
            await address.update_from_dict(defaults_for_address_creation)
            await address.save()
        else:
            print(f"ElemeAddress created (id: {address.id})")

        # 处理订单数据
        # 从订单信息中排除 address_info DTO，因为它已被处理
        order_data_dict = order_info.model_dump(exclude={"address_info"})

        # 将 ElemeAddress 对象实例赋给订单的外键字段 "address"
        order_data_dict["address"] = address  # 使用外键对象实例

        # 将 SubjectDTO 列表转换为字典列表，用于JSONField
        if order_info.subjects:
            order_data_dict["subjects"] = [s.model_dump() for s in order_info.subjects]
        else:
            order_data_dict["subjects"] = []

        # 确保订单主键 'id' 不包含在创建或更新数据中，因为它应自动生成或已存在
        order_data_dict.pop("id", None)

        existing_order = await cls.get_eleme_order_by_id(order_info.order_id)
        if existing_order:
            # 更新时过滤掉None值
            update_payload = {k: v for k, v in order_data_dict.items() if v is not None}
            # 确保在更新时也不传递 address_id, 而是依赖 address 对象
            update_payload.pop("address_id", None)
            print(f"ElemeOrder found (id: {existing_order.id}), updating with: {update_payload}")
            await existing_order.update_from_dict(update_payload)
            await existing_order.save()
            order_to_return = existing_order
        else:
            # 创建新订单时，同样不传递 address_id
            order_data_dict.pop("address_id", None)
            print(f"Creating new ElemeOrder with data: {order_data_dict}")
            order_to_return = await ElemeOrder.create(**order_data_dict)
            print(f"ElemeOrder created (id: {order_to_return.id})")

        return order_to_return
