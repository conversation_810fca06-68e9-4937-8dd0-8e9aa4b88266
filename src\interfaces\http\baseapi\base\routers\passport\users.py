# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/routers/passport/users.py
# created: 2025-05-27 01:17:31
# updated: 2025-05-27 15:03:47

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from loguru import logger

from src.domains.passport.dto import AuthPassportUserDTO
from src.infrastructures.errors import ApplicationNotMatchError
from src.interfaces.http.baseapi import Container

from ...authorization import base_api_authentication, get_env
from ...schemas import Env
from .schemas import LoginResponse, UserProfilePayload, UserProfileResponse, UserTenantsResponse

if TYPE_CHECKING:
    from src.applications.passport import UserQueryService, UserService
    from src.domains.passport.entities import UserEntity

router = APIRouter(tags=["Passport", "Users"])


@router.put("/users/profiles", name="微信小程序更新用户资料", response_model=UserProfileResponse)
@inject
async def update_wechat_mini_program_profile(
    payload: UserProfilePayload,
    env: Env = Depends(get_env),
    current_user: "UserEntity" = Depends(base_api_authentication),
    user_service: "UserService" = Depends(Provide[Container.applications.passport_user_service]),
):
    updated_user_info = await user_service.update_user_profile(current_user, **payload.model_dump())
    return UserProfileResponse(data=updated_user_info)


@router.get("/users/tenants", name="获取用户租户列表", response_model=UserTenantsResponse)
@inject
async def get_user_tenants(
    current_user: "UserEntity" = Depends(base_api_authentication),
    env: Env = Depends(get_env),
    user_query_service: "UserQueryService" = Depends(Provide[Container.applications.passport_user_query_service]),
):
    tenants = await user_query_service.get_user_tenants(current_user, env.app)
    return UserTenantsResponse(data=tenants)


@router.post("/users/tenants/{tenant_id}/switch", name="切换用户租户", response_model=LoginResponse)
@inject
async def switch_user_tenant(
    tenant_id: str,
    current_user: "UserEntity" = Depends(base_api_authentication),
    env: Env = Depends(get_env),
    user_service: "UserService" = Depends(Provide[Container.applications.passport_user_service]),
):
    user_info = await user_service.switch_user_tenant(current_user, env.app, tenant_id)
    return LoginResponse(data=user_info)
