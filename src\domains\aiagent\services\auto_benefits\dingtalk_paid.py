# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/aiagent/services/auto_benefits/dingtalk_paid.py
# created: 2025-04-18 00:46:21
# updated: 2025-04-22 09:17:24

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from dependency_injector.wiring import Provide, inject
from loguru import logger

from src.databases.models.benefits import BenefitsProductOrder
from src.infrastructures import errors

from .auto_benefits_factor import AutoBenefitStrategy

if TYPE_CHECKING:
    from redis.asyncio import Redis

    from src.domains.benefits.dto import BenefitsProductOrderDTO
    from src.domains.benefits.services import BenefitsProductService
    from src.domains.customer.services import CustomerBenefitService
    from src.repositories.customer import CustomerRepository

OPEN_PLATFORM_APP_ID = "OPEN_PLATFORM"
DINGTALK_CUSTOMER_CODE = "zjTWxm4z"
DINGTALK_TENANT_ID = "q7tkbeY4R0yxt6DV"
PRODUCT_LIST = [
    "PLsfJccNcEwpb",  # 20元无门槛红包
    "PHmRcQBUDyEus",  # 20-6元满减红包, 无商业化
    "PoFtXQNxyHfZK",  # 25-7元满减红包, 无商业化
    "PhKNJspVCQ7f2",  # 30-8元满减红包, 无商业化
    "PDlQRrg80ustw",  # 36-9元满减红包, 无商业化
    "PvdTYeHtzKIww",  # 35-8元满减红包, 无商业化
    "PTHSdqofzAgKV",  # 45-10元满减红包, 无商业化
]


class DingtalkPaidStrategy(AutoBenefitStrategy):

    @inject
    def __init__(
        self,
        redis: "Redis" = Provide["redis"],
        product_service: "BenefitsProductService" = Provide["benefits_product_service"],
        customer_repo: "CustomerRepository" = Provide["customer_repo"],
        customer_benefits_service: "CustomerBenefitService" = Provide["customer_benefit_service"],
    ):
        self.redis = redis
        self.product_service = product_service
        self.customer_repo = customer_repo
        self.customer_benefits_service = customer_benefits_service

    async def _get_charge_product_code(self) -> str:
        """获取充值的产品code

        1. 每天99个免费的20元权益
        2. 每100人发一张20元无门槛券
        3. 其余的从20-6、25-7、30-8、36-9、35-8、45-10随机
        """
        import random
        from datetime import datetime

        # 获取今天已发放的权益数量, 并更新计数器
        today = datetime.now().strftime("%Y-%m-%d")
        daily_count_key = f"dingtalk_ai_agent_new_users_count:{today}"
        daily_count = await self.redis.incr(daily_count_key)

        # 设置每日计数器的过期时间（48小时后过期）
        if daily_count == 0:
            await self.redis.expire(daily_count_key, 60 * 60 * 48)

        # 2. 每100人发一张20元无门槛券
        if (daily_count) % 100 == 0 and daily_count / 100 < 100:
            logger.info(f"每100名用户发放一张20元无门槛红包, 当前总用户数: {daily_count}")
            return PRODUCT_LIST[0]  # 20元无门槛红包

        # 3. 其余用户随机发放满减券
        random_product = random.choice(PRODUCT_LIST[1:])
        logger.info(f"随机发放满减券，选择产品: {random_product}")
        return random_product

    async def _check_user_today_charge_order(
        self, phone: str, union_id: str, throw_error: bool = True
    ) -> Optional[BenefitsProductOrder]:
        """检查用户今日是否已领取权益"""
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        order = await BenefitsProductOrder.filter(
            account=phone,
            app__app_id=OPEN_PLATFORM_APP_ID,
            tenant_id=DINGTALK_TENANT_ID,
            created_at__gte=today,
            created_at__lte=tomorrow,
        ).first()

        if order:
            logger.warning(
                f"用户今日已领取权益: {union_id}, order_id: {order.order_id}, order_status: {order.status}, order_product_code: {order.product_code}."
            )
            if not throw_error:
                return order

            raise errors.AutoBenefitsApplyAlreadyAppliedError
        return None

    async def apply(
        self, phone: str, union_id: str, agent_id: str, agent_user_id: str, uid: str
    ) -> "BenefitsProductOrderDTO":
        # 检查用户今日是否已领取权益
        order = await self._check_user_today_charge_order(phone, union_id)

        if not order:
            product_code = await self._get_charge_product_code()
            product = await self.product_service.get_product_by_code(product_code)
            if not product:
                logger.error("钉钉免费权益发放失败，权益产品不存在, product_code: {product_code}")
                raise errors.BenefitsProductNotFoundError
            logger.info(f"将用户添加到自动领取名单中: {phone}")
            await self.redis.sadd("dingtalk_ai_agent_auto_apply_users", phone)
            await self.redis.hset(
                f"dingtalk_auto_apply_blind:{phone}",
                mapping={
                    "uid": uid,
                    "product_code": product_code,
                    "created_at": str(datetime.now().timestamp()),
                    "union_id": union_id,
                    "agent_id": agent_id,
                    "agent_user_id": agent_user_id,
                },
            )

            # 发放权益
            customer = await self.customer_repo.get_by_code(DINGTALK_CUSTOMER_CODE)
            if not customer:
                logger.warning(f"客户不存在: {DINGTALK_CUSTOMER_CODE}")
                raise errors.CustomerNotFoundError

            order_info = await self.customer_benefits_service.charge_benefit(
                customer=customer,
                product_code=product.code,
                account=phone,
                out_order_id=f"dingtalk_{phone}_{product.code}_{datetime.now().strftime('%Y%m%d')}",
                notify_url="",
                sid=f"{OPEN_PLATFORM_APP_ID}.dingtalk_ai.{union_id}",
            )
            logger.info(
                f"首次订阅自动领取权益成功: {order_info.model_dump()}, 用户: {phone}, product: {product.name}-{product_code}"
            )

            return order_info
        else:
            logger.info("用户今日已领取权益")
            return await BenefitsProductOrderDTO.from_tortoise_orm(order)
