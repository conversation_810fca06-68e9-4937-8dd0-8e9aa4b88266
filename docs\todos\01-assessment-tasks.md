# 第一阶段：系统评估任务

## 任务1：分析现有系统结构

### 执行步骤：

1. 扫描现有目录结构
```bash
find . -type d -name "__pycache__" -prune -o -type d -print | head -50
```

2. 统计各模块文件数量
```bash
find apis -name "*.py" | wc -l
find domains -name "*.py" | wc -l
find apps -name "*.py" | wc -l
find core -name "*.py" | wc -l
```

3. 分析模块依赖关系
```bash
# 检查 domains 是否依赖 apis
grep -r "from apis" domains/ || echo "Good: domains doesn't depend on apis"

# 检查 domains 是否依赖 apps
grep -r "from apps" domains/ || echo "Good: domains doesn't depend on apps"
```

4. 生成现有架构报告
```python
# 创建 scripts/analyze_current_structure.py
import os
from pathlib import Path

def analyze_directory(root_path):
    """分析目录结构和文件分布"""
    stats = {}
    for root, dirs, files in os.walk(root_path):
        # 跳过特定目录
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'venv']]
        
        py_files = [f for f in files if f.endswith('.py')]
        if py_files:
            rel_path = os.path.relpath(root, root_path)
            stats[rel_path] = len(py_files)
    
    return stats

# 分析主要目录
for directory in ['apis', 'domains', 'apps', 'core']:
    if os.path.exists(directory):
        stats = analyze_directory(directory)
        print(f"\n{directory}/:")
        for path, count in sorted(stats.items()):
            print(f"  {path}: {count} files")
```

### 期望输出：
- 现有目录结构图
- 模块间依赖关系图
- 需要迁移的文件清单
- 潜在的问题和风险

## 任务2：识别需要迁移的核心模块

### 执行步骤：

1. 识别活跃的业务模块
```bash
# 查找最近修改的文件
find domains -name "*.py" -mtime -30 | head -20
find apis -name "*.py" -mtime -30 | head -20
```

2. 分析各模块的代码行数
```bash
# 统计代码行数
find domains/benefits -name "*.py" -exec wc -l {} + | tail -1
find domains/passport -name "*.py" -exec wc -l {} + | tail -1
find domains/delivery -name "*.py" -exec wc -l {} + | tail -1
```

3. 检查测试覆盖情况
```bash
# 查看现有测试
find tests -name "*test*.py" | grep -E "(benefits|passport|delivery)" | sort
```

### 期望输出：
- 核心业务模块列表（按优先级排序）
- 每个模块的复杂度评估
- 迁移风险评估

## 任务3：创建迁移前的检查清单

### 执行步骤：

1. 创建依赖关系检查脚本
```python
# scripts/check_dependencies.py
import ast
import os
from collections import defaultdict

def extract_imports(file_path):
    """提取文件中的所有导入"""
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            tree = ast.parse(f.read())
        except:
            return []
    
    imports = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.append(node.module)
    
    return imports

# 分析各层之间的依赖
layer_deps = defaultdict(set)
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            file_path = os.path.join(root, file)
            layer = root.split(os.sep)[1] if len(root.split(os.sep)) > 1 else 'root'
            
            imports = extract_imports(file_path)
            for imp in imports:
                if imp.startswith(('apis', 'domains', 'apps', 'core')):
                    target_layer = imp.split('.')[0]
                    if layer != target_layer:
                        layer_deps[layer].add(target_layer)

print("层级依赖关系:")
for layer, deps in layer_deps.items():
    print(f"{layer} -> {', '.join(deps)}")
```

2. 检查数据库迁移状态
```bash
# 检查 Aerich 迁移
poetry run aerich history

# 检查 Django 迁移（如果有）
ls apps/*/migrations/*.py 2>/dev/null | wc -l
```

### 期望输出：
- 依赖关系矩阵
- 数据库迁移清单
- 配置文件清单
- 外部服务依赖清单

## 任务4：创建差异报告

### 生成内容：

1. **架构差异对比表**
   - 现有架构 vs 目标架构
   - 需要新建的目录
   - 需要迁移的文件
   - 需要重构的模块

2. **工作量评估**
   - 各模块迁移工作量（人天）
   - 技术风险等级
   - 依赖关系复杂度

3. **迁移顺序建议**
   - 基于依赖关系的迁移顺序
   - 并行迁移的可能性
   - 关键路径识别

### 报告模板：
```markdown
# 架构迁移差异报告

## 执行摘要
- 评估日期：[DATE]
- 现有模块数：[COUNT]
- 需迁移文件数：[COUNT]
- 预估工作量：[DAYS]

## 主要发现

### 1. 架构差异
| 层级 | 现有结构 | 目标结构 | 差异说明 |
|------|----------|----------|----------|
| 接口层 | apis/* | src/interfaces/* | 需要重组和分离 |
| 应用层 | 无 | src/applications/* | 需要新建 |
| 领域层 | domains/* | src/domains/* | 路径调整 |
| 数据层 | 分散 | src/repositories/* | 需要统一 |

### 2. 依赖问题
- [ ] domains 依赖 apis（需要解耦）
- [ ] apps 与 domains 循环依赖
- [ ] core 被所有层依赖（正常）

### 3. 迁移建议
1. 先迁移 infra（core -> src/infra）
2. 再迁移 databases 和 repositories
3. 然后迁移 domains（解耦依赖）
4. 最后创建 applications 和 interfaces

## 风险评估
- 高风险：API 兼容性
- 中风险：数据库迁移
- 低风险：基础设施迁移
```

## 验证标准

完成第一阶段后，应该有：
1. ✅ 完整的现有系统结构分析
2. ✅ 清晰的模块依赖关系图
3. ✅ 详细的差异分析报告
4. ✅ 可执行的迁移计划