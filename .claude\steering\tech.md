# Technical Steering Document

## Tech Stack

### Core Framework
- **Python 3.12** with Poetry for dependency management
- **FastAPI** for new API development (preferred)
- **Django** (legacy, being phased out)
- **Tortoise-ORM** for async database operations
- **Beanie** for MongoDB document models

### Databases
- **MySQL** (primary relational database)
- **MongoDB** (document storage)
- **Redis** (caching and session management)

### Message Queue & Tasks
- **RabbitMQ** with aio-pika for async messaging
- **Celery** for background tasks (legacy, optional)
- **APScheduler** for scheduled tasks

### Infrastructure
- **Docker** & Docker Compose for containerization
- **Aliyun services**: OSS (storage), SMS, SLS (logging)
- **Dependency Injector** for IoC container management

### Testing & Quality
- **pytest** with pytest-asyncio for testing
- **Black** (line-length: 120) for code formatting
- **Ruff** for linting
- **mypy** for type checking

## Common Commands

### Development
```bash
# Install dependencies
poetry install

# Run specific service
poetry run python deploys/baseapi/main.py

# Run tests
poetry run pytest

# Code formatting
poetry run black .
poetry run ruff .

# Type checking
poetry run mypy .
```

### Docker Operations
```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d baseapi

# View logs
docker-compose logs -f baseapi
```

### Database
```bash
# Run migrations
poetry run aerich upgrade

# Create new migration
poetry run aerich migrate
```

## Project Conventions

### File Headers
Always include UTF-8 encoding, file path, and creation date:
```python
# encoding: utf-8
# src/domains/passport/services/user.py
# created: 2025-07-20 10:16:27
```

### API Versioning
- New APIs use `/v0` or `/v1` prefix
- Legacy Django APIs are being phased out
- OpenAPI endpoints require authentication

### Dependency Injection
Use dependency-injector containers for service instantiation:
```python
from dependency_injector import containers, providers
```

### Error Handling
Use custom business errors inheriting from base exceptions:
```python
from src.infrastructures.errors import BusinessError
```

### Logging
Use loguru for all logging:
```python
from loguru import logger
```

### Async Patterns
Prefer async/await for all new code:
```python
async def get_user(uid: str) -> UserEntity:
    user = await UserRepository.get_by_uid(uid)
```