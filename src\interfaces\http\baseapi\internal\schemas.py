# encoding: utf-8
# src/interfaces/http/baseapi/internal/schemas.py
# created: 2025-07-23 00:00:00

from typing import Optional, Union

from pydantic import BaseModel, Field

from src.applications.common.dto import ActivityLinksDTO, ShopInfoDTO, ShopLinksDTO
from src.applications.passport.dto import TenantInfoDTO, UserInfoDTO, UserRelationDTO
from src.infrastructures.fastapi.response import BaseResponse


class CreateUserPayload(BaseModel):
    phone: str = Field(..., description="用户手机号")
    uid: str = Field(..., description="用户ID")


class AuthorizeRequestPayload(BaseModel):
    token: str = Field(..., description="jwt token")


class CreateTenantPayload(BaseModel):
    tenant_name: str = Field(..., description="租户名称")


class DeliveryLinkExtPayload(BaseModel):
    extra_params: Optional[dict[str, Union[str, int, float]]] = Field({}, description="额外参数")


AuthorizeResponse = BaseResponse[UserInfoDTO]
TenantResponse = BaseResponse[TenantInfoDTO]
DeliveryLinkResponse = BaseResponse[ActivityLinksDTO]
UserRelationResponse = BaseResponse[UserRelationDTO]
ShopListResponse = BaseResponse[list[ShopInfoDTO]]
ShopLinkResponse = BaseResponse[ShopLinksDTO]
