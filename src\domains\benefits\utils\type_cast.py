# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/utils/type_cast.py
# created: 2025-04-06 11:33:32
# updated: 2025-05-05 00:08:08


from src.domains.benefits.dto import BenefitsSkuCreateFields, BenefitsSkuType, ChargeFuncEnum, PurchaseCreateFields
from src.utils.eleme.dto import (
    CouponPackageDto,
    CouponPackagePurchaseDetailDto,
    CouponPackagePurchaseDto,
)


def eleme_union_coupon_package_to_sku_create_fields(
    coupon_package: CouponPackageDto,
    purchase_detail: CouponPackagePurchaseDetailDto,
    supplier_id: int,
) -> BenefitsSkuCreateFields:
    """将联盟权益采购单转换为联盟权益SKU创建字段"""
    #  create fields:
    # "name", "type", "third_part_code", "supplier_id", "charge_func", "price", "cost", "stock", "detail"
    sku_create_fields = BenefitsSkuCreateFields.model_validate(
        {
            "name": coupon_package.name,
            "type": BenefitsSkuType.ZHICHONG.value,
            "third_part_code": coupon_package.item_id,
            "supplier_id": supplier_id,
            "charge_func": ChargeFuncEnum.ELEME_UNION.value,
            "cost": purchase_detail.purchase_price_cent,
            "stock": purchase_detail.remain_stock,
            "detail": coupon_package.model_dump(),
        }
    )
    return sku_create_fields


def eleme_union_purchase_to_purchase_create_fields(
    purchase: CouponPackagePurchaseDto, purchase_detail: CouponPackagePurchaseDetailDto, sku_id: int
) -> PurchaseCreateFields:
    """将联盟权益采购单转换为采购单创建字段"""
    # "purchase_id", "purchase_name", "purchase_stock", "remain_stock", "unit_price", "total_price", "sales_start_time", "sales_end_time"
    purchase_fields = PurchaseCreateFields.model_validate(
        {
            "purchase_id": purchase.purchase_id,
            "purchase_name": purchase.purchase_name,
            "purchase_stock": purchase_detail.purchase_stock,
            "remain_stock": purchase_detail.remain_stock,
            "unit_price": purchase_detail.purchase_price_cent,
            "total_price": purchase_detail.purchase_price_cent * purchase_detail.purchase_stock,
            "sales_start_time": purchase_detail.sales_start_time,
            "sales_end_time": purchase_detail.sales_end_time,
            "sku_id": sku_id,
            "detail": {
                **purchase.model_dump(),
                **purchase_detail.model_dump(),
            },
        }
    )
    return purchase_fields
