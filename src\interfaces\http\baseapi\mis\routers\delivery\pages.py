# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/delivery/pages.py
# created: 2025-02-06 12:34:16
# updated: 2025-05-26 11:02:45

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

from src.domains.delivery.services import DeliveryPageService
from src.infrastructures.fastapi.response import BaseResponse
from src.interfaces.http.baseapi import Container
from src.repositories.delivery.users import DeliveryUserRepository

# from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway  # Commented out usage
from .schemas import (
    DeliveryPageListData,
    DeliveryPageListResponse,
    DeliveryPagePayload,
    DeliveryPageResponse,
    DeliveryPageUrlPayload,
    ElemePagesUrlData,
    ElemePagesUrlResponse,
)

router = APIRouter(tags=["delivery", "delivery.pages"])


# @router.get("/tools/eleme/pages/url")
# async def get_eleme_pages_url(
#     active_id: str = Query(..., description="活动ID"),
#     pid: str = Query(..., description="页面ID"),
#     sid: str = Query(..., description="详情ID"),
# ):
#     act = await UnionDeliveryUtils.get_union_activity_info(str(active_id), pid, sid)
#     act_wx_link = await UnionService.get_union_wechat_schema(str(active_id), pid, sid)
#     link = act.get("link", {})
#     result = {
#         "alipay_link": link.get("alipay_mini_url", None),
#         "h5_link": link.get("h5_url", None),
#         "h5_short_link": link.get("h5_short_url", None),
#         "taobao_link": link.get("tb_scheme_url", None),
#         "wechat_promotion": link.get("wx_promotion", None),
#         "wechat_link": act_wx_link,
#     }
#     return ElemePagesUrlResponse(data=ElemePagesUrlData.model_validate(result))


@router.get("/pages", response_model=DeliveryPageListResponse)
async def get_delivery_pages():
    pages = await DeliveryPageService.get_pages()
    return DeliveryPageListResponse(data=DeliveryPageListData(total=len(pages), data=pages))


@router.get("/pages/{page_code}", response_model=DeliveryPageResponse)
async def get_delivery_page(page_code: str):
    page = await DeliveryPageService.get_page_by_code(page_code)
    return DeliveryPageResponse(data=page)


@router.post("/pages", response_model=DeliveryPageResponse)
async def create_delivery_page(payload: DeliveryPagePayload):
    page = await DeliveryPageService.create_page(**payload.model_dump())
    return DeliveryPageResponse(data=page)


@router.put("/pages/{page_id}", response_model=DeliveryPageResponse)
async def update_delivery_page(page_id: int, payload: DeliveryPagePayload):
    page = await DeliveryPageService.update_page(page_id, payload.model_dump())
    return DeliveryPageResponse(data=page)


@router.delete("/pages/{page_code}", response_model=BaseResponse)
async def delete_delivery_page(page_code: str):
    await DeliveryPageService.delete_page(page_code)
    return BaseResponse(data="success")


@router.post("/pages/url", response_model=BaseResponse)
@inject
async def get_delivery_page_url(
    payload: DeliveryPageUrlPayload,
    user_repo: "DeliveryUserRepository" = Depends(Provide[Container.domains.delivery_user_service]),
):
    result = await user_repo.get_user_by_phone_with_address(phone=payload.mobile)
    if result is None:
        return BaseResponse(data="user not found")

    user, address = result
    if not user or not address:
        return BaseResponse(data="user not found")

    url = await DeliveryPageService.get_access_url(
        page_code=payload.page_code,
        mobile=payload.mobile,
        user_open_id=payload.mobile,
        latitude=float(address.receive_lat),
        longitude=float(address.receive_lng),
        eleme_channel="haili",
        extra_info={"tag": "growth_hacker_test"},
    )
    return BaseResponse(data=url)
