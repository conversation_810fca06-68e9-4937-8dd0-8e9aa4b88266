# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/13 10:23

import base64

from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding

MAX_ENCRYPT_BLOCK = 117
MAX_DECRYPT_BLOCK = 256
SIGNATURE_ALGORITHM = "SHA512"


class RSAUtil:
    @staticmethod
    def get_private_key(private_key_base64: str):
        private_key_pem = f"-----BEGIN PRIVATE KEY-----\n{private_key_base64}\n-----END PRIVATE KEY-----"
        private_key = serialization.load_pem_private_key(private_key_pem.encode(), password=None)
        return private_key

    @staticmethod
    def get_public_key(public_key_base64: str):
        public_key_pem = f"-----BEGIN PUBLIC KEY-----\n{public_key_base64}\n-----<PERSON><PERSON> PUBLIC KEY-----"
        public_key = serialization.load_pem_public_key(public_key_pem.encode())
        return public_key

    # 加签
    @staticmethod
    def sign(data: str, private_key_base64: str) -> str:
        private_key = RSAUtil.get_private_key(private_key_base64)
        signature = private_key.sign(data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA512())  # type: ignore
        return base64.b64encode(signature).decode("utf-8")

    # 验签
    @staticmethod
    def verify(data: str, public_key_base64: str, signature: str) -> bool:
        public_key = RSAUtil.get_public_key(public_key_base64)
        try:
            public_key.verify(base64.b64decode(signature), data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA512())  # type: ignore
            return True
        except Exception:
            return False

    # 加密
    @staticmethod
    def encrypt(data: str, public_key_base64: str) -> str:
        public_key = RSAUtil.get_public_key(public_key_base64)
        data_bytes = data.encode("utf-8")
        encrypted_chunks = []

        for i in range(0, len(data_bytes), MAX_ENCRYPT_BLOCK):
            chunk = data_bytes[i : i + MAX_ENCRYPT_BLOCK]
            encrypted_chunk = public_key.encrypt(chunk, padding.PKCS1v15())  # type: ignore
            encrypted_chunks.append(encrypted_chunk)

        encrypted_data = b"".join(encrypted_chunks)
        return base64.b64encode(encrypted_data).decode("utf-8")

    # 解密
    @staticmethod
    def decrypt(encrypted_data_base64: str, private_key_base64: str) -> str:
        private_key = RSAUtil.get_private_key(private_key_base64)
        encrypted_data = base64.b64decode(encrypted_data_base64)
        decrypted_chunks = []

        for i in range(0, len(encrypted_data), MAX_DECRYPT_BLOCK):
            chunk = encrypted_data[i : i + MAX_DECRYPT_BLOCK]
            decrypted_chunk = private_key.decrypt(chunk, padding.PKCS1v15())  # type: ignore
            decrypted_chunks.append(decrypted_chunk)

        decrypted_data = b"".join(decrypted_chunks)
        return decrypted_data.decode("utf-8")

    # 工具方法：将字典转换为查询字符串
    @staticmethod
    def to_query_string(params: dict) -> str:
        sorted_params = sorted(params.items())
        query_string = "&".join(f"{key}={value}" for key, value in sorted_params)
        return query_string
