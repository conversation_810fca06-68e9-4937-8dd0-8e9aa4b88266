from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionPromotionLinkAnalyzeRequest(BaseRequest):

    def __init__(self, promotion_link_analyze_request: object = None):
        """
        查询request对象
        """
        self._promotion_link_analyze_request = promotion_link_analyze_request

    @property
    def promotion_link_analyze_request(self):
        return self._promotion_link_analyze_request

    @promotion_link_analyze_request.setter
    def promotion_link_analyze_request(self, promotion_link_analyze_request):
        if isinstance(promotion_link_analyze_request, object):
            self._promotion_link_analyze_request = promotion_link_analyze_request
        else:
            raise TypeError("promotion_link_analyze_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.promotion.link.analyze"

    def to_dict(self):
        request_dict = {}
        if self._promotion_link_analyze_request is not None:
            request_dict["promotion_link_analyze_request"] = convert_struct(self._promotion_link_analyze_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionPromotionLinkAnalyzePromotionLinkAnalyzeRequest:
    def __init__(self, type: int = None, link: str = None):
        """
        链接类型（1-h5；2-h5短链；3-微信小程序；4-饿了么APP；5-淘口令）
        """
        self.type = type
        """
            推广链接或者口令
        """
        self.link = link
