# Implementation Plan

## Test Infrastructure Setup

- [x] 1. Create test directory structure and base test classes
  - Create tests/unit/applications/passport/__init__.py
  - Create tests/integration/applications/passport/__init__.py
  - Implement BasePassportUnitTest class with common fixtures
  - Set up mock factories for User, App, and Tenant entities
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 2. Implement test data fixtures and factories
  - [x] 2.1 Create UserFactory for generating test user entities
    - Implement create_user method with customizable parameters
    - Add methods for creating users with different authentication types (phone, wechat, dingtalk)
    - Write unit tests for the factory methods
    - _Requirements: 4.1, 4.2, 4.4_
  
  - [x] 2.2 Create AppFactory for generating test app entities
    - Implement create_app method with all required fields
    - Add methods for creating apps with different configurations
    - Include test app secrets and third-party app IDs
    - _Requirements: 4.1, 4.2_
  
  - [x] 2.3 Create TenantFactory for generating test tenant entities
    - Implement create_tenant method
    - Add methods for active/inactive tenant states
    - _Requirements: 4.1, 4.2_

## Unit Tests Implementation

- [x] 3. Implement LoginService unit tests
  - [x] 3.1 Create test_login_service.py with SMS login tests
    - Test successful SMS login flow
    - Test SMS code validation (correct and incorrect codes)
    - Test SMS rate limiting scenarios
    - Test phone number validation
    - Mock Redis and SMS gateway interactions
    - _Requirements: 1.1, 1.3, 1.4, 1.5_
  
  - [x] 3.2 Add DingTalk login tests
    - Test successful DingTalk authentication
    - Test DingTalk API failures
    - Test user creation for new DingTalk users
    - Mock DingTalk gateway responses
    - _Requirements: 1.1, 1.3, 1.4_
  
  - [x] 3.3 Add WeChat mini-program login tests
    - Test successful WeChat login with phone
    - Test WeChat login without phone (should fail)
    - Test WeChat API error handling
    - Mock WeChat gateway responses
    - _Requirements: 1.1, 1.3, 1.4_

- [x] 4. Implement UserService unit tests
  - [x] 4.1 Create test_user_service.py with user management tests
    - Test user profile update functionality
    - Test user tenant switching
    - Test user relation creation and deletion
    - Mock repository methods for all operations
    - _Requirements: 1.2, 1.3, 1.4_
  
  - [x] 4.2 Add user creation tests
    - Test phone user creation
    - Test WeChat user creation
    - Test DingTalk user creation
    - Test duplicate user handling
    - _Requirements: 1.2, 1.3, 1.4_

- [x] 5. Implement Query Service unit tests
  - [x] 5.1 Create test_user_query_service.py
    - Test get_by_uid method
    - Test get_by_phone method
    - Test get_user_relations method
    - Mock repository queries
    - _Requirements: 1.2, 1.4, 1.5_
  
  - [x] 5.2 Create test_app_query_service.py
    - Test get_by_appid method
    - Test list_apps method
    - Test app validation methods
    - _Requirements: 1.2, 1.4, 1.5_
  
  - [x] 5.3 Create test_tenant_query_service.py
    - Test get_by_tenant_id method
    - Test list_tenants method
    - Test tenant status checks
    - _Requirements: 1.2, 1.4, 1.5_

## Integration Tests Implementation

- [ ] 6. Implement login endpoint integration tests
  - [ ] 6.1 Create test_login_endpoints.py with SMS login tests
    - Test POST /api/passport/login/sms/send endpoint
    - Test POST /api/passport/login/sms endpoint
    - Verify response status codes and body structure
    - Test authentication header generation
    - _Requirements: 2.1, 2.2, 2.3, 2.4_
  
  - [ ] 6.2 Add third-party login endpoint tests
    - Test POST /api/passport/login/dingtalk endpoint
    - Test POST /api/passport/login/wechat-mp endpoint
    - Test error responses for invalid auth codes
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 7. Implement user endpoint integration tests
  - [ ] 7.1 Create test_user_endpoints.py with profile tests
    - Test GET /api/passport/users/me endpoint
    - Test PUT /api/passport/users/me endpoint
    - Test authenticated vs unauthenticated requests
    - Verify data persistence after updates
    - _Requirements: 2.1, 2.2, 2.5_
  
  - [ ] 7.2 Add user relation endpoint tests
    - Test POST /api/passport/users/relations endpoint
    - Test DELETE /api/passport/users/relations endpoint
    - Test tenant switching functionality
    - _Requirements: 2.1, 2.2, 2.5_

- [ ] 8. Implement application endpoint integration tests
  - Create test_application_endpoints.py
  - Test GET /api/passport/applications endpoint
  - Test GET /api/passport/applications/{app_id} endpoint
  - Test authorization checks for app access
  - _Requirements: 2.1, 2.2, 2.3_

## Test Coverage and Documentation

- [ ] 9. Achieve coverage targets and add documentation
  - [ ] 9.1 Run coverage analysis and fill gaps
    - Execute pytest with coverage for passport module
    - Identify uncovered code paths
    - Add tests for uncovered scenarios
    - Ensure 80%+ coverage for all service files
    - _Requirements: 5.1, 5.2, 5.3, 5.4_
  
  - [ ] 9.2 Add comprehensive test documentation
    - Write module-level docstrings for all test files
    - Document complex test scenarios
    - Add inline comments for test data setup
    - Create README for passport test suite
    - _Requirements: 6.1, 6.2, 6.3, 6.4_

## CI/CD Integration

- [ ] 10. Configure tests for CI/CD pipeline
  - Add passport test commands to CI configuration
  - Ensure tests run without user interaction
  - Configure test result reporting
  - Set up coverage threshold checks
  - _Requirements: 7.1, 7.2, 7.3, 7.4_