# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# domains/passport/dto.py
# created: 2024-11-30 23:51:05
# updated: 2025-05-27 15:55:03

from typing import Optional

from pydantic import AliasChoices, BaseModel, Field
from tortoise.contrib.pydantic import pydantic_model_creator

from src.databases.models.passport import (
    PassportApp,
    PassportTenant,
    PassportThirdUser,
    PassportUser,
    PassportUserRelations,
)


class AuthUserInfoDTO(BaseModel):
    app_id: str = Field(..., description="应用ID", validation_alias=AliasChoices("app_id", "appid"))
    app_name: str = Field(..., description="应用名称")
    tenant_id: Optional[str] = Field(None, description="租户ID")
    tenant_name: str = Field(..., description="租户名称")
    phone: Optional[str] = Field(None, description="用户手机号")
    uid: str = Field(..., description="用户ID")
    nickname: str = Field(..., description="用户昵称")
    avatar: Optional[str] = Field(None, description="用户头像")


class DingtalkUserInfoDTO(BaseModel):
    union_id: Optional[str] = Field(None, description="钉钉用户UnionID")
    open_id: Optional[str] = Field(None, description="钉钉用户OpenID")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="用户头像")
    mobile: Optional[str] = Field(None, description="用户手机号")
    email: Optional[str] = Field(None, description="用户邮箱")
    state_code: Optional[str] = Field(None, description="用户区号")


class WechatUserInfoDTO(BaseModel):
    open_id: Optional[str] = Field(None, description="微信用户OpenID")
    union_id: Optional[str] = Field(None, description="微信用户UnionID")
    phone: str = Field(..., description="用户手机号")
    country_code: Optional[int] = Field(None, description="国家代码")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="用户头像")


class LoginResultDTO(BaseModel):
    token: str = Field(..., description="登录Token")


class UserInfoDTO(BaseModel):
    uid: str = Field(..., description="用户ID")
    phone: str = Field(..., description="用户手机号")
    nickname: str = Field(..., description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="用户头像")
    email: Optional[str] = Field(None, description="用户邮箱")

    dingtalk_profile: Optional[DingtalkUserInfoDTO] = Field(None, description="钉钉用户信息")

    class Config:
        from_attributes = True


class PassportTenantDTO(BaseModel):
    tenant_id: str = Field(..., description="租户ID")
    tenant_name: str = Field(..., description="租户名称")
    app_id: str = Field(..., description="应用ID")
    app_name: str = Field(..., description="应用名称")


# PassportTenantDTO = pydantic_model_creator(PassportTenant, exclude=("app", "customers", "passport_user_relationss"))
PassportUserRelationsDTO = pydantic_model_creator(PassportUserRelations)
PassportAppDTO = pydantic_model_creator(
    PassportApp,
    exclude=(
        "passport_tenantss",
        "customers",
        "passport_user_relationss",
    ),
)
PassportUserDTO = pydantic_model_creator(
    PassportUser,
    exclude=(
        "passport_user_relationss",
        "password",
        "passport_third_userss",
    ),
)
ThirdUserDTO = pydantic_model_creator(PassportThirdUser, exclude=("passport_user",))


class AuthPassportUserDTO(PassportUserDTO):
    app_id: Optional[str] = Field(None, description="应用ID")
    token: Optional[str] = Field(None, description="用户Token")


class AlipayDecryptContentDTO(BaseModel):
    code: str
    msg: str
    mobile: str
