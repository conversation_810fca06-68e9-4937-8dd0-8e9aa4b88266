# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/repositories/tenants.py
# created: 2025-03-24 22:24:45
# updated: 2025-03-24 22:25:55

from typing import TYPE_CHECKING, Optional

from src.databases.models.passport import PassportTenant

if TYPE_CHECKING:
    from src.domains.passport.entities import TenantEntity


class TenantRepository:

    @classmethod
    async def get_by_id(cls, id: int) -> Optional[PassportTenant]:
        return await PassportTenant.get_or_none(id=id)

    @classmethod
    async def get_by_tenant_id(cls, tenant_id: str) -> Optional[PassportTenant]:
        return await PassportTenant.get_or_none(tenant_id=tenant_id)

    @classmethod
    async def gets_by_appid(cls, app_id: str) -> list[PassportTenant]:
        return await PassportTenant.filter(app__app_id=app_id).all()

    async def save(self, tenant_entity: "TenantEntity") -> None:
        await PassportTenant.update_or_create(
            tenant_id=tenant_entity.tenant_id,
            defaults={
                "name": tenant_entity.tenant_name,
                # app_id 是外键的数值主键，需写入应用的数值 ID，而不是字符串 app_id（如 "OPEN_PLATFORM"）
                "app_id": tenant_entity.app.id,
            },
        )
