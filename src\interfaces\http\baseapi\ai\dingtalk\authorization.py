# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/ai/dingtalk/authorization.py
# created: 2024-12-12 01:37:54
# updated: 2025-04-18 02:25:16


from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from loguru import logger

from src.databases.models.passport import PassportUser
from src.domains.passport.services import AuthorizationService


async def base_api_authentication(token: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> PassportUser:
    from src.domains.passport.services import UserService

    try:
        auth_user_info = await AuthorizationService.decode_base_auth_token(token.credentials)
        user = await PassportUser.get_or_none(uid=auth_user_info.uid)
        if not user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization failed.")
        return user
    except Exception as exp:
        logger.warning(f"Base api authentication error: {exp}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization failed.") from exp


async def agent_api_authentication(token: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> str:
    """
    仅验证 Bearer token 的存在性，返回 token 字符串
    """
    return token.credentials
