from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeStorepromotionReviewbwcStockReleaseRequest(BaseRequest):

    def __init__(self, lock_id: int = None):
        """
        库存锁ID
        """
        self._lock_id = lock_id

    @property
    def lock_id(self):
        return self._lock_id

    @lock_id.setter
    def lock_id(self, lock_id):
        if isinstance(lock_id, int):
            self._lock_id = lock_id
        else:
            raise TypeError("lock_id must be int")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.storepromotion.reviewbwc.stock.release"

    def to_dict(self):
        request_dict = {}
        if self._lock_id is not None:
            request_dict["lock_id"] = convert_basic(self._lock_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
