# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/customer/repositories/benefits.py
# created: 2025-03-10 03:04:38
# updated: 2025-04-06 22:24:27

from typing import Optional, Sequence, Tuple

from pydantic import BaseModel, Field

from src.databases.models.customer import CustomerBenefits
from src.domains.customer.dto import CustomerBenefitsCreateDTO, CustomerBenefitsUpdateDTO


class BenefitsFilters(BaseModel):
    product_name: Optional[str] = Field(None, description="产品名称")
    product_code: Optional[str] = Field(None, description="产品编码")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页数量")


class BenefitsRepository:

    @classmethod
    async def create_customer_benefit(cls, benefit: CustomerBenefitsCreateDTO) -> CustomerBenefits:
        return await CustomerBenefits.create(**benefit.model_dump())

    @classmethod
    async def delete_customer_benefit_by_customer_and_product_code(cls, customer_id: int, product_code: str) -> None:
        benefit = await CustomerBenefits.filter(customer_id=customer_id, product__code=product_code).first()
        if benefit:
            await benefit.delete()

    @classmethod
    async def gets_all_by_customer(
        cls, customer_id: Optional[int] = None, customer_code: Optional[str] = None
    ) -> Sequence[CustomerBenefits]:
        query = CustomerBenefits.filter()
        if customer_id:
            query = query.filter(customer_id=customer_id)
        if customer_code:
            query = query.filter(customer__code=customer_code)
        return await query.prefetch_related("product").all()

    @classmethod
    async def gets_by_customer_id(
        cls, customer_id: int, filters: BenefitsFilters
    ) -> Tuple[int, Sequence[CustomerBenefits]]:
        query = CustomerBenefits.filter(customer_id=customer_id)
        if filters.product_name:
            query = query.filter(product__name__icontains=filters.product_name)
        if filters.product_code:
            query = query.filter(product__code__icontains=filters.product_code)
        total = await query.count()
        benefits = await query.offset((filters.page - 1) * filters.page_size).limit(filters.page_size).all()
        return total, benefits

    @classmethod
    async def get_by_customer_and_product_code(cls, customer_id: int, product_code: str) -> CustomerBenefits | None:
        return await CustomerBenefits.get_or_none(customer_id=customer_id, product__code=product_code).prefetch_related(
            "product"
        )

    @classmethod
    async def get_by_customer_and_product_for_update(
        cls,
        customer_id: int,
        product_code: str,
    ) -> CustomerBenefits | None:
        return (
            await CustomerBenefits.filter(
                customer_id=customer_id,
                product__code=product_code,
            )
            .select_for_update()
            .first()
        )

    @classmethod
    async def update_customer_benefit_by_customer_and_product_code(
        cls, customer_id: int, product_code: str, update_fileds: CustomerBenefitsUpdateDTO
    ) -> CustomerBenefits | None:
        benefit = await CustomerBenefits.get_or_none(customer_id=customer_id, product__code=product_code)
        if not benefit:
            return None
        for field, value in update_fileds.model_dump().items():
            if value is not None:
                setattr(benefit, field, value)
        await benefit.save()
        return benefit

    @classmethod
    async def update_customer_balance(cls, customer_id: int, new_balance: int) -> None:
        """更新客户余额"""
        from src.databases.models.customer import Customer

        await Customer.filter(id=customer_id).update(balance=new_balance)

    @classmethod
    async def update_benefit_stock(cls, benefit_id: int, new_stock: int) -> None:
        """更新权益产品库存"""
        await CustomerBenefits.filter(id=benefit_id).update(stock=new_stock)
