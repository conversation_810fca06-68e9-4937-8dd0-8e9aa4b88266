# 第四阶段：应用层创建任务

## 任务1：创建应用层基础架构

### 1.1 定义CQRS基础类
```python
# src/applications/base.py
"""应用层基础类 - CQRS模式"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from uuid import uuid4

TCommand = TypeVar('TCommand')
TQuery = TypeVar('TQuery')
TResult = TypeVar('TResult')

@dataclass
class Command(ABC):
    """命令基类"""
    command_id: str = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.command_id is None:
            self.command_id = str(uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class Query(ABC):
    """查询基类"""
    query_id: str = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.query_id is None:
            self.query_id = str(uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

class ICommandHandler(ABC, Generic[TCommand, TResult]):
    """命令处理器接口"""
    
    @abstractmethod
    async def handle(self, command: TCommand) -> TResult:
        """处理命令"""
        pass

class IQueryHandler(ABC, Generic[TQuery, TResult]):
    """查询处理器接口"""
    
    @abstractmethod
    async def handle(self, query: TQuery) -> TResult:
        """处理查询"""
        pass

class IEventHandler(ABC):
    """事件处理器接口"""
    
    @abstractmethod
    async def handle(self, event: Any) -> None:
        """处理事件"""
        pass

class CommandBus:
    """命令总线"""
    
    def __init__(self):
        self._handlers: dict[type, ICommandHandler] = {}
    
    def register(self, command_type: type, handler: ICommandHandler):
        """注册命令处理器"""
        self._handlers[command_type] = handler
    
    async def dispatch(self, command: Command) -> Any:
        """分发命令"""
        handler = self._handlers.get(type(command))
        if not handler:
            raise ValueError(f"No handler registered for {type(command).__name__}")
        return await handler.handle(command)

class QueryBus:
    """查询总线"""
    
    def __init__(self):
        self._handlers: dict[type, IQueryHandler] = {}
    
    def register(self, query_type: type, handler: IQueryHandler):
        """注册查询处理器"""
        self._handlers[query_type] = handler
    
    async def dispatch(self, query: Query) -> Any:
        """分发查询"""
        handler = self._handlers.get(type(query))
        if not handler:
            raise ValueError(f"No handler registered for {type(query).__name__}")
        return await handler.handle(query)

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._handlers: dict[str, list[IEventHandler]] = {}
    
    def subscribe(self, event_name: str, handler: IEventHandler):
        """订阅事件"""
        if event_name not in self._handlers:
            self._handlers[event_name] = []
        self._handlers[event_name].append(handler)
    
    async def publish(self, event: Any):
        """发布事件"""
        event_name = getattr(event, 'event_name', type(event).__name__)
        handlers = self._handlers.get(event_name, [])
        
        for handler in handlers:
            await handler.handle(event)
```

### 1.2 创建应用服务基类
```python
# src/applications/service.py
"""应用服务基类"""
from abc import ABC
from typing import Optional
from src.infra.logger.interface import ILogger

class ApplicationService(ABC):
    """应用服务基类"""
    
    def __init__(self, logger: Optional[ILogger] = None):
        self.logger = logger
    
    async def begin_transaction(self):
        """开始事务"""
        pass
    
    async def commit_transaction(self):
        """提交事务"""
        pass
    
    async def rollback_transaction(self):
        """回滚事务"""
        pass
    
    async def execute_in_transaction(self, func, *args, **kwargs):
        """在事务中执行"""
        try:
            await self.begin_transaction()
            result = await func(*args, **kwargs)
            await self.commit_transaction()
            return result
        except Exception as e:
            await self.rollback_transaction()
            raise

class IUnitOfWork(ABC):
    """工作单元接口"""
    
    @abstractmethod
    async def __aenter__(self):
        """进入上下文"""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        pass
    
    @abstractmethod
    async def commit(self):
        """提交"""
        pass
    
    @abstractmethod
    async def rollback(self):
        """回滚"""
        pass
```

## 任务2：创建权益应用层

### 2.1 定义命令
```python
# src/applications/benefits/commands/sku_commands.py
"""SKU相关命令"""
from dataclasses import dataclass
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
from src.applications.base import Command

@dataclass
class CreateSkuCommand(Command):
    """创建SKU命令"""
    name: str
    code: str
    category: str
    price: Decimal
    cost: Decimal
    initial_stock: int = 0
    min_purchase: int = 1
    max_purchase: Optional[int] = None
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None

@dataclass
class UpdateSkuPriceCommand(Command):
    """更新SKU价格命令"""
    sku_id: int
    new_price: Decimal
    reason: str

@dataclass
class AdjustSkuStockCommand(Command):
    """调整SKU库存命令"""
    sku_id: int
    quantity: int
    adjustment_type: str  # 'increase', 'decrease'
    reason: str

@dataclass
class ReserveSkuStockCommand(Command):
    """预留SKU库存命令"""
    sku_id: int
    quantity: int
    order_id: str
    customer_id: str

@dataclass
class ReleaseSkuStockCommand(Command):
    """释放SKU库存命令"""
    sku_id: int
    quantity: int
    order_id: str
    reason: str

@dataclass
class BatchUpdateSkuPricesCommand(Command):
    """批量更新SKU价格命令"""
    sku_ids: List[int]
    adjustment_value: Decimal
    adjustment_type: str  # 'percentage', 'fixed'
    reason: str
```

### 2.2 定义查询
```python
# src/applications/benefits/queries/sku_queries.py
"""SKU相关查询"""
from dataclasses import dataclass
from typing import Optional, List
from decimal import Decimal
from src.applications.base import Query

@dataclass
class GetSkuByIdQuery(Query):
    """根据ID获取SKU查询"""
    sku_id: int

@dataclass
class GetSkuByCodeQuery(Query):
    """根据编码获取SKU查询"""
    code: str

@dataclass
class SearchSkusQuery(Query):
    """搜索SKU查询"""
    keyword: Optional[str] = None
    category: Optional[str] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    in_stock_only: bool = False
    active_only: bool = True
    page: int = 1
    page_size: int = 20

@dataclass
class GetSkuStockInfoQuery(Query):
    """获取SKU库存信息查询"""
    sku_ids: List[int]

@dataclass
class GetSkuPriceHistoryQuery(Query):
    """获取SKU价格历史查询"""
    sku_id: int
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
```

### 2.3 实现命令处理器
```python
# src/applications/benefits/handlers/sku_command_handlers.py
"""SKU命令处理器"""
from typing import Optional
from src.applications.base import ICommandHandler
from src.applications.benefits.commands.sku_commands import *
from src.domains.benefits.entities.sku import Sku
from src.domains.benefits.services.sku_service import SkuDomainService
from src.domains.benefits.events import SkuCreated, SkuPriceChanged, SkuStockChanged
from src.repositories.benefits.sku_repository import ISkuRepository
from src.applications.base import EventBus

class CreateSkuCommandHandler(ICommandHandler[CreateSkuCommand, Sku]):
    """创建SKU命令处理器"""
    
    def __init__(
        self,
        sku_repository: ISkuRepository,
        sku_service: SkuDomainService,
        event_bus: EventBus
    ):
        self.sku_repository = sku_repository
        self.sku_service = sku_service
        self.event_bus = event_bus
    
    async def handle(self, command: CreateSkuCommand) -> Sku:
        """处理创建SKU命令"""
        # 检查SKU编码是否已存在
        existing = await self.sku_repository.get_by_code(command.code)
        if existing:
            raise ValueError(f"SKU with code {command.code} already exists")
        
        # 创建SKU实体
        sku = Sku(
            id=0,  # 将由仓储层分配
            name=command.name,
            code=command.code,
            category=command.category,
            price=command.price,
            cost=command.cost,
            stock=command.initial_stock,
            min_purchase=command.min_purchase,
            max_purchase=command.max_purchase,
            valid_from=command.valid_from,
            valid_to=command.valid_to
        )
        
        # 保存到仓储
        saved_sku = await self.sku_repository.save(sku)
        
        # 发布事件
        event = SkuCreated(
            sku_id=saved_sku.id,
            name=saved_sku.name,
            code=saved_sku.code,
            category=saved_sku.category,
            price=saved_sku.price
        )
        await self.event_bus.publish(event)
        
        return saved_sku

class UpdateSkuPriceCommandHandler(ICommandHandler[UpdateSkuPriceCommand, Sku]):
    """更新SKU价格命令处理器"""
    
    def __init__(
        self,
        sku_repository: ISkuRepository,
        event_bus: EventBus
    ):
        self.sku_repository = sku_repository
        self.event_bus = event_bus
    
    async def handle(self, command: UpdateSkuPriceCommand) -> Sku:
        """处理更新SKU价格命令"""
        # 获取SKU
        sku = await self.sku_repository.get_by_id(command.sku_id)
        if not sku:
            raise ValueError(f"SKU not found: {command.sku_id}")
        
        # 记录原价格
        previous_price = sku.price
        
        # 更新价格
        if command.new_price < sku.cost:
            raise ValueError(f"Price cannot be lower than cost: {command.new_price} < {sku.cost}")
        
        sku.price = command.new_price
        
        # 保存
        updated_sku = await self.sku_repository.save(sku)
        
        # 发布事件
        event = SkuPriceChanged(
            sku_id=updated_sku.id,
            previous_price=previous_price,
            current_price=updated_sku.price
        )
        await self.event_bus.publish(event)
        
        return updated_sku

class ReserveSkuStockCommandHandler(ICommandHandler[ReserveSkuStockCommand, None]):
    """预留SKU库存命令处理器"""
    
    def __init__(
        self,
        sku_repository: ISkuRepository,
        event_bus: EventBus
    ):
        self.sku_repository = sku_repository
        self.event_bus = event_bus
    
    async def handle(self, command: ReserveSkuStockCommand) -> None:
        """处理预留库存命令"""
        # 获取SKU
        sku = await self.sku_repository.get_by_id(command.sku_id)
        if not sku:
            raise ValueError(f"SKU not found: {command.sku_id}")
        
        # 记录原库存
        previous_stock = sku.stock
        previous_reserved = sku.reserved
        
        # 预留库存
        sku.reserve_stock(command.quantity)
        
        # 保存
        await self.sku_repository.save(sku)
        
        # 发布事件
        event = SkuStockChanged(
            sku_id=sku.id,
            previous_stock=previous_stock,
            current_stock=sku.stock,
            change_type='reserve',
            change_quantity=command.quantity
        )
        await self.event_bus.publish(event)
```

### 2.4 实现查询处理器
```python
# src/applications/benefits/handlers/sku_query_handlers.py
"""SKU查询处理器"""
from typing import List, Optional
from src.applications.base import IQueryHandler
from src.applications.benefits.queries.sku_queries import *
from src.domains.benefits.entities.sku import Sku
from src.repositories.benefits.sku_repository import ISkuRepository

class GetSkuByIdQueryHandler(IQueryHandler[GetSkuByIdQuery, Optional[Sku]]):
    """根据ID获取SKU查询处理器"""
    
    def __init__(self, sku_repository: ISkuRepository):
        self.sku_repository = sku_repository
    
    async def handle(self, query: GetSkuByIdQuery) -> Optional[Sku]:
        """处理查询"""
        return await self.sku_repository.get_by_id(query.sku_id)

class SearchSkusQueryHandler(IQueryHandler[SearchSkusQuery, List[Sku]]):
    """搜索SKU查询处理器"""
    
    def __init__(self, sku_repository: ISkuRepository):
        self.sku_repository = sku_repository
    
    async def handle(self, query: SearchSkusQuery) -> List[Sku]:
        """处理查询"""
        # 构建查询条件
        filters = {}
        
        if query.keyword:
            filters['keyword'] = query.keyword
        
        if query.category:
            filters['category'] = query.category
        
        if query.min_price is not None:
            filters['min_price'] = query.min_price
        
        if query.max_price is not None:
            filters['max_price'] = query.max_price
        
        if query.in_stock_only:
            filters['min_stock'] = 1
        
        if query.active_only:
            filters['status'] = 'active'
        
        # 执行搜索
        return await self.sku_repository.search(
            filters=filters,
            offset=(query.page - 1) * query.page_size,
            limit=query.page_size
        )
```

### 2.5 创建应用服务
```python
# src/applications/benefits/services/sku_application_service.py
"""SKU应用服务"""
from typing import List, Optional
from decimal import Decimal
from src.applications.service import ApplicationService
from src.applications.base import CommandBus, QueryBus
from src.applications.benefits.commands.sku_commands import *
from src.applications.benefits.queries.sku_queries import *
from src.domains.benefits.entities.sku import Sku

class SkuApplicationService(ApplicationService):
    """SKU应用服务 - 编排业务流程"""
    
    def __init__(self, command_bus: CommandBus, query_bus: QueryBus):
        super().__init__()
        self.command_bus = command_bus
        self.query_bus = query_bus
    
    async def create_sku(
        self,
        name: str,
        code: str,
        category: str,
        price: Decimal,
        cost: Decimal,
        **kwargs
    ) -> Sku:
        """创建SKU"""
        command = CreateSkuCommand(
            name=name,
            code=code,
            category=category,
            price=price,
            cost=cost,
            **kwargs
        )
        
        return await self.command_bus.dispatch(command)
    
    async def update_sku_price(
        self,
        sku_id: int,
        new_price: Decimal,
        reason: str
    ) -> Sku:
        """更新SKU价格"""
        command = UpdateSkuPriceCommand(
            sku_id=sku_id,
            new_price=new_price,
            reason=reason
        )
        
        return await self.command_bus.dispatch(command)
    
    async def search_skus(
        self,
        keyword: Optional[str] = None,
        category: Optional[str] = None,
        **filters
    ) -> List[Sku]:
        """搜索SKU"""
        query = SearchSkusQuery(
            keyword=keyword,
            category=category,
            **filters
        )
        
        return await self.query_bus.dispatch(query)
    
    async def process_order_skus(
        self,
        order_id: str,
        customer_id: str,
        sku_items: List[dict]
    ) -> None:
        """处理订单中的SKU - 复杂业务流程示例"""
        # 在事务中执行
        async def _process():
            # 1. 预留所有SKU库存
            for item in sku_items:
                command = ReserveSkuStockCommand(
                    sku_id=item['sku_id'],
                    quantity=item['quantity'],
                    order_id=order_id,
                    customer_id=customer_id
                )
                await self.command_bus.dispatch(command)
            
            # 2. 计算总价和折扣
            total_amount = Decimal('0')
            for item in sku_items:
                query = GetSkuByIdQuery(sku_id=item['sku_id'])
                sku = await self.query_bus.dispatch(query)
                if sku:
                    total_amount += sku.price * item['quantity']
            
            # 3. 应用优惠规则（这里可以调用其他领域服务）
            # ...
            
            return total_amount
        
        return await self.execute_in_transaction(_process)
```

## 任务3：创建其他领域的应用层

### 3.1 创建应用层生成器
```python
# scripts/generate_application_layer.py
"""生成应用层代码的脚本"""
import os
from typing import List

class ApplicationLayerGenerator:
    """应用层代码生成器"""
    
    def __init__(self, domain_name: str):
        self.domain_name = domain_name
        self.base_path = f'src/applications/{domain_name}'
    
    def create_structure(self):
        """创建目录结构"""
        dirs = [
            f'{self.base_path}/commands',
            f'{self.base_path}/queries',
            f'{self.base_path}/handlers',
            f'{self.base_path}/services',
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
            
        # 创建 __init__.py
        for root, dirs, _ in os.walk(self.base_path):
            init_file = os.path.join(root, '__init__.py')
            if not os.path.exists(init_file):
                open(init_file, 'w').close()
    
    def generate_command_template(self, entity_name: str):
        """生成命令模板"""
        template = f'''"""
{entity_name}相关命令
"""
from dataclasses import dataclass
from typing import Optional
from src.applications.base import Command

@dataclass
class Create{entity_name}Command(Command):
    """创建{entity_name}命令"""
    # TODO: 添加字段
    pass

@dataclass
class Update{entity_name}Command(Command):
    """更新{entity_name}命令"""
    {entity_name.lower()}_id: int
    # TODO: 添加其他字段
    pass

@dataclass
class Delete{entity_name}Command(Command):
    """删除{entity_name}命令"""
    {entity_name.lower()}_id: int
    reason: str
'''
        
        filename = f'{self.base_path}/commands/{entity_name.lower()}_commands.py'
        with open(filename, 'w') as f:
            f.write(template)
        
        print(f"✅ 生成命令文件: {filename}")
    
    def generate_query_template(self, entity_name: str):
        """生成查询模板"""
        template = f'''"""
{entity_name}相关查询
"""
from dataclasses import dataclass
from typing import Optional, List
from src.applications.base import Query

@dataclass
class Get{entity_name}ByIdQuery(Query):
    """根据ID获取{entity_name}查询"""
    {entity_name.lower()}_id: int

@dataclass
class Search{entity_name}sQuery(Query):
    """搜索{entity_name}查询"""
    keyword: Optional[str] = None
    page: int = 1
    page_size: int = 20
    # TODO: 添加其他过滤条件
'''
        
        filename = f'{self.base_path}/queries/{entity_name.lower()}_queries.py'
        with open(filename, 'w') as f:
            f.write(template)
        
        print(f"✅ 生成查询文件: {filename}")
    
    def generate(self, entities: List[str]):
        """生成应用层代码"""
        print(f"开始生成 {self.domain_name} 应用层...")
        
        self.create_structure()
        
        for entity in entities:
            self.generate_command_template(entity)
            self.generate_query_template(entity)
        
        print(f"✅ {self.domain_name} 应用层生成完成")

# 为每个领域生成应用层
domain_entities = {
    'passport': ['User', 'Session', 'Permission'],
    'delivery': ['Store', 'DeliveryArea', 'DeliveryFee'],
    'customer': ['Customer', 'Address', 'PaymentMethod'],
    'aiagent': ['Agent', 'Conversation', 'Message'],
    'activity': ['Activity', 'Promotion', 'Coupon']
}

for domain, entities in domain_entities.items():
    generator = ApplicationLayerGenerator(domain)
    generator.generate(entities)
```

## 任务4：配置依赖注入

### 4.1 创建应用层容器配置
```python
# src/applications/containers.py
"""应用层依赖注入配置"""
from dependency_injector import containers, providers
from src.applications.base import CommandBus, QueryBus, EventBus
from src.applications.benefits.handlers.sku_command_handlers import *
from src.applications.benefits.handlers.sku_query_handlers import *
from src.applications.benefits.services.sku_application_service import SkuApplicationService

class ApplicationContainer(containers.DeclarativeContainer):
    """应用层容器"""
    
    # 依赖注入
    repositories = providers.DependenciesContainer()
    domain_services = providers.DependenciesContainer()
    
    # 基础设施
    command_bus = providers.Singleton(CommandBus)
    query_bus = providers.Singleton(QueryBus)
    event_bus = providers.Singleton(EventBus)
    
    # 权益领域 - 命令处理器
    create_sku_handler = providers.Factory(
        CreateSkuCommandHandler,
        sku_repository=repositories.sku_repository,
        sku_service=domain_services.sku_domain_service,
        event_bus=event_bus
    )
    
    update_sku_price_handler = providers.Factory(
        UpdateSkuPriceCommandHandler,
        sku_repository=repositories.sku_repository,
        event_bus=event_bus
    )
    
    reserve_sku_stock_handler = providers.Factory(
        ReserveSkuStockCommandHandler,
        sku_repository=repositories.sku_repository,
        event_bus=event_bus
    )
    
    # 权益领域 - 查询处理器
    get_sku_by_id_handler = providers.Factory(
        GetSkuByIdQueryHandler,
        sku_repository=repositories.sku_repository
    )
    
    search_skus_handler = providers.Factory(
        SearchSkusQueryHandler,
        sku_repository=repositories.sku_repository
    )
    
    # 权益领域 - 应用服务
    sku_application_service = providers.Factory(
        SkuApplicationService,
        command_bus=command_bus,
        query_bus=query_bus
    )
    
    @staticmethod
    def wire_command_handlers(container):
        """注册命令处理器"""
        command_bus = container.command_bus()
        
        # 注册SKU命令处理器
        command_bus.register(
            CreateSkuCommand,
            container.create_sku_handler()
        )
        command_bus.register(
            UpdateSkuPriceCommand,
            container.update_sku_price_handler()
        )
        command_bus.register(
            ReserveSkuStockCommand,
            container.reserve_sku_stock_handler()
        )
    
    @staticmethod
    def wire_query_handlers(container):
        """注册查询处理器"""
        query_bus = container.query_bus()
        
        # 注册SKU查询处理器
        query_bus.register(
            GetSkuByIdQuery,
            container.get_sku_by_id_handler()
        )
        query_bus.register(
            SearchSkusQuery,
            container.search_skus_handler()
        )
```

## 任务5：创建应用层测试

### 5.1 创建测试配置
```python
# tests/unit/applications/conftest.py
"""应用层测试配置"""
import pytest
from unittest.mock import Mock, AsyncMock
from src.applications.base import CommandBus, QueryBus, EventBus
from src.repositories.benefits.sku_repository import ISkuRepository
from src.domains.benefits.services.sku_service import SkuDomainService

@pytest.fixture
def mock_sku_repository():
    """模拟SKU仓储"""
    repository = Mock(spec=ISkuRepository)
    repository.get_by_id = AsyncMock(return_value=None)
    repository.get_by_code = AsyncMock(return_value=None)
    repository.save = AsyncMock()
    repository.search = AsyncMock(return_value=[])
    return repository

@pytest.fixture
def mock_sku_service():
    """模拟SKU领域服务"""
    return Mock(spec=SkuDomainService)

@pytest.fixture
def event_bus():
    """事件总线"""
    bus = EventBus()
    bus.publish = AsyncMock()
    return bus

@pytest.fixture
def command_bus():
    """命令总线"""
    return CommandBus()

@pytest.fixture
def query_bus():
    """查询总线"""
    return QueryBus()
```

### 5.2 创建命令处理器测试
```python
# tests/unit/applications/benefits/test_sku_command_handlers.py
"""SKU命令处理器测试"""
import pytest
from decimal import Decimal
from src.applications.benefits.commands.sku_commands import CreateSkuCommand
from src.applications.benefits.handlers.sku_command_handlers import CreateSkuCommandHandler
from src.domains.benefits.entities.sku import Sku

class TestCreateSkuCommandHandler:
    """创建SKU命令处理器测试"""
    
    @pytest.mark.asyncio
    async def test_create_sku_success(
        self,
        mock_sku_repository,
        mock_sku_service,
        event_bus
    ):
        """测试成功创建SKU"""
        # 准备
        command = CreateSkuCommand(
            name="Test SKU",
            code="TEST001",
            category="test",
            price=Decimal("100.00"),
            cost=Decimal("60.00"),
            initial_stock=100
        )
        
        expected_sku = Sku(
            id=1,
            name=command.name,
            code=command.code,
            category=command.category,
            price=command.price,
            cost=command.cost,
            stock=command.initial_stock
        )
        
        mock_sku_repository.save.return_value = expected_sku
        
        handler = CreateSkuCommandHandler(
            mock_sku_repository,
            mock_sku_service,
            event_bus
        )
        
        # 执行
        result = await handler.handle(command)
        
        # 验证
        assert result.id == 1
        assert result.name == "Test SKU"
        assert result.code == "TEST001"
        
        # 验证仓储调用
        mock_sku_repository.get_by_code.assert_called_once_with("TEST001")
        mock_sku_repository.save.assert_called_once()
        
        # 验证事件发布
        event_bus.publish.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_sku_duplicate_code(
        self,
        mock_sku_repository,
        mock_sku_service,
        event_bus
    ):
        """测试创建重复编码的SKU"""
        # 准备
        command = CreateSkuCommand(
            name="Test SKU",
            code="EXISTING",
            category="test",
            price=Decimal("100.00"),
            cost=Decimal("60.00")
        )
        
        # 模拟已存在的SKU
        existing_sku = Sku(
            id=999,
            name="Existing SKU",
            code="EXISTING",
            category="test",
            price=Decimal("50.00"),
            cost=Decimal("30.00")
        )
        
        mock_sku_repository.get_by_code.return_value = existing_sku
        
        handler = CreateSkuCommandHandler(
            mock_sku_repository,
            mock_sku_service,
            event_bus
        )
        
        # 执行并验证异常
        with pytest.raises(ValueError) as exc_info:
            await handler.handle(command)
        
        assert "already exists" in str(exc_info.value)
        
        # 验证没有保存
        mock_sku_repository.save.assert_not_called()
        
        # 验证没有发布事件
        event_bus.publish.assert_not_called()
```

## 验证步骤

### 1. 检查应用层结构
```bash
find src/applications -name "*.py" | grep -E "(command|query|handler|service)" | sort
```

### 2. 验证CQRS实现
```python
# scripts/verify_cqrs.py
import asyncio
from src.applications.containers import ApplicationContainer

async def verify_cqrs():
    """验证CQRS实现"""
    container = ApplicationContainer()
    
    # 模拟依赖
    container.repositories.sku_repository = lambda: Mock()
    container.domain_services.sku_domain_service = lambda: Mock()
    
    # 注册处理器
    ApplicationContainer.wire_command_handlers(container)
    ApplicationContainer.wire_query_handlers(container)
    
    # 获取服务
    sku_service = container.sku_application_service()
    
    print("✅ CQRS配置验证成功")

asyncio.run(verify_cqrs())
```

### 3. 运行应用层测试
```bash
poetry run pytest tests/unit/applications -v
```

## 完成标准

- [ ] CQRS基础架构实现
- [ ] 命令和查询分离
- [ ] 命令处理器实现
- [ ] 查询处理器实现
- [ ] 应用服务编排业务流程
- [ ] 事件总线集成
- [ ] 依赖注入配置
- [ ] 单元测试覆盖率 > 80%
- [ ] 事务管理机制
- [ ] 跨领域协调能力