# encoding: utf-8
# tests/unit/applications/passport/test_factories.py
# created: 2025-08-02 10:45:00

"""测试工厂类的单元测试"""

import pytest

from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from tests.unit.applications.passport.factories import (
    AppFactory,
    TenantFactory,
    UserFactory,
    UserRelationFactory,
)


class TestUserFactory:
    """测试用户工厂"""

    def test_create_user_entity_with_defaults(self):
        """测试使用默认值创建用户实体"""
        user = UserFactory.create_user_entity()

        assert isinstance(user, UserEntity)
        assert user.uid.startswith("uid_")
        assert user.phone == "13800138000"
        assert user.nickname == "测试用户"
        assert user.avatar_url == "https://example.com/avatar.jpg"
        assert user.email == "<EMAIL>"
        assert user.current_app is None
        assert user.current_tenant is None

    def test_create_user_entity_with_custom_values(self):
        """测试使用自定义值创建用户实体"""
        app = AppFactory.create_app_entity()
        tenant = TenantFactory.create_tenant_entity()

        user = UserFactory.create_user_entity(
            uid="custom_uid",
            phone="13900139000",
            nickname="自定义用户",
            avatar_url="https://custom.com/avatar.jpg",
            email="<EMAIL>",
            current_app=app,
            current_tenant=tenant,
        )

        assert user.uid == "custom_uid"
        assert user.phone == "13900139000"
        assert user.nickname == "自定义用户"
        assert user.avatar_url == "https://custom.com/avatar.jpg"
        assert user.email == "<EMAIL>"
        assert user.current_app == app
        assert user.current_tenant == tenant

    def test_create_user_model(self):
        """测试创建用户模型 Mock"""
        user_model = UserFactory.create_user_model()

        assert user_model.id == 1
        assert user_model.uid.startswith("uid_")
        assert user_model.phone == "13800138000"
        assert user_model.nickname == "测试用户"
        assert user_model.created_at is not None
        assert user_model.updated_at is not None

    def test_create_specific_user_types(self):
        """测试创建特定类型的用户"""
        phone_user = UserFactory.create_phone_user()
        assert phone_user.phone == "13900139000"
        assert phone_user.nickname == "手机用户"

        wechat_user = UserFactory.create_wechat_user()
        assert wechat_user.phone == "13700137000"
        assert wechat_user.nickname == "微信用户"

        dingtalk_user = UserFactory.create_dingtalk_user()
        assert dingtalk_user.phone == "13600136000"
        assert dingtalk_user.nickname == "钉钉用户"


class TestAppFactory:
    """测试应用工厂"""

    def test_create_app_entity_with_defaults(self):
        """测试使用默认值创建应用实体"""
        app = AppFactory.create_app_entity()

        assert isinstance(app, AppEntity)
        assert app.id == 1
        assert app.app_id.startswith("app_")
        assert app.app_name == "测试应用"
        assert len(app.app_secret) == 32
        assert app.wechat_app_id.startswith("wx_")
        assert app.dingtalk_app_id.startswith("dt_")

    def test_create_app_entity_with_custom_values(self):
        """测试使用自定义值创建应用实体"""
        app = AppFactory.create_app_entity(
            id=100,
            app_id="custom_app",
            app_name="自定义应用",
            app_secret="custom_secret",
            wechat_app_id="wx_custom",
            dingtalk_app_id="dt_custom",
        )

        assert app.id == 100
        assert app.app_id == "custom_app"
        assert app.app_name == "自定义应用"
        assert app.app_secret == "custom_secret"
        assert app.wechat_app_id == "wx_custom"
        assert app.dingtalk_app_id == "dt_custom"

    def test_create_app_model(self):
        """测试创建应用模型 Mock"""
        app_model = AppFactory.create_app_model()

        assert app_model.id == 1
        assert app_model.app_id.startswith("app_")
        assert app_model.name == "测试应用"
        assert len(app_model.secret) == 32
        assert app_model.created_at is not None

    def test_create_specific_app_types(self):
        """测试创建特定类型的应用"""
        internal_app = AppFactory.create_internal_app()
        assert internal_app.app_id == "internal_app"
        assert internal_app.app_name == "内部系统"

        external_app = AppFactory.create_external_app()
        assert external_app.app_id == "external_app"
        assert external_app.app_name == "外部应用"


class TestTenantFactory:
    """测试租户工厂"""

    def test_create_tenant_entity_with_defaults(self):
        """测试使用默认值创建租户实体"""
        tenant = TenantFactory.create_tenant_entity()

        assert isinstance(tenant, TenantEntity)
        assert tenant.tenant_id.startswith("tenant_")
        assert tenant.tenant_name == "测试租户"
        assert isinstance(tenant.app, AppEntity)

    def test_create_tenant_entity_with_custom_values(self):
        """测试使用自定义值创建租户实体"""
        custom_app = AppFactory.create_app_entity(app_name="自定义应用")
        tenant = TenantFactory.create_tenant_entity(
            tenant_id="custom_tenant",
            tenant_name="自定义租户",
            app=custom_app,
        )

        assert tenant.tenant_id == "custom_tenant"
        assert tenant.tenant_name == "自定义租户"
        assert tenant.app == custom_app
        assert tenant.app.app_name == "自定义应用"

    @pytest.mark.asyncio
    async def test_create_tenant_model(self):
        """测试创建租户模型 Mock"""
        tenant_model = TenantFactory.create_tenant_model()

        assert tenant_model.id == 1
        assert tenant_model.tenant_id.startswith("tenant_")
        assert tenant_model.name == "测试租户"
        assert tenant_model.app_id == 1

        # 测试 fetch_related
        await tenant_model.fetch_related("app")
        assert hasattr(tenant_model, "app")
        assert tenant_model.app.name == "测试应用"

    def test_create_specific_tenant_types(self):
        """测试创建特定类型的租户"""
        active_tenant = TenantFactory.create_active_tenant()
        assert active_tenant.tenant_name == "活跃租户"

        inactive_tenant = TenantFactory.create_inactive_tenant()
        assert inactive_tenant.tenant_name == "非活跃租户"


class TestUserRelationFactory:
    """测试用户关系工厂"""

    def test_create_user_relation_model(self):
        """测试创建用户关系模型 Mock"""
        relation = UserRelationFactory.create_user_relation_model()

        assert relation.id == 1
        assert relation.uid.startswith("uid_")
        assert relation.tenant_id.startswith("tenant_")
        assert relation.is_active is True
        assert relation.created_at is not None
        assert relation.updated_at is not None

    def test_create_user_relation_model_with_custom_values(self):
        """测试使用自定义值创建用户关系模型"""
        relation = UserRelationFactory.create_user_relation_model(
            id=100,
            uid="custom_uid",
            tenant_id="custom_tenant",
            is_active=False,
        )

        assert relation.id == 100
        assert relation.uid == "custom_uid"
        assert relation.tenant_id == "custom_tenant"
        assert relation.is_active is False
