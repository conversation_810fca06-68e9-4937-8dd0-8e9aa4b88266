# encoding: utf-8
# src/applications/openapi/services/benefits_charge.py
# created: 2025-07-29 08:17:51

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import TYPE_CHECKING, Optional, Union

from loguru import logger
from tortoise.transactions import atomic

from deploys.openapi.settings import OpenapiSettings
from src.databases.models.benefits import BenefitsTicketStatus
from src.domains.customer.entities import CustomerEntity
from src.domains.customer.entities.benefit_product import BenefitProductEntity

from ..dto import BenefitsOrderDTO, BenefitsProductChargePageDTO
from ..errors import (
    BenefitOrderNotFoundError,
    BenefitProductChargeTicketExpiredError,
    BenefitProductChargeTicketNotFoundError,
    BenefitProductChargeTicketNotUsedError,
    BenefitProductChargeTicketUsedError,
    BenefitProductNotFoundError,
    BenefitProductNotUseableError,
    CustomerNotFoundError,
)

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsProductService
    from src.repositories.benefits import ProductOrderRepository
    from src.repositories.customer import BenefitsRepository as CustomerBenefitRepository
    from src.repositories.customer import CustomerRepository


class BenefitsChargeService:

    def __init__(
        self,
        customer_benefit_repo: "CustomerBenefitRepository",
        benefits_product_service: "BenefitsProductService",
        benefits_product_order_repo: "ProductOrderRepository",
        customer_repo: "CustomerRepository",
        config: Union[dict, "OpenapiSettings"],
    ) -> None:
        self.customer_benefit_repo = customer_benefit_repo
        self.benefits_product_service = benefits_product_service
        self.benefits_product_order_repo = benefits_product_order_repo
        self.customer_repo = customer_repo
        self.config = config if isinstance(config, OpenapiSettings) else OpenapiSettings(**config)

    @atomic()
    async def charge_benefit(
        self,
        customer: "CustomerEntity",
        product_code: str,
        account: str,
        out_order_id: str,
        notify_url: str,
        source_identify: Optional[str] = None,
    ) -> BenefitsOrderDTO:
        """客户充值权益产品"""

        benefit = await self.customer_benefit_repo.get_by_customer_and_product_for_update(customer.id, product_code)
        if not benefit:
            raise BenefitProductNotFoundError
        product = await BenefitProductEntity.from_model(benefit)

        if not customer.validate_benefit(product):
            raise BenefitProductNotUseableError

        # process payment and stock
        if product.sale_mode == "finacial":
            # 扣减客户余额
            customer.payment_benefit(product)
            # 持久化客户余额变更到数据库
            await self.customer_benefit_repo.update_customer_balance(customer.id, customer.balance)
        else:
            # 减少产品库存
            product.decrease_stock()
            # 持久化库存变更到数据库
            await self.customer_benefit_repo.update_benefit_stock(benefit.id, product.stock)

        # 处理source_identify, TODO: 需要长期重构底层数据后, 删除此段代码
        if not source_identify:
            source_identify = customer.source_identify

        logger.info(
            "客户[{customer}]权益产品充值: {product_code}, 金额: {price}, 来源: {source_identify}",
            customer=f"{customer.code}-{customer.name}",
            product_code=product.code,
            price=product.sale_price,
            source_identify=source_identify,
        )

        order = await self.benefits_product_service.charge_product(
            product_code=benefit.product.code,
            account=account,
            out_order_id=out_order_id,
            notify_url=notify_url,
            source_identify=source_identify,
            sale_price=benefit.sale_price,
            from_app=customer.app_id,
            from_tenant=customer.tenant_id,
        )
        return BenefitsOrderDTO.model_validate(order.model_dump())

    async def charge_benefit_again(
        self,
        customer: "CustomerEntity",
        order_id: str,
        out_order_id: str,
        notify_url: str,
    ) -> "BenefitsOrderDTO":
        """客户重新充值权益产品"""

        # 检查订单是否匹配
        order = await self.benefits_product_order_repo.get_by_order_id(order_id)
        if not order or order.out_order_id != out_order_id:
            raise BenefitOrderNotFoundError

        # TODO: 实现重新充值的逻辑

        # 检查订单来源是否匹配
        # await self._check_order_source(order, customer)

        # order = await self.benefits_product_service.recharge_order(order_id, notify_url)

        # if not order:
        #     logger.error(f"重新充值的订单不存在: {order_id}")
        #     raise errors.ChargeOrderNotFoundError
        # if order.status != BenefitsProductOrderStatus.FAILED:
        #     logger.error(f"重新充值的订单状态不是失败: {order_id}, {order.status}")
        #     raise errors.ChargeOrderNotFailedError

        # order.status = BenefitsProductOrderStatus.PROCESSING
        # order.notify_url = notify_url
        # await order.save()
        # logger.info(f"重新充值的订单状态更新为处理中: {order_id}, {notify_url}")

        # 重新生成skus
        # skus = await SkuRepository.gets_by_product_code(order.product_code)
        # for sku in skus:
        #     sku_charge_record = await SkuChargeRecordRepository.create_record(
        #         sku=sku,
        #         charge_order_id=str(order.order_id),
        #         account=order.account,
        #         source_identify=order.source_identify,
        #     )
        #     _ = ChargeSkuMessage(
        #         record_id=sku_charge_record.id,
        #         account=order.account,
        #     )
        # todo: 需要更新
        # await publish_message(
        #     message.model_dump_json(),
        #     routing_key="benefits.charge_sku",
        #     message_id=f"BENEFITS_RECHARGE_SKU_{sku_charge_record.id}_{int(time.time())}",
        # )
        return BenefitsOrderDTO.model_validate(order.model_dump())  # type: ignore

    async def create_ticket(
        self,
        customer: "CustomerEntity",
        product_code: str,
        account: str,
        notify_url: str,
    ) -> BenefitsProductChargePageDTO:

        # charge code生成 日期+手机号+6位随机数
        ticket_code = f"{datetime.now().strftime('%Y%m%d')}{account}{uuid.uuid4().hex[:6]}"
        expired_at = datetime.now() + timedelta(days=30)

        ticket = await self.benefits_product_order_repo.create_charge_ticket(
            ticket_code=ticket_code,
            customer_id=customer.id,
            product_code=product_code,
            account=account,
            notify_url=notify_url,
            expired_at=expired_at,
        )

        return BenefitsProductChargePageDTO(
            ticket_code=ticket.ticket_code,
            charge_url=f"{self.config.fastapi.hostname.base_fe}/benefits/charge/{ticket.ticket_code}",
            expire_time=int(expired_at.timestamp() * 1000),
        )

    async def charge_by_ticket(self, ticket_code: str) -> str:
        ticket = await self.benefits_product_order_repo.get_charge_ticket_by_ticket_code(ticket_code=ticket_code)
        if not ticket:
            raise BenefitProductChargeTicketNotFoundError

        if ticket.status == BenefitsTicketStatus.USED:
            raise BenefitProductChargeTicketUsedError

        if ticket.expired_at < datetime.now().replace(tzinfo=ticket.expired_at.tzinfo):
            if ticket.status == BenefitsTicketStatus.PENDING:
                ticket.status = BenefitsTicketStatus.EXPIRED
                await ticket.save()
            raise BenefitProductChargeTicketExpiredError

        customer = await self.customer_repo.get_by_id(ticket.customer_id)

        if not customer:
            raise CustomerNotFoundError

        customer_entity = await CustomerEntity.from_model(customer)

        order = await self.charge_benefit(
            customer=customer_entity,
            product_code=ticket.product_code,
            account=ticket.account,
            out_order_id=ticket.ticket_code,
            notify_url=ticket.notify_url,
            source_identify=None,
        )

        ticket.status = BenefitsTicketStatus.USED
        ticket.used_at = datetime.now()
        ticket.order_id = int(order.order_id)
        await ticket.save()

        return str(order.order_id)

    async def get_order_by_ticket(self, ticket_code: str) -> tuple["CustomerEntity", int]:
        ticket = await self.benefits_product_order_repo.get_charge_ticket_by_ticket_code(ticket_code=ticket_code)
        if not ticket:
            raise BenefitProductChargeTicketNotFoundError

        if not ticket.order_id:
            raise BenefitProductChargeTicketNotUsedError

        customer = await self.customer_repo.get_by_id(ticket.customer_id)

        if not customer:
            raise CustomerNotFoundError

        customer_entity = await CustomerEntity.from_model(customer)

        return customer_entity, ticket.order_id
