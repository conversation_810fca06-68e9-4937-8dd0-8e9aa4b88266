# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/aiagent/services/auto_benefits.py
# created: 2025-04-18 00:40:32
# updated: 2025-04-18 01:05:43

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject

if TYPE_CHECKING:
    from redis.asyncio import Redis


class AutoBenefitsService:

    def __init__(self, redis: "Redis"):
        self.redis = redis

    async def check_user_subscribe_state(self, agent_id: str, agent_user_id: str):
        pass

    async def subscribe_user(self, agent_id: str, agent_user_id: str):
        pass
