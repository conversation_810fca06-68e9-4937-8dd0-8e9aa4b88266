# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/aiagent/services/auto_benefits/__init__.py
# created: 2025-04-18 00:41:22
# updated: 2025-04-18 00:51:22

from .auto_benefits_factor import AutoBenefitsFactor
from .dingtalk_free import DingtalkFreeStrategy
from .dingtalk_paid import DingtalkPaidStrategy

DINGTALK_CAMPUS_AGENT = "64598ba338694f529fa244627fd0d4c3"

AutoBenefitsFactor.register_strategy(DINGTALK_CAMPUS_AGENT, "apply", DingtalkFreeStrategy)
AutoBenefitsFactor.register_strategy(DINGTALK_CAMPUS_AGENT, "subscribe", DingtalkPaidStrategy)
