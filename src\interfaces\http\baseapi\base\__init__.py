# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/__init__.py
# created: 2024-11-30 22:23:28
# updated: 2025-05-22 00:04:22

# Export base API routers for use in main.py
from .routers.delivery import router as delivery_router
from .routers.oss import router as oss_router
from .routers.passport import router as passport_router

# from .routers.dingtalk import router as dingtalk_router
# from .routers.dingtalk_blind import router as dingtalk_blind_router
# from .routers.benefits_center import router as benefits_center_router

__all__ = [
    "delivery_router",
    "oss_router",
    "passport_router",
]
