# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/services/authenticator/iauthenticator.py
# created: 2025-04-11 00:39:25
# updated: 2025-04-11 00:41:23

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Optional, Tuple

from fastapi import Request

if TYPE_CHECKING:
    from src.domains.customer.entities import CustomerEntity


class IAuthenticator(ABC):
    """认证基类"""

    @abstractmethod
    async def authenticate(self, request: Request) -> <PERSON><PERSON>[bool, Optional["CustomerEntity"], str]:
        """认证方法"""
        pass
