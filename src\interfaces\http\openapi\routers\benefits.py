# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/openapi/routers/benefits.py
# created: 2024-12-04 00:50:44
# updated: 2025-04-10 22:05:40

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

from src.containers import Container

from ..authorization import openapi_authentication
from ..schemas import (
    BenefitsOrderResponse,
    BenefitsProductChargeByTicketPayload,
    BenefitsProductChargeByTicketResponse,
    BenefitsProductChargePagePayload,
    BenefitsProductChargePageResponse,
    ChargeRequestPayload,
    CustomerBenefitsResponse,
    RechargeRequestPayload,
)

if TYPE_CHECKING:
    from src.applications.openapi.queries import BenefitsQueryService
    from src.applications.openapi.services import BenefitsChargeService
    from src.domains.customer.entities import CustomerEntity

router = APIRouter(tags=["benefits"])


@router.get("/products", name="获取权益产品列表", response_model=CustomerBenefitsResponse)
@inject
async def get_products(
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_service: "BenefitsQueryService" = Depends(Provide[Container.applications.openapi_benefits_query_service]),
):
    data = await benefits_service.get_products(customer)
    return CustomerBenefitsResponse(data=data)


@router.get("/orders/{order_id}", name="获取权益订单详情", response_model=BenefitsOrderResponse)
@inject
async def get_order_detail(
    order_id: str,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_service: "BenefitsQueryService" = Depends(Provide[Container.applications.openapi_benefits_query_service]),
):
    order = await benefits_service.get_order_by_order_id(customer, order_id)
    return BenefitsOrderResponse(data=order)


@router.get(
    "/orders/outorder/{out_order_id}",
    name="根据第三方订单号获取权益订单",
    response_model=BenefitsOrderResponse,
)
@inject
async def get_order_detail_by_out_order_id(
    out_order_id: str,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_service: "BenefitsQueryService" = Depends(Provide[Container.applications.openapi_benefits_query_service]),
):
    order = await benefits_service.get_order_by_out_order_id(customer, out_order_id)
    return BenefitsOrderResponse(data=order)


@router.post(
    "/orders/{order_id}/recharge",
    name="权益订单重新充值",
    response_model=BenefitsOrderResponse,
)
@inject
async def recharge_order(
    order_id: str,
    payload: RechargeRequestPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_charge_service: "BenefitsChargeService" = Depends(
        Provide[Container.applications.openapi_benefits_charge_service]
    ),
):
    order = await benefits_charge_service.charge_benefit_again(
        customer, order_id, payload.out_order_id, payload.notify_url
    )
    return BenefitsOrderResponse(data=order)


@router.post(
    "/products/{product_code}/charge",
    name="权益充值",
    response_model=BenefitsOrderResponse,
)
@inject
async def charge_product(
    product_code: str,
    payload: ChargeRequestPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_charge_service: "BenefitsChargeService" = Depends(
        Provide[Container.applications.openapi_benefits_charge_service]
    ),
):
    order = await benefits_charge_service.charge_benefit(
        customer,
        product_code,
        payload.account,
        payload.out_order_id,
        payload.notify_url,
        payload.source_identify,
    )
    return BenefitsOrderResponse(data=order)


@router.post(
    "/products/{product_code}/ticket",
    name="权益充值页面",
    response_model=BenefitsProductChargePageResponse,
)
@inject
async def create_ticket(
    product_code: str,
    payload: BenefitsProductChargePagePayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    benefits_charge_service: "BenefitsChargeService" = Depends(
        Provide[Container.applications.openapi_benefits_charge_service]
    ),
):
    ticket = await benefits_charge_service.create_ticket(
        customer,
        product_code,
        payload.account,
        payload.notify_url,
    )
    return BenefitsProductChargePageResponse(data=ticket)


@router.post(
    "/products/charge_by_ticket",
    name="通过充值凭证充值",
    response_model=BenefitsProductChargeByTicketResponse,
)
@inject
async def charge_by_ticket(
    payload: BenefitsProductChargeByTicketPayload,
    benefits_charge_service: "BenefitsChargeService" = Depends(
        Provide[Container.applications.openapi_benefits_charge_service]
    ),
):
    order_id = await benefits_charge_service.charge_by_ticket(
        ticket_code=payload.ticket_code,
    )
    return BenefitsProductChargeByTicketResponse(data=order_id)


@router.post(
    "/orders/get_by_ticket",
    name="通过充值凭证获取订单",
    response_model=BenefitsOrderResponse,
)
@inject
async def get_order_by_ticket(
    payload: BenefitsProductChargeByTicketPayload,
    benefits_charge_service: "BenefitsChargeService" = Depends(
        Provide[Container.applications.openapi_benefits_charge_service]
    ),
    benefits_service: "BenefitsQueryService" = Depends(Provide[Container.applications.openapi_benefits_query_service]),
):
    customer, order_id = await benefits_charge_service.get_order_by_ticket(
        ticket_code=payload.ticket_code,
    )
    order = await benefits_service.get_order_by_order_id(customer, str(order_id))

    return BenefitsOrderResponse(data=order)
