# 测试验证方案

## 🎯 测试策略

采用多层次测试策略，确保迁移过程中的功能正确性和性能不退化。

### 测试层次

| 测试层次 | 测试范围 | 工具 | 覆盖率目标 |
|----------|----------|------|------------|
| **单元测试** | Services层业务逻辑 | pytest + mock | >90% |
| **集成测试** | API端点功能 | TestClient | >85% |
| **接口测试** | API兼容性 | curl + scripts | 100% |
| **性能测试** | 响应时间和并发 | locust | 基准对比 |
| **E2E测试** | Node服务集成 | 实际调用 | 核心场景 |

## 🧪 单元测试

### 1. Services层测试

#### BenefitsProductService测试 (`tests/unit/domains/benefits/services/test_product_service.py`)

```python
import pytest
from unittest.mock import AsyncMock, MagicMock
from domains.benefits.services.product import BenefitsProductService
from domains.benefits.exceptions import ProductNotFoundError, PermissionDeniedError

class TestBenefitsProductService:
    
    @pytest.fixture
    def service(self):
        """创建测试用的服务实例"""
        product_repo = AsyncMock()
        product_order_repo = AsyncMock()
        sku_service = AsyncMock()
        producer = AsyncMock()
        
        return BenefitsProductService(
            product_repo=product_repo,
            product_order_repo=product_order_repo,
            sku_service=sku_service,
            producer=producer
        )
    
    @pytest.fixture
    def mock_product(self):
        """模拟产品数据"""
        return MagicMock(
            id=1,
            code="TEST001",
            name="测试产品",
            price=1000,
            description="测试产品描述",
            status="active",
            supplier_type="shinesun"
        )
    
    @pytest.fixture
    def mock_customer(self):
        """模拟客户数据"""
        return MagicMock(
            id=1,
            name="测试客户",
            channel_code="test_channel",
            status="active"
        )
    
    async def test_charge_product_success(self, service, mock_product, mock_customer):
        """测试充值成功场景"""
        # 准备测试数据
        payload = {
            "out_order_id": "test_order_001",
            "account": "<EMAIL>"
        }
        channel = "test_channel"
        
        # Mock依赖
        service.product_repo.get_by_code.return_value = mock_product
        service._get_customer_by_channel = AsyncMock(return_value=mock_customer)
        service.product_order_repo.create.return_value = MagicMock(id=100)
        service._get_charge_strategy = MagicMock()
        service._get_charge_strategy.return_value.charge = AsyncMock(
            return_value=MagicMock(
                status="success",
                third_order_id="third_001",
                message="充值成功"
            )
        )
        
        # 执行测试
        result = await service.charge_product(mock_product.code, payload, channel)
        
        # 验证结果
        assert result["order_id"] == "100"
        assert result["status"] == "success"
        assert result["amount"] == mock_product.price
        
        # 验证调用
        service.product_repo.get_by_code.assert_called_once_with(mock_product.code)
        service.product_order_repo.create.assert_called_once()
        service.producer.publish.assert_called_once()
    
    async def test_charge_product_not_found(self, service):
        """测试产品不存在场景"""
        # Mock产品不存在
        service.product_repo.get_by_code.return_value = None
        
        # 执行测试并验证异常
        with pytest.raises(ProductNotFoundError) as exc_info:
            await service.charge_product("NOT_EXIST", {}, "test_channel")
        
        assert "Product NOT_EXIST not found" in str(exc_info.value)
    
    async def test_get_product_by_code_success(self, service, mock_product, mock_customer):
        """测试获取产品详情成功"""
        # Mock依赖
        service.product_repo.get_by_code.return_value = mock_product
        service._get_customer_by_channel = AsyncMock(return_value=mock_customer)
        service._check_product_permission = AsyncMock(return_value=True)
        
        # 执行测试
        result = await service.get_product_by_code(mock_product.code, "test_channel")
        
        # 验证结果
        assert result["product_code"] == mock_product.code
        assert result["product_name"] == mock_product.name
        assert result["price"] == mock_product.price
        assert result["supplier"] == mock_product.supplier_type
    
    async def test_get_product_by_code_permission_denied(self, service, mock_product, mock_customer):
        """测试权限拒绝场景"""
        # Mock依赖
        service.product_repo.get_by_code.return_value = mock_product
        service._get_customer_by_channel = AsyncMock(return_value=mock_customer)
        service._check_product_permission = AsyncMock(return_value=False)
        
        # 执行测试并验证异常
        with pytest.raises(PermissionDeniedError):
            await service.get_product_by_code(mock_product.code, "test_channel")
    
    async def test_handle_shinesun_callback_success(self, service):
        """测试回调处理成功"""
        # 准备测试数据
        params = {
            "order_id": "100",
            "status": 1,
            "message": "充值成功"
        }
        
        # Mock依赖
        mock_order = MagicMock(id=100, customer_id=1)
        service.product_order_repo.get_by_id.return_value = mock_order
        
        # 执行测试
        await service.handle_shinesun_callback(params)
        
        # 验证调用
        service.product_order_repo.update.assert_called_once()
        service.producer.publish.assert_called_once_with(
            "order_success",
            {"order_id": 100, "customer_id": 1}
        )
```

### 2. SDK测试

#### SDK工厂测试 (`tests/unit/domains/benefits/utils/test_sdk_factory.py`)

```python
import pytest
from domains.benefits.utils.sdk_factory import SDKFactory
from domains.benefits.exceptions import UnsupportedSupplierError

class TestSDKFactory:
    
    def test_get_sdk_biforst(self):
        """测试获取Biforst SDK"""
        sdk = SDKFactory.get_sdk("biforst")
        assert sdk is not None
        assert hasattr(sdk, 'charge_account')
    
    def test_get_sdk_shinesun(self):
        """测试获取ShinesunSDK"""
        sdk = SDKFactory.get_sdk("shinesun")
        assert sdk is not None
        assert hasattr(sdk, 'charge_account')
    
    def test_get_sdk_unsupported(self):
        """测试不支持的供应商"""
        with pytest.raises(UnsupportedSupplierError) as exc_info:
            SDKFactory.get_sdk("unsupported")
        
        assert "Unsupported supplier: unsupported" in str(exc_info.value)
    
    def test_sdk_singleton(self):
        """测试SDK单例模式"""
        sdk1 = SDKFactory.get_sdk("biforst")
        sdk2 = SDKFactory.get_sdk("biforst")
        assert sdk1 is sdk2  # 确保是同一个实例
```

## 🔌 集成测试

### 1. API集成测试

#### 内部API测试 (`tests/integration/apis/test_internal_benefits.py`)

```python
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from apis.internal.main import app

client = TestClient(app)

class TestInternalBenefitsAPI:
    
    @patch('domains.benefits.services.product.BenefitsProductService.charge_product')
    def test_charge_product_api_success(self, mock_charge):
        """测试充值API成功调用"""
        # Mock service返回
        mock_charge.return_value = {
            "order_id": "12345",
            "status": "success",
            "amount": 1000,
            "message": "充值成功"
        }
        
        # 发送请求
        response = client.post(
            "/internal/benefit/product/TEST001/charge",
            json={
                "out_order_id": "test_order_001",
                "account": "<EMAIL>"
            },
            headers={"Channel": "test_channel"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["order_id"] == "12345"
        assert data["data"]["status"] == "success"
        
        # 验证service调用
        mock_charge.assert_called_once_with(
            "TEST001",
            {"out_order_id": "test_order_001", "account": "<EMAIL>"},
            "test_channel"
        )
    
    def test_charge_product_api_missing_channel(self):
        """测试缺少Channel header"""
        response = client.post(
            "/internal/benefit/product/TEST001/charge",
            json={
                "out_order_id": "test_order_001",
                "account": "<EMAIL>"
            }
            # 不传Channel header
        )
        
        assert response.status_code == 422  # 参数验证失败
    
    def test_charge_product_api_invalid_payload(self):
        """测试无效的请求参数"""
        response = client.post(
            "/internal/benefit/product/TEST001/charge",
            json={
                "out_order_id": "test_order_001"
                # 缺少account字段
            },
            headers={"Channel": "test_channel"}
        )
        
        assert response.status_code == 422
    
    @patch('domains.benefits.services.product.BenefitsProductService.get_product_by_code')
    def test_get_product_detail_api_success(self, mock_get_product):
        """测试获取产品详情API"""
        # Mock service返回
        mock_get_product.return_value = {
            "product_code": "TEST001",
            "product_name": "测试产品",
            "price": 1000,
            "description": "测试产品描述",
            "status": "active",
            "supplier": "shinesun"
        }
        
        # 发送请求
        response = client.get(
            "/internal/benefit/product/TEST001",
            headers={"Channel": "test_channel"}
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["product_code"] == "TEST001"
        assert data["data"]["product_name"] == "测试产品"
    
    @patch('domains.benefits.services.product.BenefitsProductService.handle_shinesun_callback')
    def test_shinesun_callback_api_success(self, mock_callback):
        """测试回调API"""
        # Mock service
        mock_callback.return_value = None
        
        # 发送请求
        response = client.get(
            "/internal/benefit/callback/shinesun/order",
            params={
                "order_id": "12345",
                "status": 1,
                "message": "充值成功"
            }
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["status"] == "success"
        
        # 验证service调用
        mock_callback.assert_called_once()
```

## 📡 接口兼容性测试

### 1. API兼容性测试脚本

#### 充值接口测试 (`tests/compatibility/test_charge_api.sh`)

```bash
#!/bin/bash
# 充值接口兼容性测试

API_BASE_URL="http://localhost:5008"
TEST_CHANNEL="test_channel"
TEST_PRODUCT="TEST001"

echo "=== 权益充值接口兼容性测试 ==="

# 测试1: 成功充值
echo "测试1: 成功充值"
response=$(curl -s -X POST \
  "${API_BASE_URL}/internal/benefit/product/${TEST_PRODUCT}/charge" \
  -H "Channel: ${TEST_CHANNEL}" \
  -H "Content-Type: application/json" \
  -d '{
    "out_order_id": "test_'$(date +%s)'",
    "account": "<EMAIL>"
  }')

echo "响应: $response"

# 检查响应格式
if echo "$response" | jq -e '.code == 0 and .data.order_id and .data.status' > /dev/null; then
  echo "✅ 充值接口格式正确"
else
  echo "❌ 充值接口格式错误"
  exit 1
fi

# 测试2: 产品详情
echo "测试2: 产品详情查询"
response=$(curl -s -X GET \
  "${API_BASE_URL}/internal/benefit/product/${TEST_PRODUCT}" \
  -H "Channel: ${TEST_CHANNEL}")

echo "响应: $response"

# 检查响应格式
if echo "$response" | jq -e '.code == 0 and .data.product_code and .data.product_name' > /dev/null; then
  echo "✅ 产品详情接口格式正确"
else
  echo "❌ 产品详情接口格式错误"
  exit 1
fi

# 测试3: 回调接口
echo "测试3: 回调接口"
response=$(curl -s -X GET \
  "${API_BASE_URL}/internal/benefit/callback/shinesun/order?order_id=test123&status=1")

echo "响应: $response"

# 检查响应格式
if echo "$response" | jq -e '.code == 0 and .data.status == "success"' > /dev/null; then
  echo "✅ 回调接口格式正确"
else
  echo "❌ 回调接口格式错误"
  exit 1
fi

echo "=== 所有兼容性测试通过 ==="
```

### 2. Node服务集成测试

#### Node端测试脚本 (`tests/compatibility/node_integration_test.js`)

```javascript
// Node服务集成测试
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5008';
const TEST_CONFIG = {
  channel: 'node_test_channel',
  productCode: 'TEST001',
  timeout: 5000
};

async function testChargeAPI() {
  console.log('=== Node服务集成测试: 充值接口 ===');
  
  try {
    const response = await axios.post(
      `${API_BASE_URL}/internal/benefit/product/${TEST_CONFIG.productCode}/charge`,
      {
        out_order_id: `node_test_${Date.now()}`,
        account: '<EMAIL>'
      },
      {
        headers: {
          'Channel': TEST_CONFIG.channel,
          'Content-Type': 'application/json'
        },
        timeout: TEST_CONFIG.timeout
      }
    );
    
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    // 验证响应格式
    if (response.data.code === 0 && response.data.data.order_id) {
      console.log('✅ 充值接口测试通过');
      return response.data.data.order_id;
    } else {
      console.log('❌ 充值接口响应格式错误');
      return null;
    }
  } catch (error) {
    console.log('❌ 充值接口调用失败:', error.message);
    return null;
  }
}

async function testProductDetailAPI() {
  console.log('=== Node服务集成测试: 产品详情接口 ===');
  
  try {
    const response = await axios.get(
      `${API_BASE_URL}/internal/benefit/product/${TEST_CONFIG.productCode}`,
      {
        headers: {
          'Channel': TEST_CONFIG.channel
        },
        timeout: TEST_CONFIG.timeout
      }
    );
    
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    // 验证响应格式
    if (response.data.code === 0 && response.data.data.product_code) {
      console.log('✅ 产品详情接口测试通过');
      return true;
    } else {
      console.log('❌ 产品详情接口响应格式错误');
      return false;
    }
  } catch (error) {
    console.log('❌ 产品详情接口调用失败:', error.message);
    return false;
  }
}

async function runIntegrationTests() {
  console.log('开始Node服务集成测试...\n');
  
  const results = {
    charge: await testChargeAPI(),
    productDetail: await testProductDetailAPI()
  };
  
  console.log('\n=== 测试结果汇总 ===');
  console.log('充值接口:', results.charge ? '✅ 通过' : '❌ 失败');
  console.log('产品详情接口:', results.productDetail ? '✅ 通过' : '❌ 失败');
  
  if (results.charge && results.productDetail) {
    console.log('\n🎉 所有集成测试通过，Node服务可以正常调用新接口');
    process.exit(0);
  } else {
    console.log('\n💥 集成测试失败，需要检查接口兼容性');
    process.exit(1);
  }
}

// 运行测试
runIntegrationTests().catch(error => {
  console.error('测试运行出错:', error);
  process.exit(1);
});
```

## ⚡ 性能测试

### 1. 基准性能测试

#### Locust性能测试 (`tests/performance/locustfile.py`)

```python
from locust import HttpUser, task, between
import json
import time

class BenefitsAPIUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """测试开始前的设置"""
        self.headers = {
            "Channel": "perf_test_channel",
            "Content-Type": "application/json"
        }
    
    @task(3)
    def test_charge_product(self):
        """测试充值接口性能"""
        payload = {
            "out_order_id": f"perf_test_{int(time.time() * 1000)}",
            "account": "<EMAIL>"
        }
        
        with self.client.post(
            "/internal/benefit/product/TEST001/charge",
            json=payload,
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    response.success()
                else:
                    response.failure(f"API返回错误: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(2)
    def test_get_product_detail(self):
        """测试产品详情接口性能"""
        with self.client.get(
            "/internal/benefit/product/TEST001",
            headers=self.headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    response.success()
                else:
                    response.failure(f"API返回错误: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(1)
    def test_callback_endpoint(self):
        """测试回调接口性能"""
        params = {
            "order_id": f"perf_test_{int(time.time())}",
            "status": 1,
            "message": "performance test"
        }
        
        with self.client.get(
            "/internal/benefit/callback/shinesun/order",
            params=params,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    response.success()
                else:
                    response.failure(f"API返回错误: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
```

#### 性能测试运行脚本 (`tests/performance/run_performance_test.sh`)

```bash
#!/bin/bash
# 性能测试运行脚本

echo "=== API性能测试 ==="
echo "目标服务: http://localhost:5008"
echo "测试时间: 60秒"
echo "并发用户: 50"

# 启动Locust性能测试
locust -f tests/performance/locustfile.py \
       --host=http://localhost:5008 \
       --users=50 \
       --spawn-rate=5 \
       --run-time=60s \
       --headless \
       --html=reports/performance_report.html

echo "性能测试完成，报告生成: reports/performance_report.html"
```

## 📊 测试报告和验收

### 1. 测试覆盖率报告

#### 生成覆盖率报告脚本 (`scripts/generate_coverage_report.sh`)

```bash
#!/bin/bash
# 生成测试覆盖率报告

echo "=== 运行测试并生成覆盖率报告 ==="

# 运行所有测试
poetry run pytest \
  --cov=apis.internal \
  --cov=domains.benefits.services \
  --cov-report=html:htmlcov \
  --cov-report=term-missing \
  --cov-report=xml:coverage.xml \
  --cov-fail-under=80

echo "覆盖率报告生成完成:"
echo "- HTML报告: htmlcov/index.html"
echo "- XML报告: coverage.xml"
```

### 2. 验收标准检查清单

#### 功能验收 (`tests/acceptance/acceptance_checklist.md`)

```markdown
# 功能验收检查清单

## API接口验收

### 充值接口 (`POST /internal/benefit/product/{code}/charge`)
- [ ] URL路径与老接口完全一致
- [ ] 请求参数格式兼容 (out_order_id, account)
- [ ] Channel header正确处理
- [ ] 响应格式兼容 (order_id, status, amount)
- [ ] 错误处理正确 (404, 400, 403)
- [ ] 响应时间 < 200ms (95th percentile)

### 产品详情接口 (`GET /internal/benefit/product/{code}`)
- [ ] URL路径与老接口完全一致
- [ ] Channel header正确处理
- [ ] 响应格式兼容 (product_code, product_name, price等)
- [ ] 权限检查正确
- [ ] 响应时间 < 100ms (95th percentile)

### 回调接口 (`GET /internal/benefit/callback/shinesun/order`)
- [ ] URL路径与老接口完全一致
- [ ] 参数格式兼容 (order_id, status, message)
- [ ] 回调处理逻辑正确
- [ ] 消息队列发送正确
- [ ] 响应时间 < 150ms (95th percentile)

## 业务逻辑验收

- [ ] 所有供应商类型支持 (biforst, shinesun, taopiaopiao, eleme_union)
- [ ] 充值策略工厂正常工作
- [ ] 消息队列发送和消费正常
- [ ] 数据库操作正确 (订单创建和更新)
- [ ] 异常处理完整 (产品不存在、权限不足等)

## 性能验收

- [ ] 单接口TPS ≥ 100 req/s
- [ ] 混合场景TPS ≥ 200 req/s  
- [ ] 响应时间不超过老系统110%
- [ ] 错误率 < 0.1%
- [ ] 内存使用不超过老系统120%

## Node服务集成验收

- [ ] Node服务无需修改任何代码
- [ ] 所有现有调用场景正常
- [ ] 充值成功率保持 ≥ 99.9%
- [ ] 第三方回调100%正常处理
```

## 🚀 自动化测试流程

### 1. CI/CD集成

#### GitHub Actions工作流 (`.github/workflows/migration_test.yml`)

```yaml
name: Migration Tests

on:
  push:
    paths:
      - 'apis/internal/**'
      - 'domains/benefits/services/**'
  pull_request:
    paths:
      - 'apis/internal/**'
      - 'domains/benefits/services/**'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test123
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      redis:
        image: redis:6
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
    
    - name: Install dependencies
      run: poetry install
    
    - name: Run unit tests
      run: |
        poetry run pytest tests/unit/ -v --cov=domains.benefits.services
    
    - name: Run integration tests
      run: |
        poetry run pytest tests/integration/ -v --cov=apis.internal
    
    - name: Run compatibility tests
      run: |
        bash tests/compatibility/test_charge_api.sh
    
    - name: Generate coverage report
      run: |
        poetry run pytest --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### 2. 测试执行脚本

#### 完整测试套件 (`scripts/run_all_tests.sh`)

```bash
#!/bin/bash
# 运行完整测试套件

set -e

echo "=== Django到FastAPI迁移 - 完整测试套件 ==="

# 1. 单元测试
echo "1. 运行单元测试..."
poetry run pytest tests/unit/ -v --tb=short

# 2. 集成测试
echo "2. 运行集成测试..."
poetry run pytest tests/integration/ -v --tb=short

# 3. API兼容性测试
echo "3. 运行API兼容性测试..."
bash tests/compatibility/test_charge_api.sh

# 4. Node服务集成测试
echo "4. 运行Node服务集成测试..."
node tests/compatibility/node_integration_test.js

# 5. 性能测试
echo "5. 运行性能测试..."
bash tests/performance/run_performance_test.sh

# 6. 生成最终报告
echo "6. 生成测试报告..."
bash scripts/generate_coverage_report.sh

echo "=== 所有测试完成 ==="
echo "查看详细报告:"
echo "- 覆盖率报告: htmlcov/index.html"
echo "- 性能报告: reports/performance_report.html"
```

这个测试验证方案确保了迁移过程的每个环节都有充分的测试覆盖，保证功能正确性和性能不退化。