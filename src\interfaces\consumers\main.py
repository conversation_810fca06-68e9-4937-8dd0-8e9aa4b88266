# encoding: utf-8
# src/interfaces/consumers/main.py
# created: 2025-07-24 00:05:11

import asyncio
from contextlib import asynccontextmanager
from typing import TYPE_CHECKING

from src.infrastructures.logger import setup

if TYPE_CHECKING:
    from src.containers import Container


@asynccontextmanager
async def lifespan(container: "Container"):
    """上下文管理器，用于初始化和清理资源"""
    # 初始化日志系统
    setup(
        {
            "app_name": "consumer",
            "sinks": [
                {"type": "console", "level": "INFO"},
                {"type": "file", "level": "DEBUG", "path": "logs/consumer.log"},
            ],
        }
    )

    # 初始化资源
    await container.infrastructures.init_tortoise()

    yield container

    # 清理资源
    from tortoise import Tortoise

    await Tortoise.close_connections()


async def main(container: "Container"):
    """主函数，接收容器参数"""
    async with lifespan(container) as active_container:
        active_container.wire(packages=["src.interfaces.consumers"])  # 注册依赖

        # 创建消费者管理器，支持配置预取数量
        consumer_mgr = active_container.infrastructures.rabbitmq_manager()

        # 获取消费者工厂容器
        consumers = active_container.consumers()

        # 根据业务重要性和处理复杂度配置不同的实例数量
        # 权益相关消费者 - 使用工厂函数
        consumer_mgr.register_consumer(consumers.union_sku_sync_consumer, instances=2, name="UnionSKUSyncConsumer")
        consumer_mgr.register_consumer(consumers.sku_charge_consumer, instances=5, name="SkuChargeConsumer")
        consumer_mgr.register_consumer(consumers.sku_charge_check_consumer, instances=10, name="SkuChargeCheckConsumer")
        consumer_mgr.register_consumer(
            consumers.union_purchase_ticket_sync_consumer, instances=2, name="UnionPurchaseTicketSyncConsumer"
        )
        consumer_mgr.register_consumer(
            consumers.benefits_order_notice_consumer, instances=10, name="BenefitsOrderNoticeConsumer"
        )
        consumer_mgr.register_consumer(
            consumers.product_order_export_consumer, instances=1, name="ProductOrderExportConsumer"
        )

        # 配送相关消费者 - 使用工厂函数
        consumer_mgr.register_consumer(consumers.union_order_sync_consumer, instances=10, name="UnionOrderSyncConsumer")
        consumer_mgr.register_consumer(consumers.sync_eleme_order_consumer, instances=2, name="SyncElemeOrderConsumer")
        consumer_mgr.register_consumer(
            consumers.union_order_notify_consumer, instances=1, name="UnionOrderNotifyConsumer"
        )

        # 启动消费者
        await consumer_mgr.start_consumer()


if __name__ == "__main__":
    # 本地测试时使用
    from src.containers import Container
    from src.infrastructures.settings import BaseServiceSettings

    container = Container()
    container.config.from_pydantic(BaseServiceSettings())
    asyncio.run(main(container))
