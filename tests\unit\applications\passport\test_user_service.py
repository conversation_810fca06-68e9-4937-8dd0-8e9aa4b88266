# encoding: utf-8
# tests/unit/applications/passport/test_user_service.py
# created: 2025-08-02 11:15:00

"""UserService 单元测试"""

from unittest.mock import MagicMock, patch

import pytest

from src.applications.passport.dto import UserInfoDTO, UserRelationDTO
from src.applications.passport.errors import (
    AppNotFoundError,
    TenantNotFoundError,
    UserNotFoundError,
    UserTenantRelationNotFoundError,
)
from src.applications.passport.services.user import UserService
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from tests.unit.applications.passport.base import BasePassportUnitTest
from tests.unit.applications.passport.factories import (
    AppFactory,
    TenantFactory,
    UserFactory,
)

# Rebuild models to handle forward references
UserEntity.model_rebuild()
AppEntity.model_rebuild()
TenantEntity.model_rebuild()


class TestUserService(BasePassportUnitTest):
    """测试 UserService 的用户管理功能"""

    @pytest.fixture
    def user_service(
        self,
        mock_user_repository,
        mock_app_repository,
        mock_tenant_repository,
    ):
        """创建 UserService 实例"""
        return UserService(
            user_repository=mock_user_repository,
            app_repository=mock_app_repository,
            tenant_repository=mock_tenant_repository,
        )

    @pytest.fixture
    def sample_user(self):
        """创建示例用户"""
        return UserFactory.create_user_entity()

    @pytest.fixture
    def sample_app(self):
        """创建示例应用"""
        return AppFactory.create_app_entity()

    @pytest.fixture
    def sample_tenant(self, sample_app):
        """创建示例租户"""
        return TenantFactory.create_tenant_entity(app=sample_app)

    @pytest.fixture
    def sample_user_model(self):
        """创建示例用户模型"""
        return UserFactory.create_user_model()

    @pytest.fixture
    def sample_app_model(self):
        """创建示例应用模型"""
        return AppFactory.create_app_model()

    @pytest.fixture
    def sample_tenant_model(self):
        """创建示例租户模型"""
        return TenantFactory.create_tenant_model()

    # 用户租户切换测试
    @pytest.mark.asyncio
    async def test_switch_user_tenant_success(
        self, user_service, mock_user_repository, sample_user, sample_app, sample_tenant_model
    ):
        """测试用户切换租户成功"""
        tenant_id = "test_tenant_001"

        # Mock 用户关系
        mock_relation = MagicMock()
        mock_relation.tenant = sample_tenant_model
        mock_user_repository.get_user_relation.return_value = mock_relation

        with patch.object(TenantEntity, "from_model") as mock_from_model:
            mock_tenant = TenantFactory.create_tenant_entity()
            mock_from_model.return_value = mock_tenant

            result = await user_service.switch_user_tenant(sample_user, sample_app, tenant_id)

            assert isinstance(result, UserInfoDTO)
            assert result.uid == sample_user.uid
            assert sample_user.current_tenant == mock_tenant
            mock_user_repository.get_user_relation.assert_called_once_with(sample_user, sample_app, tenant_id)
            mock_from_model.assert_called_once_with(sample_tenant_model)

    @pytest.mark.asyncio
    async def test_switch_user_tenant_relation_not_found(
        self, user_service, mock_user_repository, sample_user, sample_app
    ):
        """测试用户切换租户时关系不存在"""
        tenant_id = "nonexistent_tenant"
        mock_user_repository.get_user_relation.return_value = None

        with pytest.raises(UserTenantRelationNotFoundError):
            await user_service.switch_user_tenant(sample_user, sample_app, tenant_id)

    # 用户关系管理测试
    @pytest.mark.asyncio
    async def test_create_relation_success(
        self,
        user_service,
        mock_user_repository,
        mock_app_repository,
        mock_tenant_repository,
        sample_user_model,
        sample_app_model,
        sample_tenant_model,
    ):
        """测试创建用户关系成功"""
        uid = "test_uid"
        app_id = "test_app"
        tenant_id = "test_tenant"

        mock_user_repository.get_by_uid.return_value = sample_user_model
        mock_app_repository.get_by_appid.return_value = sample_app_model
        mock_tenant_repository.get_by_tenant_id.return_value = sample_tenant_model

        result = await user_service.create_relation(uid, app_id, tenant_id)

        assert isinstance(result, UserRelationDTO)
        assert result.uid == uid
        assert result.app_id == app_id
        assert result.tenant_id == tenant_id

        mock_user_repository.get_or_create_user_relation.assert_called_once_with(
            sample_user_model, sample_app_model.app_id, sample_tenant_model.tenant_id
        )

    @pytest.mark.asyncio
    async def test_create_relation_user_not_found(
        self, user_service, mock_user_repository, mock_app_repository, mock_tenant_repository
    ):
        """测试创建用户关系时用户不存在"""
        uid = "nonexistent_user"
        app_id = "test_app"
        tenant_id = "test_tenant"

        mock_user_repository.get_by_uid.return_value = None
        mock_app_repository.get_by_appid.return_value = AppFactory.create_app_model()
        mock_tenant_repository.get_by_tenant_id.return_value = TenantFactory.create_tenant_model()

        with pytest.raises(UserNotFoundError):
            await user_service.create_relation(uid, app_id, tenant_id)

    @pytest.mark.asyncio
    async def test_create_relation_app_not_found(
        self, user_service, mock_user_repository, mock_app_repository, mock_tenant_repository
    ):
        """测试创建用户关系时应用不存在"""
        uid = "test_uid"
        app_id = "nonexistent_app"
        tenant_id = "test_tenant"

        mock_user_repository.get_by_uid.return_value = UserFactory.create_user_model()
        mock_app_repository.get_by_appid.return_value = None
        mock_tenant_repository.get_by_tenant_id.return_value = TenantFactory.create_tenant_model()

        with pytest.raises(AppNotFoundError):
            await user_service.create_relation(uid, app_id, tenant_id)

    @pytest.mark.asyncio
    async def test_create_relation_tenant_not_found(
        self, user_service, mock_user_repository, mock_app_repository, mock_tenant_repository
    ):
        """测试创建用户关系时租户不存在"""
        uid = "test_uid"
        app_id = "test_app"
        tenant_id = "nonexistent_tenant"

        mock_user_repository.get_by_uid.return_value = UserFactory.create_user_model()
        mock_app_repository.get_by_appid.return_value = AppFactory.create_app_model()
        mock_tenant_repository.get_by_tenant_id.return_value = None

        with pytest.raises(TenantNotFoundError):
            await user_service.create_relation(uid, app_id, tenant_id)

    @pytest.mark.asyncio
    async def test_remove_user_relation(self, user_service, mock_user_repository):
        """测试删除用户关系"""
        uid = "test_uid"
        app_id = "test_app"
        tenant_id = "test_tenant"

        result = await user_service.remove_user_relation(uid, app_id, tenant_id)

        assert result is None
        mock_user_repository.remove_user_relation.assert_called_once_with(uid, app_id, tenant_id)

    # 用户资料更新测试
    @pytest.mark.asyncio
    async def test_update_user_profile_full_update(self, user_service, mock_user_repository, sample_user):
        """测试完整更新用户资料"""
        new_nickname = "新昵称"
        new_avatar = "http://new-avatar.url"
        new_email = "<EMAIL>"

        result = await user_service.update_user_profile(
            sample_user,
            nickname=new_nickname,
            avatar_url=new_avatar,
            email=new_email,
        )

        assert isinstance(result, UserInfoDTO)
        assert sample_user.nickname == new_nickname
        assert sample_user.avatar_url == new_avatar
        assert sample_user.email == new_email
        mock_user_repository.save.assert_called_once_with(sample_user)

    @pytest.mark.asyncio
    async def test_update_user_profile_partial_update(self, user_service, mock_user_repository, sample_user):
        """测试部分更新用户资料"""
        original_nickname = sample_user.nickname
        original_avatar = sample_user.avatar_url
        new_email = "<EMAIL>"

        result = await user_service.update_user_profile(
            sample_user,
            email=new_email,
        )

        assert isinstance(result, UserInfoDTO)
        assert sample_user.nickname == original_nickname  # 未更新
        assert sample_user.avatar_url == original_avatar  # 未更新
        assert sample_user.email == new_email  # 已更新
        mock_user_repository.save.assert_called_once_with(sample_user)

    @pytest.mark.asyncio
    async def test_update_user_profile_no_update(self, user_service, mock_user_repository, sample_user):
        """测试不更新任何字段"""
        original_nickname = sample_user.nickname
        original_avatar = sample_user.avatar_url
        original_email = sample_user.email

        result = await user_service.update_user_profile(sample_user)

        assert isinstance(result, UserInfoDTO)
        assert sample_user.nickname == original_nickname
        assert sample_user.avatar_url == original_avatar
        assert sample_user.email == original_email
        mock_user_repository.save.assert_called_once_with(sample_user)
