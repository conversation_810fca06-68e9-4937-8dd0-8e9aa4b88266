# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/openapi/routers/account.py
# created: 2024-12-08 10:01:59
# updated: 2025-04-10 00:23:54

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

from src.containers import Container
from src.interfaces.http.openapi.authorization import openapi_authentication
from src.interfaces.http.openapi.schemas import AccountBalanceResponse

if TYPE_CHECKING:
    from src.applications.openapi.queries import CustomerQueryService
    from src.domains.customer.entities import CustomerEntity

router = APIRouter(tags=["account"])


@router.get("/balance", name="获取账户余额", response_model=AccountBalanceResponse)
@inject
async def get_account_balance(
    customer: "CustomerEntity" = Depends(openapi_authentication),
    customer_query_service: "CustomerQueryService" = Depends(
        Provide[Container.applications.openapi_customer_query_service]
    ),
):
    account = await customer_query_service.get_account_info(customer)
    return AccountBalanceResponse(data=account)
