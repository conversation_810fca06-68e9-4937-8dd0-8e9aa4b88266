# encoding: utf-8
# tests/unit/applications/openapi/commands/delivery/test_order_notify.py
# created: 2025-08-19 13:00:00

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from aiohttp import ClientError

from src.applications.openapi.commands.delivery import OrderNotifyCommandService
from src.applications.openapi.commands.delivery.order_notify import OrderChannel
from src.databases.models.customer import CustomerSubscribeType
from src.domains.customer.entities import CustomerEntity
from src.domains.delivery.messages import UnionOrderInfoDTO


@pytest.fixture
def mock_config():
    """模拟配置对象"""
    config = MagicMock()
    config.thirdpart.dingtalk.order_notify_url = "http://dingtalk.example.com/notify"
    return config


@pytest.fixture
def mock_customer_query_service():
    """模拟客户查询服务"""
    return AsyncMock()


@pytest.fixture
def mock_customer_benefit_service():
    """模拟客户权益服务"""
    return AsyncMock()


@pytest.fixture
def mock_eleme_gateway():
    """模拟饿了么网关"""
    return AsyncMock()


@pytest.fixture
def order_notify_service(
    mock_config,
    mock_customer_query_service,
    mock_customer_benefit_service,
    mock_eleme_gateway,
):
    """创建订单通知服务实例"""
    return OrderNotifyCommandService(
        config=mock_config,
        customer_query_service=mock_customer_query_service,
        customer_benefit_service=mock_customer_benefit_service,
        eleme_union_delivery_gateway=mock_eleme_gateway,
    )


@pytest.fixture
def sample_order():
    """创建示例订单"""
    return UnionOrderInfoDTO(
        parent_order_id="123456",
        biz_order_id="BIZ123456",
        order_state=1,
        settle_state=1,
        ad_zone_name="test_zone",
        sid="SID:test",
        category_name="餐饮",
        shop_name="测试商店",
        pay_amount=100.0,
        settle_amount=90.0,
        tk_create_time=datetime(2025, 8, 19, 10, 0, 0),
        pay_time=datetime(2025, 8, 19, 10, 30, 0),
        receive_time=datetime(2025, 8, 19, 11, 0, 0),
    )


class TestOrderNotifyCommandService:
    """订单通知命令服务测试"""

    @pytest.mark.asyncio
    async def test_determine_order_channel_benefits(self, order_notify_service, sample_order):
        """测试判断权益订单渠道"""
        sample_order.ad_zone_name = None
        channel = order_notify_service._determine_order_channel(sample_order)
        assert channel == OrderChannel.BENEFITS

    @pytest.mark.asyncio
    async def test_determine_order_channel_dingtalk(self, order_notify_service, sample_order):
        """测试判断钉钉订单渠道"""
        sample_order.ad_zone_name = "dingtalk_zone"
        channel = order_notify_service._determine_order_channel(sample_order)
        assert channel == OrderChannel.DINGTALK

    @pytest.mark.asyncio
    async def test_determine_order_channel_customer(self, order_notify_service, sample_order):
        """测试判断客户订单渠道"""
        sample_order.ad_zone_name = "customer_zone"
        channel = order_notify_service._determine_order_channel(sample_order)
        assert channel == OrderChannel.CUSTOMER

    @pytest.mark.asyncio
    async def test_build_base_order_data(self, order_notify_service, sample_order):
        """测试构建基础订单数据"""
        data = order_notify_service._build_base_order_data(sample_order)
        
        assert data["order_id"] == "BIZ123456"
        assert data["order_state"] == "1"
        assert data["category_name"] == "餐饮"
        assert data["shop_name"] == "测试商店"
        assert data["pay_amount"] == "100.0"
        assert data["settle_amount"] == "90.0"
        assert data["tk_create_time"] == "2025-08-19 10:00:00"
        assert data["pay_time"] == "2025-08-19 10:30:00"
        assert data["receive_time"] == "2025-08-19 11:00:00"

    @pytest.mark.asyncio
    async def test_decode_sid_info_with_sid_prefix(self, order_notify_service):
        """测试解析带SID前缀的SID信息"""
        order_notify_service.eleme_gateway.decode_sid.return_value = {
            "from_channel": "dingtalk",
            "dingtalk_open_id": "open123",
            "corp_id": "corp456",
        }
        
        result = await order_notify_service._decode_sid_info("SID:test_sid")
        
        assert result["from_channel"] == "dingtalk"
        assert result["dingtalk_open_id"] == "open123"
        assert result["corp_id"] == "corp456"
        order_notify_service.eleme_gateway.decode_sid.assert_called_once_with("SID:test_sid")

    @pytest.mark.asyncio
    async def test_decode_sid_info_old_format(self, order_notify_service):
        """测试解析旧格式的SID信息"""
        result = await order_notify_service._decode_sid_info("channel.openid.corpid")
        
        assert result["from_channel"] == "channel"
        assert result["dingtalk_open_id"] == "openid"
        assert result["corp_id"] == "corpid"

    @pytest.mark.asyncio
    async def test_decode_sid_info_empty(self, order_notify_service):
        """测试解析空SID信息"""
        result = await order_notify_service._decode_sid_info(None)
        assert result == {}

    @pytest.mark.asyncio
    async def test_process_dingtalk_order_not_settled(self, order_notify_service, sample_order):
        """测试处理未结算的钉钉订单"""
        sample_order.ad_zone_name = "dingtalk_zone"
        sample_order.settle_state = 0
        
        with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
            await order_notify_service._process_dingtalk_order(sample_order)
            mock_logger.info.assert_any_call("订单[123456]状态不是已结算，跳过处理")

    @pytest.mark.asyncio
    async def test_process_dingtalk_order_success(
        self, order_notify_service, sample_order, mock_config
    ):
        """测试成功处理钉钉订单"""
        sample_order.ad_zone_name = "dingtalk_zone"
        sample_order.settle_state = 1
        
        order_notify_service.eleme_gateway.decode_sid.return_value = {
            "from_channel": "dingtalk",
            "dingtalk_open_id": "open123",
            "corp_id": "corp456",
        }
        
        with patch.object(order_notify_service, "_send_notification") as mock_send:
            await order_notify_service._process_dingtalk_order(sample_order)
            
            mock_send.assert_called_once()
            call_args = mock_send.call_args[0]
            order_data = call_args[0]
            notify_url = call_args[1]
            
            assert order_data["order_id"] == "123456"  # 钉钉使用parent_order_id
            assert order_data["user_from"] == "dingtalk"
            assert order_data["user_open_id"] == "open123"
            assert order_data["corp_id"] == "corp456"
            assert notify_url == "http://dingtalk.example.com/notify"

    @pytest.mark.asyncio
    async def test_process_benefits_order_no_customer(
        self, order_notify_service, sample_order, mock_customer_benefit_service
    ):
        """测试处理权益订单但找不到客户"""
        sample_order.ad_zone_name = None
        mock_customer_benefit_service.get_customer_by_supplier_order_id.return_value = None
        
        with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
            await order_notify_service._process_benefits_order(sample_order)
            mock_logger.warning.assert_any_call("权益订单[123456]未找到对应客户")

    @pytest.mark.asyncio
    async def test_process_benefits_order_success(
        self,
        order_notify_service,
        sample_order,
        mock_customer_benefit_service,
        mock_customer_query_service,
    ):
        """测试成功处理权益订单"""
        sample_order.ad_zone_name = None
        
        # 模拟客户
        mock_customer = MagicMock()
        mock_customer.id = 1
        mock_customer_benefit_service.get_customer_by_supplier_order_id.return_value = mock_customer
        
        # 模拟权益订单
        mock_benefits_order = MagicMock()
        mock_benefits_order.order_id = "BENEFIT123"
        mock_benefits_order.out_order_id = "OUT123"
        mock_benefits_order.product_code = "PROD001"
        mock_benefits_order.product_name = "测试产品"
        mock_benefits_order.price = 100.0
        mock_benefits_order.account = "<EMAIL>"
        mock_benefits_order.status = 1
        mock_benefits_order.created_at = datetime(2025, 8, 19, 9, 0, 0)
        mock_benefits_order.updated_at = datetime(2025, 8, 19, 10, 0, 0)
        mock_customer_benefit_service.get_order_by_order_id.return_value = mock_benefits_order
        
        # 模拟客户订阅
        mock_subscribe = MagicMock()
        mock_subscribe.url = "http://benefits.example.com/notify"
        mock_customer_query_service.get_customer_subscribe.return_value = mock_subscribe
        
        with patch.object(order_notify_service, "_send_notification") as mock_send:
            await order_notify_service._process_benefits_order(sample_order)
            
            mock_send.assert_called_once()
            call_args = mock_send.call_args[0]
            order_data = call_args[0]
            notify_url = call_args[1]
            
            assert "benefit_order" in order_data
            assert order_data["benefit_order"]["order_id"] == "BENEFIT123"
            assert order_data["benefit_order"]["product_code"] == "PROD001"
            assert notify_url == "http://benefits.example.com/notify"

    @pytest.mark.asyncio
    async def test_process_customer_order_not_settled(self, order_notify_service, sample_order):
        """测试处理未结算的客户订单"""
        sample_order.ad_zone_name = "customer_zone"
        sample_order.settle_state = 0
        
        with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
            await order_notify_service._process_customer_order(sample_order)
            mock_logger.info.assert_any_call("订单[123456]状态不是已结算，跳过处理")

    @pytest.mark.asyncio
    async def test_process_customer_order_no_customer_id(
        self, order_notify_service, sample_order
    ):
        """测试处理客户订单但SID中没有customer_id"""
        sample_order.ad_zone_name = "customer_zone"
        sample_order.settle_state = 1
        
        order_notify_service.eleme_gateway.decode_sid.return_value = {
            "from_channel": "customer",
        }
        
        with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
            await order_notify_service._process_customer_order(sample_order)
            mock_logger.error.assert_any_call("订单[BIZ123456]没有获取到customer_id")

    @pytest.mark.asyncio
    async def test_send_notification_empty_url(self, order_notify_service):
        """测试发送通知但URL为空"""
        with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
            await order_notify_service._send_notification({}, "")
            mock_logger.warning.assert_any_call("通知URL为空，跳过发送")

    @pytest.mark.asyncio
    async def test_send_notification_success(self, order_notify_service):
        """测试成功发送通知"""
        order_data = {"order_id": "123"}
        notify_url = "http://example.com/notify"
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = "OK"
        
        with patch("aiohttp.ClientSession") as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            await order_notify_service._send_notification(order_data, notify_url)
            
            mock_session.post.assert_called_once_with(notify_url, json=order_data)

    @pytest.mark.asyncio
    async def test_send_notification_server_error(self, order_notify_service):
        """测试发送通知时遇到服务器错误"""
        order_data = {"order_id": "123"}
        notify_url = "http://example.com/notify"
        
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = "Server Error"
        
        with patch("aiohttp.ClientSession") as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(ConnectionError, match="服务器错误"):
                await order_notify_service._send_notification(order_data, notify_url)

    @pytest.mark.asyncio
    async def test_send_notification_client_error(self, order_notify_service):
        """测试发送通知时遇到客户端错误"""
        order_data = {"order_id": "123"}
        notify_url = "http://example.com/notify"
        
        mock_response = AsyncMock()
        mock_response.status = 400
        mock_response.text.return_value = "Bad Request"
        
        with patch("aiohttp.ClientSession") as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            with patch("src.applications.openapi.commands.delivery.order_notify.logger") as mock_logger:
                await order_notify_service._send_notification(order_data, notify_url)
                mock_logger.error.assert_any_call(
                    "客户端错误，不重试，HTTP状态码: 400, 响应: Bad Request"
                )

    @pytest.mark.asyncio
    async def test_send_notification_network_error(self, order_notify_service):
        """测试发送通知时遇到网络错误"""
        order_data = {"order_id": "123"}
        notify_url = "http://example.com/notify"
        
        with patch("aiohttp.ClientSession") as mock_session_class:
            mock_session = AsyncMock()
            mock_session_class.return_value.__aenter__.return_value = mock_session
            mock_session.post.side_effect = ClientError("Connection failed")
            
            with pytest.raises(ConnectionError, match="网络请求失败"):
                await order_notify_service._send_notification(order_data, notify_url)

    @pytest.mark.asyncio
    async def test_cleanup(self, order_notify_service):
        """测试清理资源"""
        mock_session = AsyncMock()
        mock_session.closed = False
        order_notify_service._http_session = mock_session
        
        await order_notify_service.cleanup()
        
        mock_session.close.assert_called_once()
        assert order_notify_service._http_session is None

    @pytest.mark.asyncio
    async def test_notify_order_complete_flow(self, order_notify_service, sample_order):
        """测试完整的订单通知流程"""
        sample_order.ad_zone_name = "dingtalk_zone"
        
        with patch.object(order_notify_service, "_process_dingtalk_order") as mock_process:
            await order_notify_service.notify_order(sample_order)
            mock_process.assert_called_once_with(sample_order)