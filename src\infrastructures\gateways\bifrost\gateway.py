# encoding: utf-8
# src/infrastructures/gateways/bifrost/gateway.py
# created: 2025-07-30 10:16:27

import json
import time
import urllib.parse
import uuid
from typing import Union

from loguru import logger

from src.infrastructures.openapi import OpenApiClientBase
from src.utils.rsa_util import RSAUtil

from .errors import BifrostBusinessError, BifrostError, BifrostSystemError
from .schemas import RightsApplyResult, RightsGrantResult, RightsQueryResult
from .settings import BifrostSettings


class BifrostGateway(OpenApiClientBase):
    """饿了么权益发放SDK网关"""

    ERROR_MESSAGES = {
        "0000": "请求接收成功",
        "1001": "非法渠道/合作方",
        "1002": "解密失败",
        "1003": "签名错误",
        "1004": "请求已过期",
        "1005": "接口已下线",
        "2001": "partnerId为空",
        "2002": "reqId为空",
        "2003": "data为空",
        "2004": "sign为空",
        "2005": "timestamp为空",
        "2101": "外部交易流水号为空",
        "2102": "外部交易流水号长度超过32",
        "2103": "手机号为空",
        "2104": "手机号长度超过20",
        "2105": "权益方案号为空",
        "3001": "权益方案号缺失",
        "3002": "权益方案号与渠道不匹配",
        "3003": "权益方案号暂不可用",
        "3004": "权益进件降级",
        "3005": "渠道降级",
        "3101": "重复的外部交易流水号",
        "3102": "无此外部交易流水",
        "3103": "现金发放预算不足",
        "3200": "其他原因申请失败",
        "4000": "内部系统异常,请稍后重试",
        "4001": "内部系统异常,可通过查询接口获取结果",
    }

    def __init__(self, config: Union[BifrostSettings, dict]):
        if isinstance(config, dict):
            config = BifrostSettings(**config)

        self.public_key = config.public_key
        self.private_key = config.private_key
        self.account = config.account
        self.secret_key = config.secret_key
        self.token = ""
        self.token_expire_time = 0
        self.prefix = config.prefix
        super().__init__(base_url=config.base_url)

    async def _async_refresh_token(self, refresh: bool = False) -> tuple[str, int]:
        """
        统一处理获取 & 更新 Token
        Args:
            refresh: 是否是更新 Token, False 表示首次获取, True 表示更新
        Returns:
            (token, token_expire_time)
        """
        endpoint = "author/updateToken" if refresh else "author/getToken"
        logger.info(f"获取或更新Token: {endpoint}, account: {self.account}, secret_key: {self.secret_key}")
        payload = {"account": self.account, "secretkey": self.secret_key}
        try:
            response = await self._async_request("POST", endpoint, payload=payload)
            if response.get("success") and response.get("resultCode") == "200":
                data = response.get("data", {})
                return data.get("token"), int(data.get("expireTime"))
            else:
                raise Exception(f"获取或更新Token失败: {response}")
        except Exception as e:
            logger.exception(f"获取Token失败: {e}")
            raise

    async def _async_get_token(self) -> str | None:
        """
        对外只提供此方法获取 Token
        1. 未持有 token 或已过期 → 先获取一次
        2. 若依然过期或失败 → 再尝试更新一次
        3. 返回可用 token
        """
        now_ts = int(time.time() * 1000)  # ms时间戳对比

        # 如果当前没有 token 或 token 已经过期，则刷新
        if not self.token or self.token_expire_time <= now_ts:
            try:
                self.token, self.token_expire_time = await self._async_refresh_token(refresh=False)
            except Exception as e:
                logger.exception(f"获取Token失败: {e}")
            finally:
                if not self.token or self.token_expire_time <= now_ts:
                    self.token, self.token_expire_time = await self._async_refresh_token(refresh=True)
        return self.token

    async def _async_get_headers(self) -> dict:
        """
        获取通用请求头，添加 Token
        """
        token = await self._async_get_token()
        return {"Content-Type": "application/json", "Author-Token": token}

    def _build_signed_payload(self, raw_data: dict) -> dict:
        """
        将原始业务数据进行：
        1. JSON化
        2. RSA加密
        3. 生成整体请求体(Sign 等)
        """
        plaintext = json.dumps(raw_data)
        encrypt_data = RSAUtil.encrypt(plaintext, self.public_key)
        request_body = {
            "reqId": uuid.uuid4().hex,
            "partnerId": "lihai",
            "data": encrypt_data,
            "timestamp": str(int(time.time() * 1000)),
        }

        sign_str = RSAUtil.to_query_string(request_body)
        sign = RSAUtil.sign(sign_str, self.private_key)
        request_body["sign"] = sign
        return request_body

    def _parse_bifrost_response(self, response: dict, method_name: str):
        """
        统一处理 Bifrost 接口返回，包括以下动作：
        1. 判断接口是否success
        2. 对 data.body 做解析
        3. 判断 respCode, 若在 SUCCESS_CODES 中再做解密
        4. 返回解密后的数据（如需要）
        """
        if not response.get("success"):
            logger.error(f"{method_name} 接口调用失败: {response.get('resultMsg')}, response: {response}")
            raise BifrostBusinessError("5000", f"{method_name} 接口调用失败: {response}")

        body = response.get("data", {}).get("body", {})
        if not body:
            raise Exception(f"{method_name} 接口返回数据不完整: {response}")
        return json.loads(body)

    def get_error_message(self, resp_code: str) -> str:
        """
        获取错误信息
        """
        return self.ERROR_MESSAGES.get(resp_code, "未知错误")

    async def apply_welfare(
        self, out_trade_no: str, mobile: str, welfare_case_no: str, with_prefix: bool = True
    ) -> RightsApplyResult:
        """
        权益进件接口
        """
        endpoint = "user/rightsGrant"
        raw_data = {
            "outTradeNo": f"{self.prefix}_{out_trade_no}" if with_prefix else out_trade_no,
            "mobile": mobile,
            "welfareCaseNo": welfare_case_no,
        }
        request_body = self._build_signed_payload(raw_data)
        headers = await self._async_get_headers()

        try:
            response = await self._async_request(
                method="POST",
                endpoint=endpoint,
                payload=request_body,
                headers=headers,
            )
            logger.info(f"apply_welfare response: {response}, request_data: {raw_data}, request_body: {request_body}")
            result = RightsApplyResult.model_validate(
                self._parse_bifrost_response(response, "apply_welfare"),
            )
            result.decrypt_with_key(self.private_key)
            return result
        except BifrostError:
            raise
        except Exception as e:
            logger.error(f"apply_welfare error: {e}")
            raise BifrostSystemError("5000", f"接口调用异常: {str(e)}") from e

    async def query_welfare(self, out_trade_no: str, with_prefix: bool = True) -> RightsQueryResult:
        """
        查询权益发放结果
        """
        endpoint = "user/rightsGrantQuery"
        raw_data = {
            "outTradeNo": f"{self.prefix}_{out_trade_no}" if with_prefix else out_trade_no,
        }
        request_body = self._build_signed_payload(raw_data)
        headers = await self._async_get_headers()

        try:
            response = await self._async_request(
                method="POST",
                endpoint=endpoint,
                payload=request_body,
                headers=headers,
            )
            logger.info(f"query_welfare response: {response}, request_data: {raw_data}, request_body: {request_body}")
            result = RightsQueryResult.model_validate(
                self._parse_bifrost_response(response, "query_welfare"),
            )
            result.decrypt_with_key(self.private_key)
            return result
        except Exception as e:
            logger.error(f"query_welfare error: {e}")
            raise BifrostSystemError("5000", f"接口调用异常: {str(e)}") from e

    async def apply_city_welfare(self, mobile: str, welfare_case_no: str, name: str = "福利红包") -> RightsGrantResult:
        endpoint = "user/userCityrights"
        data = {
            "name": name,
            "reqId": uuid.uuid4().hex,
            "userId": mobile,
            "activityId": welfare_case_no,
        }
        try:
            response = await self._async_request(
                method="POST", endpoint=endpoint, payload=data, headers=await self._async_get_headers()
            )
            if response.get("success") and response.get("resultCode") == "200":
                return RightsGrantResult(success=True, response=response)  # type: ignore
            else:
                logger.error(f"city_welfare error: {response.get('resultMsg', response['resultMsg'])}")
                return RightsGrantResult(success=False, response=response)  # type: ignore
        except Exception as e:
            logger.error(f"city_welfare error: {e}")
            raise BifrostSystemError("5000", f"接口调用异常: {str(e)}") from e

    async def query_orders(self, order_ids: list[str]):
        endpoint = "order/queryOrder"
        data = order_ids
        try:
            response = await self._async_request(
                method="POST", endpoint=endpoint, payload=data, headers=await self._async_get_headers()
            )
            logger.info(f"query_orders response: {response}, request_data: {data}")

            # 检查响应是否成功
            if not response:
                raise BifrostSystemError("5000", "接口返回空响应")

            if not response.get("success", False):
                result_code = response.get("resultCode", "UNKNOWN")
                result_msg = response.get("resultMsg", "未知错误")

                # 特殊处理未授权IP错误
                if result_code == "1003" and "未授权ip" in result_msg:
                    logger.error(f"Bifrost API 未授权IP访问: {result_msg}")
                    raise BifrostBusinessError(result_code, f"API访问被拒绝: {result_msg}")

                # 根据错误码判断是业务错误还是系统错误
                if result_code.startswith("3"):
                    raise BifrostBusinessError(result_code, result_msg)
                else:
                    raise BifrostSystemError(result_code, result_msg)

            return response
        except BifrostError:
            # 直接传递已知的Bifrost错误
            raise
        except Exception as e:
            logger.error(f"query_orders error: {e}")
            raise BifrostSystemError("5000", f"接口调用异常: {str(e)}") from e

    async def get_applet_short_link(self, page_url: str) -> str:
        endpoint = "shop/getAppletShortLink"
        data = {
            "token": "",
            "appId": "",
            "pageUrl": f"pages/container/index?q={urllib.parse.quote(page_url)}",
            "pageTitle": "饿了么",
            "isPermanent": False,  # 是否永久有效, 默认false
        }
        try:
            response = await self._async_request(
                method="POST", endpoint=endpoint, payload=data, headers=await self._async_get_headers()
            )
            logger.info(f"get_applet_short_link response: {response}")
            return response.get("data", {}).get("data", {}).get("link", "")
        except Exception as e:
            logger.error(f"get_applet_short_link error: {e}")
            return ""

    async def get_applet_schema(self, page_url: str) -> str:
        endpoint = "shop/getAppletScheme"
        data = {
            "token": "",
            "appId": "wxece3a9a4c82f58c9",
            "jumpWxa": {
                "path": "pages/container/index",
                "query": f"q={urllib.parse.quote(page_url)}",
            },
            "isExpire": True,  # 到期失效：True, 永久有效：False
            "expireType": 1,  # 失效时间: 0, 失效天数: 1
            "expireTime": 0,  # 失效时间Unix时间戳
            "expireInterval": 10,  # 失效天数
        }
        try:
            response = await self._async_request(
                method="POST", endpoint=endpoint, payload=data, headers=await self._async_get_headers()
            )

            # debug code
            logger.info(f"request_data: {data}")
            logger.info(f"get_applet_schema response: {response}")
            _tmp = response.get("data", {})
            logger.info(f"get_applet_schema response: {_tmp}")
            _tmp = _tmp.get("data", {})
            logger.info(f"get_applet_schema response: {_tmp}")

            return response.get("data", {}).get("data", {}).get("openlink", "")
        except Exception as e:
            logger.error(f"get_applet_schema error: {e}")
            return ""
