from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbStoreItemQueryRequest(BaseRequest):

    def __init__(self, store_id: str = None, biz_type: str = None, pid: str = None, sid: str = None):
        """
        门店ID
        """
        self._store_id = store_id
        """
            场景类型（"kb_natural";）
        """
        self._biz_type = biz_type
        """
            推广位
        """
        self._pid = pid
        """
            sid（申请权限后可用）
        """
        self._sid = sid

    @property
    def store_id(self):
        return self._store_id

    @store_id.setter
    def store_id(self, store_id):
        if isinstance(store_id, str):
            self._store_id = store_id
        else:
            raise TypeError("store_id must be str")

    @property
    def biz_type(self):
        return self._biz_type

    @biz_type.setter
    def biz_type(self, biz_type):
        if isinstance(biz_type, str):
            self._biz_type = biz_type
        else:
            raise TypeError("biz_type must be str")

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, sid):
        if isinstance(sid, str):
            self._sid = sid
        else:
            raise TypeError("sid must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.kb.store.item.query"

    def to_dict(self):
        request_dict = {}
        if self._store_id is not None:
            request_dict["store_id"] = convert_basic(self._store_id)

        if self._biz_type is not None:
            request_dict["biz_type"] = convert_basic(self._biz_type)

        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._sid is not None:
            request_dict["sid"] = convert_basic(self._sid)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
