from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeStorepromotionReviewbwcDiagnoseRequest(BaseRequest):

    def __init__(self, lock_id: int = None, order_id: str = None):
        """
        资格ID
        """
        self._lock_id = lock_id
        """
            饿了么订单ID
        """
        self._order_id = order_id

    @property
    def lock_id(self):
        return self._lock_id

    @lock_id.setter
    def lock_id(self, lock_id):
        if isinstance(lock_id, int):
            self._lock_id = lock_id
        else:
            raise TypeError("lock_id must be int")

    @property
    def order_id(self):
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        if isinstance(order_id, str):
            self._order_id = order_id
        else:
            raise TypeError("order_id must be str")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.storepromotion.reviewbwc.diagnose"

    def to_dict(self):
        request_dict = {}
        if self._lock_id is not None:
            request_dict["lock_id"] = convert_basic(self._lock_id)

        if self._order_id is not None:
            request_dict["order_id"] = convert_basic(self._order_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
