from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionOtherchannelGetRequest(BaseRequest):

    def __init__(self, other_promotion_link_request: object = None):
        """
        查询request对象
        """
        self._other_promotion_link_request = other_promotion_link_request

    @property
    def other_promotion_link_request(self):
        return self._other_promotion_link_request

    @other_promotion_link_request.setter
    def other_promotion_link_request(self, other_promotion_link_request):
        if isinstance(other_promotion_link_request, object):
            self._other_promotion_link_request = other_promotion_link_request
        else:
            raise TypeError("other_promotion_link_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.otherchannel.get"

    def to_dict(self):
        request_dict = {}
        if self._other_promotion_link_request is not None:
            request_dict["other_promotion_link_request"] = convert_struct(self._other_promotion_link_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionOtherchannelGetOtherPromotionLinkRequest:
    def __init__(self, type: int = None, sid: str = None):
        """
        链接类型 1 抖音schema
        """
        self.type = type
        """
            用户扩展id
        """
        self.sid = sid
