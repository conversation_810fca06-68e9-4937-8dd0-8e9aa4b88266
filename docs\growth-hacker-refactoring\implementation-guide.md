# 实施指南

## 🚀 快速启动指南

### 环境准备清单
```bash
# 1. 代码仓库准备
git checkout main
git pull origin main
git checkout -b refactor/growth-hacker-system

# 2. 开发环境检查
poetry --version  # 确保Poetry已安装
poetry install    # 安装项目依赖
poetry run pytest --version  # 确保测试环境正常

# 3. 代码质量工具检查
poetry run ruff --version    # 静态分析工具
poetry run mypy --version    # 类型检查工具  
poetry run black --version   # 代码格式化工具

# 4. 基础设施准备
docker-compose up -d redis postgres rabbitmq  # 启动依赖服务
```

### 团队角色分工确认
| 角色 | 姓名 | 主要职责 | 联系方式 |
|------|------|---------|----------|
| **项目负责人** | [待填写] | 整体协调、决策支持 | [待填写] |
| **架构师** | [待填写] | 架构设计、代码审查 | [待填写] |
| **高级开发A** | [待填写] | TaskService重构 | [待填写] |
| **高级开发B** | [待填写] | Interactor重构 | [待填写] |
| **测试工程师** | [待填写] | 测试策略、质量保证 | [待填写] |

## 📋 Phase 1: 核心重构实施 (5天)

### Day 1: 环境准备和TaskService设计

#### 上午任务 (9:00-12:00)
```bash
# 任务1: 创建新的服务文件结构
mkdir -p src/applications/growth_hacker/services/refactored
mkdir -p src/domains/growth_tasks/services/environment
mkdir -p src/domains/growth_tasks/services/lifecycle

# 任务2: 创建基础接口文件
touch src/applications/growth_hacker/services/refactored/task_orchestrator.py
touch src/domains/growth_tasks/services/environment/environment_preparer.py
touch src/domains/growth_tasks/services/lifecycle/task_lifecycle_manager.py
```

#### 实施步骤详解

##### 步骤1: TaskEnvironment数据类设计
```python
# 文件: src/domains/growth_tasks/entities/task_environment.py
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from src.databases.models.growth_hacker import UserDeviceCache
    from src.infrastructures.ip_proxy import IpProxy
    from src.infrastructures.browsers import BrowserSessionConfig
    from src.interfaces.growth_hacker.schemas import TaskMessageContent

@dataclass
class TaskEnvironment:
    """任务执行环境 - 封装任务执行所需的所有环境信息"""
    
    task: "TaskMessageContent"
    user_profile: Optional["UserDeviceCache"]
    ip_proxy: Optional["IpProxy"] 
    browser_session: "BrowserSessionConfig"
    created_at: datetime = field(default_factory=datetime.now)
    
    @property
    def has_proxy(self) -> bool:
        """是否有可用代理"""
        return self.ip_proxy is not None
    
    @property  
    def environment_summary(self) -> str:
        """环境摘要信息"""
        proxy_info = f"代理: {self.ip_proxy.identify}" if self.ip_proxy else "无代理"
        profile_info = f"档案: 有缓存" if self.user_profile else "档案: 无缓存"
        return f"任务环境 - {proxy_info}, {profile_info}"
```

##### 步骤2: EnvironmentPreparer实现
```python
# 文件: src/domains/growth_tasks/services/environment/environment_preparer.py
from typing import Optional, TYPE_CHECKING
from loguru import logger

from src.applications.growth_hacker.errors import NoIpProxyError
from src.domains.growth_tasks.entities.task_environment import TaskEnvironment
from src.infrastructures.browsers import (
    BrowserSessionConfig, DeviceConfig, WebViewConfig, ExtraData, 
    GeolocationData, ProxyConfig
)

if TYPE_CHECKING:
    from src.infrastructures.ip_proxy import ProxyManager, IpProxy
    from src.domains.growth_tasks.services import UserProfileDomainService
    from src.interfaces.growth_hacker.schemas import TaskMessageContent
    from src.databases.models.growth_hacker import UserDeviceCache

class EnvironmentPreparer:
    """环境准备器 - 负责任务执行前的环境准备和清理"""
    
    def __init__(
        self,
        proxy_manager: "ProxyManager",
        user_profile_service: "UserProfileDomainService"
    ):
        self.proxy_manager = proxy_manager
        self.user_profile_service = user_profile_service
    
    async def prepare(
        self, 
        task: "TaskMessageContent", 
        enable_none_proxy: bool = False
    ) -> TaskEnvironment:
        """准备任务执行环境
        
        Args:
            task: 任务消息内容
            enable_none_proxy: 是否允许无代理执行
            
        Returns:
            TaskEnvironment: 完整的任务执行环境
            
        Raises:
            NoIpProxyError: 需要代理但无法分配时
        """
        logger.info(f"开始准备任务环境: {task.task_id}")
        
        # 1. 获取用户档案
        user_profile = await self._get_user_profile(task.phone)
        logger.debug(f"用户档案: {'已获取' if user_profile else '无缓存'}")
        
        # 2. 分配IP代理
        ip_proxy = await self._allocate_proxy(task.city, enable_none_proxy)
        logger.debug(f"代理分配: {ip_proxy.identify if ip_proxy else '无代理'}")
        
        # 3. 构建浏览器会话配置
        browser_session = self._build_browser_session(
            task.lat, task.lng, user_profile, ip_proxy
        )
        
        environment = TaskEnvironment(
            task=task,
            user_profile=user_profile,
            ip_proxy=ip_proxy,
            browser_session=browser_session
        )
        
        logger.success(f"环境准备完成: {environment.environment_summary}")
        return environment
    
    async def cleanup(self, environment: TaskEnvironment, success: bool) -> None:
        """清理环境资源
        
        Args:
            environment: 需要清理的环境
            success: 任务执行是否成功
        """
        logger.info(f"开始清理环境资源: 成功={success}")
        
        if environment.ip_proxy:
            try:
                await self.proxy_manager.release(
                    environment.ip_proxy.identify,
                    environment.task.city,
                    success
                )
                logger.debug(f"代理释放完成: {environment.ip_proxy.identify}")
            except Exception as e:
                logger.error(f"代理释放失败: {e}")
        
        logger.info("环境清理完成")
    
    async def _get_user_profile(self, phone: str) -> Optional["UserDeviceCache"]:
        """获取用户档案"""
        try:
            return await self.user_profile_service.get_user_profile(phone)
        except Exception as e:
            logger.warning(f"获取用户档案失败: {e}")
            return None
    
    async def _allocate_proxy(
        self, 
        city: str, 
        enable_none_proxy: bool
    ) -> Optional["IpProxy"]:
        """分配IP代理"""
        try:
            ip_proxy = await self.proxy_manager.alloc(city)
            
            if not enable_none_proxy and not ip_proxy:
                raise NoIpProxyError()
                
            return ip_proxy
        except Exception as e:
            logger.error(f"代理分配失败: {e}")
            if not enable_none_proxy:
                raise
            return None
    
    def _build_browser_session(
        self,
        lat: str | float,
        lng: str | float,
        user_profile: Optional["UserDeviceCache"] = None,
        ip_proxy: Optional["IpProxy"] = None,
    ) -> BrowserSessionConfig:
        """构建浏览器会话配置"""
        
        # 构建设备配置
        device_config = None
        if user_profile and user_profile.device_config:
            device_config = DeviceConfig(**user_profile.device_config)
        
        # 构建WebView配置
        webview_config = None  
        if user_profile and user_profile.webview_config:
            webview_config = WebViewConfig(**user_profile.webview_config)
        
        # 构建代理配置
        proxy_config = None
        if ip_proxy:
            proxy_config = ProxyConfig(
                server=ip_proxy.server,
                username=ip_proxy.username,
                password=ip_proxy.password,
            )
        
        # 构建额外数据
        extra_data = ExtraData(
            geolocation=GeolocationData(
                latitude=float(lat),
                longitude=float(lng),
            ),
            local_storage=user_profile.local_storage if user_profile else {},
            session_storage=user_profile.session_storage if user_profile else {},
            cookies=user_profile.cookies if user_profile else [],
        )
        
        return BrowserSessionConfig(
            proxy=proxy_config,
            device_config=device_config,
            webview_config=webview_config,
            extra_data=extra_data,
        )
```

#### 下午任务 (14:00-18:00)
```bash
# 任务3: 实现TaskLifecycleManager
# 任务4: 编写对应的单元测试
# 任务5: 验证组件独立性
```

##### 步骤3: TaskLifecycleManager实现
```python
# 文件: src/domains/growth_tasks/services/lifecycle/task_lifecycle_manager.py
import time
from typing import Dict, TYPE_CHECKING
from loguru import logger

from src.databases.models.growth_hacker import TaskStatus

if TYPE_CHECKING:
    from src.domains.growth_tasks.services import TaskDomainService

class TaskLifecycleManager:
    """任务生命周期管理器 - 负责任务状态管理和时间追踪"""
    
    def __init__(self, task_service: "TaskDomainService"):
        self.task_service = task_service
        self._start_times: Dict[str, float] = {}
        
    async def start_task(self, task_id: str) -> None:
        """开始任务执行
        
        Args:
            task_id: 任务ID
        """
        self._start_times[task_id] = time.time()
        
        await self.task_service.update_task_status(task_id, TaskStatus.RUNNING)
        
        logger.info(f"🚀 任务开始执行: {task_id}")
        
    async def complete_task(self, task_id: str, status: TaskStatus) -> None:
        """完成任务执行
        
        Args:
            task_id: 任务ID  
            status: 最终状态
        """
        execution_time = self._calculate_execution_time(task_id)
        
        await self.task_service.update_task_status(
            task_id, status, execution_time=execution_time
        )
        
        self._log_completion(task_id, status, execution_time)
        self._cleanup_tracking(task_id)
        
    def _calculate_execution_time(self, task_id: str) -> float:
        """计算执行时间"""
        start_time = self._start_times.get(task_id)
        if not start_time:
            logger.warning(f"任务 {task_id} 未找到开始时间")
            return 0.0
            
        return time.time() - start_time
        
    def _log_completion(self, task_id: str, status: TaskStatus, execution_time: float) -> None:
        """记录完成日志"""
        status_messages = {
            TaskStatus.SUCCESS: f"✅ 任务执行成功: {task_id}, 耗时: {execution_time:.2f}s",
            TaskStatus.ALREADY_CLAIMED: f"🧧 任务已领取: {task_id}, 耗时: {execution_time:.2f}s", 
            TaskStatus.RISK_DETECTED: f"🚧 检测到风险: {task_id}, 耗时: {execution_time:.2f}s",
            TaskStatus.FAILED: f"❌ 任务执行失败: {task_id}, 耗时: {execution_time:.2f}s"
        }
        
        message = status_messages.get(status, f"任务完成: {task_id}, 状态: {status}, 耗时: {execution_time:.2f}s")
        
        if status == TaskStatus.SUCCESS:
            logger.success(message)
        elif status in [TaskStatus.ALREADY_CLAIMED, TaskStatus.RISK_DETECTED]:
            logger.warning(message)
        else:
            logger.error(message)
            
    def _cleanup_tracking(self, task_id: str) -> None:
        """清理跟踪数据"""
        self._start_times.pop(task_id, None)
```

### Day 2-3: TaskOrchestrator实现和集成

#### TaskOrchestrator完整实现
```python
# 文件: src/applications/growth_hacker/services/refactored/task_orchestrator.py
from typing import TYPE_CHECKING
from loguru import logger

from src.databases.models.growth_hacker import TaskStatus
from src.domains.growth_tasks.errors import AlreadyClaimedError, RiskDetectedError

if TYPE_CHECKING:
    from src.domains.growth_tasks.services.environment import EnvironmentPreparer
    from src.domains.growth_tasks.services.lifecycle import TaskLifecycleManager  
    from src.domains.growth_tasks.services import UserProfileDomainService
    from src.infrastructures.browsers import BrowserManager
    from src.domains.growth_tasks.interactors.eleme10883 import Eleme10083Interactor
    from src.interfaces.growth_hacker.schemas import TaskMessageContent

class TaskOrchestrator:
    """任务编排器 - 负责协调整个任务执行流程"""
    
    def __init__(
        self,
        environment_preparer: "EnvironmentPreparer",
        lifecycle_manager: "TaskLifecycleManager", 
        profile_updater: "ProfileUpdater",
        browser_manager: "BrowserManager",
    ):
        self.environment_preparer = environment_preparer
        self.lifecycle_manager = lifecycle_manager
        self.profile_updater = profile_updater
        self.browser_manager = browser_manager
        
    async def execute_task(
        self, 
        task: "TaskMessageContent", 
        enable_none_proxy: bool = False
    ) -> None:
        """执行任务 - 仅负责流程编排，不包含具体实现
        
        Args:
            task: 任务消息内容
            enable_none_proxy: 是否允许无代理执行
        """
        logger.info(f"开始编排任务执行: {task.task_id}")
        
        # 1. 准备执行环境
        environment = await self.environment_preparer.prepare(task, enable_none_proxy)
        
        # 2. 开始任务生命周期跟踪
        await self.lifecycle_manager.start_task(task.task_id)
        
        success = False
        final_status = TaskStatus.FAILED
        
        try:
            # 3. 执行核心任务
            async with self.browser_manager.get_instance_context(
                environment.browser_session
            ) as browser_instance:
                
                page = await self._load_page(browser_instance, environment.task.access_url)
                
                # 创建交互器并执行任务
                interactor = Eleme10083Interactor(page=page)
                await interactor.detect_page()
                await interactor.execute_main_action()
                
                # 执行成功
                success = True
                final_status = TaskStatus.SUCCESS
                
                # 4. 更新用户档案 (仅成功时)
                await self._update_user_profile(environment, browser_instance)
                
        except AlreadyClaimedError:
            success = True  # 已领取也视为成功完成
            final_status = TaskStatus.ALREADY_CLAIMED
            
        except RiskDetectedError:
            final_status = TaskStatus.RISK_DETECTED
            
        except Exception as e:
            logger.error(f"任务执行异常: {str(e)}")
            final_status = TaskStatus.FAILED
            raise
            
        finally:
            # 5. 清理资源和完成生命周期
            await self.environment_preparer.cleanup(environment, success)
            await self.lifecycle_manager.complete_task(task.task_id, final_status)
            
        logger.info(f"任务编排完成: {task.task_id}, 状态: {final_status}")
        
    async def _load_page(self, browser_instance, access_url: str):
        """加载页面 - 从原TaskService迁移"""
        await browser_instance.goto(access_url, wait_until="load", timeout=30000)
        if not browser_instance.page:
            raise NavigationError("加载页面失败")
        return browser_instance.page
        
    async def _update_user_profile(self, environment, browser_instance) -> None:
        """更新用户档案 - 从原TaskService迁移"""
        try:
            browser_data = await browser_instance.collect_data()
            await self.profile_updater.update_profile(
                environment.task.phone, 
                browser_data
            )
            logger.info("🔄 用户浏览器数据保存完成")
        except Exception as e:
            logger.error(f"更新用户档案失败: {str(e)}")
```

### Day 4-5: Interactor模块化重构

#### 创建组件化结构
```bash
# 创建Interactor模块化目录结构
mkdir -p src/domains/growth_tasks/interactors/eleme10883/components
touch src/domains/growth_tasks/interactors/eleme10883/components/__init__.py
touch src/domains/growth_tasks/interactors/eleme10883/components/page_detector.py
touch src/domains/growth_tasks/interactors/eleme10883/components/behavior_simulator.py
touch src/domains/growth_tasks/interactors/eleme10883/components/action_executor.py
```

## 📋 Phase 2: 错误处理统一 (3天)

### 异常体系重构实施

#### 步骤1: 创建统一异常基类
```python
# 文件: src/domains/growth_tasks/exceptions/base.py
from typing import Dict, Any, Optional

class GrowthTaskError(Exception):
    """增长任务异常基类 - 统一的错误处理基础"""
    
    def __init__(
        self, 
        message: str, 
        retryable: bool = False,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.retryable = retryable
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}
        super().__init__(self.message)
    
    def with_context(self, **kwargs) -> "GrowthTaskError":
        """添加上下文信息"""
        self.context.update(kwargs)
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于序列化"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'retryable': self.retryable,
            'error_code': self.error_code,
            'context': self.context
        }
    
    def __str__(self) -> str:
        context_str = f", 上下文: {self.context}" if self.context else ""
        return f"{self.message}{context_str}"
```

#### 步骤2: 重构现有异常使用
```bash
# 查找和替换现有异常导入
grep -r "from.*errors import" src/domains/growth_tasks/
grep -r "from.*errors import" src/applications/growth_hacker/

# 使用脚本批量替换
python scripts/refactor_exceptions.py
```

## 📋 Phase 3: 测试体系建设 (5天)

### 单元测试实施指南

#### TaskOrchestrator测试实现
```python
# 文件: tests/unit/applications/growth_hacker/services/test_task_orchestrator.py
import pytest
from unittest.mock import AsyncMock, Mock, patch
from src.applications.growth_hacker.services.refactored.task_orchestrator import TaskOrchestrator
from src.domains.growth_tasks.exceptions import AlreadyClaimedError

class TestTaskOrchestrator:
    """TaskOrchestrator单元测试套件"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """模拟所有依赖"""
        return {
            'environment_preparer': AsyncMock(),
            'lifecycle_manager': AsyncMock(),
            'profile_updater': AsyncMock(), 
            'browser_manager': AsyncMock()
        }
    
    @pytest.fixture  
    def task_orchestrator(self, mock_dependencies):
        """创建TaskOrchestrator测试实例"""
        return TaskOrchestrator(**mock_dependencies)
    
    @pytest.fixture
    def sample_task(self):
        """示例任务数据"""
        return Mock(
            task_id="test_task_123",
            phone="13800138000",
            city="上海",
            lat="31.2304",
            lng="121.4737", 
            access_url="https://example.com/test"
        )
    
    @pytest.mark.unit
    async def test_execute_task_success_flow(
        self, task_orchestrator, mock_dependencies, sample_task
    ):
        """测试成功执行流程"""
        # Given: 配置模拟行为
        environment_mock = Mock()
        browser_instance_mock = AsyncMock()
        page_mock = Mock()
        browser_instance_mock.page = page_mock
        
        mock_dependencies['environment_preparer'].prepare.return_value = environment_mock
        mock_dependencies['browser_manager'].get_instance_context.return_value.__aenter__ = AsyncMock(return_value=browser_instance_mock)
        mock_dependencies['browser_manager'].get_instance_context.return_value.__aexit__ = AsyncMock(return_value=None)
        
        # When: 执行任务
        await task_orchestrator.execute_task(sample_task)
        
        # Then: 验证调用序列
        mock_dependencies['environment_preparer'].prepare.assert_called_once_with(sample_task, False)
        mock_dependencies['lifecycle_manager'].start_task.assert_called_once_with(sample_task.task_id)
        mock_dependencies['lifecycle_manager'].complete_task.assert_called_once()
        mock_dependencies['environment_preparer'].cleanup.assert_called_once_with(environment_mock, True)
    
    @pytest.mark.unit
    async def test_execute_task_already_claimed_handling(
        self, task_orchestrator, mock_dependencies, sample_task
    ):
        """测试已领取异常处理"""
        # Given: 模拟已领取异常
        environment_mock = Mock()
        mock_dependencies['environment_preparer'].prepare.return_value = environment_mock
        
        # 模拟浏览器上下文和异常
        browser_context_mock = AsyncMock()
        browser_context_mock.__aenter__.side_effect = AlreadyClaimedError("测试已领取")
        mock_dependencies['browser_manager'].get_instance_context.return_value = browser_context_mock
        
        # When & Then: 执行任务，不应抛出异常
        await task_orchestrator.execute_task(sample_task)
        
        # 验证正确的状态传递
        args = mock_dependencies['lifecycle_manager'].complete_task.call_args
        assert args[0][1] == TaskStatus.ALREADY_CLAIMED  # 第二个参数是状态
        
        # 验证资源清理，success=True（已领取视为成功）
        mock_dependencies['environment_preparer'].cleanup.assert_called_once_with(environment_mock, True)
```

### 集成测试实施指南
```python
# 文件: tests/integration/growth_hacker/test_task_execution_integration.py
import pytest
from tests.fixtures import TestDatabase, TestRedis, TestBrowser

class TestTaskExecutionIntegration:
    """任务执行集成测试"""
    
    @pytest.fixture
    async def test_infrastructure(self):
        """测试基础设施"""
        test_db = TestDatabase()
        test_redis = TestRedis()
        test_browser = TestBrowser()
        
        await test_db.setup()
        await test_redis.setup()
        await test_browser.setup()
        
        yield {
            'database': test_db,
            'redis': test_redis, 
            'browser': test_browser
        }
        
        await test_browser.cleanup()
        await test_redis.cleanup()
        await test_db.cleanup()
    
    @pytest.mark.integration
    async def test_complete_task_flow_integration(self, test_infrastructure):
        """完整任务流程集成测试"""
        # 使用真实的数据库、Redis和浏览器环境
        # 但数据完全隔离，不影响其他测试
        pass
```

## 🔧 开发工具和最佳实践

### 代码质量检查配置
```toml
# pyproject.toml 更新配置
[tool.ruff]
select = ["E", "W", "F", "I", "N", "B", "S", "C4", "COM", "DTZ", "DJ", "EM", "EXE", "ICN", "LOG", "G", "INP", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "INT", "ARG", "PTH", "PGH", "TRY", "FLY", "PERF", "FURB", "RUF"]
ignore = ["E501", "COM812", "ISC001"]
line-length = 120
target-version = "py312"

[tool.ruff.per-file-ignores]
"tests/**/*.py" = ["S101", "ARG001", "ARG002"]  # 允许测试中使用assert和未使用参数

[tool.mypy]  
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
no_implicit_optional = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false  # 测试文件可以不强制类型注解
```

### 预提交钩子配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.280
    hooks:
      - id: ruff
        args: [--fix]
        
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        
  - repo: local
    hooks:
      - id: run-tests
        name: 运行单元测试
        entry: poetry run pytest tests/unit/ -x -v
        language: system
        pass_filenames: false
        always_run: true
```

### 自动化脚本工具
```python
# scripts/refactor_helper.py
"""重构辅助脚本"""
import ast
import os
from pathlib import Path

class RefactorHelper:
    """重构辅助工具"""
    
    def analyze_method_complexity(self, file_path: str) -> dict:
        """分析方法复杂度"""
        with open(file_path, 'r', encoding='utf-8') as f:
            tree = ast.parse(f.read())
        
        complexity_report = {}
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_complexity(node)
                complexity_report[node.name] = {
                    'lines': node.end_lineno - node.lineno + 1,
                    'complexity': complexity,
                    'needs_refactor': complexity > 10 or (node.end_lineno - node.lineno) > 50
                }
        
        return complexity_report
    
    def generate_refactor_plan(self, target_dir: str) -> dict:
        """生成重构计划"""
        plan = {'high_priority': [], 'medium_priority': [], 'low_priority': []}
        
        for py_file in Path(target_dir).rglob('*.py'):
            report = self.analyze_method_complexity(str(py_file))
            
            for method, metrics in report.items():
                if metrics['needs_refactor']:
                    priority = 'high_priority' if metrics['complexity'] > 15 else 'medium_priority'
                    plan[priority].append({
                        'file': str(py_file),
                        'method': method,
                        'metrics': metrics
                    })
        
        return plan

if __name__ == '__main__':
    helper = RefactorHelper()
    plan = helper.generate_refactor_plan('src/applications/growth_hacker/')
    
    print("🔍 重构优先级分析:")
    for priority, items in plan.items():
        print(f"\n{priority.upper()}:")
        for item in items:
            print(f"  - {item['file']}:{item['method']} (复杂度: {item['metrics']['complexity']})")
```

## 📊 进度跟踪和监控

### 每日进度检查清单
```yaml
# daily_checklist.yaml
每日检查项:
  代码质量:
    - [ ] 新增代码通过ruff检查
    - [ ] 新增代码通过mypy检查
    - [ ] 代码覆盖率保持或提升
    - [ ] 无新增TODO/FIXME标记
    
  测试质量:
    - [ ] 新增功能有对应单元测试
    - [ ] 所有测试通过
    - [ ] 重构后功能测试通过
    - [ ] 性能测试无显著下降
    
  重构进度:
    - [ ] 当日计划任务完成情况
    - [ ] 遇到的阻碍问题记录
    - [ ] 明日计划任务确认
    - [ ] 风险评估更新
```

### 重构进度仪表板
```python
# scripts/progress_dashboard.py
"""重构进度仪表板"""
import json
from datetime import datetime
from pathlib import Path

class ProgressDashboard:
    """进度跟踪仪表板"""
    
    def __init__(self):
        self.metrics_file = Path('refactor_metrics.json')
        self.daily_logs = Path('daily_progress.json')
    
    def update_daily_progress(self, phase: str, completed_tasks: list, blockers: list):
        """更新每日进度"""
        progress_data = {
            'date': datetime.now().isoformat(),
            'phase': phase,
            'completed_tasks': completed_tasks,
            'blockers': blockers,
            'code_metrics': self._collect_code_metrics()
        }
        
        # 追加到日志文件
        logs = self._load_daily_logs()
        logs.append(progress_data)
        
        with open(self.daily_logs, 'w', encoding='utf-8') as f:
            json.dump(logs, f, indent=2, ensure_ascii=False)
    
    def generate_progress_report(self) -> str:
        """生成进度报告"""
        logs = self._load_daily_logs()
        if not logs:
            return "暂无进度数据"
        
        latest = logs[-1]
        total_tasks = sum(len(log['completed_tasks']) for log in logs)
        
        report = f"""
# 重构进度报告 ({latest['date'][:10]})

## 总体进度
- 当前阶段: {latest['phase']}
- 累计完成任务: {total_tasks}个
- 当前阻碍: {len(latest['blockers'])}个

## 代码质量指标
- 测试覆盖率: {latest['code_metrics'].get('coverage', 'N/A')}%
- 代码复杂度: {latest['code_metrics'].get('complexity', 'N/A')}
- 技术债务: {latest['code_metrics'].get('tech_debt', 'N/A')}个

## 近期完成
"""
        
        # 添加最近3天的任务
        for log in logs[-3:]:
            report += f"\n### {log['date'][:10]}\n"
            for task in log['completed_tasks']:
                report += f"- ✅ {task}\n"
            for blocker in log['blockers']:
                report += f"- ❌ {blocker}\n"
        
        return report
    
    def _collect_code_metrics(self) -> dict:
        """收集代码指标"""
        # 这里可以集成实际的代码分析工具
        return {
            'coverage': 85.2,
            'complexity': 'Medium',
            'tech_debt': 45
        }
    
    def _load_daily_logs(self) -> list:
        """加载日志数据"""
        if not self.daily_logs.exists():
            return []
        
        with open(self.daily_logs, 'r', encoding='utf-8') as f:
            return json.load(f)

if __name__ == '__main__':
    dashboard = ProgressDashboard()
    print(dashboard.generate_progress_report())
```

## 🎯 质量保证检查点

### Phase 1 完成检查点
```bash
#!/bin/bash
# scripts/phase1_checkpoint.sh

echo "🔍 Phase 1 质量检查开始..."

# 1. 代码质量检查
echo "检查代码质量..."
poetry run ruff src/applications/growth_hacker/services/refactored/
poetry run mypy src/applications/growth_hacker/services/refactored/

# 2. 测试覆盖率检查
echo "检查测试覆盖率..."
poetry run pytest tests/unit/applications/growth_hacker/ --cov=src/applications/growth_hacker --cov-report=term-missing --cov-fail-under=80

# 3. 功能回归测试
echo "执行功能回归测试..."
poetry run pytest tests/integration/growth_hacker/ -v

# 4. 性能基准测试  
echo "执行性能基准测试..."
poetry run python scripts/performance_benchmark.py

echo "✅ Phase 1 检查点通过！"
```

### 部署前最终检查
```bash
#!/bin/bash
# scripts/pre_deploy_check.sh

echo "🚀 部署前最终检查..."

# 1. 完整测试套件
poetry run pytest tests/ --maxfail=1

# 2. 代码质量全面检查
poetry run ruff src/
poetry run mypy src/
poetry run black --check src/

# 3. 安全扫描
poetry run safety check

# 4. 依赖检查  
poetry check

# 5. 构建验证
poetry build

echo "✅ 部署前检查全部通过！"
```

---

*本实施指南提供了详细的操作步骤和工具支持，确保重构过程的有序进行和质量保证。*