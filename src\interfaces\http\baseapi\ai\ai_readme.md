# AI 接口总体设计

## DB：

### 现有 redis：

- 钉钉自动领红包用户列表 set：

```python
await redis.smembers("dingtalk_ai_agent_auto_apply_users", current_user.phone)
```

- 用户订阅信息哈希表

```python
    await redis.hset(  # 用户产品信息哈希表
        f"dingtalk_auto_apply:{current_user.phone}",  # type: ignore
        mapping={
            "uid": current_user.uid,  # type: ignore
            "product_code": payload.product_code,
            "created_at": str(datetime.now().timestamp()),
            "union_id": dingtalk_user.union_id,
            "agent_id": payload.agent_id,
            "agent_user_id": payload.agent_user_id,
        },
    )
```

### 迁移 mysql orm

```sql
CREATE TABLE dingtalk_auto_apply_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `idx_phone` (phone)
) COMMENT '钉钉自动领红包用户表';
```

```sql
CREATE TABLE dingtalk_user_subscriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(64) NOT NULL COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    union_id VARCHAR(64) NOT NULL COMMENT '钉钉用户union_id',
    product_code VARCHAR(32) NOT NULL COMMENT '产品代码',
    agent_id VARCHAR(64) NOT NULL COMMENT '应用ID',
    agent_user_id VARCHAR(64) NOT NULL COMMENT '应用用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `idx_phone_product` (phone, product_code),
    INDEX `idx_uid` (uid),
    INDEX `idx_union_id` (union_id)
) COMMENT '钉钉用户订阅信息表';
```

## APIs

- dingtalk/get_subscribe_url 获取订阅 url（新增）

  - 用途：agent 调用 API,
  - query:
    agent_id
    agent_user_id
  - handler：
    1. 查询用户订阅状态
    2. 组装地址返回（新用户、旧用户未发、旧用户今日已发）
  - return:
    h5url

- dingtalk/subscribe_benefits 订阅自动领权益（盲盒版）

  - 用途： h5 调用订阅

- dingtalk/unsubscribe_benefits 取消订阅（盲盒版）
  - 用途： h5 调用订阅

## schedule

- 和现有逻辑相比，就是权益发放的时候给一个随机摇券，然后推送中给出不同的卡片

## 原 AI 接口

/couponlist 获取推荐红包
/storelist 获取推荐商店
/another_storelist 换一批商店
/htrip/chat 携旅 ai chat 接口

## 原 AI DB （sqlite）

- recommend_history 记录用户查询记录 以支持换一批商店查询接口（这个其实可以用 redis，因为会过期）

```sql
            CREATE TABLE IF NOT EXISTS recommend_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_id TEXT NOT NULL,
                restaurant_names TEXT NOT NULL,  -- JSON格式存储商铺名称列表
                tag TEXT NOT NULL,               -- 单个标签字符串
                latitude TEXT,                   -- 纬度
                longitude TEXT,                  -- 经度
                created_at TEXT NOT NULL         -- 精确到毫秒的时间戳
            )
```
