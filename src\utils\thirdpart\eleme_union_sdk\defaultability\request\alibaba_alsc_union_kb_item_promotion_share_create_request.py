from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionKbItemPromotionShareCreateRequest(BaseRequest):

    def __init__(
        self,
        pid: str = None,
        item_id: str = None,
        biz_unit: int = None,
        include_mini_qr_code: bool = None,
        include_mini_qr_code_hyaline: bool = None,
        include_img_url: bool = None,
        sid: str = None,
        include_wx_img_url: bool = None,
        include_alipay_img_url: bool = None,
        include_alipay_wathword: bool = None,
    ):
        """
        推广位pid
        """
        self._pid = pid
        """
            商品ID，默认CPA的品，如果推广其他业务单元的品，请填写对应的biz_unit
        """
        self._item_id = item_id
        """
            业务单元，1-CPA，2-CPS，3-SPU。默认1-CPA
        """
        self._biz_unit = biz_unit
        """
            废弃
        """
        self._include_mini_qr_code = include_mini_qr_code
        """
            废弃
        """
        self._include_mini_qr_code_hyaline = include_mini_qr_code_hyaline
        """
            废弃
        """
        self._include_img_url = include_img_url
        """
            第三方会员id扩展
        """
        self._sid = sid
        """
            是否合成微信推广图
        """
        self._include_wx_img_url = include_wx_img_url
        """
            是否合成支付宝推广图
        """
        self._include_alipay_img_url = include_alipay_img_url
        """
            是否返回吱口令
        """
        self._include_alipay_wathword = include_alipay_wathword

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def item_id(self):
        return self._item_id

    @item_id.setter
    def item_id(self, item_id):
        if isinstance(item_id, str):
            self._item_id = item_id
        else:
            raise TypeError("item_id must be str")

    @property
    def biz_unit(self):
        return self._biz_unit

    @biz_unit.setter
    def biz_unit(self, biz_unit):
        if isinstance(biz_unit, int):
            self._biz_unit = biz_unit
        else:
            raise TypeError("biz_unit must be int")

    @property
    def include_mini_qr_code(self):
        return self._include_mini_qr_code

    @include_mini_qr_code.setter
    def include_mini_qr_code(self, include_mini_qr_code):
        if isinstance(include_mini_qr_code, bool):
            self._include_mini_qr_code = include_mini_qr_code
        else:
            raise TypeError("include_mini_qr_code must be bool")

    @property
    def include_mini_qr_code_hyaline(self):
        return self._include_mini_qr_code_hyaline

    @include_mini_qr_code_hyaline.setter
    def include_mini_qr_code_hyaline(self, include_mini_qr_code_hyaline):
        if isinstance(include_mini_qr_code_hyaline, bool):
            self._include_mini_qr_code_hyaline = include_mini_qr_code_hyaline
        else:
            raise TypeError("include_mini_qr_code_hyaline must be bool")

    @property
    def include_img_url(self):
        return self._include_img_url

    @include_img_url.setter
    def include_img_url(self, include_img_url):
        if isinstance(include_img_url, bool):
            self._include_img_url = include_img_url
        else:
            raise TypeError("include_img_url must be bool")

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, sid):
        if isinstance(sid, str):
            self._sid = sid
        else:
            raise TypeError("sid must be str")

    @property
    def include_wx_img_url(self):
        return self._include_wx_img_url

    @include_wx_img_url.setter
    def include_wx_img_url(self, include_wx_img_url):
        if isinstance(include_wx_img_url, bool):
            self._include_wx_img_url = include_wx_img_url
        else:
            raise TypeError("include_wx_img_url must be bool")

    @property
    def include_alipay_img_url(self):
        return self._include_alipay_img_url

    @include_alipay_img_url.setter
    def include_alipay_img_url(self, include_alipay_img_url):
        if isinstance(include_alipay_img_url, bool):
            self._include_alipay_img_url = include_alipay_img_url
        else:
            raise TypeError("include_alipay_img_url must be bool")

    @property
    def include_alipay_wathword(self):
        return self._include_alipay_wathword

    @include_alipay_wathword.setter
    def include_alipay_wathword(self, include_alipay_wathword):
        if isinstance(include_alipay_wathword, bool):
            self._include_alipay_wathword = include_alipay_wathword
        else:
            raise TypeError("include_alipay_wathword must be bool")

    def get_api_name(self):
        return "alibaba.alsc.union.kb.item.promotion.share.create"

    def to_dict(self):
        request_dict = {}
        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._item_id is not None:
            request_dict["item_id"] = convert_basic(self._item_id)

        if self._biz_unit is not None:
            request_dict["biz_unit"] = convert_basic(self._biz_unit)

        if self._include_mini_qr_code is not None:
            request_dict["include_mini_qr_code"] = convert_basic(self._include_mini_qr_code)

        if self._include_mini_qr_code_hyaline is not None:
            request_dict["include_mini_qr_code_hyaline"] = convert_basic(self._include_mini_qr_code_hyaline)

        if self._include_img_url is not None:
            request_dict["include_img_url"] = convert_basic(self._include_img_url)

        if self._sid is not None:
            request_dict["sid"] = convert_basic(self._sid)

        if self._include_wx_img_url is not None:
            request_dict["include_wx_img_url"] = convert_basic(self._include_wx_img_url)

        if self._include_alipay_img_url is not None:
            request_dict["include_alipay_img_url"] = convert_basic(self._include_alipay_img_url)

        if self._include_alipay_wathword is not None:
            request_dict["include_alipay_wathword"] = convert_basic(self._include_alipay_wathword)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
