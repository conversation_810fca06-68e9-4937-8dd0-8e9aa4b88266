# encoding: utf-8
# .claude/specs/container_integration/design.md  
# created: 2025-08-10 10:30:00

# 容器服务整合架构设计文档

## Overview

本设计文档提供了一套完整的容器服务整合架构方案，旨在解决当前项目中容器结构混乱、配置重复、生命周期管理复杂等问题。通过统一的容器层次结构和配置管理机制，实现模块化单体架构下的服务独立部署和统一管理。

## Architecture Design

### System Architecture Diagram

```mermaid
graph TB
    subgraph "服务部署层 Deploy Layer"
        HTTP[HTTP API Services]
        CONSUMER[Consumer Services]  
        SCHEDULER[Scheduler Services]
        GROWTH[Growth Hacker Services]
    end

    subgraph "统一容器管理层 Container Management"
        CM[ContainerManager]
        CSF[ContainerServiceFactory]
    end

    subgraph "核心容器层 Core Containers"
        INFRA[Infrastructures Container]
        REPO[Repositories Container] 
        DOMAIN[Domains Container]
    end

    subgraph "应用容器层 Application Containers"
        PASSPORT[Passport Applications]
        OPENAPI[OpenAPI Applications]
        COMMON[Common Applications]
        GROWTH_APP[Growth Hacker Applications]
    end

    subgraph "配置管理层 Configuration Management"
        CONFIG[Unified Config Manager]
        LIFECYCLE[Lifecycle Manager]
    end

    HTTP --> CM
    CONSUMER --> CM
    SCHEDULER --> CM
    GROWTH --> CM
    
    CM --> CSF
    CSF --> INFRA
    CSF --> REPO
    CSF --> DOMAIN
    CSF --> PASSPORT
    CSF --> OPENAPI
    CSF --> COMMON
    CSF --> GROWTH_APP
    
    CM --> CONFIG
    CM --> LIFECYCLE
```

### Data Flow Diagram

```mermaid
graph LR
    A[Service Bootstrap] --> B[Container Manager]
    B --> C[Load Configuration]
    C --> D[Initialize Core Containers]
    D --> E{Service Type?}
    E -->|HTTP| F[Setup HTTP Container]
    E -->|Consumer| G[Setup Consumer Container] 
    E -->|Scheduler| H[Setup Scheduler Container]
    E -->|Growth Hacker| I[Setup Growth Container]
    F --> J[Wire Dependencies]
    G --> J
    H --> J
    I --> J
    J --> K[Start Service]
```

## Component Design

### ContainerManager

- **职责**：
  - 统一管理所有容器的创建和配置
  - 提供服务类型感知的容器组装
  - 管理容器间依赖关系
  - 控制容器生命周期

- **接口**：
```python
class ContainerManager:
    def setup_service(self, service_type: ServiceType, config: Config) -> Container
    def get_container(self, name: str) -> Container
    def shutdown_all(self) -> None
    def wire_dependencies(self, packages: List[str]) -> None
```

- **依赖**：
  - ContainerServiceFactory
  - UnifiedConfigManager  
  - LifecycleManager

### ContainerServiceFactory

- **职责**：
  - 基于服务类型创建对应的容器组合
  - 实现容器工厂模式
  - 处理容器间的依赖注入配置

- **接口**：
```python
class ContainerServiceFactory:
    def create_http_service_container(self, config: Config) -> HTTPServiceContainer
    def create_consumer_service_container(self, config: Config) -> ConsumerServiceContainer
    def create_scheduler_service_container(self, config: Config) -> SchedulerServiceContainer
    def create_growth_service_container(self, config: Config) -> GrowthServiceContainer
```

### UnifiedConfigManager

- **职责**：
  - 统一配置管理和注入
  - 支持环境变量和配置文件
  - 配置验证和类型转换

- **接口**：
```python
class UnifiedConfigManager:
    def load_config(self, config_path: Optional[str] = None) -> Config
    def validate_config(self, config: Config) -> bool
    def get_service_config(self, service_type: ServiceType) -> ServiceConfig
```

### LifecycleManager

- **职责**：
  - 管理容器和服务的生命周期
  - 优雅启动和关闭
  - 健康检查和监控

- **接口**：
```python
class LifecycleManager:
    def startup(self, container: Container) -> None
    def shutdown(self, container: Container) -> None  
    def health_check(self, container: Container) -> HealthStatus
```

## Data Model

### Core Data Structure Definitions

```python
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

class ServiceType(str, Enum):
    HTTP = "http"
    CONSUMER = "consumer" 
    SCHEDULER = "scheduler"
    GROWTH_HACKER = "growth_hacker"

class ContainerConfig(BaseModel):
    """容器配置基类"""
    name: str
    dependencies: List[str] = []
    lifecycle_hooks: Dict[str, str] = {}
    
class ServiceConfig(BaseModel):
    """服务配置"""
    service_type: ServiceType
    containers: List[ContainerConfig]
    wiring_packages: List[str]
    
class HealthStatus(BaseModel):
    """健康状态"""
    is_healthy: bool
    message: Optional[str] = None
    details: Dict[str, Any] = {}

class ContainerRegistry(BaseModel):
    """容器注册表"""
    containers: Dict[str, Any] = {}
    dependencies: Dict[str, List[str]] = {}
```

### Data Model Diagrams

```mermaid
erDiagram
    ContainerManager ||--o{ Container : manages
    ContainerManager ||--|| UnifiedConfigManager : uses
    ContainerManager ||--|| LifecycleManager : uses
    Container ||--o{ Provider : contains
    Container ||--|| ContainerConfig : configured_by
    ServiceConfig ||--o{ ContainerConfig : includes
    HealthStatus ||--|| Container : monitors
```

## Business Process

### Process 1：服务启动流程

```mermaid
flowchart TD
    A[服务启动] --> B[创建 ContainerManager]
    B --> C[containerManager.setup_service]
    C --> D[configManager.load_config]
    D --> E[factory.create_service_container]
    E --> F{检查依赖}
    F -->|缺少| G[创建依赖容器]
    F -->|完整| H[lifecycleManager.startup]
    G --> H
    H --> I[containerManager.wire_dependencies]
    I --> J[服务就绪]
```

### Process 2：配置管理流程

```mermaid
sequenceDiagram
    participant SM as Service Main
    participant CM as ContainerManager  
    participant UCM as UnifiedConfigManager
    participant CSF as ContainerServiceFactory
    
    SM->>CM: setup_service(service_type, config_path)
    CM->>UCM: load_config(config_path)
    UCM->>UCM: validate_config()
    UCM-->>CM: validated_config
    CM->>CSF: create_service_container(config)
    CSF->>CSF: setup_core_containers()
    CSF->>CSF: setup_application_containers()
    CSF-->>CM: service_container
    CM->>CM: wire_dependencies()
    CM-->>SM: configured_container
```

### Process 3：容器生命周期管理流程

```mermaid
flowchart TD
    A[容器创建] --> B[lifecycleManager.startup]
    B --> C[初始化基础设施]
    C --> D[建立数据库连接]
    D --> E[启动消息队列]
    E --> F[注册健康检查]
    F --> G[容器就绪]
    
    G --> H{运行中}
    H -->|正常| I[定期健康检查]
    H -->|关闭信号| J[lifecycleManager.shutdown]
    I --> H
    J --> K[停止新请求]
    K --> L[完成现有请求]
    L --> M[关闭连接池]
    M --> N[容器关闭]
```

## Error Handling Strategy

### 错误处理层次

1. **容器初始化错误**：
   - 配置验证失败 → 抛出 ConfigurationError
   - 依赖注入失败 → 抛出 DependencyError  
   - 资源连接失败 → 抛出 ResourceError

2. **运行时错误**：
   - 健康检查失败 → 记录警告，重试机制
   - 服务不可用 → 熔断机制，降级服务
   - 资源耗尽 → 限流和队列管理

3. **关闭过程错误**：
   - 优雅关闭超时 → 强制关闭
   - 资源释放失败 → 记录错误日志

### 错误恢复机制

```python
class ContainerErrorHandler:
    def handle_initialization_error(self, error: Exception) -> bool:
        """处理初始化错误，返回是否可恢复"""
        
    def handle_runtime_error(self, error: Exception) -> None:
        """处理运行时错误"""
        
    def handle_shutdown_error(self, error: Exception) -> None:
        """处理关闭错误"""
```

## Testing Strategy

### 单元测试策略

1. **容器管理测试**：
   ```python
   class TestContainerManager:
       def test_setup_http_service(self):
       def test_setup_consumer_service(self):
       def test_container_lifecycle(self):
       def test_dependency_injection(self):
   ```

2. **配置管理测试**：
   ```python
   class TestUnifiedConfigManager:
       def test_load_config_from_file(self):
       def test_load_config_from_env(self):
       def test_config_validation(self):
   ```

3. **工厂模式测试**：
   ```python
   class TestContainerServiceFactory:
       def test_create_service_containers(self):
       def test_dependency_resolution(self):
   ```

### 集成测试策略

1. **服务端到端测试**：
   - 测试各服务类型的完整启动流程
   - 验证容器间依赖关系正确建立
   - 测试配置热重载功能

2. **性能测试**：
   - 容器启动时间测试
   - 内存使用量测试  
   - 并发服务启动测试

### 测试工具和框架

```python
# 测试基础设施
class ContainerTestBase:
    def setup_test_config(self) -> Config:
    def create_mock_dependencies(self) -> Dict[str, Any]:
    def cleanup_test_containers(self) -> None:

# 集成测试辅助
class IntegrationTestHelper:
    def start_test_services(self, service_types: List[ServiceType]) -> None:
    def verify_service_health(self, service_type: ServiceType) -> bool:
    def shutdown_test_services(self) -> None:
```

## 实现计划

### Phase 1: 核心架构实现（第1-2周）

1. **创建统一容器管理器**：
   - 实现 ContainerManager 核心逻辑
   - 实现 ContainerServiceFactory
   - 创建统一配置管理器

2. **重构核心容器**：
   - 整合现有的 Infrastructures, Repositories, Domains 容器
   - 统一配置注入方式
   - 优化依赖关系

### Phase 2: 应用层容器整合（第3-4周）

1. **重构应用层容器**：
   - 统一各应用容器接口
   - 消除重复依赖定义
   - 实现容器组合模式

2. **服务入口重构**：
   - 重构各服务的 main.py
   - 统一容器初始化流程
   - 实现配置管理统一化

### Phase 3: 生命周期和监控（第5-6周）

1. **生命周期管理**：
   - 实现 LifecycleManager
   - 添加健康检查机制
   - 实现优雅关闭

2. **监控和错误处理**：
   - 添加容器状态监控
   - 实现错误恢复机制
   - 完善日志记录

### Phase 4: 测试和文档（第7-8周）

1. **完善测试覆盖**：
   - 单元测试覆盖率达到90%+
   - 集成测试覆盖所有服务类型
   - 性能基准测试

2. **迁移和部署**：
   - 制定迁移计划
   - 逐步替换现有容器
   - 更新部署脚本

## 迁移策略

### 向前兼容迁移

1. **保持现有接口**：
   - 新系统提供现有容器接口的适配器
   - 逐步迁移各服务到新容器系统
   - 支持新旧系统并行运行

2. **分阶段迁移**：
   - Phase 1: 基础设施容器迁移
   - Phase 2: 应用层容器迁移  
   - Phase 3: 服务入口迁移
   - Phase 4: 清理旧代码

### 迁移验证

```python
class MigrationValidator:
    def validate_container_compatibility(self, old_container, new_container) -> bool:
    def validate_service_behavior(self, service_type: ServiceType) -> bool:
    def validate_performance_regression(self) -> PerformanceReport:
```

## 风险评估和缓解

### 主要风险

1. **兼容性风险**：现有服务可能依赖特定的容器行为
   - 缓解：提供适配器层和渐进式迁移

2. **性能风险**：新架构可能影响启动性能
   - 缓解：性能基准测试和优化

3. **复杂性风险**：统一管理可能增加系统复杂性
   - 缓解：清晰的文档和示例代码

### 回滚计划

1. **快速回滚**：保留现有容器代码直到新系统完全稳定
2. **数据完整性**：确保配置和状态数据兼容性
3. **监控告警**：建立完善的监控指标识别问题

通过这套完整的容器服务整合架构，项目将实现：
- 统一的容器管理和配置
- 清晰的服务依赖关系
- 简化的部署和运维
- 更好的可测试性和可维护性