# encoding: utf-8
# src/domains/passport/entities/tenant.py
# created: 2025-07-31 22:10:05

from typing import TYPE_CHECKING

import nanoid
from pydantic import BaseModel

from .app import AppEntity

if TYPE_CHECKING:
    from src.databases.models.passport import PassportTenant


class TenantEntity(BaseModel):

    tenant_id: str
    tenant_name: str

    app: "AppEntity"

    @classmethod
    async def from_model(cls, model: "PassportTenant") -> "TenantEntity":
        await model.fetch_related("app")
        return cls(
            tenant_id=model.tenant_id,
            tenant_name=model.name,
            app=await AppEntity.from_model(model.app),
        )

    @classmethod
    def create_tenant(cls, tenant_name: str, app: "AppEntity") -> "TenantEntity":
        return cls(
            tenant_id=nanoid.generate(
                size=16, alphabet="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
            ),
            tenant_name=tenant_name,
            app=app,
        )
