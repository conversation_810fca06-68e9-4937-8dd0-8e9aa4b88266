# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/base/routers/passport/schemas.py
# created: 2025-05-27 00:47:00
# updated: 2025-05-27 07:18:47

from typing import Dict, List, Optional

from pydantic import BaseModel, Field

from src.applications.passport.dto import TenantInfoDTO, UserInfoDTO
from src.infrastructures.fastapi.response import BaseResponse


class LoginDingtalkRequestPayload(BaseModel):
    auth_code: str = Field(description="钉钉授权码")


class SendSmsRequestPayload(BaseModel):
    phone_number: str = Field(..., pattern=r"^1[3-9]\d{9}$", description="手机号码")


class LoginSmsRequestPayload(BaseModel):
    auth_code: str = Field(description="短信验证码")
    phone_number: str = Field(..., pattern=r"^1[3-9]\d{9}$", description="手机号码")


class AuthUser(BaseModel):
    app_id: str = Field(..., description="应用ID")
    app_name: str = Field(..., description="应用名称")
    tenant_id: Optional[str] = Field("", description="租户ID")
    tenant_name: str = Field(..., description="租户名称")
    uid: str = Field(..., description="用户ID")
    nickname: str = Field(..., description="用户昵称")
    phone: str = Field(..., description="用户手机号")
    avatar_url: Optional[str] = Field(None, description="用户头像")
    token: Optional[str] = Field("", description="用户Token")
    session_key: Optional[str] = Field(None, description="微信小程序session_key")
    is_new_user: Optional[bool] = Field(None, description="是否为新用户")


class SendSmsResponseData(BaseModel):
    expired_at: int = Field(..., description="验证码过期时间")


class WechatMpLoginPayload(BaseModel):
    login_code: str = Field(..., description="微信小程序登录code")
    phone_code: Optional[str] = Field(None, description="微信小程序绑定的手机号code")


class AlipayMpLoginPayload(BaseModel):
    encrypted_data: str = Field(..., description="支付宝小程序加密数据")


class Qrcode(BaseModel):
    base64: str = Field(..., description="二维码")


class GetWechatMpUnlimitedQrcodeParams(BaseModel):
    scene: Optional[str] = Field(None, description="场景值")
    page: str = Field(..., description="页面路径")
    env_version: str = Field(..., description="环境版本")
    width: int = Field(..., description="二维码宽度")
    auto_color: Optional[bool] = Field(True, description="是否自动配置线条颜色")
    line_color: Optional[Dict[str, int]] = Field(None, description="自定义线条颜色")
    is_hyaline: Optional[bool] = Field(True, description="是否透明底色")


class UserProfilePayload(BaseModel):
    nickname: Optional[str] = Field(None, description="用户昵称", max_length=50)
    avatar_url: Optional[str] = Field(None, description="用户头像URL", max_length=500)
    email: Optional[str] = Field(None, description="用户邮箱", max_length=100)


LoginResponse = BaseResponse[UserInfoDTO]
SendSmsResponse = BaseResponse[SendSmsResponseData]
QrcodeResponse = BaseResponse[Qrcode]
UserTenantsResponse = BaseResponse[list[TenantInfoDTO]]
UserProfileResponse = BaseResponse[UserInfoDTO]
