from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeStorepromotionReviewbwcBindLinkGetRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.storepromotion.reviewbwc.bind.link.get"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeStorepromotionReviewbwcBindLinkGetReviewBwcSidBindLinkRequest:
    def __init__(self, pid: str = None, sid: str = None, activity_id: str = None):
        """
        推广位
        """
        self.pid = pid
        """
            会员ID
        """
        self.sid = sid
        """
            活动ID
        """
        self.activity_id = activity_id
