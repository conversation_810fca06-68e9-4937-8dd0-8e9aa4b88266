# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/openapi/authorization.py
# created: 2024-12-12 01:33:39
# updated: 2025-04-11 02:46:54

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger

from src.containers import Container

if TYPE_CHECKING:
    from src.applications.openapi.authenticator import OpenAPIAKSKAuthenticator, OpenAPITokenAuthenticator
    from src.domains.customer.entities import CustomerEntity


@inject
async def openapi_authentication(
    request: Request,
    token: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False)),
    bearer_auth: "OpenAPITokenAuthenticator" = Depends(Provide[Container.applications.openapi_bearer_authenticator]),
    aksk_auth: "OpenAPIAKSKAuthenticator" = Depends(Provide[Container.applications.openapi_aksk_authenticator]),
) -> "CustomerEntity":
    """OpenAPI认证方法, 同时支持Bearer Token和AK/SK签名认证"""

    # Bearer Token认证
    valid, secret, error_msg = await bearer_auth.authenticate(request)
    logger.info(
        "Bearer Token认证结果: {valid}, {secret}, {error_msg}",
        valid=valid,
        secret=f"[{secret.id}] {secret.name}" if secret else "None",
        error_msg=error_msg,
    )
    if valid and secret:
        return secret

    # 尝试AK/SK认证
    valid, secret, error_msg = await aksk_auth.authenticate(request)
    logger.info(f"AK/SK认证结果: {valid}, {secret}, {error_msg}")
    if valid and secret:
        return secret

    # 所有认证方式都失败
    logger.warning(f"Authentication failed: {error_msg}")
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Authorization failed: {error_msg}")
