# encoding: utf-8
# src/infrastructures/ip_proxy/settings.py
# created: 2025-07-25 18:26:08

from pydantic import Field
from pydantic_settings import BaseSettings, PydanticBaseSettingsSource, SettingsConfigDict, TomlConfigSettingsSource

# from .config_v2 import config


class QGShortConfig(BaseSettings):
    """青果ip proxy短效代理"""

    endpoint: str = "https://share.proxy.qg.net"
    key: str = ""
    password: str = ""


class QGLongConfig(BaseSettings):
    """青果ip proxy长效代理"""

    endpoing: str = "https://longterm.proxy.qg.net"
    key: str = ""
    password: str = ""


class ProxySettings(BaseSettings):
    """代理配置"""

    local: dict[str, str] = Field(default_factory=dict, description="本地使用代理")
    sae: dict[str, str] = Field(default_factory=dict, description="SAE使用代理")
    qg_short_settings: QGShortConfig = Field(default_factory=QGShortConfig, description="青果ip proxy短效代理")
    qg_long_settings: QGLongConfig = Field(default_factory=QGLongConfig, description="青果ip proxy长效代理")

    model_config = SettingsConfigDict(toml_file="proxy.toml")

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        return (TomlConfigSettingsSource(settings_cls),)

    def get_proxy(self, city: str) -> str | None:
        # if config.environment == "sae":
        #     return self.sae.get(city, None)
        return self.local.get(city, None)


proxy_settings = ProxySettings()
