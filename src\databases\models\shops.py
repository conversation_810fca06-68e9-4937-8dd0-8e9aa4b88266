# encoding: utf-8
# src/databases/models/shops.py
# created: 2025-08-04 11:16:42

import nanoid
from tortoise import fields
from tortoise.models import Model

from .utils import SoftDeleteMixin


def generate_id() -> str:
    return nanoid.generate(size=16, alphabet="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")


class Shop(SoftDeleteMixin, Model):  # type: ignore
    """店铺总表"""

    id = fields.BigIntField(pk=True, description="主键ID")
    shop_id = fields.CharField(max_length=255, index=True, unique=True, default=generate_id, description="店铺ID")
    union_shop_id = fields.CharField(max_length=255, index=True, default="", description="用户联盟店铺ID")
    bifrost_shop_id = fields.CharField(max_length=255, index=True, default="", description="eleme-bifrost店铺ID")
    eleme_shop_id = fields.CharField(max_length=255, index=True, default="", description="eleme店铺ID")
    title = fields.CharField(max_length=255, description="店铺名称")
    category_id = fields.CharField(max_length=255, description="店铺分类ID", index=True)
    category = fields.CharField(max_length=255, description="店铺分类")
    logo = fields.CharField(max_length=255, description="店铺logo")
    recommend_reasons = fields.TextField(null=True, description="推荐理由")
    items = fields.JSONField(null=True, description="店铺商品列表")  # type: ignore
    union_detail = fields.JSONField(null=True, description="用户联盟店铺详情")  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "shop"
        table_description = "店铺表"


class Area(SoftDeleteMixin, Model):  # type: ignore
    """商户圈集表"""

    id = fields.BigIntField(pk=True, description="主键ID")
    area_id = fields.CharField(max_length=255, index=True, default=generate_id, description="区域ID", unique=True)
    title = fields.CharField(max_length=255, description="区域名称")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "shop_areas"
        table_description = "区域表"

    class PydanticMeta:
        exclude = ["shops"]


class AreaShops(SoftDeleteMixin, Model):  # type: ignore
    """区域店铺表"""

    id = fields.BigIntField(pk=True, description="主键ID")
    area = fields.ForeignKeyField("models.Area", description="区域", db_constraint=False)  # type: ignore
    shop = fields.ForeignKeyField("models.Shop", description="店铺", db_constraint=False)  # type: ignore
    is_hot = fields.BooleanField(default=False, description="是否热门店铺")
    is_recommend = fields.BooleanField(default=False, description="是否推荐店铺")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "shop_areas_shops"
        table_description = "区域店铺表"
