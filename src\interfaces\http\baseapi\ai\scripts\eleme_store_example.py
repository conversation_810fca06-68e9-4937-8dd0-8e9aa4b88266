#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
饿了么店铺列表查询示例
"""

import json
import os

from ..agent_apis.eleme_store import ElemeStoreClient

# 从环境变量或配置文件中获取配置信息
# 实际使用时请替换为您的真实配置
APP_KEY = os.environ.get("ELEME_APP_KEY", "34813436")
APP_SECRET = os.environ.get("ELEME_APP_SECRET", "929eafdde0fb34c57ff0290e271b54c3")
TOP_GATEWAY_URL = os.environ.get("TOP_GATEWAY_URL", "https://eco.taobao.com/router/rest")
PID = os.environ.get("ELEME_PID", "alsc_28806810_9518007_26738131")

# 示例坐标 (深圳市南山区科技园)
LONGITUDE = "113.9514"
LATITUDE = "22.5406"


def main():
    """主函数"""
    # 创建客户端
    client = ElemeStoreClient(app_key=APP_KEY, app_secret=APP_SECRET, top_gateway_url=TOP_GATEWAY_URL, verify_ssl=True)

    # print("=" * 50)
    # print("饿了么店铺列表查询示例")
    # print("=" * 50)

    # 示例1: 获取默认店铺列表
    print("\n示例1: 获取默认店铺列表")
    print("-" * 30)

    response = client.get_store_list(
        pid=PID,
        longitude=LONGITUDE,
        latitude=LATITUDE,
        sort_type="normal",
        page_size=5,  # 为了演示，只获取5条记录
        include_dynamic=True,
    )

    if "error" in response:
        print(f"获取店铺列表失败: {response['error']}")
    else:
        # 提取店铺数据
        if "data" in response:
            data = response["data"]
            # 检查是否包含stores字段，如果没有则尝试使用records字段
            stores = data.get("stores", data.get("records", []))

            print(f"获取到 {len(stores)} 家店铺")

            # 打印店铺信息
            for i, store in enumerate(stores, 1):

                print(f"\n店铺 {i}:")
                print(store)
        else:
            print(f"数据格式异常: {response}")

    # 示例2: 搜索特定店铺
    # print("\n\n示例2: 搜索特定店铺")
    # print("-" * 30)

    # search_keyword = ""
    # print(f"搜索关键词: {search_keyword}")

    # stores = client.search_stores_by_name(
    #     pid=PID,
    #     longitude=LONGITUDE,
    #     latitude=LATITUDE,
    #     store_name=search_keyword,
    #     max_pages=1  # 为了演示，只获取第一页
    # )

    # print(f"找到 {len(stores)} 家匹配的店铺")

    # # 打印店铺信息
    # for i, store in enumerate(stores, 1):
    #     print(f"\n店铺 {i}:")
    #     print(f"  名称: {store.get('title', store.get('store_name', '未知'))}")
    #     print(f"  类别: {store.get('category_1_name', '未知')}")
    #     print(f"  评分: {store.get('service_rating', '未知')}")
    #     print(f"  销量: {store.get('indistinct_monthly_sales', '未知')}")
    #     print(f"  佣金率: {store.get('commission_rate', '未知')}")

    # 示例3: 使用高级筛选条件
    # print("\n\n示例3: 使用高级筛选条件")
    # print("-" * 30)

    # stores = client.get_store_list_with_pagination(
    #     pid=PID,
    #     longitude=LONGITUDE,
    #     latitude=LATITUDE,
    #     max_pages=1,  # 为了演示，只获取第一页
    #     filter_one_point_five_categories="10"
    # )

    # print(f"找到 {len(stores)} 家符合条件的店铺")
    # # 保存到storelist.json
    # with open("storelist.json", "w") as f:
    #     json.dump(stores, f)

    # # 打印店铺信息
    # for i, store in enumerate(stores[:3], 1):  # 只显示前3家
    #     print(f"\n店铺 {i}:")
    #     print(f"  名称: {store.get('title', store.get('store_name', '未知'))}")
    #     print(f"  类别: {store.get('category_1_name', '未知')}")
    #     print(f"  佣金: {store.get('commission', '未知')}")
    #     print(f"  佣金率: {store.get('commission_rate', '未知')}")
    #     print(f"  销量: {store.get('indistinct_monthly_sales', '未知')}")

    # if len(stores) > 3:
    #     print(f"\n... 还有 {len(stores) - 3} 家店铺未显示")


if __name__ == "__main__":
    main()
