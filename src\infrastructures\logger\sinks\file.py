# encoding: utf-8
# src/infrastructures/logger/sinks/file.py
# created: 2025-08-14 14:45:00

from pathlib import Path

from ..settings import FileSinkConfig
from ..types import LogMessage
from .base import BaseSink


class FileSink(BaseSink):
    """文件输出实现

    输出日志到文件
    支持日志轮转、压缩和保留策略
    """

    def __init__(self, config: FileSinkConfig, app_name: str):
        """初始化文件输出

        Args:
            config: FileSinkConfig 配置
            app_name: 应用名称
        """
        super().__init__(config, app_name)

        # 处理文件路径，支持 {app_name} 占位符
        self.file_path = self._process_file_path(config.path)

        # 确保日志目录存在
        self._ensure_log_dir()

    def _process_file_path(self, path: str) -> Path:
        """处理文件路径

        Args:
            path: 配置的文件路径

        Returns:
            处理后的Path对象
        """
        # 替换应用名称占位符
        path = path.format(app_name=self.app_name)

        # 如果是相对路径，基于项目根目录
        file_path = Path(path)
        if not file_path.is_absolute():
            # 获取项目根目录（假设在 src/infrastructures/logger/sinks/）
            base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
            file_path = base_dir / path

        return file_path

    def _ensure_log_dir(self) -> None:
        """确保日志目录存在"""
        log_dir = self.file_path.parent
        log_dir.mkdir(parents=True, exist_ok=True)

    def write(self, message: LogMessage) -> None:
        """写入消息到文件

        由于使用 loguru 的原生功能，这里直接传递

        Args:
            message: loguru Message 对象
        """
        # loguru 会自动处理文件写入、轮转等
        pass

    def __call__(self, message: LogMessage) -> None:
        """返回文件路径让 loguru 处理

        对于文件输出，我们返回路径而不是自定义处理
        这样可以利用 loguru 的轮转和压缩功能
        """
        pass
