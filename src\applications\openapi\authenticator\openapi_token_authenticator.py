# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/services/authenticator/openapi_token_authenticator.py
# created: 2025-04-11 00:41:43
# updated: 2025-04-11 02:46:13

from typing import TYPE_CHECKING, Optional, Tuple

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, Request
from loguru import logger

from src.domains.customer.entities import CustomerEntity

from .iauthenticator import IAuthenticator

if TYPE_CHECKING:

    from src.repositories.customer import CustomerRepository


class OpenAPITokenAuthenticator(IAuthenticator):
    """Bearer Token认证类"""

    def __init__(self, customer_repo: "CustomerRepository"):
        self.customer_repo = customer_repo

    async def authenticate(self, request: Request) -> Tuple[bool, Optional["CustomerEntity"], str]:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return False, None, "Invalid or missing Bearer token"

        token = auth_header.replace("Bearer ", "")
        try:
            secret = await self.customer_repo.get_secret_by_token(token)
            if not secret:
                return False, None, "Invalid token"
            return True, await CustomerEntity.from_secret(secret), ""
        except Exception as e:
            logger.warning(f"Bearer token authentication error: {e}")
            return False, None, f"Bearer token authentication failed: {str(e)}"
