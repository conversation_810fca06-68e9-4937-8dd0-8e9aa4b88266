# encoding: utf-8
# tests/unit/applications/common/commands/delivery/test_order_sync.py
# created: 2025-08-19 13:10:00

import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.applications.common.commands.delivery import OrderSyncCommandService
from src.databases.models.delivery import OrderStateEnum, SettleStateEnum
from src.domains.delivery.dto import ElemeAddressDTO, ElemeOrderDTO, SubjectDTO
from src.domains.delivery.messages import UnionOrderInfoDTO


@pytest.fixture
def mock_delivery_order_repository():
    """模拟外卖订单仓库"""
    return AsyncMock()


@pytest.fixture
def mock_eleme_order_repository():
    """模拟饿了么订单仓库"""
    return AsyncMock()


@pytest.fixture
def mock_eleme_gateway():
    """模拟饿了么网关"""
    return AsyncMock()


@pytest.fixture
def mock_bifrost_gateway():
    """模拟Bifrost网关"""
    return AsyncMock()


@pytest.fixture
def order_sync_service(
    mock_delivery_order_repository,
    mock_eleme_order_repository,
    mock_eleme_gateway,
    mock_bifrost_gateway,
):
    """创建订单同步服务实例"""
    return OrderSyncCommandService(
        delivery_order_repository=mock_delivery_order_repository,
        eleme_order_repository=mock_eleme_order_repository,
        eleme_union_delivery_gateway=mock_eleme_gateway,
        bifrost_gateway=mock_bifrost_gateway,
    )


@pytest.fixture
def sample_union_order():
    """创建示例联盟订单"""
    return UnionOrderInfoDTO(
        parent_order_id="123456",
        biz_order_id="BIZ123456",
        order_state=3,
        settle_state=1,
        ad_zone_name="test_zone",
        sid="SID:test",
        category_name="餐饮",
        shop_name="测试商店",
        pay_amount=100.0,
        settle_amount=90.0,
        tk_create_time=datetime(2025, 8, 19, 10, 0, 0),
        pay_time=datetime(2025, 8, 19, 10, 30, 0),
        receive_time=datetime(2025, 8, 19, 11, 0, 0),
    )


@pytest.fixture
def sample_eleme_order_data():
    """创建示例饿了么订单数据"""
    return {
        "alscOrderModelInfo": {
            "orderInfo": {
                "buyerUserId": 789012,
                "totalAmount": 100.0,
                "merchantSubsidyAmount": 5.0,
                "agentSubsidyAmount": 3.0,
                "realAmount": 92.0,
                "bizScene": "ELEME_RETAIL",
                "extInfo": {
                    "buyerBindPhone": "***********",
                    "alscAddressInfo": json.dumps({
                        "address": "测试地址",
                        "latitude": 31.23,
                        "longitude": 121.47,
                        "city": "上海市",
                        "district": "浦东新区",
                    }),
                    "userTags": "VIP",
                },
            },
            "paymentOrderInfoList": [
                {
                    "paymentAmount": 92.0,
                    "extInfo": {
                        "tradeType": "ALIPAY",
                        "payTime": "2025-08-19 10:30:00",
                        "ppPayDetail": json.dumps([
                            {
                                "payeeAccountNo": "payee123",
                                "payerAccountNo": "payer456",
                            }
                        ]),
                    },
                }
            ],
            "subOrderInfoList": [
                {
                    "subject": "炸鸡套餐",
                    "realAmount": 50.0,
                    "price": 55.0,
                    "amount": 55.0,
                    "quantity": 1,
                    "unit": "份",
                    "subsidyAmount": 5.0,
                    "merchantSubsidyAmount": 3.0,
                    "agentSubsidyAmount": 2.0,
                },
                {
                    "subject": "可乐",
                    "realAmount": 8.0,
                    "price": 10.0,
                    "amount": 10.0,
                    "quantity": 2,
                    "unit": "瓶",
                    "subsidyAmount": 2.0,
                    "merchantSubsidyAmount": 1.0,
                    "agentSubsidyAmount": 1.0,
                },
            ],
        }
    }


class TestOrderSyncCommandService:
    """订单同步命令服务测试"""

    @pytest.mark.asyncio
    async def test_sync_union_order_already_exists(
        self, order_sync_service, sample_union_order, mock_delivery_order_repository
    ):
        """测试同步已存在的联盟订单"""
        mock_order = MagicMock()
        mock_delivery_order_repository.get_order_by_id_state.return_value = mock_order
        
        await order_sync_service.sync_union_order(sample_union_order)
        
        mock_delivery_order_repository.get_order_by_id_state.assert_called_once_with(
            "123456",
            OrderStateEnum(3),
            SettleStateEnum(1),
        )
        mock_delivery_order_repository.create_or_update.assert_not_called()

    @pytest.mark.asyncio
    async def test_sync_union_order_create_new(
        self, order_sync_service, sample_union_order, mock_delivery_order_repository
    ):
        """测试同步新的联盟订单"""
        mock_delivery_order_repository.get_order_by_id_state.return_value = None
        mock_order = MagicMock()
        mock_order.order_id = "123456"
        mock_delivery_order_repository.create_or_update.return_value = mock_order
        
        await order_sync_service.sync_union_order(sample_union_order)
        
        mock_delivery_order_repository.get_order_by_id_state.assert_called_once()
        mock_delivery_order_repository.create_or_update.assert_called_once()
        
        # 验证传递给create_or_update的数据
        call_args = mock_delivery_order_repository.create_or_update.call_args[0][0]
        assert call_args.order_id == "123456"
        assert call_args.biz_order_id == "BIZ123456"
        assert call_args.order_state == 3
        assert call_args.settle_state == 1

    @pytest.mark.asyncio
    async def test_sync_union_order_with_error(
        self, order_sync_service, sample_union_order, mock_delivery_order_repository
    ):
        """测试同步订单时遇到错误"""
        mock_delivery_order_repository.get_order_by_id_state.side_effect = Exception("Database error")
        
        with pytest.raises(Exception, match="Database error"):
            await order_sync_service.sync_union_order(sample_union_order)

    @pytest.mark.asyncio
    async def test_parse_eleme_order_with_alipay(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析支付宝支付的饿了么订单"""
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        assert isinstance(result, ElemeOrderDTO)
        assert result.order_id == "123456"
        assert result.buyer_uid == "789012"
        assert result.buyer_phone == "***********"
        assert result.total_amount == 100.0
        assert result.payment_amount == 92.0
        assert result.trade_type == "ALIPAY"
        assert result.payee_account_no == "payee123"
        assert result.payer_account_no == "payer456"
        assert len(result.subjects) == 2
        assert result.subjects[0].subject_name == "炸鸡套餐"
        assert result.subjects[1].subject_name == "可乐"

    @pytest.mark.asyncio
    async def test_parse_eleme_order_without_alipay(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析非支付宝支付的饿了么订单"""
        sample_eleme_order_data["alscOrderModelInfo"]["paymentOrderInfoList"][0]["extInfo"]["tradeType"] = "WECHAT"
        
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        assert result.trade_type == "WECHAT"
        assert result.payee_account_no == ""
        assert result.payer_account_no == ""

    @pytest.mark.asyncio
    async def test_parse_eleme_order_with_empty_payment_list(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析没有支付信息的饿了么订单"""
        sample_eleme_order_data["alscOrderModelInfo"]["paymentOrderInfoList"] = []
        
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        # 应该使用空字典作为默认值
        assert result.payment_amount == 0
        assert result.trade_type is None

    @pytest.mark.asyncio
    async def test_parse_eleme_order_with_invalid_pay_time(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析支付时间格式错误的饿了么订单"""
        sample_eleme_order_data["alscOrderModelInfo"]["paymentOrderInfoList"][0]["extInfo"]["payTime"] = "invalid"
        
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        # 应该使用None作为默认值
        assert result.pay_time is None

    @pytest.mark.asyncio
    async def test_sync_eleme_order_detail_already_exists(
        self,
        order_sync_service,
        sample_union_order,
        mock_delivery_order_repository,
        mock_bifrost_gateway,
    ):
        """测试同步已存在的饿了么订单详情"""
        mock_order = MagicMock()
        mock_delivery_order_repository.get_order_by_id_state.return_value = mock_order
        
        await order_sync_service.sync_eleme_order_detail(sample_union_order)
        
        mock_bifrost_gateway.query_orders.assert_not_called()

    @pytest.mark.asyncio
    async def test_sync_eleme_order_detail_create_new(
        self,
        order_sync_service,
        sample_union_order,
        sample_eleme_order_data,
        mock_delivery_order_repository,
        mock_eleme_order_repository,
        mock_bifrost_gateway,
    ):
        """测试同步新的饿了么订单详情"""
        mock_delivery_order_repository.get_order_by_id_state.return_value = None
        mock_bifrost_gateway.query_orders.return_value = {
            "data": {
                "valueList": [sample_eleme_order_data]
            }
        }
        
        mock_order = MagicMock()
        mock_order.order_id = "123456"
        mock_eleme_order_repository.create_or_update.return_value = mock_order
        
        await order_sync_service.sync_eleme_order_detail(sample_union_order)
        
        mock_bifrost_gateway.query_orders.assert_called_once_with(["123456"])
        mock_eleme_order_repository.create_or_update.assert_called_once()
        
        # 验证传递给create_or_update的数据
        call_args = mock_eleme_order_repository.create_or_update.call_args[0][0]
        assert isinstance(call_args, ElemeOrderDTO)
        assert call_args.order_id == "123456"
        assert call_args.buyer_phone == "***********"

    @pytest.mark.asyncio
    async def test_sync_eleme_order_detail_empty_response(
        self,
        order_sync_service,
        sample_union_order,
        mock_delivery_order_repository,
        mock_bifrost_gateway,
    ):
        """测试同步饿了么订单详情时返回空数据"""
        mock_delivery_order_repository.get_order_by_id_state.return_value = None
        mock_bifrost_gateway.query_orders.return_value = {
            "data": {
                "valueList": []
            }
        }
        
        with pytest.raises(IndexError):
            await order_sync_service.sync_eleme_order_detail(sample_union_order)

    @pytest.mark.asyncio
    async def test_sync_eleme_order_detail_with_error(
        self,
        order_sync_service,
        sample_union_order,
        mock_delivery_order_repository,
        mock_bifrost_gateway,
    ):
        """测试同步饿了么订单详情时遇到错误"""
        mock_delivery_order_repository.get_order_by_id_state.return_value = None
        mock_bifrost_gateway.query_orders.side_effect = Exception("Gateway error")
        
        with pytest.raises(Exception, match="Gateway error"):
            await order_sync_service.sync_eleme_order_detail(sample_union_order)

    @pytest.mark.asyncio
    async def test_parse_eleme_order_address_info(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析饿了么订单的地址信息"""
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        assert isinstance(result.address_info, ElemeAddressDTO)
        assert result.address_info.address == "测试地址"
        assert result.address_info.city == "上海市"
        assert result.address_info.district == "浦东新区"

    @pytest.mark.asyncio
    async def test_parse_eleme_order_with_no_user_tags(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析没有用户标签的饿了么订单"""
        del sample_eleme_order_data["alscOrderModelInfo"]["orderInfo"]["extInfo"]["userTags"]
        
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        assert result.user_tag == ""

    @pytest.mark.asyncio
    async def test_parse_eleme_order_subjects(
        self, order_sync_service, sample_eleme_order_data
    ):
        """测试解析饿了么订单的商品信息"""
        result = order_sync_service._parse_eleme_order("123456", sample_eleme_order_data)
        
        assert len(result.subjects) == 2
        
        # 验证第一个商品
        subject1 = result.subjects[0]
        assert isinstance(subject1, SubjectDTO)
        assert subject1.subject_name == "炸鸡套餐"
        assert subject1.real_amount == 50.0
        assert subject1.price == 55.0
        assert subject1.quantity == 1
        assert subject1.unit == "份"
        
        # 验证第二个商品
        subject2 = result.subjects[1]
        assert subject2.subject_name == "可乐"
        assert subject2.quantity == 2
        assert subject2.unit == "瓶"