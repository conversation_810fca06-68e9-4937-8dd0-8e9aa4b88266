# encoding: utf-8
# deploys/growth_hacker/main.py
# created: 2025-07-26 12:25:15

import asyncio

from src.containers import Container
from src.infrastructures.browsers.pools import init_browser_pool
from src.infrastructures.logger import setup
from src.interfaces.growth_hacker import GrowthHackerConsumer
from src.interfaces.growth_hacker.container import lifespan

from .loggers import patch_record
from .settings import config


async def main(container: Container):

    await container.infrastructures.init_tortoise()  # 初始化数据库加载

    # 初始化浏览器池（使用配置初始化）
    browser_pool = init_browser_pool(config.browser)
    await browser_pool.init()

    try:
        # 创建消费者管理器，支持配置预取数量
        consumer_mgr = container.infrastructures.rabbitmq_manager()

        # 注册消费者 - 现在支持多实例配置
        consumer_mgr.register_consumer(GrowthHackerConsumer, instances=config.rabbitmq.consumer_instances)

        # 启动消费者
        await consumer_mgr.start_consumer()
    finally:
        # 清理浏览器池资源
        await browser_pool.close()


if __name__ == "__main__":
    container = Container()
    container.config.from_pydantic(config)
    container.wire(modules=[__name__, "src"])  # 🔧 只wire当前模块

    # 初始化日志系统
    logger_config = {"app_name": "growth_hacker", "sinks": []}

    # 从配置中提取日志配置
    if hasattr(config, "logger") and hasattr(config.logger, "sinks"):
        for sink in config.logger.sinks:
            sink_dict = sink.dict() if hasattr(sink, "dict") else sink
            # 转换 sink 类型
            if sink_dict.get("sink") == "stdout":
                sink_dict["type"] = "console"
            elif sink_dict.get("sink") == "file":
                sink_dict["type"] = "file"
                if "path" not in sink_dict:
                    sink_dict["path"] = "logs/growth_hacker.log"
            logger_config["sinks"].append(sink_dict)
    else:
        # 默认配置
        logger_config["sinks"] = [
            {"type": "console", "level": "INFO"},
            {"type": "file", "level": "DEBUG", "path": "logs/growth_hacker.log"},
        ]

    setup(logger_config)

    asyncio.run(main(container))
