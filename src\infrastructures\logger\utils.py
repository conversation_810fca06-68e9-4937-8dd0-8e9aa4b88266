# encoding: utf-8
# src/infrastructures/logger/utils.py
# created: 2025-08-14 15:00:00

import logging
from typing import Optional

from loguru import logger


class StdlibInterceptor(logging.Handler):
    """标准库日志拦截器

    将Python标准库的日志重定向到loguru
    避免日志系统的混乱和重复
    """

    def __init__(self, level: int = logging.NOTSET):
        """初始化拦截器

        Args:
            level: 日志级别
        """
        super().__init__(level)

    def emit(self, record: logging.LogRecord) -> None:
        """发送日志记录到loguru

        Args:
            record: 标准库日志记录
        """
        # 跳过loguru自己的记录，防止循环
        if record.name.startswith("loguru."):
            return

        # 查找真实的调用者
        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        # 处理异常信息
        exception_info = None
        if record.exc_info:
            exception_info = record.exc_info

        # 使用loguru记录
        logger.opt(depth=depth, exception=exception_info).log(record.levelname, record.getMessage())


def get_project_root() -> str:
    """获取项目根目录

    Returns:
        项目根目录路径
    """
    from pathlib import Path

    # 从当前文件位置推算项目根目录
    # src/infrastructures/logger/utils.py -> 向上4级
    current_file = Path(__file__).resolve()
    project_root = current_file.parent.parent.parent.parent

    return str(project_root)


def sanitize_logstore_name(name: str) -> str:
    """清理logstore名称

    移除或替换不合法的字符

    Args:
        name: 原始名称

    Returns:
        清理后的名称
    """
    # 替换点号为横线（阿里云SLS不支持点号）
    name = name.replace(".", "-")

    # 替换其他特殊字符
    name = name.replace("/", "-")
    name = name.replace("\\", "-")
    name = name.replace(" ", "-")

    # 转换为小写
    name = name.lower()

    return name


def format_size(size_bytes: int) -> str:
    """格式化字节大小

    Args:
        size_bytes: 字节数

    Returns:
        格式化后的字符串，如 "1.5 MB"
    """
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0

    return f"{size_bytes:.2f} PB"


def truncate_message(message: str, max_length: int = 10000) -> str:
    """截断过长的消息

    Args:
        message: 原始消息
        max_length: 最大长度

    Returns:
        截断后的消息
    """
    if len(message) <= max_length:
        return message

    # 保留开头和结尾，中间用省略号
    half_length = (max_length - 20) // 2
    return f"{message[:half_length]}... [truncated] ...{message[-half_length:]}"


def merge_configs(*configs) -> dict:
    """合并多个配置字典

    后面的配置会覆盖前面的配置

    Args:
        *configs: 配置字典

    Returns:
        合并后的配置
    """
    result = {}

    for config in configs:
        if config:
            result.update(config)

    return result


__all__ = [
    "StdlibInterceptor",
    "get_project_root",
    "sanitize_logstore_name",
    "format_size",
    "truncate_message",
    "merge_configs",
]
