# encoding: utf-8
# src/interfaces/schedulers/manager.py
# created: 2025-08-13 10:47:58

import asyncio
import signal
from typing import Any, Callable, Optional

import pytz
from apscheduler.job import Job
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from loguru import logger


class SchedulerManager:
    """调度器管理类，负责调度器的初始化、启动和关闭"""

    def __init__(self, timezone: str = "Asia/Shanghai"):
        self.scheduler = AsyncIOScheduler(
            timezone=pytz.timezone(timezone),
            job_defaults={
                "coalesce": True,
                "max_instances": 5,
                "misfire_grace_time": 60 * 60 * 2,  # 错过执行时间的宽限期（秒）
            },
        )
        self._stop_event = asyncio.Event()
        self._loop: Optional[asyncio.AbstractEventLoop] = None

    def add_job(self, func: Callable, trigger: str, **kwargs) -> Job:
        """添加定时任务"""
        if not callable(func):
            raise ValueError(f"func must be callable, got {type(func)}")
        return self.scheduler.add_job(func, trigger, **kwargs)

    async def _handle_signal(self, sig_type):
        """处理信号"""
        logger.info(f"收到退出信号 {sig_type.name}...")
        self.shutdown(wait=True)  # 关闭调度器

        # 取消所有任务并停止循环
        if self._loop:
            tasks = [t for t in asyncio.all_tasks(self._loop) if t is not asyncio.current_task()]
            if tasks:
                logger.info(f"取消 {len(tasks)} 个任务")
                [task.cancel() for task in tasks]
                await asyncio.gather(*tasks, return_exceptions=True)
            self._loop.stop()

        await self.stop()  # 触发停止事件

    def setup_signal_handlers(self, loop: Optional[asyncio.AbstractEventLoop] = None):
        """设置信号处理程序"""
        self._loop = loop or asyncio.get_running_loop()

        # 为不同的终止信号注册处理程序
        for sig in (signal.SIGINT, signal.SIGTERM):
            self._loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self._handle_signal(s)))  # type: ignore

        logger.info("信号处理程序已设置")

    def start(self):
        """启动调度器"""
        if not self.scheduler.running:
            self.scheduler.start()
            logger.info("调度器已启动")

    def shutdown(self, wait: bool = True):
        """关闭调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=wait)
            logger.info("调度器已安全关闭")

    async def stop(self):
        """触发停止事件"""
        self._stop_event.set()

    async def wait_for_stop(self):
        """等待停止事件"""
        await self._stop_event.wait()
