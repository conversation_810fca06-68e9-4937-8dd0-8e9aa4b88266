# encoding: utf-8
# src/domains/growth_tasks/services/user_profile.py
# created: 2025-07-27 15:35:11

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from src.databases.models.growth_hacker import UserDeviceCache
    from src.infrastructures.browsers import BrowserInstanceData
    from src.repositories.growth_hacker import UserRepository


class UserProfileDomainService:

    def __init__(self, user_repository: "UserRepository") -> None:
        self.user_repository = user_repository

    async def get_user_profile(self, phone: str) -> Optional["UserDeviceCache"]:
        """获取用户缓存"""
        result = await self.user_repository.get_by_phone(phone)
        return result

    async def update_user_profile(self, phone: str, browser_data: dict) -> None:
        """更新用户缓存"""
        local_storage = browser_data.get("local_storage", {})
        session_storage = browser_data.get("session_storage", {})
        cookies = browser_data.get("cookies", [])
        device_config = browser_data.get("device_config", {})
        webview_config = browser_data.get("webview_config", {})

        await self.user_repository.upsert_user_profile(
            phone, local_storage, session_storage, cookies, device_config, webview_config
        )
