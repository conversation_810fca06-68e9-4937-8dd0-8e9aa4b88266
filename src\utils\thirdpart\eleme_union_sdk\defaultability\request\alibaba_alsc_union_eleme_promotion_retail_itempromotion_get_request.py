from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionRetailItempromotionGetRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.retail.itempromotion.get"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionRetailItempromotionGetRetailItemPromotionDetailRequest:
    def __init__(self, pid: str = None, item_id: str = None, sid: str = None, include_wx_img: bool = None):
        """
        推广位
        """
        self.pid = pid
        """
            商品ID
        """
        self.item_id = item_id
        """
            会员ID（需要联系运营申请）
        """
        self.sid = sid
        """
            是否返回微信推广图片
        """
        self.include_wx_img = include_wx_img
