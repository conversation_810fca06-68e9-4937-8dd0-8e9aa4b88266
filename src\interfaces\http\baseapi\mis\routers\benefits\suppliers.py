# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/benefits/suppliers.py
# created: 2024-12-08 01:45:04
# updated: 2025-05-26 11:04:22

from fastapi import APIRouter, Query

from src.domains.benefits.dto import SupplierCreateDTO, SupplierDetailDTO, SupplierDTO, SupplierUpdateDTO
from src.infrastructures import errors
from src.repositories.benefits.supplier import SupplierFilters, SupplierRepository

from ...schemas import BaseResponse, ListData, SupplierListResponse, SupplierResponse

router = APIRouter(tags=["benefits", "suppliers"])


@router.delete("/suppliers/{supplier_id}", response_model=SupplierResponse)
async def delete_supplier(supplier_id: int):
    await SupplierRepository.delete_by_id(supplier_id)
    return BaseResponse(message="供应商删除成功")


@router.post("/suppliers", response_model=SupplierResponse)
async def create_supplier(supplier: SupplierCreateDTO):  # type: ignore
    supplier = await SupplierRepository.create(supplier)
    return SupplierResponse(data=await SupplierDetailDTO.from_tortoise_orm(supplier))


@router.put("/suppliers/{supplier_id}", response_model=SupplierResponse)
async def update_supplier(supplier_id: int, update_fields: SupplierUpdateDTO):  # type: ignore
    supplier = await SupplierRepository.update_supplier_by_id(id=supplier_id, update_fields=update_fields)
    if supplier is None:
        raise errors.SupplierNotFoundError
    return SupplierResponse(data=await SupplierDetailDTO.from_tortoise_orm(supplier))


@router.get("/suppliers", response_model=SupplierListResponse)
async def get_supplier_list(filters: SupplierFilters = Query(...)):
    total, suppliers = await SupplierRepository.gets_by_filters(filters)
    supplier_dtos = [await SupplierDTO.from_tortoise_orm(supplier) for supplier in suppliers]
    return SupplierListResponse(data=ListData(total=total, data=supplier_dtos))


@router.get("/suppliers/{supplier_id}", response_model=SupplierResponse)
async def get_supplier_detail(supplier_id: int):
    supplier = await SupplierRepository.get_by_id(supplier_id)
    if supplier is None:
        raise errors.SupplierNotFoundError
    return SupplierResponse(data=await SupplierDetailDTO.from_tortoise_orm(supplier))
