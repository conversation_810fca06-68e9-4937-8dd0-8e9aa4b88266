# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/customers/customers.py
# created: 2025-02-06 15:08:37
# updated: 2025-05-26 11:03:31

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

from src.domains.customer.dto import CustomerCreateFields, CustomerDTO, CustomerUpdateFields
from src.domains.passport.dto import AuthPassportUserDTO
from src.infrastructures import errors
from src.interfaces.http.baseapi import Container
from src.interfaces.http.baseapi.base.authorization import base_api_authentication
from src.repositories.customer import CustomerFilters, CustomerRepository

from ...schemas import (
    CustomerListResponse,
    CustomerRechargeRecordListResponse,
    CustomerResponse,
    ListData,
    RechargePayload,
)

if TYPE_CHECKING:
    from src.applications.openapi.services.customer import CustomerService

router = APIRouter(tags=["customer", "customer.customer"])


@router.post("/", response_model=CustomerResponse)
@inject
async def create_customer(
    create_fields: CustomerCreateFields,
    customer_service: "CustomerService" = Depends(Provide[Container.applications.openapi_customer_service]),
):
    if not create_fields.name or create_fields.name == "":
        raise errors.CustomerNameEmptyError
    customer = await customer_service.create_customer(create_fields)
    return CustomerResponse(data=customer)


@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(customer_id: int, update_fields: CustomerUpdateFields):
    customer = await CustomerRepository.update_customer(customer_id=customer_id, update_fields=update_fields)
    if not customer:
        raise errors.CustomerNotFoundError
    return CustomerResponse(data=await CustomerDTO.from_tortoise_orm(customer))


@router.get("/", response_model=CustomerListResponse)
async def get_customer_list(params: CustomerFilters = Query(...)):
    customers, total = await CustomerRepository.get_customer_list(params)
    customer_list = [await CustomerDTO.from_tortoise_orm(customer) for customer in customers]
    return CustomerListResponse(data=ListData(total=total, data=customer_list))


@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer_detail(customer_id: int):
    customer = await CustomerRepository.get_by_id(customer_id)
    if not customer:
        raise errors.CustomerNotFoundError
    return CustomerResponse(data=await CustomerDTO.from_tortoise_orm(customer))


@router.delete("/{customer_code}", response_model=CustomerResponse)
async def delete_customer(customer_code: str):
    customer = await CustomerRepository.get_by_code(customer_code)
    if not customer:
        raise errors.CustomerNotFoundError
    await CustomerRepository.delete_by_code(customer_code)
    return CustomerResponse(data=await CustomerDTO.from_tortoise_orm(customer))


@router.post("/{customer_id}/recharge", response_model=CustomerResponse, name="客户充值")
@inject
async def recharge_customer(
    customer_id: int,
    payload: RechargePayload,
    current_user: AuthPassportUserDTO = Depends(base_api_authentication),
    customer_service: "CustomerService" = Depends(Provide[Container.applications.openapi_customer_service]),
):
    customer = await customer_service.recharge(
        customer_id,
        payload.amount,
        payload.description or "",
        operator_id=str(current_user.uid),  # type: ignore
        operator_name=current_user.nickname,  # type: ignore
    )
    return CustomerResponse(data=customer)


@router.get("/{customer_id}/recharge_records", response_model=CustomerRechargeRecordListResponse, name="客户充值记录")
@inject
async def get_recharge_records(
    customer_id: int,
    customer_service: "CustomerService" = Depends(Provide[Container.applications.openapi_customer_service]),
):
    records = await customer_service.get_recharge_records(customer_id)
    return CustomerRechargeRecordListResponse(data=records)
