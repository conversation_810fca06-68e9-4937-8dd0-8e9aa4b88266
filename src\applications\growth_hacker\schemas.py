# encoding: utf-8

from datetime import datetime
from typing import List
from uuid import uuid4

from pydantic import BaseModel, Field, HttpUrl

from src.infrastructures.rabbitmq import message_creator


class WashOrderTask(BaseModel):
    task_id: str
    phone: str
    city: str = ""
    lat: str
    lng: str
    access_url: str
    success: bool
    processed_at: str


class InputData(BaseModel):
    generated_at: str
    total_count: int
    success_count: int
    results: List[WashOrderTask]


class RedPacketTask(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid4()))
    phone: str = Field(..., pattern=r"^1[3-9]\d{9}$")  # 手机号验证
    latitude: float = Field(..., ge=-90, le=90)  # 纬度验证
    longitude: float = Field(..., ge=-180, le=180)  # 经度验证
    target_url: HttpUrl  # URL验证

    priority: int = Field(default=5, ge=1, le=10)  # 优先级，1-10，10为最高优先级
    max_retries: int = Field(default=3, ge=0, le=10)  # 最大重试次数


class TaskMessageContent(BaseModel):
    phone: str = Field(..., description="用户手机号")
    city: str = Field(default="", description="城市")
    lat: str | float = Field(..., description="纬度")
    lng: str | float = Field(..., description="经度")
    access_url: str = Field(..., description="访问URL")
    task_id: str = Field(..., description="任务ID")
    batch_name: str = Field(default="", description="批次名称")


TaskMessage = message_creator(
    "WashOrderTaskMessage", TaskMessageContent, "delivery.wash_order_task", exchange_name="delivery.topic"
)
