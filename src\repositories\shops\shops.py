# encoding: utf-8
# src/repositories/shops/shops.py
# created: 2025-08-04 11:16:08

from typing import Optional

from src.databases.models.shops import AreaShops, Shop
from src.infrastructures.gateways.eleme.union.dto import UnionShopDto


class ShopRepository:

    async def get_by_shop_id(self, shop_id: str) -> Optional[Shop]:
        return await Shop.filter(shop_id=shop_id).first()

    async def create_shop_from_union_data(self, union_shop: "UnionShopDto") -> Shop:
        """从联盟数据创建店铺"""
        # 处理商品列表
        items_data = []
        for item in union_shop.items:
            items_data.append(
                {
                    "title": item.title,
                    "picture": item.picture,
                    "price": int(float(item.price) * 100) if isinstance(item.price, str) else item.price * 100,
                    "origin_price": (
                        int(float(item.origin_price) * 100)
                        if item.origin_price and isinstance(item.origin_price, str)
                        else 0
                    ),
                }
            )

        return await Shop.create(
            union_shop_id=union_shop.shop_id,
            title=union_shop.title,
            logo=union_shop.shop_logo,
            category_id=union_shop.category_1_id,
            category=union_shop.category_1_name,
            recommend_reasons=", ".join(union_shop.recommend_reasons or []),
            items=items_data,
            union_detail=union_shop.model_dump(),
        )

    async def create_or_update_shop_from_union_data(self, union_shop: "UnionShopDto") -> Shop:
        """从联盟数据创建或更新店铺"""
        # 尝试根据shop_id查找现有店铺
        existing_shop = await Shop.get_or_none(union_shop_id=union_shop.shop_id)

        # 处理商品列表
        items_data = []
        for item in union_shop.items:
            items_data.append(
                {
                    "title": item.title,
                    "picture": item.picture,
                    "price": int(float(item.price) * 100) if isinstance(item.price, str) else item.price * 100,
                    "origin_price": (
                        int(float(item.origin_price) * 100)
                        if item.origin_price and isinstance(item.origin_price, str)
                        else 0
                    ),
                }
            )

        if existing_shop:
            # 更新现有店铺
            existing_shop.title = union_shop.title
            existing_shop.logo = union_shop.shop_logo
            existing_shop.union_shop_id = union_shop.shop_id
            existing_shop.recommend_reasons = ", ".join(union_shop.recommend_reasons or [])
            existing_shop.items = items_data
            existing_shop.category_id = union_shop.category_1_id
            existing_shop.category = union_shop.category_1_name
            existing_shop.union_detail = union_shop.model_dump()
            await existing_shop.save()
            return existing_shop  # type: ignore
        else:
            # 创建新店铺
            return await self.create_shop_from_union_data(union_shop)

    async def get_shops_by_area(self, area_id: str) -> list[AreaShops]:
        return await AreaShops.filter(area__area_id=area_id).prefetch_related("shop").all()

    async def get_hot_shops_by_area(self, area_id: str) -> list[AreaShops]:
        return await AreaShops.filter(area__area_id=area_id, is_hot=True).prefetch_related("shop").all()

    async def get_recommend_shops_by_area(self, area_id: str) -> list[AreaShops]:
        return await AreaShops.filter(area__area_id=area_id, is_recommend=True).prefetch_related("shop").all()
