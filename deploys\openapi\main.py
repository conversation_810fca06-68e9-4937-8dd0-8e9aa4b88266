# encoding: utf-8
# deploys/openapi/main.py
# created: 2025-07-23 16:30:00

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from .settings import config


def setup_container():
    """设置依赖注入容器"""
    from src.interfaces.http.openapi.main import container

    # 重新配置已存在的容器实例
    container.config.from_pydantic(config)

    # 进行依赖注入的线路连接
    container.wire(
        modules=[
            __name__,
            "src.interfaces.http.openapi",
            "src.interfaces.http.openapi.authorization",
            "src.interfaces.http.openapi.routers",
            "src.interfaces.http.openapi.routers.account",
            "src.interfaces.http.openapi.routers.benefits",
            "src.interfaces.http.openapi.routers.delivery",
            "src.interfaces.http.openapi.routers.dine_in",
        ],
        packages=[
            "src.domains.benefits.services.charge_strategy",
            "src.applications.common.commands.benefits",
        ],
    )

    return container


def create_app():
    """创建并配置 FastAPI 应用（用于多进程模式）"""
    setup_container()
    from src.interfaces.http.openapi.main import create_app as _create_app

    return _create_app(config)


if __name__ == "__main__":
    # 设置容器
    setup_container()

    import uvicorn

    fastapi_settings = config.fastapi
    uvicorn.run(
        "deploys.openapi.main:create_app",
        host="0.0.0.0",
        port=fastapi_settings.port,
        workers=fastapi_settings.workers,
        reload=False,
        factory=True,
    )
