# encoding: utf-8
# src/infrastructures/settings/base.py
# created: 2025-08-18 15:31:00

from pathlib import Path
from typing import Optional, Tuple, Type

from pydantic import Field
from pydantic_settings import (
    BaseSettings,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
    TomlConfigSettingsSource,
)

from src.infrastructures.databases import DatabaseSettings
from src.infrastructures.fastapi import FastapiSettings
from src.infrastructures.gateways import (
    BifrostSettings,
    ElemeUnionSettings,
    WifiMasterSettings,
)
from src.infrastructures.gateways.aliyun import AliyunSmsSettings
from src.infrastructures.rabbitmq import RabbitmqSettings


class BaseServiceSettings(BaseSettings):
    """基础服务配置类，所有服务配置都应继承此类"""

    # 通用配置字段
    rabbitmq: RabbitmqSettings = Field(default_factory=RabbitmqSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    bifrost: BifrostSettings = Field(default_factory=BifrostSettings)
    wifimaster: WifiMasterSettings = Field(default_factory=WifiMasterSettings)
    eleme_union: ElemeUnionSettings = Field(default_factory=ElemeUnionSettings)

    # 服务特定字段由子类定义

    @classmethod
    def get_config_files(cls) -> Tuple[Path, Path]:
        """获取配置文件路径，子类可以重写此方法"""
        import inspect

        # 获取调用类的文件路径
        caller_file = Path(inspect.getfile(cls))
        parent_dir = caller_file.parent

        toml_file = parent_dir / ".env.toml"
        env_file = parent_dir / ".env"
        return toml_file, env_file

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        """
        自定义配置源优先级
        优先级顺序：环境变量 > .env文件 > TOML文件
        这样可以确保 SAE 等云环境中设置的环境变量具有最高优先级
        """
        sources = []

        # 获取配置文件路径
        toml_file, env_file = cls.get_config_files()

        # 1. 环境变量优先级最高
        sources.append(env_settings)

        # 2. .env 文件次之（如果存在）
        if env_file.exists():
            sources.append(dotenv_settings)

        # 3. TOML 文件优先级最低（作为默认配置）
        if toml_file.exists():
            sources.append(TomlConfigSettingsSource(settings_cls))

        return tuple(sources)


class BaseApiSettings(BaseServiceSettings):
    """API 服务基础配置"""

    fastapi: FastapiSettings = Field(default_factory=FastapiSettings)
    sms: AliyunSmsSettings = Field(default_factory=AliyunSmsSettings)

    model_config = SettingsConfigDict(
        env_nested_delimiter="__",  # 方便嵌套字段注入
    )
