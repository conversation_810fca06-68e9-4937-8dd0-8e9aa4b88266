# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/13 13:14


import hashlib
import json
import time
from typing import Any, Dict, Optional, Tuple, Union

from loguru import logger

from src.utils.thirdpart.openapi_client import OpenApiClientBase
from src.utils.thirdpart.shinesun_sdk.schemas import Account, Product, ShinesunChargePayload, ShinesunChargeResult


class ShineSunClient(OpenApiClientBase):

    def __init__(self, base_url: str, key: str, partner_id: Union[int, str]):
        self.key = key
        self.partner_id = str(partner_id)
        super().__init__(base_url)

    async def _async_prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        return params, payload  # type: ignore

    def _create_signature(self, params: dict) -> str:
        """根据指定的签名算法生成 MD5 签名"""
        sorted_params = {k: v for k, v in sorted(params.items()) if v is not None and k != "sign"}
        param_str = "".join(f"{k}{v}" for k, v in sorted_params.items()) + str(self.key)
        return hashlib.md5(param_str.encode("utf-8")).hexdigest()

    def _prepare_params(
        self, method: str, params: Optional[Dict[str, Any]] = None, payload: Optional[Dict[str, Any]] = None
    ) -> Tuple[Any, Any]:
        tmp_params = (params if method.upper() == "GET" else payload) or {}
        tmp_params["macid"] = self.partner_id
        tmp_params["time"] = int(time.time())
        tmp_params["sign"] = self._create_signature(tmp_params)
        return (tmp_params, payload) if method.upper() == "GET" else (params, tmp_params)

    def charge(self, payload: ShinesunChargePayload) -> ShinesunChargeResult:
        """执行充值下单操作"""
        endpoint = "/api/owned/charge"
        data = payload.model_dump(by_alias=True)
        response = self._request("POST", endpoint, payload=data)
        if response["errCode"] != 0:
            logger.error(f"sunshine apis error, errorCode: {response["errCode"]}, errInfo: {response["errInfo"]}")
            raise Exception(f"充值失败, {response.get("errInfo")}")
        result = ShinesunChargeResult(**response["data"], status=response["orderStatus"], detail=json.dumps(response))
        return result

    def query_order(self, order_id: str):
        """查询订单状态"""
        endpoint = "/api/owned/query"
        params = {"id": order_id}
        response = self._request("GET", endpoint, params)
        if response["errCode"] != 0:
            logger.error(f"sunshine apis error, errorCode: {response["errCode"]}, errInfo: {response["errInfo"]}")
            raise Exception("shine sun query_order failed")
        result = ShinesunChargeResult(**response["data"], status=response["orderStatus"], detail=json.dumps(response))
        return result

    def check_balance(self) -> Account:
        """查询余额"""
        endpoint = "/api/macinfo/balance"
        response = self._request("GET", endpoint, {})
        if response["errCode"] != 0:
            logger.error(f"sunshine apis error, errorCode: {response["errCode"]}, errInfo: {response["errInfo"]}")
            raise Exception("查询余额失败")
        return Account.model_validate(response["data"])

    def query_products(self, phone: Optional[str] = None, ids_str: Optional[str] = None) -> list[Product]:
        """查询产品列表"""
        endpoint = "/api/owned/product"
        data = {"phone": phone, "idsstr": ids_str}  # noqa
        response = self._request("POST", endpoint, payload=data)
        if response["errCode"] != 0:
            logger.error(f"sunshine apis error, errorCode: {response["errCode"]}, errInfo: {response["errInfo"]}")
            raise Exception("查询产品列表失败")
        return [Product.model_validate(p) for p in response["data"]]
