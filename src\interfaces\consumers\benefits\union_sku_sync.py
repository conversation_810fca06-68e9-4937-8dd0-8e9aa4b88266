# encoding: utf-8
# src/interfaces/consumers/benefits/union_sku_sync.py
# created: 2025-04-05 23:30:51

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.domains.benefits.messages import UnionSKUSyncMessage, UnionSKUSyncMessageContent
from src.infrastructures.rabbitmq import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import UnionSkuSyncCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class UnionSKUSyncConsumer(BaseConsumer):
    """处理UnionSKU同步消息, 用于SKU库存管理"""

    queue_name = "benefits_union_sku_sync"
    routing_key = "eleme.union.sku_sync"
    exchange_name = "benefits.topic"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        sync_service: "UnionSkuSyncCommandService",
    ):
        self.sync_service = sync_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = UnionSKUSyncMessage.model_validate_json(message.body)
        payload: UnionSKUSyncMessageContent = msg.payload
        logger.info(f"UnionSKUSyncConsumer 收到消息: sku_id={payload.sku_id}")

        await self.sync_service.sync_sku(payload.sku_id, payload.stock)

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - SKU同步失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "UnionSKUSyncConsumer 回滚: 消息[{message_id}] 同步失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
