# encoding: utf-8
# src/infrastructures/exceptions/growth_hacker.py
# created: 2025-08-08 04:10:54

"""
Growth Hacker 异常体系统一定义

按业务模块和严重程度分类的完整异常体系，提供统一的错误处理机制。
"""

from enum import Enum
from typing import Any, Dict, Optional

from src.infrastructures.exceptions import RetryableError


class ErrorSeverity(Enum):
    """错误严重程度"""

    LOW = "low"  # 轻微错误，可以继续执行
    MEDIUM = "medium"  # 中等错误，影响当前操作
    HIGH = "high"  # 严重错误，影响任务执行
    CRITICAL = "critical"  # 致命错误，需要人工介入


class GrowthHackerError(RetryableError):
    """Growth Hacker 异常基类 - 继承自 RetryableError 以支持重试机制"""

    def __init__(
        self,
        message: str,
        error_code: str = "GH_0000",
        retryable: bool = False,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
    ):
        # 调用父类构造函数，传递 retryable 参数
        super().__init__(message=message, retryable=retryable)
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}

    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "retryable": self.retryable,
            "severity": self.severity.value,
            "context": self.context,
        }


# ================================
# 配置相关异常
# ================================


class ConfigurationError(GrowthHackerError):
    """配置错误基类"""

    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"配置错误: {message}",
            error_code="GH_1000",
            retryable=False,
            severity=ErrorSeverity.HIGH,
            context=context,
        )


class InvalidConfigError(ConfigurationError):
    """无效配置错误"""

    def __init__(self, config_key: str, config_value: Any = None):
        context = {"config_key": config_key, "config_value": config_value}
        super().__init__(f"无效配置项 '{config_key}': {config_value}", context=context)
        self.error_code = "GH_1001"


# ================================
# 资源相关异常
# ================================


class ResourceError(GrowthHackerError):
    """资源错误基类"""

    def __init__(self, message: str, retryable: bool = True, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"资源错误: {message}",
            error_code="GH_2000",
            retryable=retryable,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )


class NoIpProxyError(ResourceError):
    """无可用IP代理错误"""

    def __init__(self, city: Optional[str] = None, pool_size: int = 0):
        context = {"city": city, "pool_size": pool_size}
        message = "无可用IP代理"
        if city:
            message += f" (城市: {city})"
        super().__init__(message, retryable=True, context=context)
        self.error_code = "GH_2001"


class BrowserPoolError(ResourceError):
    """浏览器池错误"""

    def __init__(self, message: str, pool_status: Optional[Dict] = None):
        context = {"pool_status": pool_status or {}}
        super().__init__(message, retryable=True, context=context)
        self.error_code = "GH_2002"


class BrowserInitError(ResourceError):
    """浏览器初始化错误"""

    def __init__(self, message: str, browser_config: Optional[Dict] = None):
        context = {"browser_config": browser_config or {}}
        super().__init__(message, retryable=True, context=context)
        self.error_code = "GH_2003"


# ================================
# 任务相关异常
# ================================


class TaskError(GrowthHackerError):
    """任务错误基类"""

    def __init__(
        self,
        message: str,
        task_id: Optional[str] = None,
        retryable: bool = False,
        context: Optional[Dict[str, Any]] = None,
    ):
        if context is None:
            context = {}
        if task_id:
            context["task_id"] = task_id
        super().__init__(
            message=f"任务错误: {message}",
            error_code="GH_3000",
            retryable=retryable,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )


class TaskNotFoundError(TaskError):
    """任务不存在错误"""

    def __init__(self, task_id: str):
        super().__init__(f"任务不存在: {task_id}", task_id=task_id, retryable=False)
        self.error_code = "GH_3001"
        self.severity = ErrorSeverity.LOW


class TaskValidationError(TaskError):
    """任务验证错误"""

    def __init__(self, message: str, task_id: Optional[str] = None, validation_errors: Optional[list] = None):
        context = {"validation_errors": validation_errors or []}
        super().__init__(message, task_id=task_id, retryable=False, context=context)
        self.error_code = "GH_3002"


class TaskStateError(TaskError):
    """任务状态错误"""

    def __init__(
        self,
        message: str,
        task_id: Optional[str] = None,
        current_state: Optional[str] = None,
        expected_state: Optional[str] = None,
    ):
        context = {"current_state": current_state, "expected_state": expected_state}
        super().__init__(message, task_id=task_id, retryable=False, context=context)
        self.error_code = "GH_3003"


class AlreadyClaimedError(TaskError):
    """红包已领取错误"""

    def __init__(self, message: str = "红包已被领取", task_id: Optional[str] = None, claim_info: Optional[Dict] = None):
        context = {"claim_info": claim_info or {}}
        super().__init__(message, task_id=task_id, retryable=False, context=context)
        self.error_code = "GH_3004"
        self.severity = ErrorSeverity.LOW


# ================================
# 交互相关异常
# ================================


class InteractionError(GrowthHackerError):
    """交互错误基类"""

    def __init__(
        self,
        message: str,
        page_url: Optional[str] = None,
        retryable: bool = True,
        context: Optional[Dict[str, Any]] = None,
    ):
        if context is None:
            context = {}
        if page_url:
            context["page_url"] = page_url
        super().__init__(
            message=f"交互错误: {message}",
            error_code="GH_4000",
            retryable=retryable,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )


class NavigationError(InteractionError):
    """页面导航错误"""

    def __init__(self, message: str, target_url: Optional[str] = None, current_url: Optional[str] = None):
        context = {"target_url": target_url, "current_url": current_url}
        super().__init__(message, retryable=True, context=context)
        self.error_code = "GH_4001"


class ElementNotFoundError(InteractionError):
    """页面元素未找到错误"""

    def __init__(self, element_selector: str, page_url: Optional[str] = None, wait_timeout: int = 0):
        context = {"element_selector": element_selector, "wait_timeout": wait_timeout}
        super().__init__(f"元素未找到: {element_selector}", page_url=page_url, retryable=True, context=context)
        self.error_code = "GH_4002"


class PageContentError(InteractionError):
    """页面内容错误"""

    def __init__(
        self,
        message: str,
        page_url: Optional[str] = None,
        content_length: int = 0,
        content_snippet: Optional[str] = None,
    ):
        context = {
            "content_length": content_length,
            "content_snippet": content_snippet[:200] if content_snippet else None,
        }
        super().__init__(message, page_url=page_url, retryable=True, context=context)
        self.error_code = "GH_4003"


class ElementInteractionError(InteractionError):
    """元素交互错误"""

    def __init__(self, message: str, element_selector: str, action: str, page_url: Optional[str] = None):
        context = {"element_selector": element_selector, "action": action}
        super().__init__(message, page_url=page_url, retryable=True, context=context)
        self.error_code = "GH_4004"


# ================================
# 安全相关异常
# ================================


class SecurityError(GrowthHackerError):
    """安全错误基类"""

    def __init__(self, message: str, page_url: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        if context is None:
            context = {}
        if page_url:
            context["page_url"] = page_url
        super().__init__(
            message=f"安全检测: {message}",
            error_code="GH_5000",
            retryable=False,
            severity=ErrorSeverity.HIGH,
            context=context,
        )


class RiskDetectedError(SecurityError):
    """风险检测错误"""

    def __init__(
        self, message: str, risk_type: str, page_url: Optional[str] = None, detection_details: Optional[Dict] = None
    ):
        context = {"risk_type": risk_type, "detection_details": detection_details or {}}
        super().__init__(f"检测到风险 ({risk_type}): {message}", page_url=page_url, context=context)
        self.error_code = "GH_5001"
        self.severity = ErrorSeverity.CRITICAL


class CaptchaError(SecurityError):
    """验证码错误"""

    def __init__(self, message: str = "遇到验证码", captcha_type: str = "unknown", page_url: Optional[str] = None):
        context = {"captcha_type": captcha_type}
        super().__init__(message, page_url=page_url, context=context)
        self.error_code = "GH_5002"
        self.severity = ErrorSeverity.HIGH


class AntiCrawlerError(SecurityError):
    """反爬虫检测错误"""

    def __init__(self, message: str, detection_method: str, page_url: Optional[str] = None):
        context = {"detection_method": detection_method}
        super().__init__(message, page_url=page_url, context=context)
        self.error_code = "GH_5003"
        self.severity = ErrorSeverity.HIGH


# ================================
# 网络相关异常
# ================================


class NetworkError(GrowthHackerError):
    """网络错误基类"""

    def __init__(self, message: str, url: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        if context is None:
            context = {}
        if url:
            context["url"] = url
        super().__init__(
            message=f"网络错误: {message}",
            error_code="GH_6000",
            retryable=True,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )


class TimeoutError(NetworkError):
    """网络超时错误"""

    def __init__(self, message: str = "请求超时", url: Optional[str] = None, timeout_seconds: int = 0):
        context = {"timeout_seconds": timeout_seconds}
        super().__init__(message, url=url, context=context)
        self.error_code = "GH_6001"


class ConnectionError(NetworkError):
    """连接错误"""

    def __init__(self, message: str, url: Optional[str] = None, retry_count: int = 0):
        context = {"retry_count": retry_count}
        super().__init__(message, url=url, context=context)
        self.error_code = "GH_6002"


class ProxyConnectionError(NetworkError):
    """代理连接错误"""

    def __init__(
        self,
        message: str,
        error_type: str,  # ERR_TUNNEL_CONNECTION_FAILED, ERR_SSL_PROTOCOL_ERROR 等
        proxy_server: Optional[str] = None,
        url: Optional[str] = None,
    ):
        context = {"error_type": error_type, "proxy_server": proxy_server}
        super().__init__(f"代理连接失败 ({error_type}): {message}", url=url, context=context)
        self.error_code = "GH_6003"
        self.error_type = error_type


class ProxyTunnelError(ProxyConnectionError):
    """代理隧道连接失败"""

    def __init__(self, proxy_server: Optional[str] = None, url: Optional[str] = None):
        super().__init__("代理服务器隧道建立失败", "ERR_TUNNEL_CONNECTION_FAILED", proxy_server=proxy_server, url=url)
        self.error_code = "GH_6004"


class ProxySSLError(ProxyConnectionError):
    """代理SSL协议错误"""

    def __init__(self, proxy_server: Optional[str] = None, url: Optional[str] = None):
        super().__init__("代理服务器SSL握手失败", "ERR_SSL_PROTOCOL_ERROR", proxy_server=proxy_server, url=url)
        self.error_code = "GH_6005"


# ================================
# 环境准备异常
# ================================


class PrepareEnvironmentError(GrowthHackerError):
    """环境准备错误"""

    def __init__(self, message: str, step: str, context: Optional[Dict[str, Any]] = None):
        if context is None:
            context = {}
        context["step"] = step
        super().__init__(
            message=f"环境准备失败 ({step}): {message}",
            error_code="GH_7000",
            retryable=True,
            severity=ErrorSeverity.MEDIUM,
            context=context,
        )


# ================================
# 便捷函数
# ================================


def get_error_by_code(error_code: str) -> Optional[type]:
    """根据错误码获取异常类"""
    error_map = {
        "GH_1000": ConfigurationError,
        "GH_1001": InvalidConfigError,
        "GH_2000": ResourceError,
        "GH_2001": NoIpProxyError,
        "GH_2002": BrowserPoolError,
        "GH_2003": BrowserInitError,
        "GH_3000": TaskError,
        "GH_3001": TaskNotFoundError,
        "GH_3002": TaskValidationError,
        "GH_3003": TaskStateError,
        "GH_3004": AlreadyClaimedError,
        "GH_4000": InteractionError,
        "GH_4001": NavigationError,
        "GH_4002": ElementNotFoundError,
        "GH_4003": PageContentError,
        "GH_4004": ElementInteractionError,
        "GH_5000": SecurityError,
        "GH_5001": RiskDetectedError,
        "GH_5002": CaptchaError,
        "GH_5003": AntiCrawlerError,
        "GH_6000": NetworkError,
        "GH_6001": TimeoutError,
        "GH_6002": ConnectionError,
        "GH_6003": ProxyConnectionError,
        "GH_6004": ProxyTunnelError,
        "GH_6005": ProxySSLError,
        "GH_7000": PrepareEnvironmentError,
    }
    return error_map.get(error_code)


def is_retryable_error(error: Exception) -> bool:
    """判断异常是否可重试"""
    if isinstance(error, GrowthHackerError):
        return error.retryable
    return False


def get_error_severity(error: Exception) -> ErrorSeverity:
    """获取异常严重程度"""
    if isinstance(error, GrowthHackerError):
        return error.severity
    return ErrorSeverity.MEDIUM


# ================================
# 导出所有异常类
# ================================

__all__ = [
    # 基础类
    "ErrorSeverity",
    "GrowthHackerError",
    # 配置异常
    "ConfigurationError",
    "InvalidConfigError",
    # 资源异常
    "ResourceError",
    "NoIpProxyError",
    "BrowserPoolError",
    "BrowserInitError",
    # 任务异常
    "TaskError",
    "TaskNotFoundError",
    "TaskValidationError",
    "TaskStateError",
    "AlreadyClaimedError",
    # 交互异常
    "InteractionError",
    "NavigationError",
    "ElementNotFoundError",
    "PageContentError",
    "ElementInteractionError",
    # 安全异常
    "SecurityError",
    "RiskDetectedError",
    "CaptchaError",
    "AntiCrawlerError",
    # 网络异常
    "NetworkError",
    "TimeoutError",
    "ConnectionError",
    "ProxyConnectionError",
    "ProxyTunnelError",
    "ProxySSLError",
    # 环境异常
    "PrepareEnvironmentError",
    # 工具函数
    "get_error_by_code",
    "is_retryable_error",
    "get_error_severity",
]
