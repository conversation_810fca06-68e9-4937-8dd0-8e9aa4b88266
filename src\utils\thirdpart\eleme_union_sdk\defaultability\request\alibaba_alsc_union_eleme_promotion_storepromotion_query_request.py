from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemePromotionStorepromotionQueryRequest(BaseRequest):

    def __init__(self, query_request: object = None):
        """
        查询rquest
        """
        self._query_request = query_request

    @property
    def query_request(self):
        return self._query_request

    @query_request.setter
    def query_request(self, query_request):
        if isinstance(query_request, object):
            self._query_request = query_request
        else:
            raise TypeError("query_request must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.promotion.storepromotion.query"

    def to_dict(self):
        request_dict = {}
        if self._query_request is not None:
            request_dict["query_request"] = convert_struct(self._query_request)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemePromotionStorepromotionQueryPromotionQueryRequest:
    def __init__(
        self,
        session_id: str = None,
        pid: str = None,
        longitude: str = None,
        latitude: str = None,
        city_id: str = None,
        sort_type: str = None,
        in_activity: bool = None,
        has_bonus_stock: bool = None,
        min_commission_rate: str = None,
        page_size: int = None,
        sid: str = None,
        biz_type: str = None,
        filter_first_categories: str = None,
        filter_one_point_five_categories: str = None,
        media_activity_id: str = None,
        search_content: str = None,
        include_dynamic: bool = None,
    ):
        """
        会话ID（分页场景首次请求结果返回，后续请求必须携带，服务根据session_id相同请求次数自动翻页返回）
        """
        self.session_id = session_id
        """
            渠道PID
        """
        self.pid = pid
        """
            经度
        """
        self.longitude = longitude
        """
            纬度
        """
        self.latitude = latitude
        """
            城市编码（只用于经纬度覆盖多个城市时过滤）
        """
        self.city_id = city_id
        """
            排序类型，默认normal 排序规则包括:{"normal":"佣金倒序","distance":"距离由近到远","commission":"佣金倒序","monthlySale":"月销量","couponAmount":"叠加券金额倒序","activityReward":"奖励金金额倒序","commissionRate":"佣金比例倒序"}
        """
        self.sort_type = sort_type
        """
            是否参与奖励金活动（默认false不做过滤）
        """
        self.in_activity = in_activity
        """
            否当前有c端奖励金活动库存（默认false不做过滤）
        """
        self.has_bonus_stock = has_bonus_stock
        """
            店铺佣金比例下限，代表筛选店铺全店佣金大于等于0.01的店铺
        """
        self.min_commission_rate = min_commission_rate
        """
            每页数量（include_dynamic=true时，范围1~20；include_dynamic=false时，范围1~100）
        """
        self.page_size = page_size
        """
            三方扩展id
        """
        self.sid = sid
        """
            指定召回供给枚举
        """
        self.biz_type = biz_type
        """
            以一级类目进行类目限定，以,或者|进行类目分隔
        """
        self.filter_first_categories = filter_first_categories
        """
            1.5级类目查询，以"|"分隔
        """
        self.filter_one_point_five_categories = filter_one_point_five_categories
        """
            媒体出资活动ID
        """
        self.media_activity_id = media_activity_id
        """
            检索内容（支持门店名称）
        """
        self.search_content = search_content
        """
            是否返回门店动态信息，默认返回（false-不返回门店动态信息，page_size最大支持100；true-返回门店动态信息，page_size最大支持20）
        """
        self.include_dynamic = include_dynamic
