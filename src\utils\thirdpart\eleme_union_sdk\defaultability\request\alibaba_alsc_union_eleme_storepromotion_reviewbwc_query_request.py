from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeStorepromotionReviewbwcQueryRequest(BaseRequest):

    def __init__(
        self,
        page_size: int = None,
        session_id: str = None,
        pid: str = None,
        longitude: str = None,
        latitude: str = None,
        sort_type: str = None,
        min_commission_rate: str = None,
        sid: str = None,
        filter_first_categories: str = None,
        filter_one_point_five_categories: str = None,
        filter_city_id: str = None,
        search_content: str = None,
        exclude_link: bool = None,
    ):
        """
        每页数量（1~20，默认20）
        """
        self._page_size = page_size
        """
            会话ID（分页场景首次请求结果返回，后续请求必须携带，服务根据同一个session_id的请求次数自动叠加翻页返回数据，直至分页结束，返回空）
        """
        self._session_id = session_id
        """
            渠道PID
        """
        self._pid = pid
        """
            经度
        """
        self._longitude = longitude
        """
            纬度
        """
        self._latitude = latitude
        """
            排序类型，默认normal，排序规则包括:{"normal":"佣金倒序","distance_asc":"距离由近到远","commission_desc":"佣金倒序","month_sales_desc":"月销量从高到低","commission_rate_desc":"佣金比例倒序", "activity_reward_desc":"返现金额倒序"}
        """
        self._sort_type = sort_type
        """
            店铺佣金比例下限，代表筛选店铺全店佣金大于等于0.01的店铺
        """
        self._min_commission_rate = min_commission_rate
        """
            三方扩展id
        """
        self._sid = sid
        """
            以一级类目进行类目限定，以,或者|进行类目分隔
        """
        self._filter_first_categories = filter_first_categories
        """
            1.5级类目查询，以"|"分隔
        """
        self._filter_one_point_five_categories = filter_one_point_five_categories
        """
            城市ID（经纬度范围覆盖多个城市时，精准召回）
        """
        self._filter_city_id = filter_city_id
        """
            搜索内容（店铺名）
        """
        self._search_content = search_content
        """
            false-返回链接，true-不返回链接；8.1号及以后该属性移除，默认不返回链接
        """
        self._exclude_link = exclude_link

    @property
    def page_size(self):
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        if isinstance(page_size, int):
            self._page_size = page_size
        else:
            raise TypeError("page_size must be int")

    @property
    def session_id(self):
        return self._session_id

    @session_id.setter
    def session_id(self, session_id):
        if isinstance(session_id, str):
            self._session_id = session_id
        else:
            raise TypeError("session_id must be str")

    @property
    def pid(self):
        return self._pid

    @pid.setter
    def pid(self, pid):
        if isinstance(pid, str):
            self._pid = pid
        else:
            raise TypeError("pid must be str")

    @property
    def longitude(self):
        return self._longitude

    @longitude.setter
    def longitude(self, longitude):
        if isinstance(longitude, str):
            self._longitude = longitude
        else:
            raise TypeError("longitude must be str")

    @property
    def latitude(self):
        return self._latitude

    @latitude.setter
    def latitude(self, latitude):
        if isinstance(latitude, str):
            self._latitude = latitude
        else:
            raise TypeError("latitude must be str")

    @property
    def sort_type(self):
        return self._sort_type

    @sort_type.setter
    def sort_type(self, sort_type):
        if isinstance(sort_type, str):
            self._sort_type = sort_type
        else:
            raise TypeError("sort_type must be str")

    @property
    def min_commission_rate(self):
        return self._min_commission_rate

    @min_commission_rate.setter
    def min_commission_rate(self, min_commission_rate):
        if isinstance(min_commission_rate, str):
            self._min_commission_rate = min_commission_rate
        else:
            raise TypeError("min_commission_rate must be str")

    @property
    def sid(self):
        return self._sid

    @sid.setter
    def sid(self, sid):
        if isinstance(sid, str):
            self._sid = sid
        else:
            raise TypeError("sid must be str")

    @property
    def filter_first_categories(self):
        return self._filter_first_categories

    @filter_first_categories.setter
    def filter_first_categories(self, filter_first_categories):
        if isinstance(filter_first_categories, str):
            self._filter_first_categories = filter_first_categories
        else:
            raise TypeError("filter_first_categories must be str")

    @property
    def filter_one_point_five_categories(self):
        return self._filter_one_point_five_categories

    @filter_one_point_five_categories.setter
    def filter_one_point_five_categories(self, filter_one_point_five_categories):
        if isinstance(filter_one_point_five_categories, str):
            self._filter_one_point_five_categories = filter_one_point_five_categories
        else:
            raise TypeError("filter_one_point_five_categories must be str")

    @property
    def filter_city_id(self):
        return self._filter_city_id

    @filter_city_id.setter
    def filter_city_id(self, filter_city_id):
        if isinstance(filter_city_id, str):
            self._filter_city_id = filter_city_id
        else:
            raise TypeError("filter_city_id must be str")

    @property
    def search_content(self):
        return self._search_content

    @search_content.setter
    def search_content(self, search_content):
        if isinstance(search_content, str):
            self._search_content = search_content
        else:
            raise TypeError("search_content must be str")

    @property
    def exclude_link(self):
        return self._exclude_link

    @exclude_link.setter
    def exclude_link(self, exclude_link):
        if isinstance(exclude_link, bool):
            self._exclude_link = exclude_link
        else:
            raise TypeError("exclude_link must be bool")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.storepromotion.reviewbwc.query"

    def to_dict(self):
        request_dict = {}
        if self._page_size is not None:
            request_dict["page_size"] = convert_basic(self._page_size)

        if self._session_id is not None:
            request_dict["session_id"] = convert_basic(self._session_id)

        if self._pid is not None:
            request_dict["pid"] = convert_basic(self._pid)

        if self._longitude is not None:
            request_dict["longitude"] = convert_basic(self._longitude)

        if self._latitude is not None:
            request_dict["latitude"] = convert_basic(self._latitude)

        if self._sort_type is not None:
            request_dict["sort_type"] = convert_basic(self._sort_type)

        if self._min_commission_rate is not None:
            request_dict["min_commission_rate"] = convert_basic(self._min_commission_rate)

        if self._sid is not None:
            request_dict["sid"] = convert_basic(self._sid)

        if self._filter_first_categories is not None:
            request_dict["filter_first_categories"] = convert_basic(self._filter_first_categories)

        if self._filter_one_point_five_categories is not None:
            request_dict["filter_one_point_five_categories"] = convert_basic(self._filter_one_point_five_categories)

        if self._filter_city_id is not None:
            request_dict["filter_city_id"] = convert_basic(self._filter_city_id)

        if self._search_content is not None:
            request_dict["search_content"] = convert_basic(self._search_content)

        if self._exclude_link is not None:
            request_dict["exclude_link"] = convert_basic(self._exclude_link)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
