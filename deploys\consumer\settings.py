# encoding: utf-8
# deploys/consumer/settings.py
# created: 2025-08-04 16:18:01

from pathlib import Path

from pydantic import Field
from pydantic_settings import SettingsConfigDict

from src.infrastructures.fastapi import FastapiSettings
from src.infrastructures.gateways.aliyun import AliyunSmsSettings
from src.infrastructures.settings import BaseServiceSettings


class ConsumerSettings(BaseServiceSettings):
    """Consumer 服务配置"""

    # Consumer 服务特有的配置
    fastapi: FastapiSettings = Field(default_factory=FastapiSettings)
    sms: AliyunSmsSettings = Field(default_factory=AliyunSmsSettings)

    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",  # 方便嵌套字段注入
    )


config = ConsumerSettings()
