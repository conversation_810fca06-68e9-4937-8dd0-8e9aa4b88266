(() => {
    'use strict';

    const screenWidth = {{ screen_width }};
    const screenHeight = {{ screen_height }};

    Object.defineProperty(screen, 'width', {
        get: () => screenWidth,
        configurable: true
    });

    Object.defineProperty(screen, 'height', {
        get: () => screenHeight,
        configurable: true
    });

    Object.defineProperty(screen, 'availWidth', {
        get: () => screenWidth,
        configurable: true
    });

    Object.defineProperty(screen, 'availHeight', {
        get: () => screenHeight,
        configurable: true
    });

    const localStorageData = {{ local_storage }};
    for (const [key, value] of Object.entries(localStorageData)) {
        try {
            localStorage.setItem(key, value);
        } catch (e) {
            console.debug('Failed to set localStorage item:', key, e);
        }
    }

    const sessionStorageData = {{ session_storage }};
    for (const [key, value] of Object.entries(sessionStorageData)) {
        try {
            sessionStorage.setItem(key, value);
        } catch (e) {
            console.debug('Failed to set sessionStorage item:', key, e);
        }
    }

    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
        configurable: true
    });

    if (!window.chrome) {
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
    }

    Object.defineProperty(navigator, 'plugins', {
        get: () => [{
            name: 'Chrome PDF Plugin',
            filename: 'internal-pdf-viewer',
            description: 'Portable Document Format'
        }],
        configurable: true
    });

    Object.defineProperty(navigator, 'languages', {
        get: () => ['zh-CN', 'zh', 'en'],
        configurable: true
    });

    const originalEvaluate = document.evaluate;
    document.evaluate = function() {
        return originalEvaluate.apply(this, arguments);
    };

    const originalQuery = navigator.permissions.query;
    navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
            Promise.resolve({ state: Notification.permission }) :
            originalQuery(parameters)
    );

    if (!window.TouchEvent) {
        window.TouchEvent = class TouchEvent extends Event {
            constructor(type, eventInitDict = {}) {
                super(type, eventInitDict);
                this.touches = eventInitDict.touches || [];
                this.targetTouches = eventInitDict.targetTouches || [];
                this.changedTouches = eventInitDict.changedTouches || [];
            }
        };
    }

    Object.defineProperty(navigator, 'platform', {
        get: () => 'iPhone',
        configurable: true
    });

    Object.defineProperty(navigator, 'maxTouchPoints', {
        get: () => 5,
        configurable: true
    });

    Object.defineProperty(navigator, 'deviceMemory', {
        get: () => 4,
        configurable: true
    });

    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => 6,
        configurable: true
    });
})();
