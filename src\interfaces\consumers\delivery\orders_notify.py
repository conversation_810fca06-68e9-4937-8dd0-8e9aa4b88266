# encoding: utf-8
# src/interfaces/consumers/delivery/orders_notify.py
# created: 2025-07-01 18:04:25

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger
from pydantic import ValidationError

from src.domains.delivery.messages import UnionOrderSyncMessage
from src.infrastructures.rabbitmq import BaseConsumer

if TYPE_CHECKING:
    from src.applications.openapi.commands.delivery import OrderNotifyCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class UnionOrderNotifyConsumer(BaseConsumer):
    """联盟订单通知消费者"""

    exchange_name = "delivery.topic"
    queue_name = "delivery_orders_notify"
    routing_key = "delivery.union_order"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        order_notify_service: "OrderNotifyCommandService",
    ):
        self.order_notify_service = order_notify_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = UnionOrderSyncMessage.model_validate_json(message.body)
        logger.info(f"UnionOrderNotifyConsumer 收到消息: {msg.payload.parent_order_id}")
        await self.order_notify_service.notify_order(msg.payload)

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 订单通知失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "UnionOrderNotifyConsumer 回滚: 消息[{message_id}] 通知失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
