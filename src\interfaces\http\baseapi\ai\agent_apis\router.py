from typing import Any, Dict, Optional

from fastapi import APIRouter

# from .eleme_store import (
#     async_getStoreDetail,
#     async_getStoreDetailLLM,
#     getStoreH5Url,
#     searchStoreByName,
# )
from .eleme_wxscheme import get_wx_scheme_url
from .models import Coupon, CouponListResponse
from .query import search_activity

# from .recommand_history import RecommendHistory

router = APIRouter()
# history = RecommendHistory()
# 定义根路由


@router.get("/")
async def hello_world():
    return {"message": "Haili AI API"}


def get_weixin_scheme_url(activity_id: str) -> str:
    """
    拼接微信小程序链接

    Args:
        wx_appid: 微信小程序的appid
        wx_full_path: 微信小程序的路径

    Returns:
        str: 拼接好的微信小程序链接
    """
    # 拆分path和query
    # 获取微信 scheme URL
    scheme_url = get_wx_scheme_url(
        activity_id=activity_id,
    )
    return scheme_url


def get_scheme_url(link_data: dict, activity_id: str) -> str:
    """
    根据优先级获取 scheme_url
    优先级：微信 > 支付宝 > 淘宝 > 饿了么
    如果都没有则返回空字符串
    """

    # 1. 微信小程序
    if link_data.get("wx_url_scheme"):
        return link_data.get("wx_url_scheme") or ""

    # 2. 支付宝小程序
    if alipay_data := link_data.get("alipay_promotion", {}):
        if alipay_scheme := alipay_data.get("alipay_scheme_url"):
            return alipay_scheme

    # 3. 淘宝
    if taobao_data := link_data.get("taobao_promotion", {}):
        if taobao_url := taobao_data.get("h5_url"):
            return taobao_url

    # 4. 饿了么
    if ele_scheme := link_data.get("ele_scheme_url"):
        return ele_scheme

    # 默认返回空字符串
    return ""


def get_h5_url(link_data: dict) -> str:
    """
    获取h5_url，优先从h5_promotion中获取
    """
    if h5_data := link_data.get("h5_promotion", {}):
        if tj_h5_url := h5_data.get("tj_h5_url"):
            return tj_h5_url
    return link_data.get("h5_url", "")


# 根据标签获取权益列表接口
default_activity: Dict[str, Any] = {
    "data": {
        "description": "最高抢66元大额红包",
        "end_time": 1746028799,
        "id": "10145",
        "link": {
            "alipay_mini_url": "alipays://platformapi/startapp?appId=2021001110676437&page=plugin-private%3A%2F%2F2021002141657015%2Fpages%2Findex-bdlm%2Findex%3FconfigKey%3DBDLM_ELE_ALIPAY_DG%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2",
            "alipay_promotion": {
                "alipay_scheme_url": "alipays://platformapi/startapp?appId=2021001110676437&page=plugin-private%3A%2F%2F2021002141657015%2Fpages%2Findex-bdlm%2Findex%3FconfigKey%3DBDLM_ELE_ALIPAY_DG%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2",
                "app_id": "2021001110676437",
                "app_path": "plugin-private%3A%2F%2F2021002141657015%2Fpages%2Findex-bdlm%2Findex%3FconfigKey%3DBDLM_ELE_ALIPAY_DG%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2",
                "h5_url": "https://render.alipay.com/p/s/i/?scheme=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FappId%3D2021001110676437%26page%3Dplugin-private%253A%252F%252F2021002141657015%252Fpages%252Findex-bdlm%252Findex%253FconfigKey%253DBDLM_ELE_ALIPAY_DG%2526scene%253D63c622a0b41f4554ba3cd15b528cb6b2",
            },
            "app_promotion": {
                "deep_link": "eleme://web?action=ali.open.nav&module=h5&packageName=me.ele&bc_fl_src=locallife_wtzt_0-0-ADGROUPID-__REQID__-2&url=https%3A%2F%2Fppe-h5.ele.me%2Fadminiappsub%2Fpages%2Fh5%2Findex%3FconfigKey%3DBDLM_ELE_H5_DG_TC%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2%26o2i_1st_clk%3D__CLICK_ID__&fastmode=1"
            },
            "ele_scheme_url": "eleme://web?action=ali.open.nav&module=h5&packageName=me.ele&bc_fl_src=locallife_wtzt_0-0-ADGROUPID-__REQID__-2&url=https%3A%2F%2Fppe-h5.ele.me%2Fadminiappsub%2Fpages%2Fh5%2Findex%3FconfigKey%3DBDLM_ELE_H5_DG_TC%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2%26o2i_1st_clk%3D__CLICK_ID__&fastmode=1",
            "full_taobao_word": "28 HU7405   2:/Dpq9eR1ixhq₴欢最高抢66元大额红包",
            "h5_promotion": {
                "h5_url": "https://ppe-h5.ele.me/adminiappsub/pages/h5/index?configKey=BDLM_ELE_H5_DG_TC&scene=63c622a0b41f4554ba3cd15b528cb6b2",
                "short_link": "https://u.ele.me/USh8ZV1f",
            },
            "h5_short_link": "https://u.ele.me/USh8ZV1f",
            "h5_url": "https://ppe-h5.ele.me/adminiappsub/pages/h5/index?configKey=BDLM_ELE_H5_DG_TC&scene=63c622a0b41f4554ba3cd15b528cb6b2",
            "taobao_promotion": {
                "app_id": "8251537",
                "app_path": "plugin-private://2021003183669766/pages/h5/index?configKey=PAGE_TAOBAO_CATER&scene=63c622a0b41f4554ba3cd15b528cb6b2",
                "h5_url": "https://m.duanqu.com/?_ariver_appid=8251537&page=plugin-private%3A%2F%2F2021003183669766%2Fpages%2Fh5%2Findex%3FconfigKey%3DPAGE_TAOBAO_CATER%26scene%3D63c622a0b41f4554ba3cd15b528cb6b2",
                "scheme_url": "tbopen://m.taobao.com/tbopen/index.html?&action=ali.open.nav&module=h5&h5Url=https%3A%2F%2Fm.duanqu.com%2F%3F_ariver_appid%3D8251537%26page%3Dplugin-private%253A%252F%252F2021003183669766%252Fpages%252Fh5%252Findex%253FconfigKey%253DPAGE_TAOBAO_CATER%2526scene%253D63c622a0b41f4554ba3cd15b528cb6b2",
            },
            "taobao_word": "￥Dpq9eR1ixhq￥/ HU7405",
            "tb_scheme_url": "tbopen://m.taobao.com/tbopen/index.html?&action=ali.open.nav&module=h5&h5Url=https%3A%2F%2Fm.duanqu.com%2F%3F_ariver_appid%3D8251537%26page%3Dplugin-private%253A%252F%252F2021003183669766%252Fpages%252Fh5%252Findex%253FconfigKey%253DPAGE_TAOBAO_CATER%2526scene%253D63c622a0b41f4554ba3cd15b528cb6b2",
            "wx_appid": "wxece3a9a4c82f58c9",
            "wx_path": "commercialize/pages/taoke-guide/index?scene=63c622a0b41f4554ba3cd15b528cb6b2",
            "wx_promotion": {
                "wx_app_id": "wxece3a9a4c82f58c9",
                "wx_path": "commercialize/pages/taoke-guide/index?scene=63c622a0b41f4554ba3cd15b528cb6b2",
            },
        },
        "picture": "https://img.alicdn.com/imgextra/i1/6000000000548/O1CN011Fv1r4S4jdPORaj_!!6000000000548-2-o2oad.png",
        "start_time": 1649865600,
        "title": "饿了么天天领红包",
        "expanded_content": "麻辣烫，米线，披萨，华莱士，全鸡汉堡，杨铭宇，黄焖鸡，焖饼炒鸡，招牌，韩国炸鸡，小碗菜，卤肉饭，西雅苏，新疆，炒米粉，益禾堂",
    },
    "message": "success",
    "result_code": 0,
    "request_id": "16kyw7lkopm33",
}


@router.get("/api/couponlist")
async def get_coupon_list(
    coupon_tag: str,
    current_input: str = "",
    sender: Optional[str] = None,
):
    # 拼接搜索文本
    search_text = f"{current_input} {coupon_tag}"
    print(f"搜索文本: {search_text}, 发送者: {sender}")

    try:
        # 搜索相似活动，获取前5个结果
        search_results = search_activity(search_text, top_k=3)

        if not search_results:
            # 如果搜索结果为空，则返回默认活动
            search_results = [(default_activity, 1)]
            return {
                "code": 0,
                "data": CouponListResponse(
                    total=0,
                    couponlist=[
                        Coupon(
                            similarity=1.0,
                            title=default_activity["data"]["title"],
                            description=default_activity["data"]["description"],
                            picture=default_activity["data"]["picture"],
                            h5_url=default_activity["data"]["link"]["h5_url"],
                            scheme_url=default_activity["data"]["link"].get("ele_scheme_url", ""),
                        )
                    ],
                ),
                "msg": "未找到相关优惠券",
            }

        # 将搜索结果转换为Coupon对象列表
        coupons = []
        for activity, similarity in search_results:
            activity_data = activity.get("data", {})
            link_data = activity_data.get("link", {})

            # 根据优先级获取 scheme_url
            scheme_url = get_scheme_url(link_data, activity_data.get("id", ""))
            # 获取h5_url
            h5_url = get_h5_url(link_data)
            if not h5_url:
                h5_url = scheme_url

            coupon = Coupon(
                similarity=similarity,
                title=activity_data.get("title", ""),
                picture=activity_data.get("picture", ""),
                description=activity_data.get("description", ""),
                h5_url=h5_url,
                scheme_url=scheme_url,
            )
            coupons.append(coupon)

        # 构造返回数据
        response_data = CouponListResponse(total=len(coupons), couponlist=coupons)

        return {"code": 0, "data": response_data, "msg": "success"}

    except Exception as e:
        print(f"搜索优惠券时发生错误: {str(e)}")
        return {"code": -1, "data": CouponListResponse(total=0, couponlist=[]), "msg": f"搜索优惠券失败: {str(e)}"}


# 根据标签获取商店列表接口


# @router.get("/api/storelist")
# async def get_store_list(
#     storeTag: str,
#     currentInput: Optional[str] = '',
#     sender: Optional[str] = None,
#     longitude: Optional[str] = None,
#     latitude: Optional[str] = None,
# ):
#     print(
#         f"搜索商店: {storeTag},用户输入： {currentInput}, 发送者: {sender}, 经度: {longitude}, 纬度: {latitude}")
#     stores = searchStoreByName(storeTag, longitude or '', latitude or '')
#     # 遍历stores 通过storeid 获取store详情,改为异步并发获取
#     store_details = await asyncio.gather(*[async_getStoreDetail(store['shop_id']) for store in stores])
#     store_detail_recommands = await asyncio.gather(*[async_getStoreDetailLLM(detail) for detail in store_details])
#     # 将store_details转换为Store对象列表
#     storelist = [Store(
#         category=store['category_1_name'],
#         indistinct_monthly_sales=store['indistinct_monthly_sales'][2:],
#         h5_url=getStoreH5Url(store_details[index]['data']['link']['h5_url']),
#         service_rating=store['service_rating'],
#         shop_logo=store['shop_logo'],
#         title=store['title'],
#         delivery_distance=store['delivery_distance'],
#         delivery_price=store['delivery_price'],
#         delivery_time=store['delivery_time'],
#         # list str to list object [{content:str}]
#         recommend_reasons=[RecommendReason(content=reason)
#                            for reason in store_details[index]['data'].get('recommend_reasons', [])],
#         recommend_description=store_detail_recommands[index]
#     ) for index, store in enumerate(stores)]
#     # 添加推荐记录
#     history.add_recommendation(
#         sender or '',
#         [store['title'][:4] for store in stores],
#         storeTag,
#         latitude or '',
#         longitude or '',
#     )
#     # 返回结果
#     return {
#         "code": 0,
#         "data": StoreListResponse(total=len(stores), storelist=storelist),
#         "msg": "success"
#     }


# @router.get("/api/another_storelist")
# async def get_another_store_list(
#     sender: str,
# ):
#     # 获取推荐记录
#     recommendations = history.get_latest_recommendation(sender)
#     print(sender, recommendations)
#     if not recommendations:
#         return {
#             "code": -1,
#             "data": StoreListResponse(total=0, storelist=[]),
#             "msg": "success"
#         }
#     restaurant_names = recommendations.get('restaurant_names', [])
#     tag = recommendations.get('tag', '')
#     print(restaurant_names, tag)
#     latitude = recommendations.get('latitude', '')
#     longitude = recommendations.get('longitude', '')
#     # 搜索相同tag的餐厅
#     stores = searchStoreByName(
#         tag, longitude, latitude, restaurant_names, recommendations.get('count', 3) + 1)
#     # 遍历stores 通过storeid 获取store详情,改为异步并发获取
#     store_details = await asyncio.gather(*[async_getStoreDetail(store['shop_id']) for store in stores])
#     store_detail_recommands = await asyncio.gather(*[async_getStoreDetailLLM(detail) for detail in store_details])
#     storelist = [Store(
#         category=store['category_1_name'],
#         indistinct_monthly_sales=store['indistinct_monthly_sales'],
#         h5_url=getStoreH5Url(store_details[index]['data']['link']['h5_url']),
#         service_rating=store['service_rating'],
#         shop_logo=store['shop_logo'],
#         title=store['title'],
#         delivery_distance=store['delivery_distance'],
#         delivery_price=store['delivery_price'],
#         delivery_time=store['delivery_time'],
#         # list str to list object [{content:str}]
#         recommend_reasons=[RecommendReason(content=reason)
#                            for reason in store_details[index]['data'].get('recommend_reasons', [])],
#         recommend_description=store_detail_recommands[index]
#     ) for index, store in enumerate(stores)]
#     # 添加推荐记录
#     history.add_recommendation(
#         sender,
#         [store['title'][:4] for store in stores],
#         tag,
#         latitude,
#         longitude,
#     )
#     # 返回结果
#     return {
#         "code": 0,
#         "data": StoreListResponse(total=len(stores), storelist=storelist),
#         "msg": "success"
#     }
