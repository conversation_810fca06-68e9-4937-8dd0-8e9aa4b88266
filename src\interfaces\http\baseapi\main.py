# encoding: utf-8
# src/interfaces/http/baseapi/main.py
# created: 2025-07-23 16:00:00

from typing import TYPE_CHECKING

from src.infrastructures.fastapi.application import register_app
from src.interfaces.http.baseapi import container
from src.interfaces.http.baseapi.ai import get_ai_app
from src.interfaces.http.baseapi.base import delivery_router, oss_router, passport_router
from src.interfaces.http.baseapi.internal import get_internal_app
from src.interfaces.http.baseapi.mis import get_mis_app

if TYPE_CHECKING:
    from fastapi import FastAPI

# 延迟创建app，避免在import时就执行
app: "FastAPI" = None  # type: ignore


def create_app(config=None) -> "FastAPI":
    """创建并配置FastAPI应用"""
    global app

    if app is None:
        # 创建应用
        app = register_app(
            name="baseapi",
            version="1.0.0",
            description="基础服务API, 提供api服务给web端",
            container=container,
            config=config or container.config(),
        )

        # 注册路由
        app.include_router(passport_router, prefix="/v1/passport")
        app.include_router(delivery_router, prefix="/v1/delivery")
        # app.include_router(benefits_router, prefix="/v1/benefits")
        app.include_router(oss_router, prefix="/v1/oss")

        # 挂载子应用
        app.mount("/mis", get_mis_app(config))
        app.mount("/ai", get_ai_app(config))
        app.mount("/internal", get_internal_app(config))

    return app


if __name__ == "__main__":
    import uvicorn

    # 直接运行时需要先创建app
    uvicorn.run(create_app(), host="0.0.0.0", port=8002)
