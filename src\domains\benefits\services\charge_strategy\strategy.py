# encoding: utf-8
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.databases.models.benefits import BenefitsSkuChargeRecord


class ChargeStrategy(ABC):

    @abstractmethod
    async def charge(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """执行充值方法"""
        pass

    @abstractmethod
    async def check_charge_status(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """检查充值状态"""
        pass

    @abstractmethod
    async def refund(self, record: "BenefitsSkuChargeRecord") -> "BenefitsSkuChargeRecord":
        """执行退款方法"""
        pass
