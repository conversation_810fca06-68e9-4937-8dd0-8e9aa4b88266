# encoding: utf-8
# <AUTHOR> <EMAIL>
# core/rabbitmq/messages.py
# created: 2025-04-05 18:58:29
# updated: 2025-04-13 11:28:08

from enum import IntEnum
from json import dumps
from typing import Any, Generic, Optional, Type, TypeVar
from uuid import uuid4

from aio_pika import DeliveryMode
from aio_pika import Message as RMQMessage
from pydantic import BaseModel, Field


class MessagePriority(IntEnum):
    """消息优先级枚举"""

    LOW = 1
    NORMAL = 5
    HIGH = 9


class BaseMessage(BaseModel):
    """消息基类"""

    routing_key: str = Field(default="", description="消息路由键")
    exchange_name: str = Field(default="", description="消息交换机名称")
    message_id: Optional[str] = Field(default_factory=lambda: uuid4().hex, description="消息ID")
    delivery_mode: Optional[int] = Field(default=DeliveryMode.PERSISTENT, description="消息持久化模式")
    content_type: Optional[str] = Field(default="application/json", description="消息内容类型")
    expiration: Optional[int] = Field(default=None, description="消息过期时间")
    priority: Optional[int] = Field(default=MessagePriority.NORMAL, description="消息优先级")

    model_config = {"populate_by_name": True}  # 允许从类属性填充字段


T = TypeVar("T")  # 用于创建特定类型消息的泛型


class Message(BaseMessage, Generic[T]):
    """统一消息格式"""

    payload: T

    def to_rmq_message(self, **kwargs) -> RMQMessage:
        """转换为aio_pika.Message对象"""

        return RMQMessage(
            body=self.model_dump_json().encode("utf-8"),
            message_id=self.message_id,
            delivery_mode=self.delivery_mode,
            priority=self.priority,
            expiration=self.expiration,
            content_type=self.content_type,
            **kwargs,
        )


def message_creator(
    name: str,
    data_type: Type[Any],
    routing_key: str,
    exchange_name: str,
    priority: Optional[int] = MessagePriority.NORMAL,
    delivery_mode: Optional[int] = DeliveryMode.PERSISTENT,
    content_type: Optional[str] = "application/json",
    expiration: Optional[int] = None,
) -> Type[Message[Any]]:
    # 创建子类并覆盖routing_key字段
    return type(
        name,
        (Message[data_type],),
        {
            "__annotations__": {
                "payload": data_type,
                "routing_key": str,
                "priority": Optional[int],
                "delivery_mode": Optional[int],
                "content_type": Optional[str],
                "expiration": Optional[int],
                "exchange_name": str,
            },
            "routing_key": routing_key,
            "__doc__": f"{name} 消息，路由键: {routing_key}",
            "priority": priority,
            "delivery_mode": delivery_mode,
            "content_type": content_type,
            "expiration": expiration,
            "exchange_name": exchange_name,
        },
    )


# # 使用示例
# class OrderPayload(BaseModel):
#     order_id: str
#     user_id: str
#     amount: float


# # 创建消息类型
# OrderCreatedMessage = message_creator(name="OrderCreatedMessage", data_type=OrderPayload, routing_key="order.created")


# # 使用示例
# def create_order_message(
#     order_id: str, user_id: str, amount: float, message_id: Optional[str] = None
# ) -> OrderCreatedMessage:
#     return OrderCreatedMessage(
#         payload=OrderPayload(order_id=order_id, user_id=user_id, amount=amount), message_id=message_id
#     )
