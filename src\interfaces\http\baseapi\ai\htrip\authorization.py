# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/ai/dingtalk/authorization.py
# created: 2024-12-12 01:37:54
# updated: 2025-04-18 02:25:16


from fastapi import Depends, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

AGENT_CONFIG = {
    "HTRIP": "97a5e9d087e7c6bd0163543086aea449",
}


async def agent_api_authentication(token: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> str:
    """
    仅验证 Bearer token 的存在性，返回 token 字符串
    """

    for agent_name, agent_token in AGENT_CONFIG.items():
        if token.credentials == agent_token:
            return agent_name
    raise HTTPException(status_code=401, detail="Unauthorized")
