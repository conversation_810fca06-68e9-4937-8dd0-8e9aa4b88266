# encoding: utf-8
# <AUTHOR> <EMAIL>
# utils/eleme/union_benefits.py
# created: 2025-05-04 23:59:40
# updated: 2025-05-05 08:39:28

from loguru import logger

from src.infrastructures.config_compat import config
from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient
from src.utils.thirdpart.eleme_union_sdk.defaultability.defaultability import Defaultability
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_detail_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_query_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_create_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_get_request import *
from src.utils.thirdpart.eleme_union_sdk.defaultability.request.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_refund_request import *

from .dto import (
    CouponPackagePurchaseDetailDto,
    CouponPackagePurchaseDetailResponse,
    CouponPackagePurchaseListDto,
    CouponPackagePurchaseListResponse,
    CouponPackagePurchaseTicketCreateResultDto,
    CouponPackagePurchaseTicketCreateResultResponse,
    CouponPackagePurchaseTicketDetailResultDto,
    CouponPackagePurchaseTicketDetailResultResponse,
)


class ElemeUnionBenefitsUtils:

    @classmethod
    def _get_client(cls) -> Defaultability:
        client = TopApiClient(
            appkey=config.thirdpart.eleme.union.app_key,
            app_sercet=config.thirdpart.eleme.union.app_secret,
            top_gateway_url=config.thirdpart.eleme.union.top_gateway_url,
            verify_ssl=True,
        )
        ability = Defaultability(client=client)
        return ability

    @classmethod
    def get_purchase_list(cls, page: int = 1, page_size: int = 20) -> CouponPackagePurchaseListDto:
        client = cls._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseQueryRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseQueryCouponPackagePurchaseQueryRequest(
                page_number=page, page_size=page_size
            )
        )
        response = CouponPackagePurchaseListResponse.model_validate(
            client.alibaba_alsc_union_eleme_couponpackage_purchase_query(request)
        )
        return response.data

    @classmethod
    def get_purchase_detail(cls, purchase_id: str) -> CouponPackagePurchaseDetailDto:
        client = cls._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseDetailGetRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseDetailGetCouponPackagePurchaseDetailRequest(
                purchase_id=purchase_id
            )
        )
        try:
            response = CouponPackagePurchaseDetailResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_detail_get(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟采购单详情获取失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    @classmethod
    def create_ticket(
        cls, purchase_id: str, item_id: str, mobile: str, outer_order_id: str, outer_item_id: str
    ) -> CouponPackagePurchaseTicketCreateResultDto:
        client = cls._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketCreateCouponPackagePurchaseTicketCreateRequest(
                purchase_id=purchase_id,
                item_id=item_id,
                mobile=mobile,
                outer_order_id=outer_order_id,
                outer_item_id=outer_item_id,
            )
        )
        try:
            response = CouponPackagePurchaseTicketCreateResultResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_create(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟卡券充值失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    @classmethod
    def get_ticket_detail(
        cls, ticket_id: str, purchase_id: str, item_id: str, outer_order_id: str
    ) -> CouponPackagePurchaseTicketDetailResultDto:
        client = cls._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketGetCouponPackagePurchaseTicketDetailRequest(
                ticket_id=ticket_id,
                purchase_id=purchase_id,
                item_id=item_id,
                outer_order_id=outer_order_id,
            )
        )
        try:
            response = CouponPackagePurchaseTicketDetailResultResponse.model_validate(
                client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_get(request)
            )
            return response.data
        except Exception as e:
            logger.error(f"饿了么联盟卡券详情获取失败[{purchase_id}], 异常错误: {str(e)}")
            raise e

    @classmethod
    def refund_ticket(cls, ticket_id: str, outer_order_id: str, purchase_id: str, item_id: str, mobile: str) -> bool:
        client = cls._get_client()
        request = AlibabaAlscUnionElemeCouponpackagePurchaseTicketRefundRequest(
            AlibabaAlscUnionElemeCouponpackagePurchaseTicketRefundCouponPackagePurchaseTicketRefundRequest(
                ticket_id=ticket_id,
                outer_order_id=outer_order_id,
                purchase_id=purchase_id,
                item_id=item_id,
                mobile=mobile,
            )
        )
        try:
            client.alibaba_alsc_union_eleme_couponpackage_purchase_ticket_refund(request)
            return True
        except Exception as e:
            logger.error(f"饿了么联盟卡券退款失败[{ticket_id}], 异常错误: {str(e)}")
            raise e
