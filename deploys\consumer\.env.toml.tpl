[fastapi]
port = 8000
workers = 2

[fastapi.hostname]
hostname = "http://localhost"
base_fe = "https://alpha-basefe.hicaspian.com"

[rabbitmq]
url = "amqp://${amqp_user}:${amqp_password}@${amqp_host}:${amqp_port}/${amqp_vhost}"
pool_size = 2
max_channels_per_connection = 50
heartbeat = 60
connection_timeout = 10
prefetch_count = 1
consumer_instances = 4
mandatory = true

[database]
mysql_uri = "${mysql_url}"
redis_uri = "${redis_url}"

[bifrost]
public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtf7rj76PWJbb0x61WuBhKvY7F090ZWwYnww1Vs12LrJQazhhoxLQ8mwuPuIStaaXSbxJBxI2t6BwkiEx4a9UmGFEP6UilNJ3OY+lW6ChSlCQk/BZfkxughXwX3OzW+bF9YRwXKWjKZgHLDB/6YOBL+vfL30miMdIx6Ta8nMfgsGam8lQRByNl9YDcVF+Im4gCx6Q997kxBBElItQw+IwbDF54ZNkE5ZXbhsELBUqZI0RzHdz+wZU3q3ydWo8z92CCiRJW9BXeGbA8aOfhXtbzDOixk96SaxMG0vneDovoZ6fMnBVD1mIzlkSysCCCZPyscaQPVY6bQWoa2NTPTeLCQIDAQAB"
private_key = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDENxQErAlThyAdPbGU3tUy/y1EPvuGoH0Pt/Xf9aCeDnqVkGGHO0+V79J/I9GQVz02CK5D8YKhPmvHfxghtJUqbktH2OetLaSNtmLtO+kqNCMTE+OLYqYasBsZdgtRzZiILD7gB/vRoQ/WS39xtWtaQvfpJyn4t4UbvpuCm8b7aM4yRsexHq72nSjXYwsq9+axwHETXg3AEwOkdmii0WI5zGYH9GHWE3qOizFIJD2+VUu/Jn+m8AyygP4JVIlIMvZn6huiLlfwzYiXu2yRVaf8Z30uf1ovtqIOC3sO4ULFbk8jRyI9JA875IMhNWO5XoBS4eDFY/oiRRGv8zeKsq61AgMBAAECggEAKsDEhhsVIiPSccSgRauDAUxcCN9/Ty93aH4hHxYmU+IcQCv4MC7Scb1SXH0Ju71iezxLekbse2U+NzcAY1G1Ycwr74D5xABqty0LI51W+ejnzo+aGCQbxLtkADl6EG4vgtY/MfHTYvMI4BzNrVFGDaIDYgQ0TzrvkCLJQB1gryUeWU2yhDlX8CDlYbwCEmz8KOYOMDqZJaG09Luf2yLK4tzREmOTBp2nmKJzca2BV1u//6Cug2TuuKaYOlVmdQyaGi2c8lrcVv0Zt7G+J9JU6l6zXSjqw7eebq/0EuvJTPJjWCn37nVhKJ5nUWGe8rHbzLlnrksoL54SZ6BtH0NrMQKBgQDtfppTmRJGltdNugaVZO018OqNhwe+X34xKWEel9XW2hZ54clehsVTQZ8kw0TQbWDU9/z+ODisW0G/C/UMlJfmlE+ZaXvNEKOEyNC+fQTydGY2laKLS8d3XKcSYTk+N9+G+UwDnM5LFiJBIy5fprYRmSeqXV1iWTWcT0xHN8wgUQKBgQDTgQ8du/LSgVztGR1E5mU2t+4/C4OaAn78JTWFcOJdaNUDrgGlk7ac+KcwIlgedcx72D1Dfd2y4KDwiAkKQOCxpgKVnp05JHgKYeMPHFewB0UmRr/mxZgU0gW07kBlvh2NGLKdXIqInWWS5f4CqhX98TRuLNmb1iknFaxl0AwTJQKBgQCXJWS4YYnlDOjXmdXIzGO/WaUdD1vZR9L7HLenjcBVLZTyWsUaeLEqrG/JKNEpyQLAZ1dGv6cp4iY0nTqGmCcYYzlJjH1y4+z9fASFxvEYEQZgJNk+x6qZh4j4xJF2zH5g21YKUohj9yEzzV0dGO90wcEhxqvBBL9+zGTiSluKEQKBgQCN/T2Fq7DHi1s0PUD+CWJ3iqFiJ7uwv+46HkzBCdid9wvSTZYDb5gP54pt8RCRWmnt1mzCi5QzS0QgVmMjRAzUlmtzwKkyPH5uEesaMN/ZZ/gPSz33kj2X9KsqHSyUYT57g9IdfIvwTSJsrQSLC156Pd0B/******************************/hGKnLv0ngl1k9BqIV+/M3/rnacJPqn9f1cmbo76b0YpZZrF9oI1CjryXzPIr49bOqGjnF/GIUAHPu90KKZ2KVn9bkANApi2vr910pWjrJZaRzLLSS8Hp0EUv3RsLqZneuyNZ0htYAYK0W6IvcibVMR+p8/71U3hrig=="
account = "${bifrost_account}"
secret_key = "${bifrost_secret_key}"
base_url = "${bifrost_url}"
prefix = "${bifrost_prefix}"

[eleme_union]
app_key = "********"
app_secret = "929eafdde0fb34c57ff0290e271b54c3"
top_gateway_url = "https://eco.taobao.com/router/rest"
verify_ssl = true

[wifimaster]
sso_url = "https://api.ttwifi.net/opensso/finclip/phone/get"
app_id = "**********"

[sms]
access_key_id = "LTAI5tCX3VPTGfx5URDAvybo"
access_key_secret = "******************************"
endpoint = "dysmsapi.aliyuncs.com"
sign_name = "南京里海数据科技"
template_code = "SMS_302800024"
repeat_interval = 60
