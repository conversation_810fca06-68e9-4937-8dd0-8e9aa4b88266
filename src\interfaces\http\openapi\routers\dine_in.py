from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query
from loguru import logger

from src.containers import Container
from src.infrastructures.fastapi.response import BaseResponse

from ..authorization import openapi_authentication
from ..schemas import DineInAccessUrlPayload, DineInAccessUrlResponse

if TYPE_CHECKING:
    from src.applications.openapi.queries import ActivitiesQueryService
    from src.domains.customer.entities import CustomerEntity

router = APIRouter(tags=["dine_in"])


@router.post(
    "/access_url",
    name="获取到店链接",
    response_model=DineInAccessUrlResponse,
)
@inject
async def get_dine_in_access_url(
    payload: DineInAccessUrlPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    access_url = await activities_query_service.get_dine_in_access_url(payload=payload, customer=customer)
    logger.info(f"获取到店链接: {access_url}")
    return BaseResponse(data={"access_url": access_url})
