# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/services/user.py
# created: 2024-12-01 14:21:47
# updated: 2025-05-27 15:03:29

from typing import TYPE_CHECKING, Optional

import jwt
from loguru import logger

from src.databases.models.passport import (
    PassportApp,
    PassportTenant,
    PassportUserRelations,
    ThirdPlatformEnum,
)
from src.domains.passport.dto import (
    DingtalkUserInfoDTO,
    PassportTenantDTO,
    PassportUserDTO,
    ThirdUserDTO,
    UserInfoDTO,
    WechatUserInfoDTO,
)
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from src.infrastructures import errors
from src.repositories.passport import ThirdUserRepository, UserRepository

if TYPE_CHECKING:
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class UserService:

    def __init__(
        self, user_repository: "UserRepository", app_repository: "AppRepository", tenant_repository: "TenantRepository"
    ):
        self.user_repository = user_repository
        self.app_repository = app_repository
        self.tenant_repository = tenant_repository

    async def get_user_by_jwt(self, jwt_token: str, app_secret: str) -> UserEntity:
        payload = jwt.decode(jwt_token, key=app_secret, algorithms=["HS256"])
        app_id = payload.get("app_id", "")
        tenant_id = payload.get("tenant_id", None)
        app = await self.app_repository.get_by_appid(app_id)
        if not app:
            raise errors.PassportAppNotFoundError
        tenant = await self.tenant_repository.get_by_tenant_id(tenant_id) if tenant_id else None

        return UserEntity(
            uid=payload["uid"],
            phone=payload["phone"],
            nickname=payload.get("nickname", ""),
            avatar_url=payload.get("avatar_url", ""),
            email=payload.get("email", ""),
            current_app=await AppEntity.from_model(app),
            current_tenant=await TenantEntity.from_model(tenant) if tenant else None,
        )

    async def get_or_create_phone_user(
        self, phone_number: str, app: AppEntity, tenant: Optional[TenantEntity]
    ) -> UserEntity:
        user = await UserRepository.get_by_phone(phone_number)
        if not user:
            user = await UserRepository.create_by_phone(phone_number)
        await UserRepository.get_or_create_user_relation(user, app.app_id, tenant.tenant_id if tenant else None)
        user_entity = await UserEntity.from_model(user)
        user_entity.current_app = app
        user_entity.current_tenant = tenant
        return user_entity

    async def get_or_create_wechat_user(
        self, wechat_user_info: WechatUserInfoDTO, app: AppEntity, tenant: Optional[TenantEntity] = None
    ) -> UserEntity:
        user = await UserRepository.get_by_phone(wechat_user_info.phone)
        if not user:
            user = await UserRepository.create_by_wechat_user(wechat_user_info)
            logger.info(
                f"Create user from wechat: {user.uid}, {user.phone}, {user.nickname}, {user.avatar_url}, {user.email}"
            )
        await UserRepository.get_or_create_user_relation(user, app.app_id, tenant.tenant_id if tenant else None)

        # check third user exists
        third_user = await ThirdUserRepository.get_third_user(user, ThirdPlatformEnum.WECHAT)
        if not third_user:
            await ThirdUserRepository.create_third_user(
                user,
                ThirdPlatformEnum.WECHAT,
                wechat_user_info.open_id,  # type: ignore
                wechat_user_info.union_id,  # type: ignore
                wechat_user_info.model_dump(),  # type: ignore
            )
        user_entity = await UserEntity.from_model(user)
        user_entity.current_app = app
        user_entity.current_tenant = tenant
        return user_entity

    async def get_or_create_dingtalk_user(
        self, dingtalk_user_info: DingtalkUserInfoDTO, app: AppEntity, tenant: Optional[TenantEntity] = None
    ) -> UserEntity:
        if not dingtalk_user_info.mobile:
            raise ValueError("Mobile is required")
        user = await UserRepository.get_by_phone(dingtalk_user_info.mobile)

        # if user not exists, create user
        if not user:
            user = await UserRepository.create_by_dingtalk_user(dingtalk_user_info)
            logger.info(
                f"Create user from dingtalk: {user.uid}, {user.phone}, {user.nickname}, {user.avatar_url}, {user.email}"  # noqa
            )
        await UserRepository.get_or_create_user_relation(user, app.app_id, tenant.tenant_id if tenant else None)

        # check third user exists
        third_user = await ThirdUserRepository.get_third_user(user, ThirdPlatformEnum.DINGTALK)
        if not third_user:
            await ThirdUserRepository.create_third_user(
                user,
                ThirdPlatformEnum.DINGTALK,
                dingtalk_user_info.open_id,  # type: ignore
                dingtalk_user_info.union_id,  # type: ignore
                dingtalk_user_info.model_dump(),  # type: ignore
            )
        user_entity = await UserEntity.from_model(user)
        user_entity.current_app = app
        user_entity.current_tenant = tenant
        return user_entity

    @classmethod
    async def get_third_user_by_platform(cls, platform: ThirdPlatformEnum, uid: str) -> Optional[ThirdUserDTO]:  # type: ignore
        third_user = await ThirdUserRepository.get_third_user_by_platform(platform, uid)
        if not third_user:
            return None
        return await ThirdUserDTO.from_tortoise_orm(third_user)

    @classmethod
    async def update_user_profile(
        cls, uid: str, nickname: Optional[str] = None, avatar_url: Optional[str] = None, email: Optional[str] = None
    ) -> UserInfoDTO:
        """更新用户资料"""
        # 获取用户
        user = await UserRepository.get_by_uid(uid)
        if not user:
            raise errors.PassportUserNotFoundError

        # 更新用户资料
        updated_user = await UserRepository.update_user_profile(
            user=user, nickname=nickname, avatar_url=avatar_url, email=email
        )
        return UserInfoDTO.model_validate(updated_user)
