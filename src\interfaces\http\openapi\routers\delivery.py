# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/openapi/routers/delivery.py
# created: 2024-12-13 13:48:46
# updated: 2025-07-07 23:02:26

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query
from fastapi.responses import RedirectResponse

from src.containers import Container

from ..authorization import openapi_authentication
from ..schemas import (
    AccessPureUrlPayload,
    AccessUrlPayload,
    AddAixincanUserPayload,
    AddAixincanUserResponse,
    AddAixincanUserResult,
    DeliveryAccessUrlResponse,
    DeliveryWifiMasterAccessUrlPayload,
    ElemeChannelUrl,
    PureDeliveryAccessUrlResponse,
)

if TYPE_CHECKING:
    from src.applications.openapi.queries import ActivitiesQueryService
    from src.applications.openapi.services import ElemeAixincanService
    from src.domains.customer.entities import CustomerEntity

router = APIRouter(tags=["delivery"])


@router.post(
    "/access_url/wifi_master/{code}",
    name="获取wifiMaster专属eleme访问链接",
    response_model=DeliveryAccessUrlResponse,
)
@inject
async def get_wifi_master_access_url(
    code: str,
    payload: DeliveryWifiMasterAccessUrlPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    access_url = await activities_query_service.get_eleme_wifimaster_url(
        customer=customer,
        page_code=code,
        auth_code=payload.auth_code,
        latitude=payload.latitude,
        longitude=payload.longitude,
        extra_info={},
    )
    return DeliveryAccessUrlResponse(data=ElemeChannelUrl(access_url=access_url))


@router.post(
    "/access_url/{page_code}",
    name="获取eleme访问链接",
    response_model=DeliveryAccessUrlResponse,
)
@inject
async def get_access_url(
    page_code: str,
    payload: AccessUrlPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    links = await activities_query_service.get_eleme_url_with_auth(
        customer=customer,
        page_code=page_code,
        jwt_token=payload.jwt,
        extra_info=payload.extra_info or {},
        eleme_channel=payload.app or "",
        latitude=payload.latitude,
        longitude=payload.longitude,
    )
    access_url = links.h5_promotion.h5_url if links.h5_promotion and links.h5_promotion.h5_url else ""
    if payload.redirect and links:
        return RedirectResponse(url=access_url, status_code=303)
    return DeliveryAccessUrlResponse(data=ElemeChannelUrl(access_url=access_url))


@router.get(
    "/access_url/{page_code}",
    name="获取eleme访问链接",
    response_model=DeliveryAccessUrlResponse,
)
@inject
async def get_method_access_url(
    page_code: str,
    params: AccessUrlPayload = Query(...),
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    links = await activities_query_service.get_eleme_url_with_auth(
        customer=customer,
        page_code=page_code,
        jwt_token=params.jwt,
        extra_info=params.extra_info or {},
        eleme_channel=params.app or "",
        latitude=params.latitude,
        longitude=params.longitude,
    )
    access_url = links.h5_promotion.h5_url if links.h5_promotion and links.h5_promotion.h5_url else ""
    if params.redirect and links:
        return RedirectResponse(url=access_url, status_code=303)
    return DeliveryAccessUrlResponse(data=ElemeChannelUrl(access_url=access_url))


@router.post(
    "/access/pure_url/{page_code}",
    name="获取eleme访问链接, 不包含渠道版token联登",
    response_model=PureDeliveryAccessUrlResponse,
)
@inject
async def get_pure_access_url(
    page_code: str,
    payload: AccessPureUrlPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    links = await activities_query_service.get_eleme_pure_url(
        customer=customer,
        page_code=page_code,
        extra_info=payload.extra_info or {},
    )
    return PureDeliveryAccessUrlResponse(data=links)


@router.post(
    "/access/url/{page_code}",
    name="获取eleme访问链接, 渠道版token联登",
    response_model=PureDeliveryAccessUrlResponse,
)
@inject
async def get_access_url_with_auth(
    page_code: str,
    payload: AccessUrlPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    activities_query_service: "ActivitiesQueryService" = Depends(
        Provide[Container.applications.openapi_activities_query_service]
    ),
):
    links = await activities_query_service.get_eleme_url_with_auth(
        customer=customer,
        page_code=page_code,
        jwt_token=payload.jwt,
        extra_info=payload.extra_info or {},
        eleme_channel=payload.app or "",
        latitude=payload.latitude,
        longitude=payload.longitude,
    )
    return PureDeliveryAccessUrlResponse(data=links)


@router.put("/access/aixincan/users", name="添加饿了么爱心餐白名单用户", response_model=AddAixincanUserResponse)
@inject
async def add_aixincan_user(
    payload: AddAixincanUserPayload,
    customer: "CustomerEntity" = Depends(openapi_authentication),
    aixincan_service: "ElemeAixincanService" = Depends(Provide[Container.applications.openapi_aixincan_service]),
):
    result = await aixincan_service.add_aixincan_user(
        phone=payload.phone,
        province=payload.province,
        city=payload.city,
        district=payload.district,
        industry=payload.industry,
    )
    return AddAixincanUserResponse.model_validate({"data": AddAixincanUserResult(success=result)})
