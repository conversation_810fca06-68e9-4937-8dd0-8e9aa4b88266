# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/supplier.py
# created: 2025-03-10 00:59:34
# updated: 2025-03-25 00:43:15

from typing import Optional, Sequence, Tuple

from pydantic import BaseModel, Field

from src.databases.models.benefits import BenefitsSupplier
from src.domains.benefits.dto import SupplierCreateDTO, SupplierUpdateDTO


class SupplierFilters(BaseModel):
    name: Optional[str] = Field(None, description="供应商名称")
    code: Optional[str] = Field(None, description="供应商编码")
    page: int = Field(default=1, ge=1, description="页码, 最小值为1")
    page_size: int = Field(default=10, ge=1, le=100, description="每页条数, 最小值为1")


class SupplierRepository:

    @classmethod
    async def delete_by_id(cls, id: int) -> None:
        await BenefitsSupplier.filter(id=id).delete()

    @classmethod
    async def create(cls, supplier: SupplierCreateDTO) -> BenefitsSupplier:  # type: ignore
        return await BenefitsSupplier.create(**supplier.model_dump())  # type: ignore

    @classmethod
    async def get_by_id(cls, id: int) -> BenefitsSupplier | None:
        return await BenefitsSupplier.get_or_none(id=id)

    @classmethod
    async def get_by_identify(cls, identify: str) -> BenefitsSupplier | None:
        return await BenefitsSupplier.get_or_none(identify=identify)

    @classmethod
    async def gets_by_filters(cls, filters: SupplierFilters) -> Tuple[int, Sequence[BenefitsSupplier]]:
        query = BenefitsSupplier.all()
        if filters.name:
            query = query.filter(name__icontains=filters.name)
        if filters.code:
            query = query.filter(code=filters.code)
        total = await query.count()
        suppliers = await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).all()
        return total, suppliers

    @classmethod
    async def update_supplier_by_id(cls, id: int, update_fields: SupplierUpdateDTO) -> BenefitsSupplier | None:  # type: ignore
        supplier = await cls.get_by_id(id)
        if supplier is None:
            return None
        for field, value in update_fields.model_dump(exclude_none=True).items():  # type: ignore
            if value is not None:
                setattr(supplier, field, value)
        await supplier.save()
        return supplier
