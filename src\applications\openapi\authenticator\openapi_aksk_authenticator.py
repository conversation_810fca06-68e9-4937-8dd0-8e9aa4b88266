# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/services/authenticator/openapi_aksk_authenticator.py
# created: 2025-04-11 00:44:11
# updated: 2025-04-24 15:17:04

import hashlib
import hmac
import time
from typing import TYPE_CHECKING, Dict, Optional, Tuple

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, Request
from loguru import logger

from src.domains.customer.entities import CustomerEntity

from .iauthenticator import IAuthenticator

if TYPE_CHECKING:
    from src.repositories.customer import CustomerRepository


class OpenAPIAKSKAuthenticator(IAuthenticator):
    """AK/SK认证类"""

    def __init__(self, customer_repo: "CustomerRepository"):
        self.customer_repo = customer_repo

    async def get_request_params(self, request: Request) -> Dict[str, str]:
        """获取请求参数，包括查询参数和请求体"""
        params = {}

        # 获取查询参数
        for key, value in request.query_params.items():
            params[key] = value

        # 获取请求体参数
        content_type = request.headers.get("content-type", "")
        if "application/json" in content_type:
            try:
                body = await request.json()
                if isinstance(body, dict):
                    for key, value in body.items():
                        # 确保值是字符串类型
                        if isinstance(value, (dict, list)):
                            import json

                            params[key] = json.dumps(value, ensure_ascii=False, separators=(",", ":"))
                        else:
                            params[key] = str(value) if value is not None else ""
            except Exception as e:
                logger.warning(f"Failed to parse JSON body: {e}")
        elif "application/x-www-form-urlencoded" in content_type:
            try:
                form = await request.form()
                for key, value in form.items():  # type: ignore
                    if isinstance(value, (dict, list)):
                        import json

                        params[key] = json.dumps(value, ensure_ascii=False, separators=(",", ":"))
                    else:
                        params[key] = value
            except Exception as e:
                logger.warning(f"Failed to parse form data: {e}")

        return params

    def calculate_signature(self, method: str, path: str, params: Dict[str, str], secret_key: str) -> str:
        """计算请求签名"""
        params_to_sign = {k: v for k, v in params.items() if k != "signature"}  # 排除signature参数
        # 按键名排序并构造规范化字符串
        canonical_string = method.upper() + path + "".join(f"&{k}={v}" for k, v in sorted(params_to_sign.items()))
        # 使用HMAC-SHA256计算签名
        hmac_obj = hmac.new(secret_key.encode("utf-8"), canonical_string.encode("utf-8"), hashlib.sha256)
        return hmac_obj.hexdigest()

    async def validate_timestamp(self, timestamp: Optional[str], max_diff: int = 300) -> bool:
        """验证时间戳是否在有效期内(默认5分钟)"""
        if not timestamp:
            return False
        try:
            ts = int(timestamp)
            current_time = int(time.time())
            return abs(current_time - ts) <= max_diff
        except (ValueError, TypeError):
            return False

    async def authenticate(self, request: Request) -> Tuple[bool, Optional["CustomerEntity"], str]:
        # 获取签名
        signature = request.headers.get("X-Signature")
        if not signature:
            return False, None, "Missing signature"

        # 获取请求参数
        params = await self.get_request_params(request)

        # 验证必要参数
        if "access_key" not in params:
            return False, None, "Missing access_key"
        if "timestamp" not in params:
            return False, None, "Missing timestamp"
        if "nonce" not in params:
            return False, None, "Missing nonce"

        # 验证时间戳
        if not await self.validate_timestamp(params.get("timestamp")):
            return False, None, "Timestamp expired"

        try:
            # 查找匹配的access_key
            access_key = params["access_key"]
            secret = await self.customer_repo.get_secret_by_access_key(access_key)

            if not secret:
                return False, None, "Invalid access_key"

            # 计算签名
            method = request.method
            path = request.url.path
            expected_signature = self.calculate_signature(method, path, params, secret.access_secret)

            # 验证签名
            if signature != expected_signature:
                return False, None, "Invalid signature"

            # 返回客户信息
            return True, await CustomerEntity.from_secret(secret), ""

        except Exception as e:
            logger.warning(f"AK/SK authentication error: {e}")
            return False, None, f"Authentication error: {str(e)}"
