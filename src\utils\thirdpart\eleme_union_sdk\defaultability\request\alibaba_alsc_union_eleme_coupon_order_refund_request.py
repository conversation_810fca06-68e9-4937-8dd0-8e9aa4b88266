from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class AlibabaAlscUnionElemeCouponOrderRefundRequest(BaseRequest):

    def __init__(self, order_refund_dto: object = None):
        """
        退款对象
        """
        self._order_refund_dto = order_refund_dto

    @property
    def order_refund_dto(self):
        return self._order_refund_dto

    @order_refund_dto.setter
    def order_refund_dto(self, order_refund_dto):
        if isinstance(order_refund_dto, object):
            self._order_refund_dto = order_refund_dto
        else:
            raise TypeError("order_refund_dto must be object")

    def get_api_name(self):
        return "alibaba.alsc.union.eleme.coupon.order.refund"

    def to_dict(self):
        request_dict = {}
        if self._order_refund_dto is not None:
            request_dict["order_refund_dto"] = convert_struct(self._order_refund_dto)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict


class AlibabaAlscUnionElemeCouponOrderRefundCouponOrderVoucherDetailDto:
    def __init__(self, item_id: str = None, voucher_id: str = None):
        """
        商品ID，必填
        """
        self.item_id = item_id
        """
            凭证ID，必填
        """
        self.voucher_id = voucher_id


class AlibabaAlscUnionElemeCouponOrderRefundCouponOrderRefundDto:
    def __init__(self, reason: str = None, biz_order_id: str = None, voucher_list: list = None, ext_info: str = None):
        """
        用户退款原因，必填
        """
        self.reason = reason
        """
            本地生活订单号，必填
        """
        self.biz_order_id = biz_order_id
        """
            退款明细
        """
        self.voucher_list = voucher_list
        """
            扩展参数，json格式
        """
        self.ext_info = ext_info
