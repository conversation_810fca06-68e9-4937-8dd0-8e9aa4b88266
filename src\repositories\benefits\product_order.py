# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/repositories/product_order.py
# created: 2025-02-09 21:52:16
# updated: 2025-04-06 19:10:34

from datetime import datetime
from typing import Optional, Sequence, Tuple, Union

from pydantic import BaseModel, Field
from tortoise.queryset import QuerySet

from src.databases.models.benefits import BenefitsProductOrder, BenefitsTickets
from src.databases.models.customer import Customer
from src.domains.benefits.dto import BenefitsProductOrderCreateFields


class ProductOrderFilters(BaseModel):
    product_code: Optional[str] = Field(None, description="产品编码")
    order_id: Optional[str] = Field(None, description="订单ID")
    out_order_id: Optional[str] = Field(None, description="外部订单ID")
    product_name: Optional[str] = Field(None, description="产品名称")
    account: Optional[str] = Field(None, description="充值账号")
    start_at: Optional[datetime] = Field(None, description="开始时间")
    end_at: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[int] = Field(None, description="订单状态")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页条数")
    customer_id: Optional[int] = Field(None, description="客户ID")


class ProductOrderExportFilters(BaseModel):
    product_code: Optional[str] = Field(None, description="产品编码")
    order_id: Optional[str] = Field(None, description="订单ID")
    out_order_id: Optional[str] = Field(None, description="外部订单ID")
    product_name: Optional[str] = Field(None, description="产品名称")
    account: Optional[str] = Field(None, description="充值账号")
    start_at: Optional[datetime] = Field(None, description="开始时间")
    end_at: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[int] = Field(None, description="订单状态")
    customer_id: Optional[int] = Field(None, description="客户ID")


class ProductOrderRepository:

    @classmethod
    async def get_for_update(cls, order_id: str) -> Optional[BenefitsProductOrder]:
        """获取订单用于更新"""
        return await BenefitsProductOrder.filter(order_id=order_id).select_for_update().first()

    @classmethod
    async def check_order_exist(cls, out_order_id: str, app_id: str, tenant_id: Optional[str] = None) -> bool:
        """检查订单是否存在"""
        query = BenefitsProductOrder.filter(out_order_id=out_order_id, app_id=app_id)
        if tenant_id:
            query = query.filter(tenant_id=tenant_id)
        return await query.exists()

    @classmethod
    async def create_order(cls, order_create_fields: BenefitsProductOrderCreateFields) -> BenefitsProductOrder:  # type: ignore
        """创建订单"""
        return await BenefitsProductOrder.create(**order_create_fields.model_dump())  # type: ignore

    @classmethod
    async def get_all_customers(cls) -> Sequence[Customer]:
        """查询所有客户"""
        return await Customer.all()

    @classmethod
    async def _build_product_orders_query(
        cls, filters: Union[ProductOrderFilters, ProductOrderExportFilters], customers: Sequence[Customer]
    ) -> QuerySet[BenefitsProductOrder]:
        """构建产品订单查询（公共方法）"""
        query = BenefitsProductOrder.all()
        if filters.product_code:
            query = query.filter(product_code=filters.product_code)
        if filters.order_id:
            query = query.filter(order_id=filters.order_id)
        if filters.out_order_id:
            query = query.filter(out_order_id=filters.out_order_id)
        if filters.product_name:
            query = query.filter(product_name__icontains=filters.product_name)
        if filters.account:
            query = query.filter(account=filters.account)
        if filters.start_at:
            query = query.filter(created_at__gte=filters.start_at)
        if filters.end_at:
            query = query.filter(created_at__lte=filters.end_at)
        if filters.status is not None:
            query = query.filter(status=filters.status)
        if filters.customer_id:
            # 从客户列表中查找对应的客户
            customer = None
            for c in customers:
                if c.id == filters.customer_id:
                    customer = c
                    break

            if customer and customer.tenant_id:  # type: ignore
                query = query.filter(tenant_id=customer.tenant_id)  # type: ignore
            else:
                # 返回空查询，但保持客户列表
                return BenefitsProductOrder.filter(id=-1)

        return query

    @classmethod
    async def get_product_orders(
        cls, filters: ProductOrderFilters
    ) -> Tuple[int, Sequence[BenefitsProductOrder], Sequence[Customer]]:
        """获取产品订单列表（同时返回客户信息）"""
        # 先获取所有客户
        customers = await cls.get_all_customers()

        # 构建查询
        query = await cls._build_product_orders_query(filters, customers)

        # 如果查询为空（customer_id不存在），直接返回
        if await query.count() == 0 and filters.customer_id:
            return 0, [], customers

        total = await query.count()
        orders = (
            await query.limit(filters.page_size).offset((filters.page - 1) * filters.page_size).order_by("-id").all()
        )

        return total, orders, customers

    @classmethod
    async def get_product_orders_for_export(
        cls, filters: ProductOrderExportFilters
    ) -> Tuple[int, Sequence[BenefitsProductOrder], Sequence[Customer]]:
        """获取产品订单列表（同时返回客户信息）"""
        # 先获取所有客户
        customers = await cls.get_all_customers()

        # 构建查询
        query = await cls._build_product_orders_query(filters, customers)

        # 如果查询为空（customer_id不存在），直接返回
        if await query.count() == 0 and filters.customer_id:
            return 0, [], customers

        total = await query.count()
        orders = await query.order_by("-id").all()

        return total, orders, customers

    @classmethod
    async def get_by_order_id(cls, order_id: str) -> Optional[BenefitsProductOrder]:
        return await BenefitsProductOrder.get_or_none(order_id=order_id)

    @classmethod
    async def get_by_out_order_id(cls, out_order_id: str) -> Optional[BenefitsProductOrder]:
        return await BenefitsProductOrder.get_or_none(out_order_id=out_order_id)

    @classmethod
    async def create_charge_ticket(
        cls,
        ticket_code: str,
        customer_id: int,
        product_code: str,
        account: str,
        notify_url: str,
        expired_at: datetime,
    ) -> BenefitsTickets:
        return await BenefitsTickets.create(
            ticket_code=ticket_code,
            customer_id=customer_id,
            product_code=product_code,
            account=account,
            notify_url=notify_url,
            expired_at=expired_at,
        )

    @classmethod
    async def get_charge_ticket_by_ticket_code(cls, ticket_code: str) -> Optional[BenefitsTickets]:
        return await BenefitsTickets.get_or_none(ticket_code=ticket_code)
