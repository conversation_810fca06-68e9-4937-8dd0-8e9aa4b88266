# encoding: utf-8
# src/applications/common/queries/delivery/orders.py
# created: 2025-08-12 14:50:00

import asyncio
from datetime import datetime
from typing import TYPE_CHECKING

from loguru import logger

if TYPE_CHECKING:
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway
    from src.infrastructures.gateways.eleme.union.dto import UnionOrderInfoDTO


class UnionOrderQueryService:
    """饿了么联盟订单查询服务"""

    def __init__(self, eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway"):
        self.gateway = eleme_union_delivery_gateway

    async def get_orders_by_time_range(
        self, start_time: datetime, end_time: datetime, date_type: int = 4
    ) -> list["UnionOrderInfoDTO"]:
        """
        按时间范围获取饿了么联盟订单

        Args:
            start_time: 开始时间
            end_time: 结束时间
            date_type: 日期类型 (默认4-按更新时间)

        Returns:
            订单列表
        """
        orders = []
        page_number = 1

        while True:
            result = self.gateway.fetch_remote_cps_order(
                page_size=50, page_number=page_number, start_time=start_time, end_time=end_time, date_type=date_type
            )

            # QPS限制：150次/分钟，80%负载约0.5秒间隔
            await asyncio.sleep(0.4)

            if not result[0]:  # 如果没有更多数据
                break

            orders.extend(result[0])
            page_number += 1

            logger.debug("获取联盟订单第{page}页，本页{count}条", page=page_number - 1, count=len(result[0]))

        logger.info(
            "获取联盟订单完成：{total}条，时间范围：{start} - {end}",
            total=len(orders),
            start=start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end=end_time.strftime("%Y-%m-%d %H:%M:%S"),
        )

        return orders
