# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/openapi/schemas.py
# created: 2024-12-04 00:53:09
# updated: 2025-07-07 13:23:36

from typing import Any, Dict, Optional, Union

from pydantic import BaseModel, Field

from src.applications.openapi.dto import (
    AccountInfoDTO,
    ActivityLinksDTO,
    BenefitsOrderDTO,
    BenefitsProductChargePageDTO,
    BenefitsProductDTO,
)
from src.infrastructures.fastapi.response import BaseResponse


class RechargeRequestPayload(BaseModel):
    out_order_id: str = Field(..., description="外部订单ID")
    notify_url: str = Field(..., description="回调地址")


class ChargeRequestPayload(BaseModel):
    out_order_id: str = Field(..., description="外部订单ID")
    account: str = Field(..., pattern=r"^1[3-9]\d{9}$", description="充值帐户")
    notify_url: str = Field(..., description="回调地址")
    source_identify: Optional[str] = Field(None, description="来源标识, 当前仅支持海狸权益产品使用")


class DeliveryWifiMasterAccessUrlPayload(BaseModel):
    auth_code: str = Field(..., description="wifiMaster的auth_token")
    is_new: Optional[bool] = Field(False, description="是否是新用户")
    latitude: Optional[float] = Field(None, description="纬度")
    longitude: Optional[float] = Field(None, description="经度")


class AccessUrlPayload(BaseModel):
    jwt: str = Field(..., description="jwt token")
    extra_info: Optional[Dict[str, Union[str, int, float]]] = Field({}, description="额外信息, 用于渠道版token联登")
    app: Optional[str] = Field("haili", description="海狸提供的app信息")
    redirect: Optional[bool] = Field(False, description="是否重定向")
    latitude: Optional[float] = Field(None, description="纬度")
    longitude: Optional[float] = Field(None, description="经度")


class AccessPureUrlPayload(BaseModel):
    extra_info: Dict[str, Union[str, int, float]] = Field(..., description="额外信息")


class AddAixincanUserPayload(BaseModel):
    phone: str = Field(..., description="用户手机号")
    province: str = Field(..., description="省份")
    city: str = Field(..., description="城市")
    district: Optional[str] = Field(None, description="区县")
    industry: Optional[str] = Field(None, description="行业")


class AddAixincanUserResult(BaseModel):
    success: bool = Field(..., description="是否成功")


class ElemeChannelUrl(BaseModel):
    access_url: str = Field(..., description="Eleme渠道版访问链接")


class BenefitsProductChargePagePayload(BaseModel):
    account: str = Field(..., description="充值帐户")
    notify_url: str = Field(..., description="回调地址")


class BenefitsProductChargeByTicketPayload(BaseModel):
    ticket_code: str = Field(..., description="充值凭证")


class DineInAccessUrl(BaseModel):
    access_url: str = Field(..., description="到店访问链接")


class DineInAccessUrlPayload(BaseModel):
    jwt: str = Field(..., description="jwt token")
    enterprise: str = Field(..., description="企业名称")
    enterprise_code: str = Field(..., description="企业ID")
    longitude: str = Field(..., description="经度")
    latitude: str = Field(..., description="纬度")
    pname: str = Field(..., description="省份")
    cityname: str = Field(..., description="城市")
    adname: str = Field(..., description="区县")
    address: str = Field(..., description="地址")
    address_name: str = Field(..., description="地址名称")


PureDeliveryAccessUrlResponse = BaseResponse[ActivityLinksDTO]
CustomerBenefitsResponse = BaseResponse[list[BenefitsProductDTO]]
BenefitsOrderResponse = BaseResponse[BenefitsOrderDTO]
AccountBalanceResponse = BaseResponse[AccountInfoDTO]
DeliveryAccessUrlResponse = BaseResponse[ElemeChannelUrl]
AddAixincanUserResponse = BaseResponse[AddAixincanUserResult]
BenefitsProductChargePageResponse = BaseResponse[BenefitsProductChargePageDTO]
BenefitsProductChargeByTicketResponse = BaseResponse[str]
DineInAccessUrlResponse = BaseResponse[DineInAccessUrl]
