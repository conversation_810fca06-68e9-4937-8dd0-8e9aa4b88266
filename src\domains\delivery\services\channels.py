# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/services/channels.py
# created: 2024-12-01 18:31:31
# updated: 2025-03-24 22:40:14

import time
import uuid
from typing import Optional

import jwt

from src.domains.passport.dto import AuthPassportUserDTO


class ChannelsService:

    @classmethod
    def _generate_access_url(
        cls, phone: str, code: str, latitude: Optional[float] = None, longitude: Optional[float] = None
    ) -> str:

        white_code_list = [
            "9b381e2631f54d8b9b244f719bc9dcfc",
            "f07f6283c7a84baba6fcad21455cde9c",
            "27ee7fffa741486898a1a2fa65555206",
            "a32d76381c4e4d00ad573b5415209f15",
            "8a9d9346d0e34787b6566a3d6aad527d",
            "fa01bede1f5342e1ade257970b58f2e1",
        ]

        spec_code_map = {
            "dingtalk_main": "9b381e2631f54d8b9b244f719bc9dcfc",
            "dingtalk_campus_daily": "f07f6283c7a84baba6fcad21455cde9c",
            "dingtalk_wallet": "27ee7fffa741486898a1a2fa65555206",
            "wifi_master": "8ed8fe4b394a4d6a948fc69a6f46bd22",
        }

        payload = {
            "device_id": uuid.uuid4().hex,
            "open_id": uuid.uuid4().hex,
            "source": "haili",
            "mobile": phone,
            "nickname": "",
            "aud": "ele.me",
            "iat": int(time.time()) - 30,
            "exp": int(time.time()) + 60 * 60 * 24 * 30,  # 30天过期时间
        }
        token = jwt.encode(  # build eleme access token
            payload=payload,
            key="a82d232c88594435317451bc0fe50fdda8977368",
            algorithm="HS256",
            headers={"alg": "HS256", "typ": "JWT"},
        )

        if code in white_code_list:
            access_url = (
                f"https://h5.ele.me/adminiappsub/pages/h5/index?configKey=BDLM_ELE_H5_DG_TC&scene={code}&"
                f"from=mobile.haili&opensite_source=haili&jwt={token}"
            )
        elif code in spec_code_map:
            access_url = (
                f"https://h5.ele.me/adminiappsub/pages/h5/index?configKey=BDLM_ELE_H5_DG_TC&scene={spec_code_map[code]}&"
                f"from=mobile.haili&opensite_source=haili&jwt={token}"
            )
        else:
            access_url = (
                f"https://h5.ele.me/adminiappsub/pages/wh-coupon-guide/index?scene={code}&"
                f"from=mobile.haili&opensite_source=haili&jwt={token}"
            )

        if latitude and longitude:
            access_url += f"&latitude={latitude}&longitude={longitude}"
        return access_url

    @classmethod
    async def get_access_url_with_passport_user(
        cls,
        code: str,
        user_info: AuthPassportUserDTO,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
    ) -> str:
        return cls._generate_access_url(user_info.phone, code, latitude, longitude)  # type: ignore

    @classmethod
    async def generate_access_url(
        cls,
        page_code: str,
        from_channel: str,
        user_info: AuthPassportUserDTO,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
    ) -> str:
        # page_info = await DeliveryPageService.get_page_by_code(page_code)
        # if not page_info:
        #     raise errors.DeliveryPageNotFoundError

        return cls._generate_access_url(user_info.phone, page_code, from_channel, latitude, longitude)  # type: ignore
