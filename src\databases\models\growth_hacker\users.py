# encoding: utf-8
# src/databases/models/growth_hacker/users.py
# created: 2025-07-25 10:35:00

from typing import Any, Dict, List

from tortoise import Model, fields


class UserDeviceCache(Model):
    """用户设备缓存模型"""

    id = fields.BigIntField(primary_key=True)

    # 索引字段
    phone = fields.CharField(max_length=20, unique=True, description="手机号")

    # 浏览器存储 - 使用 JSON 字段存储
    local_storage = fields.JSONField(default=dict, description="本地存储")
    session_storage = fields.JSONField(default=dict, description="会话存储")
    cookies = fields.JSONField(default=list, description="Cookie列表")

    # 设备配置
    device_config = fields.JSONField(default=dict, description="设备配置")
    webview_config = fields.JSONField(default=dict, description="WebView配置")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.Datetime<PERSON><PERSON>(auto_now=True, index=True, description="更新时间")

    async def update_timestamp(self) -> None:
        """更新时间戳"""
        await self.save(update_fields=["updated_at"])

    class Meta:
        table = "gh_user_profile"
        table_description = "用户设备信息缓存表"
