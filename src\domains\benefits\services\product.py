# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/benefits/services/product.py
# created: 2024-12-08 11:21:39
# updated: 2025-06-02 23:27:31

from typing import TYPE_CHECKING, Optional

from loguru import logger
from tortoise.transactions import atomic

from src.databases.models.benefits import BenefitsProductOrderStatus as OrderStatus
from src.databases.models.benefits import BenefitsSkuChargeRecordStatus as RecordStatus
from src.domains.benefits.dto import BenefitsProductDTO, BenefitsProductOrderCreateFields, BenefitsProductOrderDTO
from src.domains.benefits.messages import BenefitsOrderNoticeMessage, BenefitsOrderNoticeMessageContent
from src.domains.passport.dto import PassportAppDTO, PassportTenantDTO
from src.infrastructures import errors
from src.infrastructures.rabbitmq import RabbitMQProducer

if TYPE_CHECKING:
    from src.domains.benefits.services import BenefitsSkuService
    from src.repositories.benefits import ProductOrderRepository, ProductRepository, SkuChargeRecordRepository


class BenefitsProductService:

    def __init__(
        self,
        product_repo: "ProductRepository",
        pdorder_repo: "ProductOrderRepository",
        sku_charge_record_repo: "SkuChargeRecordRepository",
        sku_service: "BenefitsSkuService",
        producer: "RabbitMQProducer",
    ):
        self.product_repo = product_repo
        self.pdorder_repo = pdorder_repo
        self.sku_charge_record_repo = sku_charge_record_repo
        self.sku_service = sku_service
        self.producer = producer

    async def get_product_by_code(self, code: str) -> BenefitsProductDTO:
        """根据product_code获取权益产品"""
        product = await self.product_repo.get_by_code(code=code)
        if not product:
            raise errors.BenefitsProductNotFoundError
        return await BenefitsProductDTO.from_tortoise_orm(product)

    @atomic()
    async def charge_product(
        self,
        product_code: str,
        account: str,
        out_order_id: str,
        sale_price: int,
        source_identify: str,
        notify_url: str,
        from_app: PassportAppDTO | str,
        from_tenant: Optional[PassportTenantDTO | str] = None,
        op_uid: Optional[str] = "",
        op_description: Optional[str] = "",
    ) -> BenefitsProductOrderDTO:
        """标准的产品充值接口"""

        app_id = from_app.id if isinstance(from_app, PassportAppDTO) else from_app  # type: ignore
        tenant_id = from_tenant.tenant_id if isinstance(from_tenant, PassportTenantDTO) else from_tenant

        # 幂等性检查
        idempotency = await self.pdorder_repo.check_order_exist(
            out_order_id,
            app_id,
            tenant_id,
        )
        if idempotency:
            logger.info(f"产品订单幂等性检查失败: {out_order_id}, {app_id}, {tenant_id}")
            raise errors.BenefitIdempotencyVerificationError

        # 检查产品是否存在
        product = await self.product_repo.get_by_code(product_code)
        if not product or not product.enabled:
            raise errors.BenefitsProductNotFoundError

        # 创建产品订单
        order_create_fields = BenefitsProductOrderCreateFields.model_validate(
            {
                "price": sale_price,
                "account": account,
                "out_order_id": out_order_id,
                "product_id": product.id,
                "product_name": product.name,
                "product_code": product.code,
                "app_id": app_id,
                "tenant_id": tenant_id,
                "source_identify": source_identify,
                "notify_url": notify_url,
                "op_uid": op_uid,
                "op_description": op_description,
            }
        )
        order = await self.pdorder_repo.create_order(order_create_fields)
        logger.info(
            "创建产品订单: {order_id}, {out_order_id}, {account}, {sale_price}, {product_name}, {product_code}, {notify_url}, {source_identify}, {app_id}, {tenant_id}",
            order_id=order.order_id,
            out_order_id=order.out_order_id,
            account=order.account,
            sale_price=order.price,
            product_name=order.product_name,
            product_code=order.product_code,
            notify_url=order.notify_url,
            source_identify=order.source_identify,
            app_id=app_id,
            tenant_id=tenant_id,
        )
        order_dto = await BenefitsProductOrderDTO.from_tortoise_orm(order)

        skus = await self.product_repo.get_relations_by_product_code(product_code)

        sku_codes = []
        for count, sku in skus:
            for _ in range(count):
                sku_codes.append(sku.code)

        logger.info(
            "订单[{order_id}]发放[{count}]个sku: {sku_codes}",
            order_id=order.order_id,
            count=len(sku_codes),
            sku_codes=sku_codes,
        )

        # 处理产品下面所有的sku的充值
        await self.sku_service.charge_skus(sku_codes, order_dto)
        return order_dto

    @atomic()
    async def check_charge_result(self, charge_order_id: str):
        """检查产品订单的充值结果"""
        # 1. 获取订单用于更新, 数据库加锁
        charge_order = await self.pdorder_repo.get_for_update(charge_order_id)
        if not charge_order:
            raise errors.ChargeOrderNotFoundError

        # 2. 检查订单是否所有sku都充值成功
        charge_records = await self.sku_charge_record_repo.gets_by_order_id(charge_order_id)

        success_records = []
        failed_records = []
        for record in charge_records:
            # 如果记录状态为PENDING或PROCESSING, 则退出等待
            if record.status in [RecordStatus.PENDING, RecordStatus.PROCESSING]:
                return
            if record.status in [RecordStatus.SUCCESS, RecordStatus.REFUNDED, RecordStatus.USED]:
                success_records.append(record)
            if record.status in [RecordStatus.FAILED]:
                failed_records.append(record)

        if len(success_records) == len(charge_records):  # 如果所有记录都成功, 则更新订单状态为SUCCESS
            charge_order.status = OrderStatus.SUCCESS
        elif len(failed_records) == len(charge_records):  # 如果所有记录都失败, 则更新订单状态为FAILED
            charge_order.status = OrderStatus.FAILED
        elif len(charge_records) == 0:
            charge_order.status = OrderStatus.FAILED
        else:  # 如果存在失败记录, 则更新订单状态为EXCEPTION
            charge_order.status = OrderStatus.EXCEPTION

        await charge_order.save()

        # 发送订单通知消息
        await self.producer.publish_message(
            BenefitsOrderNoticeMessage(
                payload=BenefitsOrderNoticeMessageContent(
                    order_id=str(charge_order.order_id),
                )
            ),
        )

    async def get_order_by_order_id(self, order_id: str) -> BenefitsProductOrderDTO:
        """根据订单id获取订单"""
        order = await self.pdorder_repo.get_by_order_id(order_id)
        if not order:
            raise errors.ChargeOrderNotFoundError
        return await BenefitsProductOrderDTO.from_tortoise_orm(order)

    async def get_order_by_out_order_id(self, out_order_id: str) -> BenefitsProductOrderDTO:
        """根据第三方订单号获取订单"""
        order = await self.pdorder_repo.get_by_out_order_id(out_order_id)
        if not order:
            raise errors.ChargeOrderNotFoundError
        return await BenefitsProductOrderDTO.from_tortoise_orm(order)
