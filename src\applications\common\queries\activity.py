# encoding: utf-8
# src/applications/common/queries/activity.py
# created: 2025-07-29 09:55:44

from typing import TYPE_CHECKING, Optional, Union

from src.domains.activity.entities import ActivityEntity
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from src.utils.eleme.channels import ElemeChannelsUtils

from ..dto import ActivityLinksDTO
from ..errors import ActivityNotFoundError

if TYPE_CHECKING:
    from src.domains.activity.services import ElemeActivityService
    from src.domains.delivery.services import DeliveryPageService
    from src.infrastructures.gateways import BifrostGateway
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway
    from src.repositories.delivery import DeliveryPageRepository
    from src.repositories.passport import AppRepository, TenantRepository, UserRepository


class ActivityQueryService:

    def __init__(
        self,
        delivery_page_service: "DeliveryPageService",
        delivery_page_repo: "DeliveryPageRepository",
        bifrost_gateway: "BifrostGateway",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        eleme_activity_service: "ElemeActivityService",
        app_repo: "AppRepository",
        tenant_repo: "TenantRepository",
        user_repo: "UserRepository",
    ):
        self.delivery_page_service = delivery_page_service
        self.delivery_page_repo = delivery_page_repo
        self.bifrost_gateway = bifrost_gateway
        self.eleme_union_delivery_gateway = eleme_union_delivery_gateway
        self.eleme_activity_service = eleme_activity_service
        self.app_repo = app_repo
        self.tenant_repo = tenant_repo
        self.user_repo = user_repo

    async def get_eleme_pure_url(
        self,
        app: Union[AppEntity, str],
        tenant: Union[TenantEntity, str],
        user: Union[UserEntity, str],
        page_code: str,
        extra_info: dict,
    ) -> ActivityLinksDTO:
        """获取饿了么活动纯净版链接"""
        page_info = await self.delivery_page_repo.get_page_by_code(page_code)
        if not page_info:
            raise ActivityNotFoundError

        if isinstance(app, str):
            app_model = await self.app_repo.get_by_appid(app)
            if not app_model:
                raise ActivityNotFoundError
            app = await AppEntity.from_model(app_model)
        if isinstance(tenant, str):
            tenant_model = await self.tenant_repo.get_by_tenant_id(tenant)
            if not tenant_model:
                raise ActivityNotFoundError
            tenant = await TenantEntity.from_model(tenant_model)
        if isinstance(user, str):
            user_model = await self.user_repo.get_by_uid(user)
            if not user_model:
                raise ActivityNotFoundError
            user = await UserEntity.from_model(user_model)
            user.current_app = app
            user.current_tenant = tenant

        extra_info = {**extra_info, "app_id": app.app_id, "tenant_id": tenant.tenant_id, "user_id": user.phone}
        activity = await ActivityEntity.from_delivery_page(page_info)
        return await activity.get_links_with_eleme_miniapp(
            self.bifrost_gateway, self.eleme_union_delivery_gateway, extra_info
        )

    async def get_eleme_url_with_auth(
        self,
        app: AppEntity,
        tenant: TenantEntity,
        user: UserEntity,
        page_code: str,
        extra_info: dict,
        eleme_channel: str,
        latitude: Optional[float],
        longitude: Optional[float],
    ) -> ActivityLinksDTO:
        """获取饿了么活动链接, 包含渠道版token联登"""
        page_info = await self.delivery_page_repo.get_page_by_code(page_code)
        if not page_info:
            raise ActivityNotFoundError
        activity = await ActivityEntity.from_delivery_page(page_info)

        channel_params = self.eleme_activity_service.build_channel_params(
            eleme_channel=eleme_channel,
            mobile=user.phone,
            user_open_id=user.uid,
            latitude=latitude,
            longitude=longitude,
        )

        extra_info = {**extra_info, "app_id": app.app_id, "tenant_id": tenant.tenant_id, "user_id": user.phone}
        links = await activity.get_links_with_eleme_miniapp(
            self.bifrost_gateway, self.eleme_union_delivery_gateway, extra_info
        )
        if links.h5_promotion and links.h5_promotion.h5_url:
            links.h5_promotion.h5_url = ElemeChannelsUtils.url_with_params(links.h5_promotion.h5_url, channel_params)
        return links
