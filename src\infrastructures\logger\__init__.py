# encoding: utf-8
# src/infrastructures/logger/__init__.py
# created: 2025-08-14 15:05:00

from typing import Optional, Union

from loguru import logger as _loguru_logger

from .context import LogContext
from .core import Logger
from .formatters import loguru_formatter
from .settings import LoggerSettings

# 全局日志器实例
_logger: Optional[Logger] = None


def setup(config: Union[LoggerSettings, dict]) -> Logger:
    """初始化日志系统

    Args:
        config: 日志配置，可以是 LoggerSettings 实例或字典

    Returns:
        Logger 实例

    Example:
        >>> logger = setup({
        ...     "app_name": "my_app",
        ...     "sinks": [
        ...         {"type": "console", "level": "INFO"},
        ...         {"type": "file", "level": "DEBUG"}
        ...     ]
        ... })
        >>> logger.info("Application started")
    """
    global _logger

    if isinstance(config, dict):
        config = LoggerSettings(**config)

    _logger = Logger(config)
    return _logger


def get_logger() -> Logger:
    """获取全局日志器实例

    Returns:
        Logger 实例

    Raises:
        RuntimeError: 如果日志器未初始化
    """
    if _logger is None:
        raise RuntimeError("Logger not initialized. Call setup() first or use default_logger()")
    return _logger


def default_logger(app_name: str = "app") -> Logger:
    """创建默认配置的日志器

    用于快速开始，使用最简单的配置

    Args:
        app_name: 应用名称

    Returns:
        Logger 实例
    """
    global _logger

    if _logger is None:
        from .settings import ConsoleSinkConfig

        config = LoggerSettings(
            app_name=app_name,
            sinks=[
                ConsoleSinkConfig(type="console", level="INFO"),
            ],
        )
        _logger = Logger(config)

    return _logger


# 为了兼容性，导出 loguru 的原始 logger
# 某些场景下可能需要直接使用 loguru
loguru_logger = _loguru_logger


# 导出公共接口
__all__ = [
    "setup",
    "get_logger",
    "default_logger",
    "Logger",
    "LogContext",
    "LoggerSettings",
    "loguru_logger",
    "loguru_formatter",
]
