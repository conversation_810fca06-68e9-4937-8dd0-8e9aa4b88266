# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/base/routers/passport/__init__.py
# created: 2025-05-27 00:41:21
# updated: 2025-05-27 11:03:50

from fastapi import APIRouter

from .applications import router as applications_router
from .login import router as login_router
from .users import router as users_router

router = APIRouter(tags=["Passport"])
router.include_router(users_router)
router.include_router(applications_router)
router.include_router(login_router)
