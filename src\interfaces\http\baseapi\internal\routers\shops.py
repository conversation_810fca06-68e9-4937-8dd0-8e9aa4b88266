# encoding: utf-8
# src/interfaces/http/baseapi/internal/routers/shops.py
# created: 2025-08-04 12:12:42

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends

from src.interfaces.http.baseapi import Container
from src.interfaces.http.baseapi.internal.authorization import verify_internal_app
from src.interfaces.http.baseapi.internal.schemas import ShopLinkResponse, ShopListResponse

if TYPE_CHECKING:
    from src.applications.common.queries import ShopsQueryService
    from src.domains.passport.entities import AppEntity

router = APIRouter(prefix="", tags=["shops"])


@router.get(
    "/{area_id}/shops", summary="获取商圈店铺列表", description="获取商圈店铺列表", response_model=ShopListResponse
)
@inject
async def get_shops(
    area_id: str,
    current_app: "AppEntity" = Depends(verify_internal_app),
    shops_query_service: "ShopsQueryService" = Depends(Provide[Container.applications.common_shops_query_service]),
):
    shop_list = await shops_query_service.get_shops_by_area(area_id)
    return ShopListResponse(data=shop_list)


@router.get(
    "/{area_id}/hot_shops",
    summary="获取商圈热门店铺列表",
    description="获取商圈热门店铺列表",
    response_model=ShopListResponse,
)
@inject
async def get_hot_shops(
    area_id: str,
    current_app: "AppEntity" = Depends(verify_internal_app),
    shops_query_service: "ShopsQueryService" = Depends(Provide[Container.applications.common_shops_query_service]),
):
    shop_list = await shops_query_service.get_hot_shops_by_area(area_id)
    return ShopListResponse(data=shop_list)


@router.get(
    "/{shop_id}/{pid}/access",
    summary="获取店铺推广链接",
    description="获取店铺推广链接",
    response_model=ShopLinkResponse,
)
@inject
async def get_shop_access(
    shop_id: str,
    pid: str,
    current_app: "AppEntity" = Depends(verify_internal_app),
    shops_query_service: "ShopsQueryService" = Depends(Provide[Container.applications.common_shops_query_service]),
):
    shop_link = await shops_query_service.get_shop_link(shop_id, pid)
    return ShopLinkResponse(data=shop_link)
