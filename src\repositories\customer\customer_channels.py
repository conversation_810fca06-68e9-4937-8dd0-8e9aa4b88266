# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/customer/repositories/customer_channels.py
# created: 2025-02-06 02:25:32
# updated: 2025-03-24 22:44:56


from src.databases.models.customer import Customer, CustomerChannels


class ChannelsRepository:

    @classmethod
    async def gets_by_customer_id(cls, customer_id: int) -> list[CustomerChannels]:
        return await CustomerChannels.filter(customer_id=customer_id).all()

    @classmethod
    async def get_by_code(cls, code: str) -> CustomerChannels | None:
        return await CustomerChannels.get_or_none(code=code)

    @classmethod
    async def gets_all(cls) -> list[CustomerChannels]:
        return await CustomerChannels.all()

    @classmethod
    async def create_channel(cls, customer: Customer, channel_data: dict) -> CustomerChannels:
        return await CustomerChannels.create(**channel_data, customer=customer)

    @classmethod
    async def update_channel(cls, channel: CustomerChannels, channel_data: dict) -> CustomerChannels:
        for key, value in channel_data.items():
            if value:
                setattr(channel, key, value)
        await channel.save()
        return channel

    @classmethod
    async def upsert_channel(cls, customer: Customer, channel_data: dict) -> CustomerChannels:
        channel, _ = await CustomerChannels.update_or_create(customer=customer, **channel_data)
        return channel

    @classmethod
    async def delete_channel(cls, channel: CustomerChannels):
        await channel.delete()
