# encoding: utf-8
# src/interfaces/consumers/benefits/union_purchase_sync.py
# created: 2025-04-05 23:31:03

from typing import TYPE_CHECKING

from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

from src.domains.benefits.messages import (
    UnionPurchaseTicketMessageContent,
    UnionPurchaseTicketSyncMessage,
)
from src.infrastructures.rabbitmq import BaseConsumer

if TYPE_CHECKING:
    from src.applications.common.commands.benefits import UnionPurchaseSyncCommandService
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class UnionPurchaseTicketSyncConsumer(BaseConsumer):

    queue_name = "benefits_union_purchase_sync"
    routing_key = "eleme.union.purchase_ticket_sync"
    exchange_name = "benefits.topic"

    def __init__(
        self,
        conn_pool: "RabbitMQConnectionPool",
        producer: "RabbitMQProducer",
        sync_service: "UnionPurchaseSyncCommandService",
    ):
        self.sync_service = sync_service
        super().__init__(conn_pool, producer)

    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息"""
        msg = UnionPurchaseTicketSyncMessage.model_validate_json(message.body)
        payload: UnionPurchaseTicketMessageContent = msg.payload
        logger.info(
            f"UnionPurchaseTicketSyncConsumer 收到消息: "
            f"ticket_id={payload.ticket_id}, purchase_id={payload.purchase_id}"
        )

        await self.sync_service.sync_purchase_ticket(
            ticket_id=payload.ticket_id,
            purchase_id=payload.purchase_id,
            op_type=payload.op_type,
            sub_type=payload.sub_type,
        )

    async def rollback(self, message: AbstractIncomingMessage, exp: Exception) -> None:
        """回滚 - 采购票据同步失败时记录错误信息，依赖重试机制"""
        logger.warning(
            "UnionPurchaseTicketSyncConsumer 回滚: 消息[{message_id}] 同步失败, " "将自动重试. 错误: {error}",
            message_id=message.message_id,
            error=str(exp),
        )
