# encoding: utf-8
# author: james(<EMAIL>)
# datetime: 2024/10/17 23:38
from typing import Optional

from pydantic import BaseModel, Field


class City(BaseModel):
    name: str
    id: int


class District(BaseModel):
    name: str
    id: int


class Movie(BaseModel):
    description: str
    director: str
    duration: int
    leading_role: str
    movie_code: str
    movie_id: int
    movie_name: str
    open_day: str
    poster: str
    trailer_list: list[str]
    type: str


class MovieList(BaseModel):
    count: int
    page_index: int
    page_size: int
    list: list[Movie]


class Cinema(BaseModel):
    address: str
    cinema_code: str
    cinema_id: int
    cinema_name: str
    city_id: int
    latitude: str
    longitude: str
    phone: str
    region_id: int


class CinemaList(BaseModel):
    count: int
    page_index: int
    page_size: int
    list: list[Cinema]


class ShowArea(BaseModel):
    area: str
    price: float
    clear_price: float
    service_fee: float


class Show(BaseModel):
    show_id: int
    show_start_time: str
    show_end_time: str
    language: str
    hall_name: str
    show_version_type: str
    price: str
    clear_price: str
    movie_id: str
    cinema_id: str
    areas: Optional[list[ShowArea]] = None


class ShowList(BaseModel):
    count: int
    page_index: int
    page_size: int
    list: list[Show]


class LockSeat(BaseModel):
    area: str
    id: str
    name: str


class OrderInfo(BaseModel):
    begin_time: str = Field(..., description="影片开始时间")  # 影片开始时间
    cinema_name: str = Field(..., description="影院名称")  # 影院名称
    create_at: str = Field(..., description="下单时间")  # 下单时间
    end_time: str = Field(..., description="影片结束时间")  # 影片结束时间
    is_refund: int = Field(..., description="是否可申请退款，1表示可申请退款，0表示不能申请退款")
    mobile: str = Field(..., description="购票者手机")  # 购票者手机
    movie_name: str = Field(..., description="影片名称")  # 影片名称
    order_code: str = Field(..., description="订单ID")  # 订单 ID
    partner_order_code: str = Field(..., description="商户订单号")  # 商户订单
    refund_charge: Optional[float] = Field(None, description="申请退款应收手续费")  # 申请退款应收手续费，可能为空
    seat_ids: str = Field(..., description="座位ID，多个座位用逗号分隔")  # 座位 ID
    seat_name: str = Field(..., description="座位名称")  # 座位名称
    show_id: str = Field(..., description="场次ID")  # 场次 ID
    status: int = Field(..., description="订单状态，0-下单未支付，1-订单已取消，2-出票中，4-已出票，6-退单")  # 订单状态
    ticket_code: Optional[str] = Field(None, description="取票码")  # 取票码，可能为空
    total_clear_price: float = Field(..., description="结算总价")  # 结算总价


class ConfirmOrderInfo(BaseModel):
    begin_time: str = Field(..., description="影片开始时间")  # 影片开始时间
    cinema_name: str = Field(..., description="影院名称")  # 影院名称
    end_time: str = Field(..., description="影片结束时间")  # 影片结束时间
    mobile: str = Field(..., description="购票人手机号")  # 购票人手机号
    movie_name: str = Field(..., description="影片名称")  # 影片名称（注意类型应为 int）
    order_code: str = Field(..., description="订单号")  # 订单号
    partner_order_code: str = Field(..., description="合作方单号")  # 合作方单号
    seat_ids: str = Field(..., description="座位ID")  # 座位ID，多个座位用逗号分隔
    seat_names: str = Field(..., description="座位名称")  # 座位名称
    show_id: str = Field(..., description="场次ID")  # 场次ID
    total_clear_price: str = Field(..., description="结算总价")  # 结算总价
