# encoding: utf-8
# tests/unit/applications/openapi/authenticator/test_aksk_authenticator.py
# created: 2025-08-02 15:40:00

"""OpenAPI AKSK 认证器测试"""

import hashlib
import hmac
import json
import time
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.applications.openapi.authenticator.openapi_aksk_authenticator import OpenAPIAKSKAuthenticator
from src.domains.customer.entities import CustomerEntity

from ..base import BaseOpenapiUnitTest
from ..factories import OpenAPITestDataFactory, OpenAPIMockFactory, OpenAPIFixtureFactory


@pytest.mark.openapi
@pytest.mark.authenticator
@pytest.mark.unit
class TestOpenAPIAKSKAuthenticator(BaseOpenapiUnitTest):
    """测试 OpenAPI AKSK 认证器"""

    @pytest.fixture
    def auth_fixtures(self):
        """认证固件"""
        return OpenAPIFixtureFactory.create_authentication_fixtures()

    @pytest.fixture
    def mock_customer_secret(self):
        """Mock 客户密钥"""
        mock_secret = MagicMock()
        mock_secret.customer = MagicMock()
        mock_secret.customer.id = 1
        mock_secret.customer.code = "test_customer"
        mock_secret.customer.name = "测试客户"
        mock_secret.customer.description = "测试客户描述"
        mock_secret.customer.app_id = "test_app"
        mock_secret.customer.tenant_id = "test_tenant"
        mock_secret.customer.balance = 10000
        mock_secret.customer.fetch_related = AsyncMock()
        mock_secret.access_secret = "test_secret_key_123456789"
        return mock_secret

    @pytest.fixture
    def aksk_authenticator(self, mock_customer_repository):
        """AKSK 认证器实例"""
        return OpenAPIAKSKAuthenticator(customer_repo=mock_customer_repository)

    @pytest.fixture
    def valid_request_params(self, auth_fixtures):
        """有效的请求参数"""
        current_timestamp = str(int(time.time()))
        return {
            "access_key": auth_fixtures["valid_access_key"],
            "timestamp": current_timestamp,
            "nonce": "test_nonce_123",
            "param1": "value1",
            "param2": "value2",
        }

    def calculate_test_signature(self, method: str, path: str, params: dict, secret_key: str) -> str:
        """计算测试签名（复制自实际实现）"""
        params_to_sign = {k: v for k, v in params.items() if k != "signature"}
        canonical_string = method.upper() + path + "".join(f"&{k}={v}" for k, v in sorted(params_to_sign.items()))
        hmac_obj = hmac.new(secret_key.encode("utf-8"), canonical_string.encode("utf-8"), hashlib.sha256)
        return hmac_obj.hexdigest()

    @pytest.mark.asyncio
    async def test_init(self, mock_customer_repository):
        """测试认证器初始化"""
        authenticator = OpenAPIAKSKAuthenticator(customer_repo=mock_customer_repository)
        assert authenticator.customer_repo == mock_customer_repository

    @pytest.mark.asyncio
    async def test_get_request_params_query_only(self, aksk_authenticator):
        """测试获取查询参数"""
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params={"key1": "value1", "key2": "value2"}
        )
        mock_request.headers = {"content-type": ""}
        
        params = await aksk_authenticator.get_request_params(mock_request)
        
        assert params == {"key1": "value1", "key2": "value2"}

    @pytest.mark.asyncio
    async def test_get_request_params_json_body(self, aksk_authenticator):
        """测试获取JSON请求体参数"""
        query_params = {"query_key": "query_value"}
        json_body = {"body_key": "body_value", "nested": {"key": "value"}}
        
        mock_request = OpenAPIMockFactory.create_mock_request(query_params=query_params)
        mock_request.headers = {"content-type": "application/json"}
        mock_request.json = AsyncMock(return_value=json_body)
        
        params = await aksk_authenticator.get_request_params(mock_request)
        
        expected_params = {
            "query_key": "query_value",
            "body_key": "body_value",
            "nested": '{"key":"value"}',  # 嵌套对象序列化为JSON字符串
        }
        assert params == expected_params

    @pytest.mark.asyncio
    async def test_get_request_params_form_body(self, aksk_authenticator):
        """测试获取表单请求体参数"""
        query_params = {"query_key": "query_value"}
        form_data = {"form_key": "form_value", "form_key2": "form_value2"}
        
        mock_request = OpenAPIMockFactory.create_mock_request(query_params=query_params)
        mock_request.headers = {"content-type": "application/x-www-form-urlencoded"}
        mock_request.form = AsyncMock(return_value=form_data)
        
        params = await aksk_authenticator.get_request_params(mock_request)
        
        expected_params = {
            "query_key": "query_value",
            "form_key": "form_value",
            "form_key2": "form_value2",
        }
        assert params == expected_params

    @pytest.mark.asyncio
    async def test_get_request_params_json_parse_error(self, aksk_authenticator):
        """测试JSON解析错误"""
        mock_request = OpenAPIMockFactory.create_mock_request()
        mock_request.headers = {"content-type": "application/json"}
        mock_request.json = AsyncMock(side_effect=Exception("Invalid JSON"))
        
        params = await aksk_authenticator.get_request_params(mock_request)
        
        assert params == {}

    @pytest.mark.asyncio
    async def test_get_request_params_form_parse_error(self, aksk_authenticator):
        """测试表单解析错误"""
        mock_request = OpenAPIMockFactory.create_mock_request()
        mock_request.headers = {"content-type": "application/x-www-form-urlencoded"}
        mock_request.form = AsyncMock(side_effect=Exception("Invalid form data"))
        
        params = await aksk_authenticator.get_request_params(mock_request)
        
        assert params == {}

    def test_calculate_signature(self, aksk_authenticator):
        """测试签名计算"""
        method = "POST"
        path = "/api/test"
        params = {
            "access_key": "test_ak",
            "timestamp": "1234567890",
            "nonce": "test_nonce",
            "param1": "value1",
        }
        secret_key = "test_secret_key"
        
        signature = aksk_authenticator.calculate_signature(method, path, params, secret_key)
        
        # 验证签名是64位十六进制字符串（SHA256）
        assert len(signature) == 64
        assert all(c in "0123456789abcdef" for c in signature)
        
        # 验证签名的一致性
        signature2 = aksk_authenticator.calculate_signature(method, path, params, secret_key)
        assert signature == signature2

    def test_calculate_signature_excludes_signature_param(self, aksk_authenticator):
        """测试签名计算排除signature参数"""
        method = "GET"
        path = "/api/test"
        params = {
            "access_key": "test_ak",
            "timestamp": "1234567890",
            "signature": "should_be_excluded",
            "param1": "value1",
        }
        secret_key = "test_secret_key"
        
        # 计算不含signature参数的签名
        params_without_sig = {k: v for k, v in params.items() if k != "signature"}
        expected_signature = aksk_authenticator.calculate_signature(method, path, params_without_sig, secret_key)
        
        # 计算含signature参数的签名（应该自动排除）
        actual_signature = aksk_authenticator.calculate_signature(method, path, params, secret_key)
        
        assert actual_signature == expected_signature

    @pytest.mark.asyncio
    async def test_validate_timestamp_valid(self, aksk_authenticator):
        """测试有效时间戳验证"""
        current_timestamp = str(int(time.time()))
        
        is_valid = await aksk_authenticator.validate_timestamp(current_timestamp)
        
        assert is_valid is True

    @pytest.mark.asyncio
    async def test_validate_timestamp_within_window(self, aksk_authenticator):
        """测试时间窗口内的时间戳"""
        # 2分钟前的时间戳（在5分钟窗口内）
        past_timestamp = str(int(time.time()) - 120)
        
        is_valid = await aksk_authenticator.validate_timestamp(past_timestamp)
        
        assert is_valid is True

    @pytest.mark.asyncio
    async def test_validate_timestamp_expired(self, aksk_authenticator):
        """测试过期时间戳"""
        # 10分钟前的时间戳（超出5分钟窗口）
        expired_timestamp = str(int(time.time()) - 600)
        
        is_valid = await aksk_authenticator.validate_timestamp(expired_timestamp)
        
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_validate_timestamp_future(self, aksk_authenticator):
        """测试未来时间戳"""
        # 10分钟后的时间戳（超出5分钟窗口）
        future_timestamp = str(int(time.time()) + 600)
        
        is_valid = await aksk_authenticator.validate_timestamp(future_timestamp)
        
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_validate_timestamp_invalid_format(self, aksk_authenticator):
        """测试无效时间戳格式"""
        test_cases = [
            None,
            "",
            "invalid",
            "123.456",
            "abc123",
        ]
        
        for invalid_timestamp in test_cases:
            is_valid = await aksk_authenticator.validate_timestamp(invalid_timestamp)
            assert is_valid is False, f"Should reject invalid timestamp: {invalid_timestamp}"

    @pytest.mark.asyncio
    async def test_validate_timestamp_custom_window(self, aksk_authenticator):
        """测试自定义时间窗口"""
        # 2分钟前的时间戳
        past_timestamp = str(int(time.time()) - 120)
        
        # 1分钟窗口内应该失败
        is_valid_small = await aksk_authenticator.validate_timestamp(past_timestamp, max_diff=60)
        assert is_valid_small is False
        
        # 3分钟窗口内应该成功
        is_valid_large = await aksk_authenticator.validate_timestamp(past_timestamp, max_diff=180)
        assert is_valid_large is True

    @pytest.mark.asyncio
    async def test_authenticate_success(self, aksk_authenticator, mock_customer_repository, valid_request_params, mock_customer_secret):
        """测试成功认证"""
        # 计算正确的签名
        method = "POST"
        path = "/api/test"
        secret_key = mock_customer_secret.access_secret
        expected_signature = self.calculate_test_signature(method, path, valid_request_params, secret_key)
        
        # 创建请求
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=valid_request_params,
            headers={"X-Signature": expected_signature}
        )
        mock_request.method = method
        mock_request.url.path = path
        mock_request.headers["content-type"] = ""
        
        # 设置mock返回值
        mock_customer_repository.get_secret_by_access_key.return_value = mock_customer_secret
        
        expected_customer = CustomerEntity(
            id=1,
            code="test_customer",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app",
            tenant_id="test_tenant",
            balance=10000,
        )
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 执行认证
            success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is True
        assert customer is not None
        assert customer.id == 1
        assert customer.code == "test_customer"
        assert message == ""
        
        # 验证方法调用
        mock_customer_repository.get_secret_by_access_key.assert_called_once_with(valid_request_params["access_key"])

    @pytest.mark.asyncio
    async def test_authenticate_missing_signature_header(self, aksk_authenticator):
        """测试缺少签名头"""
        mock_request = OpenAPIMockFactory.create_mock_request(headers={})
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Missing signature"

    @pytest.mark.asyncio
    async def test_authenticate_missing_access_key(self, aksk_authenticator):
        """测试缺少access_key参数"""
        params = {"timestamp": str(int(time.time())), "nonce": "test_nonce"}
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Missing access_key"

    @pytest.mark.asyncio
    async def test_authenticate_missing_timestamp(self, aksk_authenticator, auth_fixtures):
        """测试缺少timestamp参数"""
        params = {"access_key": auth_fixtures["valid_access_key"], "nonce": "test_nonce"}
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Missing timestamp"

    @pytest.mark.asyncio
    async def test_authenticate_missing_nonce(self, aksk_authenticator, auth_fixtures):
        """测试缺少nonce参数"""
        params = {
            "access_key": auth_fixtures["valid_access_key"],
            "timestamp": str(int(time.time())),
        }
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Missing nonce"

    @pytest.mark.asyncio
    async def test_authenticate_expired_timestamp(self, aksk_authenticator, auth_fixtures):
        """测试过期时间戳"""
        expired_timestamp = str(int(time.time()) - 600)  # 10分钟前
        params = {
            "access_key": auth_fixtures["valid_access_key"],
            "timestamp": expired_timestamp,
            "nonce": "test_nonce",
        }
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Timestamp expired"

    @pytest.mark.asyncio
    async def test_authenticate_invalid_access_key(self, aksk_authenticator, mock_customer_repository, auth_fixtures):
        """测试无效access_key"""
        params = {
            "access_key": auth_fixtures["invalid_access_key"],
            "timestamp": str(int(time.time())),
            "nonce": "test_nonce",
        }
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        # 设置mock返回值 - access_key不存在
        mock_customer_repository.get_secret_by_access_key.return_value = None
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Invalid access_key"

    @pytest.mark.asyncio
    async def test_authenticate_invalid_signature(self, aksk_authenticator, mock_customer_repository, valid_request_params, mock_customer_secret):
        """测试无效签名"""
        # 使用错误的签名
        wrong_signature = "invalid_signature_123"
        
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=valid_request_params,
            headers={"X-Signature": wrong_signature}
        )
        mock_request.method = "POST"
        mock_request.url.path = "/api/test"
        mock_request.headers["content-type"] = ""
        
        # 设置mock返回值
        mock_customer_repository.get_secret_by_access_key.return_value = mock_customer_secret
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert message == "Invalid signature"

    @pytest.mark.asyncio
    async def test_authenticate_repository_exception(self, aksk_authenticator, mock_customer_repository, valid_request_params):
        """测试仓储异常"""
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=valid_request_params,
            headers={"X-Signature": "test_signature"}
        )
        mock_request.headers["content-type"] = ""
        
        # 设置mock抛出异常
        exception_message = "Database connection failed"
        mock_customer_repository.get_secret_by_access_key.side_effect = Exception(exception_message)
        
        success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert f"Authentication error: {exception_message}" in message

    @pytest.mark.asyncio
    async def test_authenticate_customer_entity_creation_exception(self, aksk_authenticator, mock_customer_repository, valid_request_params, mock_customer_secret):
        """测试客户实体创建异常"""
        # 计算正确的签名
        method = "POST"
        path = "/api/test"
        secret_key = mock_customer_secret.access_secret
        expected_signature = self.calculate_test_signature(method, path, valid_request_params, secret_key)
        
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=valid_request_params,
            headers={"X-Signature": expected_signature}
        )
        mock_request.method = method
        mock_request.url.path = path
        mock_request.headers["content-type"] = ""
        
        # 设置mock返回值
        mock_customer_repository.get_secret_by_access_key.return_value = mock_customer_secret
        
        # Mock CustomerEntity.from_secret 抛出异常
        exception_message = "Entity creation failed"
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', side_effect=Exception(exception_message)):
            success, customer, message = await aksk_authenticator.authenticate(mock_request)
        
        assert success is False
        assert customer is None
        assert f"Authentication error: {exception_message}" in message

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_authenticate_performance(self, aksk_authenticator, mock_customer_repository, valid_request_params, mock_customer_secret):
        """测试认证性能"""
        import time as time_module
        
        # 计算正确的签名
        method = "POST"
        path = "/api/test"
        secret_key = mock_customer_secret.access_secret
        expected_signature = self.calculate_test_signature(method, path, valid_request_params, secret_key)
        
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=valid_request_params,
            headers={"X-Signature": expected_signature}
        )
        mock_request.method = method
        mock_request.url.path = path
        mock_request.headers["content-type"] = ""
        
        # 设置mock返回值
        mock_customer_repository.get_secret_by_access_key.return_value = mock_customer_secret
        
        expected_customer = CustomerEntity(
            id=1,
            code="test_customer",
            name="测试客户",
            description="测试客户描述",
            app_id="test_app",
            tenant_id="test_tenant",
            balance=10000,
        )
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            start_time = time_module.time()
            
            # 执行多次认证
            for _ in range(10):
                success, customer, message = await aksk_authenticator.authenticate(mock_request)
                assert success is True
            
            end_time = time_module.time()
            
            # 验证性能 - 10次认证应该在1秒内完成
            assert (end_time - start_time) < 1.0


@pytest.mark.openapi
@pytest.mark.authenticator
@pytest.mark.integration
class TestOpenAPIAKSKAuthenticatorIntegration:
    """OpenAPI AKSK 认证器集成测试"""

    def calculate_signature(self, method: str, path: str, params: dict, secret_key: str) -> str:
        """计算签名"""
        params_to_sign = {k: v for k, v in params.items() if k != "signature"}
        canonical_string = method.upper() + path + "".join(f"&{k}={v}" for k, v in sorted(params_to_sign.items()))
        hmac_obj = hmac.new(secret_key.encode("utf-8"), canonical_string.encode("utf-8"), hashlib.sha256)
        return hmac_obj.hexdigest()

    @pytest.mark.asyncio
    async def test_full_authentication_flow(self):
        """测试完整的认证流程"""
        # 创建测试数据
        customer_data = OpenAPITestDataFactory.create_customer_data()
        secret_data = OpenAPITestDataFactory.create_customer_secret_data(customer_id=customer_data["id"])
        
        # 创建认证参数
        current_timestamp = str(int(time.time()))
        auth_params = {
            "access_key": secret_data["access_key"],
            "timestamp": current_timestamp,
            "nonce": "integration_test_nonce",
            "business_param": "test_value",
        }
        
        # 计算签名
        method = "POST"
        path = "/api/integration/test"
        signature = self.calculate_signature(method, path, auth_params, secret_data["secret_key"])
        
        # 创建mock对象
        mock_customer_repo = OpenAPIMockFactory.create_mock_customer_repository()
        
        # 创建mock secret
        mock_secret = MagicMock()
        mock_secret.customer = MagicMock(**customer_data)
        mock_secret.customer.fetch_related = AsyncMock()
        mock_secret.access_secret = secret_data["secret_key"]
        
        # 设置返回值
        mock_customer_repo.get_secret_by_access_key.return_value = mock_secret
        
        # 创建认证器
        authenticator = OpenAPIAKSKAuthenticator(customer_repo=mock_customer_repo)
        
        # 创建请求
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=auth_params,
            headers={"X-Signature": signature}
        )
        mock_request.method = method
        mock_request.url.path = path
        mock_request.headers["content-type"] = ""
        
        # Mock CustomerEntity.from_secret
        expected_customer = CustomerEntity(**customer_data)
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 执行认证
            success, customer, message = await authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is True
        assert customer is not None
        assert customer.id == customer_data["id"]
        assert customer.code == customer_data["code"]
        assert message == ""

    @pytest.mark.asyncio
    async def test_complex_request_parameters(self):
        """测试复杂请求参数的处理"""
        # 创建包含JSON和表单数据的复杂请求
        customer_data = OpenAPITestDataFactory.create_customer_data()
        secret_data = OpenAPITestDataFactory.create_customer_secret_data(customer_id=customer_data["id"])
        
        # 查询参数
        query_params = {
            "access_key": secret_data["access_key"],
            "timestamp": str(int(time.time())),
            "nonce": "complex_test_nonce",
        }
        
        # JSON请求体
        json_body = {
            "user_info": {"name": "测试用户", "age": 25},
            "preferences": ["option1", "option2"],
            "settings": {"theme": "dark", "language": "zh-CN"},
        }
        
        # 合并所有参数用于签名计算
        all_params = query_params.copy()
        all_params.update({
            "user_info": json.dumps({"name": "测试用户", "age": 25}, ensure_ascii=False, separators=(",", ":")),
            "preferences": json.dumps(["option1", "option2"], ensure_ascii=False, separators=(",", ":")),
            "settings": json.dumps({"theme": "dark", "language": "zh-CN"}, ensure_ascii=False, separators=(",", ":")),
        })
        
        # 计算签名
        method = "POST"
        path = "/api/complex"
        signature = self.calculate_signature(method, path, all_params, secret_data["secret_key"])
        
        # 创建mock对象
        mock_customer_repo = OpenAPIMockFactory.create_mock_customer_repository()
        mock_secret = MagicMock()
        mock_secret.customer = MagicMock(**customer_data)
        mock_secret.customer.fetch_related = AsyncMock()
        mock_secret.access_secret = secret_data["secret_key"]
        mock_customer_repo.get_secret_by_access_key.return_value = mock_secret
        
        # 创建认证器
        authenticator = OpenAPIAKSKAuthenticator(customer_repo=mock_customer_repo)
        
        # 创建请求
        mock_request = OpenAPIMockFactory.create_mock_request(
            query_params=query_params,
            headers={"X-Signature": signature}
        )
        mock_request.method = method
        mock_request.url.path = path
        mock_request.headers = {"content-type": "application/json", "X-Signature": signature}
        mock_request.json = AsyncMock(return_value=json_body)
        
        expected_customer = CustomerEntity(**customer_data)
        
        with patch('src.domains.customer.entities.CustomerEntity.from_secret', return_value=expected_customer):
            # 执行认证
            success, customer, message = await authenticator.authenticate(mock_request)
        
        # 验证结果
        assert success is True
        assert customer is not None
        assert message == ""

    @pytest.mark.asyncio
    async def test_signature_calculation_consistency(self):
        """测试签名计算的一致性"""
        # 准备测试数据
        method = "GET"
        path = "/api/consistency/test"
        params = {
            "access_key": "test_ak_123",
            "timestamp": "1234567890",
            "nonce": "consistency_nonce",
            "param_z": "last",
            "param_a": "first",
            "param_m": "middle",
        }
        secret_key = "test_secret_consistency"
        
        # 创建认证器
        mock_repo = OpenAPIMockFactory.create_mock_customer_repository()
        authenticator = OpenAPIAKSKAuthenticator(customer_repo=mock_repo)
        
        # 多次计算签名，验证一致性
        signatures = []
        for _ in range(10):
            signature = authenticator.calculate_signature(method, path, params, secret_key)
            signatures.append(signature)
        
        # 验证所有签名都相同
        assert len(set(signatures)) == 1, "签名计算应该保持一致"
        
        # 验证参数顺序不影响签名
        reordered_params = {
            "param_a": "first",
            "access_key": "test_ak_123",
            "param_z": "last",
            "timestamp": "1234567890",
            "param_m": "middle",
            "nonce": "consistency_nonce",
        }
        
        reordered_signature = authenticator.calculate_signature(method, path, reordered_params, secret_key)
        assert reordered_signature == signatures[0], "参数顺序不应影响签名结果"