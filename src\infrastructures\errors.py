# encoding: utf-8
# Author: yaof<PERSON> <<EMAIL>>
# core/errors.py
# created: 2024-12-02 23:22:01
# updated: 2025-05-28 09:41:33

from fastapi import HTTPException
from starlette import status

# 系统级别错误

# HTTP Code = 401
ApplicationNotFoundError = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Application not found.")
MissingParamsInHeadersError = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED, detail="Missing params in headers."
)
TenantNotFoundError = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Tenant not found.")
ApplicationNotMatchError = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Application not match.")


# 业务错误基类
class BusinessError(Exception):
    """
    A custom exception class for handling business-specific errors.

    This class allows creating custom exceptions with a specific error code and message,
    which can be used to provide more detailed information about business logic errors.

    Attributes:
        code (int): A unique error code representing the specific type of business error.
        message (str): A human-readable description of the error.
    """

    def __init__(self, code: int, message: str, extra_message: str = ""):
        self.code = code
        self.message = message
        if extra_message:
            self.message = f"{message}, {extra_message}"
        super().__init__(self.message)


# 错误类工厂函数 - 用于简洁定义错误类
def create_error(name: str, code: int, message: str):
    """创建BusinessError子类的工厂函数

    Args:
        name: 错误类名称
        code: 错误代码
        message: 错误默认消息

    Returns:
        新创建的BusinessError子类
    """

    def _init(self, extra_message: str = ""):
        BusinessError.__init__(self, code, message, extra_message)

    return type(name, (BusinessError,), {"__init__": _init})


# 通用错误 (1000-1999)
DingtalkLoginError = create_error("DingtalkLoginError", 1000, "钉钉登录异常")
ElemeUnionSDKError = create_error("ElemeUnionSDKError", 1001, "饿了么联盟SDK异常")
WifiMasterSSOError = create_error("WifiMasterSSOError", 1002, "wifiMaster SSO异常")
BenefitsProductNotFoundError = create_error("BenefitsProductNotFoundError", 1004, "权益产品不存在")
SmsSendTooFrequentError = create_error("SmsSendTooFrequentError", 1005, "短信发送频率过高，请稍后再试")
SmsVerificationCodeIncorrectError = create_error("SmsVerificationCodeIncorrectError", 1006, "短信验证码不正确")
PhoneNumberRequiredError = create_error("PhoneNumberRequiredError", 1007, "手机号不能为空")
ConfigNotFoundError = create_error("ConfigNotFoundError", 1008, "配置不存在")
DingtalkSuitTicketNotFoundError = create_error("DingtalkSuitTicketNotFoundError", 1009, "钉钉Suitkey不存在")
DingtalkUserNotFoundError = create_error("DingtalkUserNotFoundError", 1010, "钉钉用户不存在")
# AgentNotFoundError = create_error("AgentNotFoundError", 1010, "Agent错误")

# 客户相关错误 (2000-3099)
CustomerAppSecretError = create_error("CustomerAppSecretError", 2001, "客户密钥错误")
CustomerNotFoundError = create_error("CustomerNotFoundError", 2002, "客户不存在")
CustomerCodeExistsError = create_error("CustomerCodeExistsError", 2003, "客户编码已存在")
CustomerCreateError = create_error("CustomerCreateError", 2004, "创建客户失败")
CustomerUpdateError = create_error("CustomerUpdateError", 2005, "更新客户信息失败")
CustomerBenefitsProductStockEmptyError = create_error(
    "CustomerBenefitsProductStockEmptyError", 2006, "客户权益产品库存不足"
)
CustomerBalanceNotEnoughError = create_error("CustomerBalanceNotEnoughError", 2007, "客户余额不足")
CustomerSecretNotFoundError = create_error("CustomerSecretNotFoundError", 2008, "客户密钥不存在")
CustomerNotSupportSourceIdentifyError = create_error(
    "CustomerNotSupportSourceIdentifyError", 2009, "客户不支持来源标识"
)
CustomerNameEmptyError = create_error("CustomerNameEmptyError", 2010, "客户名称不能为空")
BenefitsProductAlreadyExistsError = create_error("BenefitsProductAlreadyExistsError", 2011, "客户权益产品已存在")
InvalidMobileError = create_error("InvalidMobileError", 2012, "手机号不合法")

# 权益相关错误 (3000-4099)
SupplierIdentifyExistsError = create_error("SupplierIdentifyExistsError", 3001, "供应商标识已存在")
SupplierCreateError = create_error("SupplierCreateError", 3002, "创建供应商失败")
SupplierNotFoundError = create_error("SupplierNotFoundError", 3003, "供应商不存在")
SupplierUpdateError = create_error("SupplierUpdateError", 3004, "更新供应商信息失败")
BenefitIdempotencyVerificationError = create_error(
    "BenefitIdempotencyVerificationError", 3005, "权益充值幂等性验证失败"
)
ChargeRecordNotFoundError = create_error("ChargeRecordNotFoundError", 3006, "SKU权益充值记录不存在")
ChargeOrderNotFoundError = create_error("ChargeOrderNotFoundError", 3007, "权益充值订单不存在")
SkuNotFoundError = create_error("SkuNotFoundError", 3008, "SKU不存在")
BenefitChargeRecordNotFoundError = create_error("BenefitChargeRecordNotFoundError", 3009, "权益发放记录不存在")
BenefitsNotSupportRefundError = create_error("BenefitsNotSupportRefundError", 3010, "权益不支持退款")
UnionBenefitsPurchaseNotFoundError = create_error(
    "UnionBenefitsPurchaseNotFoundError", 3011, "饿了么联盟权益列表不存在"
)
ChargeOrderNotFailedError = create_error("ChargeOrderNotFailedError", 3012, "权益充值订单状态不是失败")
BenefitsProductSkuNotFoundError = create_error("BenefitsProductSkuNotFoundError", 3013, "权益产品SKU不存在")
UserAlreadySubscribedError = create_error("UserAlreadySubscribedError", 3014, "用户已开通且今日已领取")
BenefitChargeRecordCreateError = create_error("BenefitChargeRecordCreateError", 3015, "创建权益发放记录失败")
UnionBenefitsChargeRecordInvalidError = create_error(
    "UnionBenefitsChargeRecordInvalidError", 3016, "饿了么联盟权益发放记录无效"
)
SKUNotSupportRefundError = create_error("SKUNotSupportRefundError", 3017, "SKU不支持退款")
ElemeBenefitChargeNotFoundError = create_error("ElemeBenefitChargeNotFoundError", 3018, "Bifrost 饿了么权益进件未成功")

# Passport 相关错误 (4000-5099)
PassportAppNotFoundError = create_error("PassportAppNotFoundError", 4001, "应用不存在")
PassportUserNotFoundError = create_error("PassportUserNotFoundError", 4002, "用户不存在")
PassportTenantNotFoundError = create_error("PassportTenantNotFoundError", 4003, "租户不存在")
PassportUserTenantRelationNotFoundError = create_error(
    "PassportUserTenantRelationNotFoundError", 4004, "用户与租户关系不存在"
)

# Delivery 相关错误 (5000-6099)
InvalidTokenError = create_error("InvalidTokenError", 5000, "JWT Token无效")
ChannelNotFoundError = create_error("ChannelNotFoundError", 5001, "渠道不存在")
DeliveryPageNotFoundError = create_error("DeliveryPageNotFoundError", 5002, "投放页面不存在")
SIDNotFoundError = create_error("SIDNotFoundError", 5003, "sid不存在")
ActivityH5LinkNotFoundError = create_error("ActivityH5LinkNotFoundError", 5004, "联盟活动H5链接不存在")
PageNotFoundError = create_error("PageNotFoundError", 5005, "页面不存在")

# AI Agent 相关错误 (6000-7099)
AgentNotFoundError = create_error("AgentNotFoundError", 6000, "AI Agent不存在")
AutoBenefitsApplyAlreadyAppliedError = create_error("AutoBenefitsApplyAlreadyAppliedError", 6001, "用户今日已发放")
UserNotSubscribedError = create_error("UserNotSubscribedError", 6002, "用户未订阅自动领取权益")

# 洗单任务相关错误 (7000-8099)
WashOrderExportRecordNotFoundError = create_error("WashOrderExportRecordNotFoundError", 7000, "洗单任务导出记录不存在")
WashOrderNotReadyError = create_error("WashOrderNotReadyError", 7001, "洗单任务未准备好")

# 新的错误可以直接用工厂函数一行定义
# 示例:
# PaymentFailedError = create_error("PaymentFailedError", 6001, "支付失败")
