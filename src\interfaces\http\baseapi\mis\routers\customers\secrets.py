# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/customers/secrets.py
# created: 2025-02-06 15:07:41
# updated: 2025-05-26 11:03:41

from fastapi import APIRouter

from src.domains.customer.dto import CustomerSecretCreateDTO, CustomerSecretDTO
from src.infrastructures import errors
from src.repositories.customer import CustomerRepository

from ...schemas import BaseResponse, CustomerSecretResponse

router = APIRouter(tags=["customer", "customer.secret"])


@router.get("/{customer_id}/secrets", response_model=CustomerSecretResponse)
async def get_customer_secrets(customer_id: int):
    secrets = await CustomerRepository.get_secrets_by_customer_id(customer_id)
    secrets = [await CustomerSecretDTO.from_tortoise_orm(secret) for secret in secrets]  # type: ignore
    return CustomerSecretResponse(data=secrets)  # type: ignore


@router.post("/{customer_id}/secrets", response_model=CustomerSecretResponse)
async def create_customer_secret(customer_id: int, payload: CustomerSecretCreateDTO):
    customer = await CustomerRepository.get_by_id(customer_id)
    if not customer:
        raise errors.CustomerNotFoundError
    await CustomerRepository.create_customer_secret(
        customer=customer,
        secret_name=payload.name,
    )
    secrets = await CustomerRepository.get_secrets_by_customer_id(customer_id)
    secrets = [await CustomerSecretDTO.from_tortoise_orm(secret) for secret in secrets]  # type: ignore
    return CustomerSecretResponse(data=secrets)  # type: ignore


@router.delete("/{customer_id}/secrets/{secret_id}", response_model=BaseResponse)
async def delete_customer_secret(customer_id: int, secret_id: int):
    await CustomerRepository.delete_secret_by_customer_id(customer_id, secret_id)
    return BaseResponse(message="客户密钥删除成功")
