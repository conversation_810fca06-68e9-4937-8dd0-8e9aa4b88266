# encoding: utf-8
# docs/settings-refactor/03-implementation.md
# created: 2025-08-18 18:20:00

# 配置系统实施细节

## 代码示例

### 基类实现

```python
# src/infrastructures/settings/base.py

from pathlib import Path
from typing import Tu<PERSON>, Type
from pydantic import Field
from pydantic_settings import (
    BaseSettings,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
    TomlConfigSettingsSource,
)

class BaseServiceSettings(BaseSettings):
    """基础服务配置类"""
    
    # 通用配置字段
    rabbitmq: RabbitmqSettings = Field(default_factory=RabbitmqSettings)
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    bifrost: BifrostSettings = Field(default_factory=BifrostSettings)
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        """配置源优先级管理"""
        sources = []
        
        # 1. 环境变量最高优先级
        sources.append(env_settings)
        
        # 2. .env 文件次之
        toml_file, env_file = cls.get_config_files()
        if env_file.exists():
            sources.append(dotenv_settings)
        
        # 3. TOML 文件最低优先级
        if toml_file.exists():
            sources.append(TomlConfigSettingsSource(settings_cls))
        
        return tuple(sources)
```

### 服务配置实现

```python
# deploys/baseapi/settings.py

from pathlib import Path
from pydantic import Field
from pydantic_settings import SettingsConfigDict
from src.infrastructures.settings import BaseServiceSettings

class BaseapiSettings(BaseServiceSettings):
    """Base API 服务配置"""
    
    # 服务特有配置
    fastapi: FastapiSettings = Field(default_factory=FastapiSettings)
    sms: AliyunSmsSettings = Field(default_factory=AliyunSmsSettings)
    
    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent / ".env",
        toml_file=Path(__file__).parent / ".env.toml",
        env_nested_delimiter="__",
    )

# 创建全局配置实例
config = BaseapiSettings()
```

### 模块配置实现

```python
# src/infrastructures/databases/settings.py

from pydantic import BaseModel

class DatabaseSettings(BaseModel):
    """数据库配置"""
    
    mysql_uri: str = ""
    redis_uri: str = ""
    
    # 不需要 settings_customise_sources
    # 因为这是 BaseModel，不直接从环境变量加载
```

## 配置使用

### 在应用中使用

```python
# deploys/baseapi/main.py

from .settings import config

async def main():
    # 使用配置
    database_url = config.database.mysql_uri
    redis_url = config.database.redis_uri
    
    # 初始化服务
    app = FastAPI()
    app.state.config = config
    
    # 启动服务
    uvicorn.run(app, port=config.fastapi.port)
```

### 依赖注入使用

```python
# src/containers/interface.py

from dependency_injector import containers, providers

class Container(containers.DeclarativeContainer):
    config = providers.Configuration()
    
    # 注入配置到其他服务
    database = providers.Singleton(
        Database,
        url=config.database.mysql_uri
    )

# 使用
container = Container()
container.config.from_pydantic(config)
```

## 配置文件格式

### 环境变量格式 (.env)

```bash
# 数据库配置
DATABASE__MYSQL_URI=mysql://user:password@localhost:3306/dbname
DATABASE__REDIS_URI=redis://localhost:6379

# RabbitMQ配置
RABBITMQ__URL=amqp://guest:guest@localhost:5672/
RABBITMQ__POOL_SIZE=2
RABBITMQ__PREFETCH_COUNT=10

# FastAPI配置
FASTAPI__PORT=8000
FASTAPI__WORKERS=4

# 嵌套配置使用双下划线
FASTAPI__HOSTNAME__HOSTNAME=api.example.com
FASTAPI__HOSTNAME__BASE_FE=https://example.com
```

### TOML格式 (.env.toml)

```toml
# 数据库配置
[database]
mysql_uri = "mysql://user:password@localhost:3306/dbname"
redis_uri = "redis://localhost:6379"

# RabbitMQ配置
[rabbitmq]
url = "amqp://guest:guest@localhost:5672/"
pool_size = 2
prefetch_count = 10
connection_timeout = 10

# FastAPI配置
[fastapi]
port = 8000
workers = 4

[fastapi.hostname]
hostname = "api.example.com"
base_fe = "https://example.com"

# 复杂配置示例
[[logger.sinks]]
type = "console"
level = "INFO"
colorize = true

[[logger.sinks]]
type = "file"
level = "DEBUG"
path = "logs/app.log"
rotation = "10 MB"
```

## 迁移步骤

### Step 1: 创建基类

```bash
# 创建目录
mkdir -p src/infrastructures/settings

# 创建基类文件
touch src/infrastructures/settings/base.py
touch src/infrastructures/settings/__init__.py
```

### Step 2: 修改服务配置

```python
# 原代码（重复的）
class ServiceSettings(BaseSettings):
    # 很多重复的字段定义
    # 很多重复的 settings_customise_sources
    pass

# 新代码（简洁的）
class ServiceSettings(BaseServiceSettings):
    # 只需要服务特有的字段
    pass
```

### Step 3: 更新导入

```python
# 原导入
from src.infrastructures.config import config

# 新导入（使用兼容层）
from src.infrastructures.config_compat import config

# 或直接使用新配置
from deploys.baseapi.settings import config
```

### Step 4: 验证测试

```bash
# 运行配置检查
python scripts/check_settings.py

# 运行单元测试
pytest tests/

# 启动服务测试
python deploys/baseapi/main.py
```

## 常见模式

### 单例模式

```python
class ConfigManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = ServiceSettings()
        return cls._instance
```

### 工厂模式

```python
def create_config(service_type: str):
    configs = {
        'baseapi': BaseapiSettings,
        'consumer': ConsumerSettings,
        'scheduler': SchedulerSettings,
    }
    return configs[service_type]()
```

### 观察者模式

```python
class ConfigObserver:
    def __init__(self):
        self._observers = []
        self._config = None
    
    def attach(self, observer):
        self._observers.append(observer)
    
    def notify(self):
        for observer in self._observers:
            observer.update(self._config)
    
    def set_config(self, config):
        self._config = config
        self.notify()
```

## 测试策略

### 单元测试

```python
# tests/test_settings.py

def test_config_loading():
    """测试配置加载"""
    config = BaseapiSettings()
    assert config.database.mysql_uri is not None

def test_env_override():
    """测试环境变量覆盖"""
    os.environ['DATABASE__MYSQL_URI'] = 'test_uri'
    config = BaseapiSettings()
    assert config.database.mysql_uri == 'test_uri'

def test_config_validation():
    """测试配置验证"""
    with pytest.raises(ValidationError):
        InvalidSettings()
```

### 集成测试

```python
# tests/integration/test_config_integration.py

async def test_database_connection():
    """测试数据库连接"""
    config = BaseapiSettings()
    db = Database(config.database.mysql_uri)
    await db.connect()
    assert db.is_connected

async def test_service_startup():
    """测试服务启动"""
    config = BaseapiSettings()
    app = create_app(config)
    async with TestClient(app) as client:
        response = await client.get('/health')
        assert response.status_code == 200
```

## 故障处理

### 配置加载失败

```python
try:
    config = ServiceSettings()
except ValidationError as e:
    logger.error(f"配置验证失败: {e}")
    # 使用默认配置或退出
    sys.exit(1)
except FileNotFoundError:
    logger.warning("配置文件未找到，使用默认配置")
    config = ServiceSettings()
```

### 配置热更新

```python
import watchdog

class ConfigWatcher:
    def __init__(self, config_path):
        self.config_path = config_path
        self.observer = Observer()
        
    def on_modified(self, event):
        if event.src_path == self.config_path:
            self.reload_config()
    
    def reload_config(self):
        # 重新加载配置
        new_config = ServiceSettings()
        # 通知应用更新
        app.update_config(new_config)
```

## 性能考虑

### 配置缓存

```python
from functools import lru_cache

@lru_cache(maxsize=1)
def get_config():
    return ServiceSettings()

# 清除缓存
get_config.cache_clear()
```

### 配置预加载

```python
# 应用启动时预加载所有配置
class AppStartup:
    def __init__(self):
        self.config = self.preload_config()
    
    def preload_config(self):
        config = ServiceSettings()
        # 验证所有必需配置
        self.validate_required(config)
        return config
```