# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/base/authorization.py
# created: 2024-12-12 01:37:54
# updated: 2025-05-27 01:06:11

from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger

from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from src.infrastructures.errors import ApplicationNotFoundError, MissingParamsInHeadersError
from src.interfaces.http.baseapi import Container
from src.repositories.passport import AppRepository, TenantRepository

from .schemas import Env, PassportEnv

if TYPE_CHECKING:
    from src.applications.passport.services import AuthService


async def get_env(request: Request) -> Env:
    """获取应用和租户环境"""

    app_id = request.headers.get("APPID")
    if not app_id:
        raise MissingParamsInHeadersError

    app = await AppRepository.get_by_appid(app_id)
    if not app:
        raise ApplicationNotFoundError
    app_entity = await AppEntity.from_model(app)

    tenant_id = request.headers.get("TENANTID", None)
    tenant = await TenantRepository.get_by_tenant_id(tenant_id) if tenant_id else None
    tenant_entity = await TenantEntity.from_model(tenant) if tenant else None
    return Env(app=app_entity, tenant=tenant_entity)


@inject
async def base_api_authentication(
    token: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    auth_env: Env = Depends(get_env),
    auth_service: "AuthService" = Depends(Provide[Container.applications.passport_auth_service]),
) -> UserEntity:
    try:
        user_entity = await auth_service.baseapi_authentication(
            token.credentials, auth_env.app.app_id, auth_env.tenant.tenant_id if auth_env.tenant else None
        )
        user_entity.current_app = auth_env.app
        user_entity.current_tenant = auth_env.tenant
        return user_entity
    except Exception as exp:
        logger.exception(f"Base api authentication error: {exp}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization failed.") from exp


async def get_passport_env(request: Request) -> PassportEnv:
    app_id = request.headers.get("APPID")
    tenant_id = request.headers.get("TENANTID", "")
    if not app_id:
        raise MissingParamsInHeadersError
    return PassportEnv(app_id=app_id, tenant_id=tenant_id)
