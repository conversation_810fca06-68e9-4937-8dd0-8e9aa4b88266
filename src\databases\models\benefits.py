# encoding: utf-8
# <AUTHOR> <EMAIL>
# databases/models/benefits.py
# created: 2025-03-23 23:43:14
# updated: 2025-04-18 03:08:11

from datetime import datetime
from enum import IntEnum, StrEnum
from typing import Any, Union

import nanoid
from tortoise import fields
from tortoise.models import Model

from src.utils.idalloc import id_alloc_factor

from .utils import SoftDeleteMixin


class BenefitsProductOrderStatus(IntEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2
    PROCESSING = 3
    EXCEPTION = 4
    REFUNDED = 5


class ChargeFuncEnum(StrEnum):
    ELEME_UNION = "eleme_union"
    ELEME_CITY = "biforst_eleme_city"
    ELEME_BENEFITS = "biforst_eleme"
    SUNSHINE = "shinesun"
    UNKNOWN = "unknown"


class ProductOrderStatus(StrEnum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    PROCESSING = "processing"


class BenefitsSkuChargeRecordStatus(StrEnum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    PROCESSING = "processing"
    REFUNDED = "refunded"
    UNKNOWN = "unknown"
    USED = "used"


class BenefitsSkuType(StrEnum):
    UNION_ZHICHONG = "直冲"
    HOUJIE = "后结"
    ZHICHONG = "直充"


def order_id_generate():
    return int(datetime.now().strftime("%Y%m%d") + nanoid.generate(alphabet="1234567890", size=8))


sku_code_generate = id_alloc_factor(prefix="SKU", length=10)


class BenefitsSupplier(Model, SoftDeleteMixin):  # type: ignore
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, null=False, default="", description="供应商名称")
    identify = fields.CharField(max_length=255, null=False, default="", description="供应商标识", unique=True)
    contact = fields.CharField(max_length=255, null=False, default="", description="联系人")
    phone = fields.CharField(max_length=20, null=False, default="", description="联系电话")
    balance = fields.IntField(null=False, default=0, description="余额(分)")
    extra: Any = fields.JSONField(null=True, default=None, description="额外信息")
    settled_at = fields.DatetimeField(auto_now_add=True, description="入驻时间")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "benefits_supplier"
        table_description = "权益供应商表"


class BenefitsSku(Model, SoftDeleteMixin):  # type: ignore
    id = fields.BigIntField(pk=True)
    name = fields.CharField(max_length=255, description="SKU名称", default="")
    type = fields.CharEnumField(enum_type=BenefitsSkuType, description="SKU类型", default=BenefitsSkuType.ZHICHONG)
    code = fields.CharField(max_length=14, description="SKU编码", default=sku_code_generate, unique=True)
    third_part_code = fields.CharField(max_length=255, null=False, default="", description="第三方编码")
    supplier = fields.ForeignKeyField("models.BenefitsSupplier", null=False, related_name="skus", description="供应商")  # type: ignore
    charge_func = fields.CharEnumField(
        enum_type=ChargeFuncEnum, null=False, default=ChargeFuncEnum.UNKNOWN, description="充值方法的identify"
    )
    price = fields.IntField(default=0, description="商品标价(分)")
    cost = fields.IntField(default=0, description="成本(分)")
    stock = fields.IntField(default=0, description="库存")
    detail = fields.JSONField(null=True, default=None, description="商品详情")  # type: ignore
    enabled = fields.BooleanField(default=True, description="是否启用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    skus = fields.ManyToManyField(
        "models.BenefitsProduct",
        through="benefits_product_skus",
        forward_key="product_id",
        backward_key="sku_id",
        # on_delete=fields.CASCADE,
        description="产品与SKU关联",
    )

    class Meta:
        table = "benefits_sku"
        table_description = "权益SKU表"

    class PydanticMeta:
        exclude = ("products", "products_set", "purchase_records", "benefits_sku_charge_records")

    def __str__(self) -> str:
        return f"{self.name}({self.code})"


class BenefitsSKUPurchase(Model, SoftDeleteMixin):  # type: ignore
    id = fields.IntField(pk=True)
    purchase_id = fields.CharField(max_length=255, null=False, default="", description="采购ID", unique=True)
    purchase_name = fields.CharField(max_length=255, null=False, default="", description="采购名称")
    sku: fields.ForeignKeyRelation["BenefitsSku"] = fields.ForeignKeyField(
        "models.BenefitsSku", related_name="purchase_records", description="SKU"
    )
    purchase_stock = fields.IntField(description="采购数量", default=0)
    remain_stock = fields.IntField(description="剩余数量", default=0)
    unit_price = fields.IntField(description="采购单价（分）", default=0)
    total_price = fields.IntField(description="总价（分）", default=0)
    sales_start_time = fields.DatetimeField(description="售卖开始时间", null=True, default=None)
    sales_end_time = fields.DatetimeField(description="售卖结束时间", null=True, default=None)
    detail = fields.JSONField(description="采购详情", null=True, default=None)  # type: ignore
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "benefits_sku_purchase"
        table_description = "权益SKU采购记录"


class BenefitsProductOrder(Model, SoftDeleteMixin):  # type: ignore
    order_id = fields.BigIntField(description="order_id", default=order_id_generate, unique=True, index=True)
    status = fields.IntEnumField(
        BenefitsProductOrderStatus, description="充值状态", default=BenefitsProductOrderStatus.PENDING
    )
    price = fields.IntField(description="价格(单位分)", null=False, default=0)
    account = fields.CharField(max_length=255, description="充值账户", null=False, default="")
    out_order_id = fields.CharField(max_length=255, description="外部订单 ID", null=False, default="", unique=True)

    product_id = fields.IntField(description="产品 ID", null=False, default=0)
    product_name = fields.CharField(max_length=255, description="产品名称", null=False, default="")
    product_code = fields.CharField(max_length=255, description="产品编码", null=False, default="")

    source_identify = fields.CharField(max_length=255, description="用户标识, app_id.tenant_id.user_id")
    notify_url = fields.CharField(max_length=1024, description="回调地址", null=False, default="")
    detail = fields.JSONField(description="订单详情", null=False, default={})  # type: ignore

    app = fields.ForeignKeyField(
        "models.PassportApp", description="来源应用", null=True, related_name="benefits_product_orders"
    )  # type: ignore
    tenant = fields.ForeignKeyField(
        "models.PassportTenant", description="来源租户", null=True, related_name="benefits_product_orders"
    )  # type: ignore
    op_uid = fields.CharField(max_length=255, description="操作人UID", null=False, default="")
    op_description = fields.CharField(max_length=255, description="操作人描述", null=False, default="")

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "benefits_product_order"
        table_description = "权益充值订单"

    class PydanticMeta:
        exclude = ("app", "tenant")


class BenefitsProduct(Model, SoftDeleteMixin):  # type: ignore
    id = fields.BigIntField(pk=True)
    name = fields.CharField(max_length=255, description="产品名称")
    code = fields.CharField(
        max_length=14, description="产品编码", default=id_alloc_factor(prefix="P", length=12), unique=True
    )
    # single:单一SKU, combo:组合SKU
    type = fields.CharField(max_length=10, description="产品类型", default="single")
    price = fields.IntField(description="商品标价（分）", default=0)
    sale_price = fields.IntField(description="商品售价（分）", default=0)
    description = fields.TextField(description="产品描述", null=True)
    detail: Any = fields.JSONField(description="产品详情", null=True)
    enabled = fields.BooleanField(default=True, description="是否启用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    skus = fields.ManyToManyField(
        "models.BenefitsSku",
        through="benefits_product_skus",
        forward_key="sku_id",
        backward_key="product_id",
        # on_delete=fields.CASCADE,
        description="产品与SKU关联",
    )

    class Meta:
        table = "benefits_product"
        table_description = "权益产品"

    class PydanticMeta:
        exclude = ("sku_products", "products_set", "customer_benefitss", "benefits_product_skuss")


class BenefitsProductSku(Model, SoftDeleteMixin):  # type: ignore
    id = fields.BigIntField(pk=True)
    product = fields.ForeignKeyField("models.BenefitsProduct", description="所属产品")  # type: ignore
    sku = fields.ForeignKeyField("models.BenefitsSku", description="所属SKU")  # type: ignore
    count = fields.IntField(default=1, null=False, description="数量")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "benefits_product_skus"
        table_description = "权益产品SKU关联表"


class BenefitsSkuChargeRecord(Model, SoftDeleteMixin):  # type: ignore
    id = fields.IntField(pk=True, description="主键ID")
    sku: fields.ForeignKeyRelation["BenefitsSku"] = fields.ForeignKeyField("models.BenefitsSku", description="权益SKU")
    status = fields.CharField(
        max_length=30, description="充值状态", default=BenefitsSkuChargeRecordStatus.PENDING.value
    )
    supplier_sku_code = fields.CharField(max_length=255, description="供应商SKU ID", null=True, default="")
    supplier_order_id = fields.CharField(max_length=255, description="供应商订单ID", null=True, default="")
    account = fields.CharField(max_length=255, description="充值账户", null=False, default="")
    charge_order_id = fields.CharField(max_length=255, description="充值订单ID", null=True, default=None)
    detail = fields.JSONField(description="充值详情(第三方返回明细)", null=True, default=dict())  # type: ignore
    source_identify = fields.CharField(max_length=255, description="来源标识", null=False, default="")
    app = fields.ForeignKeyField(
        "models.PassportApp", description="来源应用", null=True, related_name="benefits_sku_charge_records"
    )  # type: ignore
    tenant = fields.ForeignKeyField(
        "models.PassportTenant", description="来源租户", null=True, related_name="benefits_sku_charge_records"
    )  # type: ignore
    charged_at = fields.DatetimeField(description="充值时间", null=True, default=None)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "benefits_sku_charge_record"
        table_description = "权益SKU充值记录表"


class BenefitsTicketStatus(StrEnum):
    PENDING = "pending"
    EXPIRED = "expired"
    USED = "used"


class BenefitsTickets(Model):
    id = fields.IntField(pk=True, description="主键ID")
    ticket_code = fields.CharField(max_length=255, description="充值凭证", null=False, default="", unique=True)
    status = fields.CharField(max_length=30, description="充值状态", default=BenefitsTicketStatus.PENDING.value)
    product_code = fields.CharField(max_length=255, description="产品编码", null=False, default="")
    account = fields.CharField(max_length=255, description="充值账号", null=False, default="")
    customer_id = fields.IntField(description="客户ID", null=False, default=0)
    order_id = fields.BigIntField(description="充值订单ID", null=True, default=None)
    notify_url = fields.CharField(max_length=1024, description="回调地址", null=False, default="")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    used_at = fields.DatetimeField(description="使用时间", null=True, default=None)
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    expired_at = fields.DatetimeField(description="过期时间【创建时间+30天】", null=True, default=None)

    class Meta:
        table = "benefits_tickets"
        table_description = "权益兑换码表"
        indexes = [
            ("created_at", "used_at", "status"),  # idx_created_at_used_at_status
            ("product_code", "status"),  # idx_product_code_status
        ]
