# 监控与验收标准

## 📊 成功指标定义

### 核心成功指标 (KSI - Key Success Indicators)

#### 1. 代码质量指标

| 指标名称 | 当前值 | 目标值 | 权重 | 测量方式 | 验收标准 |
|---------|--------|--------|------|----------|----------|
| **测试覆盖率** | ~60% | ≥80% | 20% | pytest-cov | 必须达标 |
| **平均方法长度** | ~60行 | ≤20行 | 15% | 静态分析 | 必须达标 |
| **圈复杂度** | 高 | ≤10 | 15% | radon | 必须达标 |
| **技术债务标记** | 90个 | ≤10个 | 10% | grep搜索 | 必须达标 |
| **代码重复率** | 待测量 | ≤5% | 10% | 代码审查 | 建议达标 |
| **类职责数** | 5-8个 | 1-2个 | 10% | 架构审查 | 必须达标 |
| **依赖违规** | 待测量 | 0个 | 10% | 依赖分析 | 必须达标 |
| **静态分析告警** | 待测量 | 0个严重 | 10% | ruff+mypy | 必须达标 |

**代码质量评分公式**：
```python
质量得分 = Σ(指标达成率 × 权重) × 100
```

**验收标准**：
- 🎯 **优秀 (≥90分)**：所有必须达标项 + 建议达标项全部满足
- ✅ **合格 (≥80分)**：所有必须达标项满足
- ❌ **不合格 (<80分)**：存在必须达标项未满足

#### 2. 性能提升指标

| 指标名称 | 基线值 | 目标值 | 权重 | 测量方式 | 验收标准 |
|---------|--------|--------|------|----------|----------|
| **启动时间** | 待测量 | ≤30秒 | 25% | 部署日志分析 | 必须达标 |
| **任务执行时间** | 待测量 | 较基线-20% | 20% | APM监控 | 必须达标 |
| **并发处理能力** | ~2任务/分钟 | ≥6任务/分钟 | 20% | 压力测试 | 必须达标 |
| **浏览器初始化** | 15-45秒 | ≤10秒 | 15% | 计时统计 | 必须达标 |
| **代理分配时间** | 待测量 | ≤1秒 | 10% | Redis监控 | 建议达标 |
| **内存使用** | 待测量 | 较基线-30% | 10% | 系统监控 | 建议达标 |

**性能提升评分公式**：
```python
性能得分 = Σ(改善百分比 × 权重)
```

#### 3. 可维护性指标

| 指标名称 | 评估方式 | 目标值 | 权重 | 验收标准 |
|---------|----------|--------|------|----------|
| **新功能开发时间** | 实际开发统计 | 较重构前-30% | 30% | 必须达标 |
| **Bug修复时间** | 问题跟踪统计 | 较重构前-40% | 25% | 必须达标 |
| **代码理解时间** | 团队调研 | 新人≤2小时理解 | 20% | 建议达标 |
| **文档完整性** | 文档覆盖检查 | ≥90%覆盖 | 15% | 建议达标 |
| **团队协作效率** | 团队反馈 | 满意度≥85% | 10% | 建议达标 |

### 业务连续性指标

| 指标名称 | 目标值 | 监控方式 | 告警阈值 |
|---------|--------|----------|----------|
| **服务可用率** | ≥99.5% | 健康检查 | <99% |
| **功能回归数** | 0个 | 回归测试 | >0个 |
| **数据丢失事件** | 0个 | 数据完整性检查 | >0个 |
| **部署回滚次数** | ≤1次 | 部署日志 | >1次 |

## 🔍 实时监控体系

### 1. 代码质量监控仪表板

```python
# 文件: src/monitoring/quality_dashboard.py
import subprocess
import json
from pathlib import Path
from typing import Dict, Any
import xml.etree.ElementTree as ET

class QualityDashboard:
    """代码质量监控仪表板"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.metrics_cache = {}
    
    async def collect_all_metrics(self) -> Dict[str, Any]:
        """收集所有质量指标"""
        metrics = {
            'timestamp': time.time(),
            'test_coverage': await self._get_test_coverage(),
            'code_complexity': await self._get_code_complexity(),
            'technical_debt': await self._get_technical_debt(),
            'static_analysis': await self._get_static_analysis(),
            'architecture_compliance': await self._get_architecture_compliance()
        }
        
        # 计算总体质量得分
        metrics['overall_score'] = self._calculate_quality_score(metrics)
        
        return metrics
    
    async def _get_test_coverage(self) -> Dict[str, Any]:
        """获取测试覆盖率"""
        try:
            # 运行pytest获取覆盖率
            result = subprocess.run([
                'poetry', 'run', 'pytest', 
                '--cov=src', '--cov-report=xml', '--cov-report=term-missing',
                'tests/'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 解析coverage.xml
            coverage_file = self.project_root / 'coverage.xml'
            if coverage_file.exists():
                tree = ET.parse(coverage_file)
                root = tree.getroot()
                
                coverage_data = {
                    'line_rate': float(root.attrib.get('line-rate', 0)) * 100,
                    'lines_covered': int(root.attrib.get('lines-covered', 0)),
                    'lines_valid': int(root.attrib.get('lines-valid', 0)),
                    'status': 'success'
                }
                
                # 分析各模块覆盖率
                packages = {}
                for package in root.findall('.//package'):
                    name = package.attrib.get('name', '')
                    if 'growth_hacker' in name:
                        packages[name] = {
                            'line_rate': float(package.attrib.get('line-rate', 0)) * 100,
                            'lines_covered': int(package.attrib.get('lines-covered', 0)),
                            'lines_valid': int(package.attrib.get('lines-valid', 0))
                        }
                
                coverage_data['packages'] = packages
                return coverage_data
            
        except Exception as e:
            logger.error(f"获取测试覆盖率失败: {e}")
        
        return {'status': 'failed', 'error': 'Unable to collect coverage data'}
    
    async def _get_code_complexity(self) -> Dict[str, Any]:
        """获取代码复杂度"""
        try:
            # 使用radon分析复杂度
            result = subprocess.run([
                'poetry', 'run', 'radon', 'cc', 
                'src/applications/growth_hacker',
                'src/domains/growth_tasks',
                '--json'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                complexity_data = json.loads(result.stdout)
                
                # 分析高复杂度方法
                high_complexity_methods = []
                total_methods = 0
                total_complexity = 0
                
                for file_path, file_data in complexity_data.items():
                    for item in file_data:
                        if item['type'] == 'method' or item['type'] == 'function':
                            total_methods += 1
                            total_complexity += item['complexity']
                            
                            if item['complexity'] > 10:
                                high_complexity_methods.append({
                                    'file': file_path,
                                    'name': item['name'],
                                    'complexity': item['complexity'],
                                    'lineno': item['lineno']
                                })
                
                return {
                    'avg_complexity': total_complexity / total_methods if total_methods > 0 else 0,
                    'total_methods': total_methods,
                    'high_complexity_count': len(high_complexity_methods),
                    'high_complexity_methods': high_complexity_methods,
                    'status': 'success'
                }
        
        except Exception as e:
            logger.error(f"获取代码复杂度失败: {e}")
        
        return {'status': 'failed', 'error': 'Unable to collect complexity data'}
    
    async def _get_technical_debt(self) -> Dict[str, Any]:
        """获取技术债务"""
        try:
            debt_patterns = ['TODO', 'FIXME', 'HACK', 'BUG', 'XXX']
            debt_items = []
            
            for pattern in debt_patterns:
                result = subprocess.run([
                    'grep', '-r', '-n', '--include=*.py', pattern,
                    'src/applications/growth_hacker',
                    'src/domains/growth_tasks'
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if ':' in line:
                            file_path, line_info = line.split(':', 1)
                            line_no, content = line_info.split(':', 1)
                            debt_items.append({
                                'type': pattern,
                                'file': file_path,
                                'line': int(line_no),
                                'content': content.strip()
                            })
            
            return {
                'total_debt_items': len(debt_items),
                'debt_by_type': {
                    pattern: len([item for item in debt_items if item['type'] == pattern])
                    for pattern in debt_patterns
                },
                'debt_items': debt_items,
                'status': 'success'
            }
        
        except Exception as e:
            logger.error(f"获取技术债务失败: {e}")
        
        return {'status': 'failed', 'error': 'Unable to collect debt data'}
    
    def _calculate_quality_score(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """计算质量得分"""
        score_components = {}
        total_score = 0
        
        # 测试覆盖率得分 (20%)
        if metrics['test_coverage']['status'] == 'success':
            coverage_rate = metrics['test_coverage']['line_rate']
            coverage_score = min(100, max(0, coverage_rate))
            score_components['coverage'] = coverage_score * 0.2
        
        # 代码复杂度得分 (30%)
        if metrics['code_complexity']['status'] == 'success':
            avg_complexity = metrics['code_complexity']['avg_complexity']
            high_complexity_count = metrics['code_complexity']['high_complexity_count']
            
            # 复杂度得分：平均复杂度越低得分越高
            complexity_score = max(0, 100 - avg_complexity * 10)
            # 高复杂度方法惩罚
            complexity_score -= high_complexity_count * 5
            complexity_score = max(0, complexity_score)
            
            score_components['complexity'] = complexity_score * 0.3
        
        # 技术债务得分 (25%)
        if metrics['technical_debt']['status'] == 'success':
            debt_count = metrics['technical_debt']['total_debt_items']
            debt_score = max(0, 100 - debt_count * 2)  # 每个债务项-2分
            score_components['debt'] = debt_score * 0.25
        
        # 静态分析得分 (25%)
        if metrics['static_analysis']['status'] == 'success':
            error_count = metrics['static_analysis']['error_count']
            warning_count = metrics['static_analysis']['warning_count']
            
            analysis_score = max(0, 100 - error_count * 10 - warning_count * 2)
            score_components['analysis'] = analysis_score * 0.25
        
        total_score = sum(score_components.values())
        
        return {
            'total_score': total_score,
            'components': score_components,
            'grade': self._get_grade(total_score)
        }
    
    def _get_grade(self, score: float) -> str:
        """获取等级"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B' 
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
```

### 2. 性能监控仪表板

```python
# 文件: src/monitoring/performance_dashboard.py
import asyncio
import time
from typing import Dict, List, Any
import psutil
from datetime import datetime, timedelta

class PerformanceDashboard:
    """性能监控仪表板"""
    
    def __init__(self, redis_manager):
        self.redis = redis_manager.client
        self.baseline_metrics = {}
        self.current_metrics = {}
    
    async def collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        metrics = {
            'timestamp': time.time(),
            'system_metrics': await self._collect_system_metrics(),
            'application_metrics': await self._collect_application_metrics(),
            'business_metrics': await self._collect_business_metrics()
        }
        
        # 计算性能改善
        metrics['performance_improvement'] = self._calculate_improvement(metrics)
        
        return metrics
    
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        return {
            'cpu_usage': psutil.cpu_percent(interval=1),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg(),
            'process_count': len(psutil.pids())
        }
    
    async def _collect_application_metrics(self) -> Dict[str, Any]:
        """收集应用指标"""
        # 从Redis获取应用指标
        app_metrics = await self.redis.hgetall("app:metrics:current")
        
        return {
            'browser_pool_size': int(app_metrics.get('browser_pool_size', 0)),
            'browser_pool_usage': float(app_metrics.get('browser_pool_usage', 0)),
            'proxy_pool_size': int(app_metrics.get('proxy_pool_size', 0)),
            'queue_length': int(app_metrics.get('queue_length', 0)),
            'avg_task_duration': float(app_metrics.get('avg_task_duration', 0)),
            'task_success_rate': float(app_metrics.get('task_success_rate', 0))
        }
    
    async def _collect_business_metrics(self) -> Dict[str, Any]:
        """收集业务指标"""
        # 获取最近1小时的业务指标
        end_time = time.time()
        start_time = end_time - 3600  # 1小时前
        
        # 从时间序列数据中统计
        business_data = await self.redis.zrangebyscore(
            "business:metrics:timeline",
            start_time,
            end_time
        )
        
        task_count = len(business_data)
        success_count = 0
        total_duration = 0
        
        for data_json in business_data:
            try:
                data = json.loads(data_json)
                if data.get('status') == 'success':
                    success_count += 1
                total_duration += data.get('duration', 0)
            except:
                continue
        
        return {
            'tasks_per_hour': task_count,
            'success_rate': success_count / task_count if task_count > 0 else 0,
            'avg_task_duration': total_duration / task_count if task_count > 0 else 0,
            'throughput': task_count / 60  # 每分钟任务数
        }
    
    def _calculate_improvement(self, current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能改善"""
        if not self.baseline_metrics:
            return {'status': 'no_baseline'}
        
        improvements = {}
        
        # 比较关键指标
        comparisons = [
            ('avg_task_duration', 'lower_is_better'),
            ('throughput', 'higher_is_better'),
            ('success_rate', 'higher_is_better'),
            ('cpu_usage', 'lower_is_better'),
            ('memory_usage', 'lower_is_better')
        ]
        
        for metric, direction in comparisons:
            baseline_value = self._get_nested_value(self.baseline_metrics, metric)
            current_value = self._get_nested_value(current_metrics, metric)
            
            if baseline_value and current_value:
                if direction == 'lower_is_better':
                    improvement = (baseline_value - current_value) / baseline_value * 100
                else:
                    improvement = (current_value - baseline_value) / baseline_value * 100
                
                improvements[metric] = {
                    'baseline': baseline_value,
                    'current': current_value,
                    'improvement_percent': improvement,
                    'status': 'improved' if improvement > 0 else 'degraded'
                }
        
        return improvements
    
    def _get_nested_value(self, data: dict, key: str) -> Any:
        """获取嵌套字典值"""
        for section in data.values():
            if isinstance(section, dict) and key in section:
                return section[key]
        return None
    
    async def set_baseline(self, metrics: Dict[str, Any]):
        """设置性能基线"""
        self.baseline_metrics = metrics
        
        # 保存到Redis
        await self.redis.set(
            "performance:baseline", 
            json.dumps(metrics),
            ex=86400 * 30  # 30天过期
        )
    
    async def load_baseline(self):
        """加载性能基线"""
        baseline_data = await self.redis.get("performance:baseline")
        if baseline_data:
            self.baseline_metrics = json.loads(baseline_data)
```

### 3. 实时告警系统

```python
# 文件: src/monitoring/alert_system.py
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Any, Callable
import asyncio

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning" 
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class AlertRule:
    """告警规则"""
    name: str
    condition: Callable[[Dict[str, Any]], bool]
    level: AlertLevel
    message_template: str
    cooldown_minutes: int = 15  # 冷却时间，防止重复告警

class AlertSystem:
    """告警系统"""
    
    def __init__(self, redis_manager):
        self.redis = redis_manager.client
        self.alert_rules: List[AlertRule] = []
        self.alert_history: Dict[str, float] = {}  # 记录最后告警时间
        self.notification_channels: List[Callable] = []
    
    def add_quality_rules(self):
        """添加代码质量相关告警规则"""
        self.alert_rules.extend([
            AlertRule(
                name="test_coverage_low",
                condition=lambda m: m.get('test_coverage', {}).get('line_rate', 100) < 80,
                level=AlertLevel.ERROR,
                message_template="测试覆盖率过低: {coverage:.1f}%，目标 ≥80%",
                cooldown_minutes=60
            ),
            AlertRule(
                name="technical_debt_high",
                condition=lambda m: m.get('technical_debt', {}).get('total_debt_items', 0) > 20,
                level=AlertLevel.WARNING,
                message_template="技术债务过高: {debt_count}个，目标 ≤10个",
                cooldown_minutes=30
            ),
            AlertRule(
                name="complexity_high",
                condition=lambda m: m.get('code_complexity', {}).get('high_complexity_count', 0) > 5,
                level=AlertLevel.WARNING,
                message_template="高复杂度方法过多: {count}个，建议重构",
                cooldown_minutes=60
            )
        ])
    
    def add_performance_rules(self):
        """添加性能相关告警规则"""
        self.alert_rules.extend([
            AlertRule(
                name="task_duration_slow",
                condition=lambda m: m.get('business_metrics', {}).get('avg_task_duration', 0) > 60,
                level=AlertLevel.WARNING,
                message_template="任务执行时间过长: {duration:.1f}秒，目标 <30秒",
                cooldown_minutes=15
            ),
            AlertRule(
                name="success_rate_low",
                condition=lambda m: m.get('business_metrics', {}).get('success_rate', 1) < 0.8,
                level=AlertLevel.ERROR,
                message_template="任务成功率过低: {rate:.1%}，目标 ≥90%",
                cooldown_minutes=15
            ),
            AlertRule(
                name="cpu_usage_high",
                condition=lambda m: m.get('system_metrics', {}).get('cpu_usage', 0) > 80,
                level=AlertLevel.ERROR,
                message_template="CPU使用率过高: {usage:.1f}%，目标 <80%",
                cooldown_minutes=5
            ),
            AlertRule(
                name="memory_usage_high",
                condition=lambda m: m.get('system_metrics', {}).get('memory_usage', 0) > 85,
                level=AlertLevel.CRITICAL,
                message_template="内存使用率过高: {usage:.1f}%，目标 <85%",
                cooldown_minutes=5
            )
        ])
    
    async def check_alerts(self, metrics: Dict[str, Any]):
        """检查告警条件"""
        current_time = time.time()
        
        for rule in self.alert_rules:
            try:
                if rule.condition(metrics):
                    # 检查冷却时间
                    last_alert_time = self.alert_history.get(rule.name, 0)
                    if current_time - last_alert_time < rule.cooldown_minutes * 60:
                        continue  # 在冷却期内，跳过
                    
                    # 触发告警
                    await self._trigger_alert(rule, metrics)
                    self.alert_history[rule.name] = current_time
                    
            except Exception as e:
                logger.error(f"告警规则检查异常 {rule.name}: {e}")
    
    async def _trigger_alert(self, rule: AlertRule, metrics: Dict[str, Any]):
        """触发告警"""
        # 格式化告警消息
        message = self._format_alert_message(rule, metrics)
        
        alert_data = {
            'rule_name': rule.name,
            'level': rule.level.value,
            'message': message,
            'timestamp': time.time(),
            'metrics_snapshot': metrics
        }
        
        # 记录告警历史
        await self.redis.lpush(
            "alerts:history", 
            json.dumps(alert_data)
        )
        
        # 限制历史记录长度
        await self.redis.ltrim("alerts:history", 0, 999)
        
        # 发送通知
        for channel in self.notification_channels:
            try:
                await channel(alert_data)
            except Exception as e:
                logger.error(f"告警通知发送失败: {e}")
        
        logger.warning(f"告警触发: {rule.name} - {message}")
    
    def _format_alert_message(self, rule: AlertRule, metrics: Dict[str, Any]) -> str:
        """格式化告警消息"""
        try:
            # 提取相关指标值用于格式化
            format_values = {}
            
            if 'coverage' in rule.message_template:
                format_values['coverage'] = metrics.get('test_coverage', {}).get('line_rate', 0)
            
            if 'debt_count' in rule.message_template:
                format_values['debt_count'] = metrics.get('technical_debt', {}).get('total_debt_items', 0)
            
            if 'count' in rule.message_template:
                format_values['count'] = metrics.get('code_complexity', {}).get('high_complexity_count', 0)
            
            if 'duration' in rule.message_template:
                format_values['duration'] = metrics.get('business_metrics', {}).get('avg_task_duration', 0)
            
            if 'rate' in rule.message_template:
                format_values['rate'] = metrics.get('business_metrics', {}).get('success_rate', 0)
            
            if 'usage' in rule.message_template:
                format_values['usage'] = metrics.get('system_metrics', {}).get('cpu_usage', 0) or \
                                        metrics.get('system_metrics', {}).get('memory_usage', 0)
            
            return rule.message_template.format(**format_values)
            
        except Exception as e:
            logger.error(f"告警消息格式化失败: {e}")
            return f"{rule.name}: {rule.message_template}"
    
    def add_notification_channel(self, channel: Callable):
        """添加通知渠道"""
        self.notification_channels.append(channel)
```

## 📋 验收测试用例

### 1. 代码质量验收测试

```python
# 文件: tests/acceptance/test_code_quality_acceptance.py
import pytest
import subprocess
from pathlib import Path

class TestCodeQualityAcceptance:
    """代码质量验收测试"""
    
    @pytest.mark.acceptance
    def test_test_coverage_meets_target(self):
        """测试覆盖率达到目标"""
        result = subprocess.run([
            'poetry', 'run', 'pytest', 
            '--cov=src/applications/growth_hacker',
            '--cov=src/domains/growth_tasks', 
            '--cov-fail-under=80',
            'tests/'
        ], capture_output=True, text=True)
        
        assert result.returncode == 0, f"测试覆盖率未达到80%: {result.stdout}"
    
    @pytest.mark.acceptance
    def test_method_length_compliance(self):
        """方法长度合规性测试"""
        long_methods = []
        
        for py_file in Path('src/applications/growth_hacker').rglob('*.py'):
            if 'refactored' in str(py_file):  # 检查重构后的文件
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                in_method = False
                method_start = 0
                method_name = ""
                
                for i, line in enumerate(lines):
                    if line.strip().startswith('def '):
                        if in_method and (i - method_start) > 20:
                            long_methods.append({
                                'file': str(py_file),
                                'method': method_name,
                                'length': i - method_start
                            })
                        
                        in_method = True
                        method_start = i
                        method_name = line.split('def ')[1].split('(')[0].strip()
                    elif line.strip().startswith('class ') and in_method:
                        if (i - method_start) > 20:
                            long_methods.append({
                                'file': str(py_file),
                                'method': method_name,
                                'length': i - method_start
                            })
                        in_method = False
        
        assert len(long_methods) == 0, f"发现长方法: {long_methods}"
    
    @pytest.mark.acceptance  
    def test_technical_debt_reduction(self):
        """技术债务减少测试"""
        debt_patterns = ['TODO', 'FIXME', 'HACK']
        debt_count = 0
        
        for pattern in debt_patterns:
            result = subprocess.run([
                'grep', '-r', '-c', '--include=*.py', pattern,
                'src/applications/growth_hacker',
                'src/domains/growth_tasks'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if ':' in line:
                        count = int(line.split(':')[1])
                        debt_count += count
        
        assert debt_count <= 10, f"技术债务标记过多: {debt_count}个，目标 ≤10个"
    
    @pytest.mark.acceptance
    def test_static_analysis_clean(self):
        """静态分析无严重问题"""
        # Ruff检查
        ruff_result = subprocess.run([
            'poetry', 'run', 'ruff', 'check', 
            'src/applications/growth_hacker',
            'src/domains/growth_tasks'
        ], capture_output=True, text=True)
        
        # MyPy检查
        mypy_result = subprocess.run([
            'poetry', 'run', 'mypy',
            'src/applications/growth_hacker',
            'src/domains/growth_tasks'
        ], capture_output=True, text=True)
        
        assert ruff_result.returncode == 0, f"Ruff检查失败: {ruff_result.stdout}"
        assert mypy_result.returncode == 0, f"MyPy检查失败: {mypy_result.stdout}"
```

### 2. 性能验收测试

```python
# 文件: tests/acceptance/test_performance_acceptance.py
import pytest
import asyncio
import time
from src.applications.growth_hacker.services.refactored.task_orchestrator import TaskOrchestrator

class TestPerformanceAcceptance:
    """性能验收测试"""
    
    @pytest.mark.acceptance
    @pytest.mark.performance
    async def test_startup_time_meets_target(self, app_factory):
        """启动时间达到目标"""
        start_time = time.time()
        
        # 模拟应用启动过程
        app = await app_factory.create_application()
        await app.initialize()
        
        startup_time = time.time() - start_time
        
        assert startup_time <= 30, f"启动时间过长: {startup_time:.2f}秒，目标 ≤30秒"
    
    @pytest.mark.acceptance
    @pytest.mark.performance  
    async def test_task_execution_performance(self, task_orchestrator, sample_tasks):
        """任务执行性能测试"""
        execution_times = []
        
        for task in sample_tasks[:10]:  # 执行10个任务
            start_time = time.time()
            
            try:
                await task_orchestrator.execute_task(task)
                execution_time = time.time() - start_time
                execution_times.append(execution_time)
            except Exception as e:
                pytest.fail(f"任务执行失败: {e}")
        
        avg_execution_time = sum(execution_times) / len(execution_times)
        
        assert avg_execution_time <= 60, f"平均执行时间过长: {avg_execution_time:.2f}秒，目标 ≤60秒"
    
    @pytest.mark.acceptance
    @pytest.mark.performance
    async def test_concurrent_processing_capacity(self, task_orchestrator, sample_tasks):
        """并发处理能力测试"""
        start_time = time.time()
        
        # 并发执行多个任务
        tasks = [
            task_orchestrator.execute_task(task) 
            for task in sample_tasks[:5]
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        execution_time = time.time() - start_time
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        
        throughput = success_count / (execution_time / 60)  # 每分钟任务数
        
        assert throughput >= 3, f"并发处理能力不足: {throughput:.2f}任务/分钟，目标 ≥3任务/分钟"
```

### 3. 功能回归验收测试

```python
# 文件: tests/acceptance/test_functional_regression.py
import pytest
from src.databases.models.growth_hacker import TaskStatus

class TestFunctionalRegression:
    """功能回归验收测试"""
    
    @pytest.mark.acceptance
    @pytest.mark.regression
    async def test_task_execution_compatibility(self, task_service, sample_task):
        """任务执行兼容性测试"""
        # 验证重构后的任务执行与原有功能完全一致
        
        # 1. 任务验证功能
        valid = await task_service.check_build_task(sample_task)
        assert isinstance(valid, bool)
        
        if valid:
            # 2. 任务执行功能
            await task_service.execute_task(sample_task)
            
            # 3. 验证任务状态更新
            # 这里需要检查数据库中的任务记录
            # 确保状态、时间戳等信息正确记录
    
    @pytest.mark.acceptance
    @pytest.mark.regression
    async def test_error_handling_compatibility(self, task_service):
        """错误处理兼容性测试"""
        from src.domains.growth_tasks.exceptions import AlreadyClaimedError, RiskDetectedError
        
        # 测试各种异常情况的处理
        # 确保新的异常体系与原有行为一致
        
        # 1. 已领取异常
        with pytest.raises(AlreadyClaimedError):
            raise AlreadyClaimedError("测试")
        
        # 2. 风控检测异常
        with pytest.raises(RiskDetectedError):
            raise RiskDetectedError("测试")
    
    @pytest.mark.acceptance
    @pytest.mark.regression
    async def test_data_consistency(self, database_connection):
        """数据一致性测试"""
        # 验证重构后数据处理的一致性
        # 确保没有数据丢失或corruption
        
        # 检查关键数据表的完整性
        tasks_count = await database_connection.fetch_val("SELECT COUNT(*) FROM gh_tasks")
        profiles_count = await database_connection.fetch_val("SELECT COUNT(*) FROM gh_user_profile")
        
        assert tasks_count >= 0
        assert profiles_count >= 0
```

## 📊 验收报告模板

### 自动化验收报告生成

```python
# 文件: scripts/generate_acceptance_report.py
import json
import time
from datetime import datetime
from pathlib import Path

class AcceptanceReportGenerator:
    """验收报告生成器"""
    
    def __init__(self):
        self.report_data = {
            'generated_at': datetime.now().isoformat(),
            'project': 'Growth Hacker System Refactoring',
            'version': '1.0.0',
            'test_results': {},
            'metrics': {},
            'compliance': {}
        }
    
    async def generate_full_report(self) -> dict:
        """生成完整验收报告"""
        
        # 1. 收集测试结果
        self.report_data['test_results'] = await self._collect_test_results()
        
        # 2. 收集质量指标
        self.report_data['metrics'] = await self._collect_quality_metrics()
        
        # 3. 检查合规性
        self.report_data['compliance'] = await self._check_compliance()
        
        # 4. 计算总体评分
        self.report_data['overall_score'] = self._calculate_overall_score()
        
        # 5. 生成结论和建议
        self.report_data['conclusion'] = self._generate_conclusion()
        
        return self.report_data
    
    async def _collect_test_results(self) -> dict:
        """收集测试结果"""
        test_results = {}
        
        # 运行验收测试
        import subprocess
        
        result = subprocess.run([
            'poetry', 'run', 'pytest', 
            'tests/acceptance/',
            '-v', '--tb=short', '--json-report', '--json-report-file=test_report.json'
        ], capture_output=True, text=True)
        
        if Path('test_report.json').exists():
            with open('test_report.json', 'r') as f:
                test_data = json.load(f)
            
            test_results = {
                'total_tests': test_data['summary']['total'],
                'passed_tests': test_data['summary']['passed'],
                'failed_tests': test_data['summary']['failed'],
                'success_rate': test_data['summary']['passed'] / test_data['summary']['total'] * 100,
                'execution_time': test_data['duration'],
                'detailed_results': test_data['tests']
            }
        
        return test_results
    
    def _calculate_overall_score(self) -> dict:
        """计算总体评分"""
        scores = {}
        
        # 测试通过率得分 (40%)
        test_success_rate = self.report_data['test_results'].get('success_rate', 0)
        scores['test_score'] = test_success_rate * 0.4
        
        # 代码质量得分 (30%)
        quality_score = self.report_data['metrics'].get('overall_score', {}).get('total_score', 0)
        scores['quality_score'] = quality_score * 0.3
        
        # 性能改善得分 (20%)
        performance_improvements = self.report_data['metrics'].get('performance_improvement', {})
        avg_improvement = 0
        if performance_improvements:
            improvements = [
                imp.get('improvement_percent', 0) 
                for imp in performance_improvements.values() 
                if isinstance(imp, dict)
            ]
            avg_improvement = sum(improvements) / len(improvements) if improvements else 0
        scores['performance_score'] = min(100, max(0, avg_improvement)) * 0.2
        
        # 合规性得分 (10%)
        compliance_rate = (
            sum(1 for v in self.report_data['compliance'].values() if v.get('passed', False)) /
            len(self.report_data['compliance']) * 100
            if self.report_data['compliance'] else 0
        )
        scores['compliance_score'] = compliance_rate * 0.1
        
        total_score = sum(scores.values())
        
        return {
            'component_scores': scores,
            'total_score': total_score,
            'grade': self._get_grade(total_score),
            'status': self._get_acceptance_status(total_score)
        }
    
    def _get_grade(self, score: float) -> str:
        """获取等级"""
        if score >= 90:
            return 'A (优秀)'
        elif score >= 80:
            return 'B (良好)'
        elif score >= 70:
            return 'C (及格)'
        elif score >= 60:
            return 'D (待改进)'
        else:
            return 'F (不合格)'
    
    def _get_acceptance_status(self, score: float) -> str:
        """获取验收状态"""
        if score >= 80:
            return 'PASSED (通过验收)'
        elif score >= 70:
            return 'CONDITIONAL (有条件通过)'
        else:
            return 'FAILED (未通过验收)'
    
    def _generate_conclusion(self) -> dict:
        """生成结论和建议"""
        overall_score = self.report_data['overall_score']['total_score']
        status = self.report_data['overall_score']['status']
        
        conclusion = {
            'summary': f"重构项目整体评分: {overall_score:.1f}分，{status}",
            'achievements': [],
            'issues': [],
            'recommendations': []
        }
        
        # 分析成就
        if self.report_data['test_results']['success_rate'] >= 95:
            conclusion['achievements'].append("测试通过率优秀，功能质量有保障")
        
        if self.report_data['metrics'].get('overall_score', {}).get('total_score', 0) >= 85:
            conclusion['achievements'].append("代码质量显著提升，达到优秀水平")
        
        # 分析问题
        if self.report_data['test_results']['failed_tests'] > 0:
            conclusion['issues'].append(f"存在 {self.report_data['test_results']['failed_tests']} 个测试失败")
        
        # 生成建议
        if overall_score < 80:
            conclusion['recommendations'].append("建议继续优化关键指标，达到验收标准")
        
        return conclusion
    
    def save_report(self, filename: str = None):
        """保存报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"acceptance_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.report_data, f, indent=2, ensure_ascii=False)
        
        print(f"验收报告已保存到: {filename}")
        return filename

if __name__ == '__main__':
    import asyncio
    
    async def main():
        generator = AcceptanceReportGenerator()
        report = await generator.generate_full_report()
        generator.save_report()
        
        # 打印摘要
        print("\n" + "="*50)
        print("📋 重构验收报告摘要")
        print("="*50)
        print(f"总体评分: {report['overall_score']['total_score']:.1f}分")
        print(f"验收状态: {report['overall_score']['status']}")
        print(f"测试通过率: {report['test_results']['success_rate']:.1f}%")
        print(f"代码质量: {report['metrics'].get('overall_score', {}).get('grade', 'N/A')}")
        print(f"结论: {report['conclusion']['summary']}")
        print("="*50)
    
    asyncio.run(main())
```

---

*本监控与验收标准文档为重构项目提供了全面的质量保证体系，通过客观的指标和严格的验收流程，确保重构成果的质量和价值。*