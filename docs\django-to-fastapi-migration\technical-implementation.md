# 技术实施细节

## 🏗️ 项目结构规划

### 文件组织结构

```
apis/internal/
├── __init__.py                 # 路由注册
├── main.py                     # FastAPI应用实例
└── routers/
    ├── __init__.py
    └── benefits.py             # 权益相关API路由

domains/benefits/services/
├── product.py                  # 产品服务 (需要完善)
└── charge_strategy/
    └── implements/
        └── taopiaopiao_strategy.py  # 新增淘票票策略

domains/benefits/utils/
├── biforst_sdk/               # ✅ 已存在
├── shinesun_sdk/              # ✅ 已存在
├── taopiaopiao_sdk/           # 🔄 从apps迁移
└── eleme_union_sdk/           # 🔄 从utils/thirdpart迁移
```

## 🔧 核心组件实现

### 1. FastAPI应用配置

#### Internal API主应用 (`apis/internal/main.py`)
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from core.containers import Container
from core.responses import CommonResponse
from .routers import benefits

# 创建FastAPI应用
app = FastAPI(
    title="Internal API",
    description="内部服务API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置依赖注入
container = Container()
container.wire(modules=["apis.internal.routers.benefits"])

# 注册路由
app.include_router(benefits.router, prefix="/internal", tags=["Benefits"])

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return CommonResponse(
        code=500,
        message=f"Internal server error: {str(exc)}",
        data=None
    )
```

#### 路由模块注册 (`apis/internal/__init__.py`)
```python
from fastapi import APIRouter
from .routers import benefits

router = APIRouter()
router.include_router(benefits.router)

__all__ = ["router"]
```

### 2. API路由实现

#### Benefits路由 (`apis/internal/routers/benefits.py`)
```python
from fastapi import APIRouter, Header, Depends, HTTPException
from dependency_injector.wiring import Provide, inject
from typing import Optional

from core.containers import Container
from domains.benefits.services.product import BenefitsProductService
from core.responses import CommonResponse
from .schemas import (
    ChargeRequestPayload,
    BenefitChargeResultDTO,
    BenefitProductDTO,
    ShineSunCallbackParams
)

router = APIRouter()

@router.post("/benefit/product/{product_code}/charge")
@inject
async def charge_product(
    product_code: str,
    payload: ChargeRequestPayload,
    channel: str = Header(..., alias="Channel"),
    service: BenefitsProductService = Depends(Provide[Container.benefits_product_service])
) -> CommonResponse[BenefitChargeResultDTO]:
    """
    权益充值接口
    
    Args:
        product_code: 产品代码
        payload: 充值请求参数
        channel: 渠道标识 (Header)
        
    Returns:
        充值结果
    """
    try:
        result = await service.charge_product(product_code, payload.dict(), channel)
        return CommonResponse(data=BenefitChargeResultDTO(**result))
    except ProductNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionDeniedError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except InsufficientBalanceError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/benefit/product/{product_code}")
@inject
async def get_product_detail(
    product_code: str,
    channel: str = Header(..., alias="Channel"),
    service: BenefitsProductService = Depends(Provide[Container.benefits_product_service])
) -> CommonResponse[BenefitProductDTO]:
    """
    获取权益产品详情
    
    Args:
        product_code: 产品代码
        channel: 渠道标识 (Header)
        
    Returns:
        产品详情
    """
    try:
        product = await service.get_product_by_code(product_code, channel)
        return CommonResponse(data=BenefitProductDTO(**product))
    except ProductNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PermissionDeniedError as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.get("/benefit/callback/shinesun/order")
@inject
async def shinesun_callback(
    params: ShineSunCallbackParams = Depends(),
    service: BenefitsProductService = Depends(Provide[Container.benefits_product_service])
) -> CommonResponse[dict]:
    """
    向上网络充值回调
    
    Args:
        params: 回调参数
        
    Returns:
        处理结果
    """
    try:
        await service.handle_shinesun_callback(params.dict())
        return CommonResponse(data={"status": "success", "message": "callback processed"})
    except OrderNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Callback processing failed: {str(e)}")
```

### 3. 数据模型定义

#### API Schemas (`apis/internal/routers/schemas.py`)
```python
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class ChargeRequestPayload(BaseModel):
    """充值请求参数"""
    out_order_id: str = Field(..., description="外部订单号")
    account: str = Field(..., description="充值账号")

class BenefitChargeResultDTO(BaseModel):
    """充值响应结果"""
    order_id: str = Field(..., description="内部订单号")
    status: str = Field(..., description="充值状态")
    amount: int = Field(..., description="充值金额")
    message: Optional[str] = Field(None, description="处理消息")

class BenefitProductDTO(BaseModel):
    """产品详情响应"""
    product_code: str = Field(..., description="产品代码")
    product_name: str = Field(..., description="产品名称")
    price: int = Field(..., description="产品价格")
    description: Optional[str] = Field(None, description="产品描述")
    status: str = Field(..., description="产品状态")
    supplier: str = Field(..., description="供应商类型")

class ShineSunCallbackParams(BaseModel):
    """向上网络回调参数"""
    order_id: str = Field(..., description="订单号")
    status: int = Field(..., description="充值状态 1:成功 0:失败")
    message: Optional[str] = Field(None, description="回调消息")
    timestamp: Optional[str] = Field(None, description="回调时间")
```

### 4. 业务异常定义

#### 异常类 (`domains/benefits/exceptions.py`)
```python
class BenefitsException(Exception):
    """权益业务异常基类"""
    pass

class ProductNotFoundError(BenefitsException):
    """产品不存在异常"""
    pass

class PermissionDeniedError(BenefitsException):
    """权限拒绝异常"""
    pass

class InsufficientBalanceError(BenefitsException):
    """余额不足异常"""
    pass

class OrderNotFoundError(BenefitsException):
    """订单不存在异常"""
    pass

class UnsupportedSupplierError(BenefitsException):
    """不支持的供应商异常"""
    pass

class ChargeFailedError(BenefitsException):
    """充值失败异常"""
    pass
```

### 5. 依赖注入配置

#### 容器配置更新 (`core/containers.py`)
```python
# 在现有Container类中添加/确认以下配置

class Container(containers.DeclarativeContainer):
    # ... 现有配置 ...
    
    # 确保benefits_product_service已正确配置
    benefits_product_service = providers.Singleton(
        BenefitsProductService,
        product_repo=product_repo,
        product_order_repo=product_order_repo,
        sku_charge_record_repo=sku_charge_record_repo,
        sku_service=benefits_sku_service,
        producer=producer,
    )
    
    # 新增customer相关服务（如果还没有）
    customer_service = providers.Singleton(
        CustomerService,
        customer_repo=customer_repo,
        benefits_repo=benefits_repo,
    )
```

## 🔌 SDK集成实现

### 1. SDK迁移脚本

#### 迁移TaoPiaoPiao SDK
```bash
#!/bin/bash
# scripts/migration/migrate_taopiaopiao_sdk.sh

echo "迁移淘票票SDK..."
source_dir="apps/benefits/utils/taopiaopiao_sdk"
target_dir="domains/benefits/utils/taopiaopiao_sdk"

if [ -d "$source_dir" ]; then
    cp -r "$source_dir" "$target_dir"
    echo "淘票票SDK迁移完成: $target_dir"
else
    echo "源目录不存在: $source_dir"
fi
```

#### 迁移ElemeUnion SDK  
```bash
#!/bin/bash
# scripts/migration/migrate_eleme_union_sdk.sh

echo "迁移饿了么联盟SDK..."
source_dir="utils/thirdpart/eleme_union_sdk"
target_dir="domains/benefits/utils/eleme_union_sdk"

if [ -d "$source_dir" ]; then
    cp -r "$source_dir" "$target_dir"
    echo "饿了么联盟SDK迁移完成: $target_dir"
else
    echo "源目录不存在: $source_dir"
fi
```

### 2. 统一SDK工厂

#### SDK工厂实现 (`domains/benefits/utils/sdk_factory.py`)
```python
from typing import Protocol
from .biforst_sdk.client import BiforstBenefitsClient
from .shinesun_sdk.client import ShinesunClient
from .taopiaopiao_sdk.client import TaoPiaoPiaoClient
from .eleme_union_sdk.client import ElemeUnionClient

class SDKProtocol(Protocol):
    """SDK接口协议"""
    async def charge_account(self, account: str, amount: int, out_order_id: str) -> dict:
        """账号充值接口"""
        ...

class SDKFactory:
    """SDK工厂类"""
    
    _instances = {}
    
    @classmethod
    def get_sdk(cls, supplier_type: str) -> SDKProtocol:
        """获取SDK实例"""
        if supplier_type not in cls._instances:
            cls._instances[supplier_type] = cls._create_sdk(supplier_type)
        return cls._instances[supplier_type]
    
    @classmethod
    def _create_sdk(cls, supplier_type: str) -> SDKProtocol:
        """创建SDK实例"""
        sdk_map = {
            "biforst": BiforstBenefitsClient,
            "shinesun": ShinesunClient,
            "taopiaopiao": TaoPiaoPiaoClient,
            "eleme_union": ElemeUnionClient,
        }
        
        sdk_class = sdk_map.get(supplier_type)
        if not sdk_class:
            raise UnsupportedSupplierError(f"Unsupported supplier: {supplier_type}")
        
        return sdk_class()
```

## 🚀 部署和配置

### 1. Docker配置

#### 更新docker-compose.yml
```yaml
# 确保internal-api服务正确配置
internal-api:
  <<: *common-setup
  container_name: internal-api
  command: poetry run uvicorn apis.internal.main:app --host 0.0.0.0 --port 8000 --reload
  ports:
    - "5008:8000"
  environment:
    - APP_NAME=internal_api
    - SERVICE_TYPE=internal-api
    - PYTHONPATH=/app
  volumes:
    - ./.env.toml:/app/.env.toml:ro
    - ./logs:/app/logs
```

### 2. Nginx配置

#### 路由切换配置
```nginx
# /etc/nginx/sites-available/internal-api

upstream internal_api_backend {
    server 127.0.0.1:5008;  # FastAPI internal service
}

upstream internal_api_backup {
    server 127.0.0.1:5009;  # Django backup service (for rollback)
}

server {
    listen 80;
    server_name internal-api.yourdomain.com;
    
    # 主要路由 - FastAPI
    location /internal/benefit/ {
        proxy_pass http://internal_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 保留原始Header
        proxy_pass_request_headers on;
        proxy_set_header Channel $http_channel;
        
        # 健康检查
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
        
        # 失败时不自动切换（手动控制）
        # proxy_next_upstream off;
    }
    
    # 备用路由 - Django (用于紧急回滚)
    location /internal/benefit/backup/ {
        proxy_pass http://internal_api_backup/internal/benefit/;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://internal_api_backend/docs;
    }
}
```

### 3. 环境变量配置

#### .env.toml配置
```toml
# 确保以下配置存在
[app]
name = "internal_api"
debug = false

[database]
url = "mysql://user:password@localhost:3306/dbname"

[rabbitmq]
host = "localhost"
port = 5672
username = "guest"
password = "guest"

[redis]
url = "redis://localhost:6379/0"

[log]
level = "INFO"
dir = "logs"
```

## 🧪 测试配置

### 1. 测试环境设置

#### pytest配置 (`pytest.ini`)
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --tb=short
    -v
    --asyncio-mode=strict
    --cov=apis.internal
    --cov=domains.benefits.services
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80

markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
```

### 2. 集成测试

#### API测试 (`tests/integration/apis/test_internal_benefits.py`)
```python
import pytest
from fastapi.testclient import TestClient
from apis.internal.main import app

client = TestClient(app)

class TestInternalBenefitsAPI:
    
    def test_charge_product_success(self):
        """测试充值成功"""
        response = client.post(
            "/internal/benefit/product/TEST001/charge",
            json={
                "out_order_id": "test_order_001",
                "account": "<EMAIL>"
            },
            headers={"Channel": "test_channel"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert "order_id" in data["data"]
    
    def test_get_product_detail_success(self):
        """测试获取产品详情成功"""
        response = client.get(
            "/internal/benefit/product/TEST001",
            headers={"Channel": "test_channel"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert data["data"]["product_code"] == "TEST001"
    
    def test_shinesun_callback_success(self):
        """测试回调处理成功"""
        response = client.get(
            "/internal/benefit/callback/shinesun/order",
            params={
                "order_id": "test_order_001",
                "status": 1,
                "message": "success"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
```

## 📈 监控和日志

### 1. 结构化日志

#### 日志配置
```python
# 在每个API方法中添加结构化日志
from core.logger import logger

@router.post("/benefit/product/{product_code}/charge")
async def charge_product(...):
    logger.info(
        "开始处理权益充值请求",
        product_code=product_code,
        out_order_id=payload.out_order_id,
        channel=channel
    )
    
    try:
        result = await service.charge_product(...)
        logger.info(
            "权益充值处理成功",
            product_code=product_code,
            order_id=result["order_id"],
            status=result["status"]
        )
        return result
    except Exception as e:
        logger.error(
            "权益充值处理失败",
            product_code=product_code,
            error=str(e),
            exc_info=True
        )
        raise
```

### 2. 性能监控

#### API性能监控
```python
import time
from functools import wraps

def monitor_performance(func):
    """API性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(
                "API调用完成",
                endpoint=func.__name__,
                duration=duration,
                status="success"
            )
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                "API调用失败",
                endpoint=func.__name__,
                duration=duration,
                error=str(e),
                status="error"
            )
            raise
    return wrapper
```

这些技术实施细节为迁移项目提供了完整的实现指南，确保每个组件都能正确集成到新的FastAPI架构中。