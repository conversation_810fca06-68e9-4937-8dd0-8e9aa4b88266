# encoding: utf-8
# <AUTHOR> <EMAIL>
# src/interfaces/http/baseapi/mis/routers/delivery/orders.py
# created: 2025-02-06 12:28:29
# updated: 2025-04-10 21:06:55


from datetime import datetime, timedelta
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query

from src.interfaces.http.baseapi import Container

from .schemas import (
    DeliveryOrderListData,
    DeliveryOrderParams,
    OrderListResponse,
)

if TYPE_CHECKING:
    from src.domains.delivery.services import DeliveryOrderService

router = APIRouter(tags=["delivery", "delivery.orders"])


@router.get("/orders", response_model=OrderListResponse)
@inject
async def get_orders_list(
    params: DeliveryOrderParams = Query(...),
    delivery_order_service: "DeliveryOrderService" = Depends(Provide[Container.domains.delivery_order_service]),
):
    total, order_list = await delivery_order_service.get_orders_list(
        page=params.page or 1,
        page_size=params.page_size or 10,
        start_time=params.start_time or datetime.now() - timedelta(days=7),
        end_time=params.end_time or datetime.now(),
    )
    return OrderListResponse(data=DeliveryOrderListData(total=total, data=order_list))


# @router.get("/orders/statistics", response_model=StatisticsResponse)
# async def get_orders_statistics():
#     result = await DeliveryStatisticsService.get_orders_statistics()
#     return StatisticsResponse(data=result)

# @router.get("/orders/daily-task-aggregated", response_model=BaseResponse)
# @inject
# async def get_daily_task_orders_aggregated(
#     params: DailyTaskAggregatedParams = Query(...),
#     delivery_order_service: "DeliveryOrderService" = Depends(Provide[Container.delivery_order_service]),
# ):
#     result = await delivery_order_service.get_daily_task_complete_aggregated(date=params.date)
#     return BaseResponse(data=result)


# @router.get("/orders/daily-task-recovery-stats", response_model=BaseResponse)
# @inject
# async def get_daily_task_recovery_stats(
#     params: DailyTaskRecoveryStatsParams = Query(...),
#     delivery_order_service: "DeliveryOrderService" = Depends(Provide[Container.delivery_order_service]),
# ):
#     result = await delivery_order_service.get_order_recovery_stats_by_lifecycle(date=params.date)
#     return BaseResponse(data=result)


# @router.get("/orders/daily-task-status-by-date-range", response_model=BaseResponse)
# @inject
# async def get_daily_task_status_by_date_range(
#     params: DailyTaskStatusByDateRangeParams = Query(...),
#     delivery_order_service: "DeliveryOrderService" = Depends(Provide[Container.delivery_order_service]),
# ):
#     result = await delivery_order_service.get_daily_task_status_by_date_range(
#         start_date=params.start_date, end_date=params.end_date
#     )
#     return BaseResponse(data=result)
