
# Claude 协作规范文档

本文件定义 Claude AI 在 Hicaspian Service Backend 项目中的使用方式，结合“模块化单体架构”设计，支持服务独立部署、统一测试策略与规范化开发。语言采用中英文混合，方便团队协作与 Claude 理解。

Default Language: Chinese

---

## 🧠 项目简介 Project Overview

该项目使用 Python 3.12 + Poetry 构建，采用“共享核心 + 独立服务”的模块化单体架构。目标是实现代码整洁、逻辑清晰、部署独立、测试完备。

主要特性：

- 支持 HTTP API / 消费者 / 定时任务 独立部署
- 多服务共享统一业务核心与数据访问层
- 统一测试规范：pytest 支持单元测试与集成测试
- 项目结构清晰、文档与代码注释完善
- 强制执行文件头声明，提升可读性与审查效率

---

## 📁 项目结构 Project Structure

```
src/
├── interfaces/        # 应用入口层（接口）：API、Consumer、Scheduler
│   ├── http/
│   ├── consumers/
│   └── schedulers/
├── applications/      # 应用服务层：业务编排
├── domains/           # 领域层：业务逻辑与实体
├── repositories/      # 数据访问层：接口 + 实现
├── databases/         # 数据模型层：模型定义 + 迁移
├── infra/             # 工具与基础设施封装
tests/
├── unit/              # 单元测试
└── integration/       # 集成测试
deploys/               # 各服务部署配置（CI/CD、main.py）
```

---

## 📚 Steering Documents

项目指导文档位于 `.claude/steering/` 目录，用于指导 AI 助手理解项目结构和规范：

- **[product.md](.claude/steering/product.md)** - 产品目标、核心功能和业务规则
- **[tech.md](.claude/steering/tech.md)** - 技术栈、构建系统和常用命令
- **[structure.md](.claude/steering/structure.md)** - 项目组织结构、文件命名规范和关键文件位置

---

## 🧪 测试与运行 Run & Test

```bash
# 安装依赖
poetry install

# 运行某个服务（以 benefits_api 为例）
poetry run python deploys/benefits_api/main.py

# 运行测试（单元 + 集成）
poetry run pytest

# 查看覆盖率报告
open htmlcov/index.html
```

测试框架为 **pytest**，已在 `pyproject.toml` 配置：

- 支持 `unit/` 与 `integration/` 测试目录
- 添加了标记说明，如 `@pytest.mark.unit`、`@pytest.mark.integration`
- 启用覆盖率检查与严格标记机制

---

## 📐 技术设计与实现规范

### ✅ 设计原则

- 保证结构 **清晰、简单、干净**
- 严格拒绝过度设计与复杂依赖
- 保持各层职责边界清晰（接口层禁止直接操作 domain / db）

### ✅ 文件头声明规范

所有 `.py` 文件必须以以下格式开头：

```python
# encoding: utf-8
# utils/eleme/regions.py
# created: 2025-07-20 10:16:27
```

Claude 修改或新建文件时必须检查并自动修复文件头，确保：

- 包含 UTF-8 编码声明
- 精确记录路径
- 添加创建时间（若已有则不覆盖）

---

## 🧰 开发工具 Dev Tools

```bash
# 格式化
poetry run black .

# 静态分析
poetry run ruff .

# 类型检查
poetry run mypy .
```

其他工具：

- pytest + coverage：测试与覆盖率
- loguru：日志
- aio-pika / APScheduler：异步队列与定时任务
- dependency-injector：依赖注入

---

## ✅ Claude 权限配置 Permissions

```json
{
  "allow": {
    "edit": true,
    "run": true,
    "mcp": false,
    "gh": true
  },
  "trusted_paths": [
    "src/interfaces/",
    "src/applications/",
    "src/domains/",
    "src/repositories/",
    "src/databases/",
    "src/infra/",
    "tests/"
  ]
}
```

---

## 🧠 Claude 协作流程建议

1. 明确修改目标（路径 + 类型，如 "重构 applications/benefits/commands/"）
2. 使用 `think` 或 `ultrathink` 获取方案建议
3. 修改代码前自动检查/补充文件头信息
4. 编写/修改对应测试
5. 执行 pytest 并确认覆盖率通过
6. 生成 PR 与 checklist 自动化提交

---

## 💬 Slash 命令建议 Slash Commands

- `/fix:headers`：检查并修复所有 Python 文件头
- `/test:all`：执行 pytest + 生成覆盖率
- `/refactor:apps`：重构应用层逻辑
- `/ultrathink:design`：生成或优化架构设计建议

---

## 🚫 禁止事项 Cautions

- domains 层禁止依赖任何基础设施或协议（如 HTTP、Redis）
- interfaces 层禁止跳过 applications/ 直接操作领域逻辑或数据访问层
- 不允许生成未测试或无注释的核心代码
- 所有提交必须通过 pytest 测试，覆盖率低于阈值将被拒绝合并

---
