# encoding: utf-8
# src/containers/interface.py
# created: 2025-08-10 16:00:00

import contextlib

from dependency_injector import containers, providers

from .applications import Applications
from .consumers import Consumers
from .domains import Domains
from .infrastructures import Infrastructures
from .repositories import Repositories


class Container(containers.DeclarativeContainer):
    """主容器，统一装配所有服务层依赖"""

    config = providers.Configuration()

    repositories = providers.Container(Repositories)
    infrastructures = providers.Container(Infrastructures, config=config)
    domains = providers.Container(Domains, repositories=repositories, infrastructures=infrastructures)
    applications = providers.Container(
        Applications, config=config, domains=domains, infrastructures=infrastructures, repositories=repositories
    )
    consumers = providers.Container(
        Consumers,
        applications=applications,
        infrastructures=infrastructures,
        repositories=repositories,
        domains=domains,
    )


@contextlib.asynccontextmanager
async def create_lifespan_manager():
    """通用生命周期管理器，用于需要资源初始化和清理的服务"""
    # 初始化资源
    container = Container()

    # 这里可以根据需要添加其他初始化逻辑
    # await container.infrastructures.init_tortoise()

    yield container

    # 清理资源
    # from tortoise import Tortoise
    # await Tortoise.close_connections()
