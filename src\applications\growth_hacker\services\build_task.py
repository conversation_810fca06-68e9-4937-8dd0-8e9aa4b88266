# encoding: utf-8
# src/applications/growth_hacker/services/build_task.py
# created: 2025-08-07 10:45:20

from datetime import datetime
from typing import TYPE_CHECKING

from loguru import logger

from src.domains.activity.entities import ActivityEntity
from src.infrastructures.gateways import region_gateway
from src.repositories.eleme import ElemeUserProfileRepository
from src.utils.eleme.channels import ElemeChannelsUtils

from ..schemas import TaskMessageContent

if TYPE_CHECKING:
    from src.domains.activity.entities import ActivityEntity
    from src.domains.activity.services import ElemeActivityService
    from src.infrastructures.gateways import BifrostGateway
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway
    from src.repositories.delivery import DeliveryPageRepository


class BuildTaskService:

    def __init__(
        self,
        eleme_user_profile_repository: "ElemeUserProfileRepository",
        delivery_page_repo: "DeliveryPageRepository",
        eleme_activity_service: "ElemeActivityService",
        bifrost_gateway: "BifrostGateway",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
    ):
        self.eleme_user_repo = eleme_user_profile_repository
        self.delivery_page_repo = delivery_page_repo
        self.eleme_activity_service = eleme_activity_service
        self.bifrost_gateway = bifrost_gateway
        self.eleme_union_delivery_gateway = eleme_union_delivery_gateway

    async def get_count_by_city_lifecycle(self, city: str, lifecycle: str) -> int:
        """根据城市和标签获取饿了么用户数量"""
        count = await self.eleme_user_repo.get_count_by_city_lifecycle(city, lifecycle)
        return count

    async def _get_access_url(
        self, page_code: str, phone: str, lat: float, lng: float, extra_info: dict, eleme_channel: str = "haili"
    ) -> str:
        """获取访问URL - 使用 domains 层服务构建带认证的链接"""

        # 获取页面信息
        page_info = await self.delivery_page_repo.get_page_by_code(page_code)
        if not page_info:
            logger.warning(f"页面代码 {page_code} 不存在")
            return ""

        # 获取活动链接
        activity = await ActivityEntity.from_delivery_page(page_info)
        links = await activity.get_links_with_eleme_miniapp(
            self.bifrost_gateway, self.eleme_union_delivery_gateway, extra_info
        )

        # 构建渠道参数
        channel_params = self.eleme_activity_service.build_channel_params(
            eleme_channel=eleme_channel,
            mobile=phone,
            user_open_id=f"gh_{phone}",
            latitude=lat,
            longitude=lng,
        )
        if links.h5_promotion and links.h5_promotion.h5_url:
            return ElemeChannelsUtils.url_with_params(links.h5_promotion.h5_url, channel_params)
        return ""

    def _city_to_py(self, city: str) -> str:
        """将城市转换为拼音 - 使用 RegionGateway"""
        city_pinyin = region_gateway.city_to_pinyin(city)
        return city_pinyin or ""

    async def build_by_city_lifecycle(
        self, batch_name: str, page_code: str, city: str, lifecycle: str, offset: int = 0, limit: int = 100
    ) -> list[TaskMessageContent]:
        """根据城市和标签获取饿了么用户并构建任务"""

        tasks = []
        users = await self.eleme_user_repo.gets_by_city_lifecycle(city, lifecycle, offset, limit)  # 获取用户

        for user in users:
            last_address = await self.eleme_user_repo.get_last_address(user.buyer_phone)
            if not last_address:
                logger.warning(f"用户 {user.buyer_phone} 没有找到地址信息")
                continue

            tasks.append(
                TaskMessageContent(
                    task_id=f"{user.buyer_phone}-{datetime.now().strftime('%Y%m%d')}",
                    phone=user.buyer_phone,
                    city=self._city_to_py(user.main_city),
                    lat=last_address.receive_lat,
                    lng=last_address.receive_lng,
                    batch_name=batch_name,
                    access_url=await self._get_access_url(
                        page_code=page_code,
                        phone=user.buyer_phone,
                        lat=float(last_address.receive_lat),
                        lng=float(last_address.receive_lng),
                        extra_info={
                            "batch": batch_name,
                            "city": user.main_city,
                            "lifecycle": lifecycle,
                            "date": datetime.now().strftime("%Y-%m-%d"),
                        },
                    ),
                )
            )

        return tasks
