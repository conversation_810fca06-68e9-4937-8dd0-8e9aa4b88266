# encoding: utf-8
# <AUTHOR> <EMAIL>
# apis/base/routers/delivery.py
# created: 2024-12-12 23:00:44
# updated: 2025-03-25 00:07:50

from fastapi import APIRouter, Depends, Query
from loguru import logger

from src.databases.models.passport import ThirdPlatformEnum
from src.domains.delivery.services import DeliveryPageService
from src.domains.delivery.services.channels import ChannelsService
from src.domains.passport.dto import AuthPassportUserDTO
from src.domains.passport.services.user import UserService

from ..authorization import base_api_authentication, get_passport_env
from ..schemas import ElemeChannelUrl, ElemeChannelUrlResponse, PassportEnv

router = APIRouter(tags=["delivery"])


@router.get("/channel/shop_url/{shop_id}", description="获取外卖店铺访问链接", response_model=ElemeChannelUrlResponse)
async def get_eleme_shop_url(
    shop_id: str,
    passport_env: PassportEnv = Depends(get_passport_env),
    current_user: AuthPassportUserDTO = Depends(base_api_authentication),
):
    # dingtalk 专属使用的获取渠道版访问链接接口
    third_user = await UserService.get_third_user_by_platform(ThirdPlatformEnum.DINGTALK, current_user.uid)  # type: ignore
    logger.info(f"获取店铺访问链接: {shop_id}, 当前用户: {current_user.uid}-{current_user.nickname}-{third_user.open_id if third_user else ''}")  # type: ignore
    access_url = await DeliveryPageService.get_shop_url(shop_id, current_user.phone, third_user.open_id if third_user else "")  # type: ignore

    return ElemeChannelUrlResponse(data=ElemeChannelUrl(url=access_url))


@router.get("/channel/url/{page_code}", description="获取渠道版访问链接", response_model=ElemeChannelUrlResponse)
async def get_eleme_channel_url(
    page_code: str,
    passport_env: PassportEnv = Depends(get_passport_env),
    current_user: AuthPassportUserDTO = Depends(base_api_authentication),
    _from: str = Query("unknown", description="来源"),
    corp_id: str = Query("unknown", description="企业ID", alias="_corpId"),
):
    code_mapping = {
        "8a9d9346d0e34787b6566a3d6aad527d": "dingtalk_daily_outside",
        "92b03e459fa545b1b9191f1b1ca37b05": "dingtalk_campus_fruit",
        "c694a67738b1480fb3aed1ed33aace12": "dingtalk_campus_supermarket",
    }

    # dingtalk 专属使用的获取渠道版访问链接接口
    third_user = await UserService.get_third_user_by_platform(ThirdPlatformEnum.DINGTALK, current_user.uid)  # type: ignore
    logger.info(f"获取渠道版访问链接: {page_code}, 当前用户: {current_user.uid}-{current_user.nickname}-{third_user.open_id if third_user else ''} 来源: {_from}, corp_id: {corp_id}")  # type: ignore

    try:
        if page_code in code_mapping:
            page_code = code_mapping[page_code]
        page = await DeliveryPageService.get_page_by_code(page_code)
        extra_info = {
            "from_channel": _from,
            "corp_id": corp_id,
            "dingtalk_open_id": third_user.open_id if third_user else "",
        }
        access_url = await DeliveryPageService.get_access_url(page.code, current_user.phone, third_user.open_id if third_user else "", extra_info=extra_info)  # type: ignore
    except Exception as e:
        logger.warning(f"获取渠道版访问链接失败, error: {e}")
        logger.info(f"非钉钉专属页面, 使用通用接口获取渠道版访问链接, page_code: {page_code}")
        access_url = await ChannelsService.get_access_url_with_passport_user(page_code, current_user)

    logger.info(f"获取渠道版访问链接成功: {access_url}")
    return ElemeChannelUrlResponse(data=ElemeChannelUrl(url=access_url))
