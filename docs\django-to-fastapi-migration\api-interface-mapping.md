# API接口映射方案

## 🎯 映射策略

**URL路径完全一致，参数格式完全兼容，响应结构完全相同**

## 📋 接口清单

### 需要迁移的API (仅3个)

| 老接口 | 新实现位置 | HTTP方法 | 功能 |
|--------|------------|----------|------|
| `/internal/benefit/product/{code}/charge` | `apis/internal/routers/benefits.py` | POST | 权益充值 |
| `/internal/benefit/product/{code}` | `apis/internal/routers/benefits.py` | GET | 权益详情 |
| `/internal/benefit/callback/shinesun/order` | `apis/internal/routers/benefits.py` | GET | 回调处理 |

## 🔧 详细实现方案

### 1. 权益充值接口

#### 接口定义
```python
@router.post("/benefit/product/{product_code}/charge")
@inject
async def charge_product(
    product_code: str,
    payload: ChargeRequestPayload,
    channel: str = Header(..., alias="Channel"),
    service: BenefitsProductService = Depends(Provide["benefits_product_service"])
):
    """权益充值接口 - 完全兼容老接口"""
    result = await service.charge_product(product_code, payload.dict(), channel)
    return BenefitChargeResultDTO(**result)
```

#### 数据模型
```python
class ChargeRequestPayload(BaseModel):
    """充值请求参数"""
    out_order_id: str
    account: str

class BenefitChargeResultDTO(BaseModel):
    """充值响应结果"""
    order_id: str
    status: str
    amount: int
    message: Optional[str] = None
```

### 2. 权益详情接口

#### 接口定义
```python
@router.get("/benefit/product/{product_code}")
@inject
async def get_product_detail(
    product_code: str,
    channel: str = Header(..., alias="Channel"),
    service: BenefitsProductService = Depends(Provide["benefits_product_service"])
):
    """获取权益产品详情"""
    product = await service.get_product_by_code(product_code, channel)
    return BenefitProductDTO(**product)
```

#### 响应模型
```python
class BenefitProductDTO(BaseModel):
    """产品详情响应"""
    product_code: str
    product_name: str
    price: int
    description: Optional[str] = None
    status: str
    supplier: str
```

### 3. 回调接口

#### 接口定义
```python
@router.get("/benefit/callback/shinesun/order")
@inject
async def shinesun_callback(
    params: ShineSunCallbackParams = Depends(),
    service: BenefitsProductService = Depends(Provide["benefits_product_service"])
):
    """向上网络充值回调"""
    await service.handle_shinesun_callback(params.dict())
    return {"status": "success", "message": "callback processed"}
```

#### 参数模型
```python
class ShineSunCallbackParams(BaseModel):
    """向上网络回调参数"""
    order_id: str
    status: int
    message: Optional[str] = None
```

## 🏗️ 技术实现细节

### 路由注册结构
```python
# apis/internal/__init__.py
from fastapi import APIRouter
from .routers import benefits

router = APIRouter()
router.include_router(benefits.router, tags=["Internal Benefits"])

# apis/internal/main.py
from fastapi import FastAPI
from . import router as internal_router

app = FastAPI(title="Internal API")
app.include_router(internal_router, prefix="/internal")
```

### 依赖注入配置
```python
# 在FastAPI应用启动时配置
from core.containers import Container

container = Container()
container.wire(modules=["apis.internal.routers.benefits"])
```

### Channel Header处理
```python
async def get_customer_from_channel(channel: str = Header(..., alias="Channel")):
    """将Channel header转换为Customer对象"""
    customer_repo = container.customer_repo()
    customer = await customer_repo.get_by_channel_code(channel)
    if not customer:
        raise HTTPException(status_code=400, detail="Invalid channel")
    return customer
```

### 错误处理兼容
```python
from fastapi import HTTPException
from core.responses import CommonResponse

@router.post("/benefit/product/{product_code}/charge")
async def charge_product(...):
    try:
        result = await service.charge_product(...)
        return CommonResponse(data=result)
    except ProductNotFoundError:
        raise HTTPException(status_code=404, detail="Product not found")
    except InsufficientBalanceError:
        raise HTTPException(status_code=400, detail="Insufficient balance")
```

## 🚀 部署配置

### nginx路由配置
```nginx
# 新接口直接代理到FastAPI
location /internal/benefit/ {
    proxy_pass http://fastapi_internal_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Channel $http_channel;
}
```

### Docker服务配置
```yaml
# docker-compose.yml
internal-api:
  <<: *common-setup
  container_name: internal-api
  command: poetry run uvicorn apis.internal:app --host 0.0.0.0 --port 8000
  ports:
    - "5008:8000"
  environment:
    - APP_NAME=internal_api
```

## ✅ 测试验证

### API兼容性测试
```bash
# 1. 充值接口测试
curl -X POST "http://localhost:5008/internal/benefit/product/ELEME001/charge" \
     -H "Channel: test_channel" \
     -H "Content-Type: application/json" \
     -d '{"out_order_id": "test123", "account": "<EMAIL>"}'

# 2. 产品详情测试  
curl -X GET "http://localhost:5008/internal/benefit/product/ELEME001" \
     -H "Channel: test_channel"

# 3. 回调测试
curl -X GET "http://localhost:5008/internal/benefit/callback/shinesun/order?order_id=123&status=1"
```

### Node服务集成测试
```javascript
const axios = require('axios');

// 测试充值接口 - 无需修改现有Node代码
const chargeResult = await axios.post(
  'http://api.internal.com/internal/benefit/product/ELEME001/charge',
  {
    out_order_id: 'node_test_001',
    account: '<EMAIL>'
  },
  {
    headers: {
      'Channel': 'node_channel'
    }
  }
);

console.log('Charge result:', chargeResult.data);
```

## 📈 实施进度

### Day 1-2: 核心接口 (充值)
- [x] 创建路由文件
- [x] 定义数据模型
- [x] 实现充值逻辑
- [x] 基础测试

### Day 3: 查询接口 (详情)
- [x] 实现产品查询
- [x] Channel兼容处理
- [x] 权限检查

### Day 4: 回调接口
- [x] 实现回调处理
- [x] 参数验证
- [x] 集成测试

## 🎯 验收标准

1. **URL完全一致**: 所有接口路径与老系统100%相同
2. **参数兼容**: 请求参数和响应格式完全兼容
3. **功能对等**: Node服务调用无需任何修改
4. **性能不退化**: 响应时间不超过老系统110%
5. **零停机切换**: 通过nginx配置实现无缝切换