# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/repositories/pages.py
# created: 2025-02-06 12:37:45
# updated: 2025-04-01 09:46:01

from typing import Optional

from src.databases.models.delivery import DeliveryPage, DeliveryPageTypeEnum


class DeliveryPageRepository:

    @classmethod
    async def get_page_by_id(cls, id: int) -> DeliveryPage | None:
        return await DeliveryPage.get_or_none(id=id)

    @classmethod
    async def get_page_by_code(cls, code: str) -> DeliveryPage | None:
        return await DeliveryPage.get_or_none(code=code)

    @classmethod
    async def get_pages(cls) -> list[DeliveryPage]:
        return await DeliveryPage.all().order_by("-created_at")

    @classmethod
    async def create_page(
        cls,
        type: DeliveryPageTypeEnum,
        name: str,
        description: str,
        comment: str,
        union_active_id: str,
        union_zone_pid: str,
        code: Optional[str] = None,
        url: Optional[str] = None,
        custom_behavior: Optional[str] = None,
    ) -> DeliveryPage:
        page_data = {
            "type": type,
            "name": name,
            "description": description,
            "comment": comment,
            "union_active_id": union_active_id,
            "union_zone_pid": union_zone_pid,
            "url": url,
            "custom_behavior": custom_behavior,
        }

        # 动态添加可选参数
        if code:
            page_data["code"] = code

        return await DeliveryPage.create(**page_data)

    @classmethod
    async def update_page(cls, page: DeliveryPage, update_data: dict) -> DeliveryPage:
        for key, value in update_data.items():
            if value:
                setattr(page, key, value)
        await page.save()
        return page

    @classmethod
    async def delete_page(cls, page: DeliveryPage) -> None:
        await page.delete()
