# encoding: utf-8
# src/interfaces/growth_hacker/tasks/silence.py
# created: 2025-08-09 07:39:30

import asyncio

from ..container import lifespan
from .common import cities, common_generate_tasks


def silence_user_tasks():
    """沉默用户任务"""

    async def run():
        async with lifespan() as container:
            container.wire(modules=[__name__])

            await asyncio.gather(*[common_generate_tasks(city, "沉默", "gh_gonghui") for city in cities])

    asyncio.run(run())


if __name__ == "__main__":
    silence_user_tasks()
