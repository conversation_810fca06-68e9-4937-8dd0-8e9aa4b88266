# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/delivery/services/orders.py
# created: 2025-02-06 14:33:28
# updated: 2025-02-12 15:27:43

from datetime import datetime
from typing import TYPE_CHECKING

from src.domains.delivery.dto import UnionOrderDTO
from src.repositories.delivery import DeliveryOrderRepository

if TYPE_CHECKING:
    from motor.motor_asyncio import AsyncIOMotorDatabase


class DeliveryOrderService:
    def __init__(self, delivery_order_repo: DeliveryOrderRepository):
        self.delivery_order_repo = delivery_order_repo

    async def get_orders_list(
        self, page: int, page_size: int, start_time: datetime, end_time: datetime
    ) -> tuple[int, list[UnionOrderDTO]]:  # type: ignore
        total = await self.delivery_order_repo.get_orders_count(start_time, end_time)
        orders = await self.delivery_order_repo.get_orders_list(page, page_size, start_time, end_time)
        order_list = [await UnionOrderDTO.from_tortoise_orm(order) for order in orders]
        return total, order_list
