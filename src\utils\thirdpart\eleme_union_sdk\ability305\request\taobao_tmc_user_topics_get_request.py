from datetime import datetime
from typing import List

from src.utils.thirdpart.eleme_union_sdk.client import BaseRequest
from src.utils.thirdpart.eleme_union_sdk.util import (
    convert_basic,
    convert_basic_list,
    convert_struct,
    convert_struct_list,
)


class TaobaoTmcUserTopicsGetRequest(BaseRequest):

    def __init__(self, nick: str = None):
        """
        卖家nick
        """
        self._nick = nick

    @property
    def nick(self):
        return self._nick

    @nick.setter
    def nick(self, nick):
        if isinstance(nick, str):
            self._nick = nick
        else:
            raise TypeError("nick must be str")

    def get_api_name(self):
        return "taobao.tmc.user.topics.get"

    def to_dict(self):
        request_dict = {}
        if self._nick is not None:
            request_dict["nick"] = convert_basic(self._nick)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
