# encoding: utf-8
# src/domains/activity/schemas.py
# created: 2025-07-29 11:04:43

from typing import Optional

from pydantic import BaseModel, Field

from src.utils.eleme.dto import (
    TopAlipayPromotionDto,
    TopAppPromotionDto,
    TopH5PromotionDto,
    TopTaobaoPromotionDto,
    TopWxPromotionDto,
)


class ActivityLinks(BaseModel):
    sid: Optional[str] = Field("", description="sid")
    h5_promotion: Optional[TopH5PromotionDto] = Field(None, description="H5推广链接详情")
    taobao_promotion: Optional[TopTaobaoPromotionDto] = Field(None, description="淘宝推广链接详情")
    wx_promotion: Optional[TopWxPromotionDto] = Field(None, description="微信推广链接详情")
    alipay_promotion: Optional[TopAlipayPromotionDto] = Field(None, description="支付宝推广链接详情")
    app_promotion: Optional[TopAppPromotionDto] = Field(None, description="APP推广链接详情")
