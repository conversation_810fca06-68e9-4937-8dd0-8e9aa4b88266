# encoding: utf-8
# src/interfaces/schedulers/tasks/sync_union_messages.py
# created: 2025-04-05 16:51:45

import asyncio
import json
from typing import TYPE_CHECKING

from dependency_injector.wiring import Provide, inject
from loguru import logger

from deploys.scheduler.settings import config
from src.domains.benefits.messages import (
    UnionPurchaseTicketMessageContent,
    UnionPurchaseTicketSyncMessage,
    UnionSKUSyncMessage,
    UnionSKUSyncMessageContent,
)
from src.interfaces.schedulers import Container
from src.utils.thirdpart.eleme_union_sdk.ability132.ability132 import Ability132
from src.utils.thirdpart.eleme_union_sdk.ability132.request.taobao_tmc_messages_confirm_request import *
from src.utils.thirdpart.eleme_union_sdk.ability132.request.taobao_tmc_messages_consume_request import *
from src.utils.thirdpart.eleme_union_sdk.client import TopApiClient

if TYPE_CHECKING:
    from src.infrastructures.rabbitmq import RabbitMQProducer

VALID_TOPICS = [
    "alsc_couponpackage_PurchaseTicketSync",
    "alsc_coupon_CouponSync",
]


@inject
def get_ability():
    return Ability132(
        TopApiClient(
            appkey=config.eleme_union.app_key,
            app_sercet=config.eleme_union.app_secret,
            top_gateway_url=config.eleme_union.top_gateway_url,
        )
    )


@logger.catch
def pull_messages(quantity: int = 200) -> list[dict]:
    ability = get_ability()
    request = TaobaoTmcMessagesConsumeRequest(group_name="default", quantity=quantity)  # 上限一次只能获取200条

    response = ability.taobao_tmc_messages_consume(request)
    logger.info(f"Pulled {len(response.get('messages', []))} messages")
    return response.get("messages", [])


@logger.catch
def ack_message(message_ids: list[str]):
    ability = get_ability()
    request = TaobaoTmcMessagesConfirmRequest(group_name="default", s_message_ids=message_ids)

    response = ability.taobao_tmc_messages_confirm(request)
    logger.info(f"Acknowledged {len(message_ids)} messages")
    return response


@inject
async def process_message(
    message: dict,
    producer: "RabbitMQProducer" = Provide[Container.infrastructures.rabbitmq_producer],
):
    if message["topic"] == "alsc_couponpackage_PurchaseTicketSync":
        rmq_message = UnionPurchaseTicketSyncMessage(
            payload=UnionPurchaseTicketMessageContent.model_validate(json.loads(message.get("content", "{}")))
        )
        logger.info(f"饿了么联盟采购单同步消息: {rmq_message.payload.model_dump()}")
    if message["topic"] == "alsc_coupon_CouponSync":
        rmq_message = UnionSKUSyncMessage(
            payload=UnionSKUSyncMessageContent.model_validate(json.loads(message.get("content", "{}")))
        )
        logger.info(f"饿了么联盟权益SKU同步消息: {rmq_message.payload.model_dump()}")
    await producer.publish_message(rmq_message, "benefits.topic")


@inject
async def sync_union_messages():
    """同步饿了么联盟消息"""

    # 拉取消息
    messages = pull_messages()
    ack_message_ids = []

    # 处理消息
    for message in messages:
        if message["topic"] not in VALID_TOPICS:
            logger.warning(f"Invalid topic: {message['topic']}, message_id: {message['id']}")
        else:
            await process_message(message)
        ack_message_ids.append(message["id"])

    # 发送消息的确认信息
    if len(ack_message_ids) > 0:
        ack_message(ack_message_ids)


async def main():
    from src.containers import Container
    from src.infrastructures.settings import BaseServiceSettings
    from src.interfaces.schedulers.main import lifespan

    # 创建容器实例
    container = Container()
    container.config.from_pydantic(BaseServiceSettings())

    async with lifespan(container) as active_container:
        active_container.wire(modules=[__name__])  # 注册依赖

        await sync_union_messages()


if __name__ == "__main__":
    asyncio.run(main())
