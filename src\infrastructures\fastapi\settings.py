# encoding: utf-8
# src/infrastructures/fastapi/settings.py
# created: 2025-07-30 11:12:16

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class HostnameSettings(BaseModel):  # 改为 BaseModel，不从环境变量加载

    hostname: str = ""
    base_fe: str = ""


class FastapiSettings(BaseModel):  # 改为 BaseModel，不从环境变量加载

    port: int = Field(default=8000)
    workers: int = Field(default=2)
    hostname: HostnameSettings = Field(default_factory=HostnameSettings)
