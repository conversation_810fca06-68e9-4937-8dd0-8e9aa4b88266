# encoding: utf-8
# tests/unit/applications/passport/factories.py
# created: 2025-08-02 10:40:00

"""测试数据工厂类"""

from datetime import datetime
from typing import Optional
from unittest.mock import MagicMock

import nanoid

from src.databases.models.passport import (
    PassportApp,
    PassportTenant,
    PassportUser,
    PassportUserRelations,
)
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity

# Rebuild models to handle forward references
UserEntity.model_rebuild()
AppEntity.model_rebuild()
TenantEntity.model_rebuild()


class UserFactory:
    """用户实体和模型工厂"""

    @staticmethod
    def create_user_entity(
        uid: Optional[str] = None,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        avatar_url: Optional[str] = None,
        email: Optional[str] = None,
        current_app: Optional[AppEntity] = None,
        current_tenant: Optional[TenantEntity] = None,
    ) -> UserEntity:
        """创建用户实体"""
        return UserEntity(
            uid=uid or f"uid_{nanoid.generate(size=10)}",
            phone=phone or "13800138000",
            nickname=nickname or "测试用户",
            avatar_url=avatar_url or "https://example.com/avatar.jpg",
            email=email or "<EMAIL>",
            current_app=current_app,
            current_tenant=current_tenant,
        )

    @staticmethod
    def create_user_model(
        id: Optional[int] = None,
        uid: Optional[str] = None,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        avatar_url: Optional[str] = None,
        email: Optional[str] = None,
        wechat_openid: Optional[str] = None,
        wechat_unionid: Optional[str] = None,
        dingtalk_uid: Optional[str] = None,
    ) -> MagicMock:
        """创建用户模型 Mock"""
        mock = MagicMock(spec=PassportUser)
        mock.id = id or 1
        mock.uid = uid or f"uid_{nanoid.generate(size=10)}"
        mock.phone = phone or "13800138000"
        mock.nickname = nickname or "测试用户"
        mock.avatar_url = avatar_url or "https://example.com/avatar.jpg"
        mock.email = email or "<EMAIL>"
        mock.wechat_openid = wechat_openid
        mock.wechat_unionid = wechat_unionid
        mock.dingtalk_uid = dingtalk_uid
        mock.created_at = datetime.now()
        mock.updated_at = datetime.now()
        return mock

    @staticmethod
    def create_phone_user() -> UserEntity:
        """创建手机号用户"""
        return UserFactory.create_user_entity(
            phone="13900139000",
            nickname="手机用户",
        )

    @staticmethod
    def create_wechat_user() -> UserEntity:
        """创建微信用户"""
        return UserFactory.create_user_entity(
            phone="13700137000",
            nickname="微信用户",
        )

    @staticmethod
    def create_dingtalk_user() -> UserEntity:
        """创建钉钉用户"""
        return UserFactory.create_user_entity(
            phone="13600136000",
            nickname="钉钉用户",
        )


class AppFactory:
    """应用实体和模型工厂"""

    @staticmethod
    def create_app_entity(
        id: Optional[int] = None,
        app_id: Optional[str] = None,
        app_name: Optional[str] = None,
        app_secret: Optional[str] = None,
        wechat_app_id: Optional[str] = None,
        wechat_app_secret: Optional[str] = None,
        dingtalk_app_id: Optional[str] = None,
        dingtalk_app_secret: Optional[str] = None,
    ) -> AppEntity:
        """创建应用实体"""
        return AppEntity(
            id=id or 1,
            app_id=app_id or f"app_{nanoid.generate(size=10)}",
            app_name=app_name or "测试应用",
            app_secret=app_secret or nanoid.generate(size=32),
            wechat_app_id=wechat_app_id or f"wx_{nanoid.generate(size=16)}",
            wechat_app_secret=wechat_app_secret or nanoid.generate(size=32),
            dingtalk_app_id=dingtalk_app_id or f"dt_{nanoid.generate(size=16)}",
            dingtalk_app_secret=dingtalk_app_secret or nanoid.generate(size=32),
        )

    @staticmethod
    def create_app_model(
        id: Optional[int] = None,
        app_id: Optional[str] = None,
        name: Optional[str] = None,
        secret: Optional[str] = None,
        wechat_app_id: Optional[str] = None,
        wechat_app_secret: Optional[str] = None,
        dingtalk_app_id: Optional[str] = None,
        dingtalk_app_secret: Optional[str] = None,
    ) -> MagicMock:
        """创建应用模型 Mock"""
        mock = MagicMock(spec=PassportApp)
        mock.id = id or 1
        mock.app_id = app_id or f"app_{nanoid.generate(size=10)}"
        mock.name = name or "测试应用"
        mock.secret = secret or nanoid.generate(size=32)
        mock.wechat_app_id = wechat_app_id or f"wx_{nanoid.generate(size=16)}"
        mock.wechat_app_secret = wechat_app_secret or nanoid.generate(size=32)
        mock.dingtalk_app_id = dingtalk_app_id or f"dt_{nanoid.generate(size=16)}"
        mock.dingtalk_app_secret = dingtalk_app_secret or nanoid.generate(size=32)
        mock.created_at = datetime.now()
        mock.updated_at = datetime.now()
        return mock

    @staticmethod
    def create_internal_app() -> AppEntity:
        """创建内部应用"""
        return AppFactory.create_app_entity(
            app_id="internal_app",
            app_name="内部系统",
        )

    @staticmethod
    def create_external_app() -> AppEntity:
        """创建外部应用"""
        return AppFactory.create_app_entity(
            app_id="external_app",
            app_name="外部应用",
        )


class TenantFactory:
    """租户实体和模型工厂"""

    @staticmethod
    def create_tenant_entity(
        tenant_id: Optional[str] = None,
        tenant_name: Optional[str] = None,
        app: Optional[AppEntity] = None,
    ) -> TenantEntity:
        """创建租户实体"""
        if app is None:
            app = AppFactory.create_app_entity()

        return TenantEntity(
            tenant_id=tenant_id or f"tenant_{nanoid.generate(size=10)}",
            tenant_name=tenant_name or "测试租户",
            app=app,
        )

    @staticmethod
    def create_tenant_model(
        id: Optional[int] = None,
        tenant_id: Optional[str] = None,
        name: Optional[str] = None,
        app_id: Optional[int] = None,
    ) -> MagicMock:
        """创建租户模型 Mock"""
        mock = MagicMock(spec=PassportTenant)
        mock.id = id or 1
        mock.tenant_id = tenant_id or f"tenant_{nanoid.generate(size=10)}"
        mock.name = name or "测试租户"
        mock.app_id = app_id or 1
        mock.created_at = datetime.now()
        mock.updated_at = datetime.now()

        # Mock fetch_related 方法
        async def fetch_related(related):
            if related == "app":
                mock.app = AppFactory.create_app_model()

        mock.fetch_related = fetch_related
        return mock

    @staticmethod
    def create_active_tenant() -> TenantEntity:
        """创建活跃租户"""
        return TenantFactory.create_tenant_entity(
            tenant_name="活跃租户",
        )

    @staticmethod
    def create_inactive_tenant() -> TenantEntity:
        """创建非活跃租户"""
        return TenantFactory.create_tenant_entity(
            tenant_name="非活跃租户",
        )


class UserRelationFactory:
    """用户关系模型工厂"""

    @staticmethod
    def create_user_relation_model(
        id: Optional[int] = None,
        uid: Optional[str] = None,
        tenant_id: Optional[str] = None,
        is_active: bool = True,
    ) -> MagicMock:
        """创建用户关系模型 Mock"""
        mock = MagicMock(spec=PassportUserRelations)
        mock.id = id or 1
        mock.uid = uid or f"uid_{nanoid.generate(size=10)}"
        mock.tenant_id = tenant_id or f"tenant_{nanoid.generate(size=10)}"
        mock.is_active = is_active
        mock.created_at = datetime.now()
        mock.updated_at = datetime.now()
        return mock
