# encoding: utf-8
# <AUTHOR> <EMAIL>
# domains/passport/repositories/app.py
# created: 2025-03-24 22:23:39
# updated: 2025-03-24 22:24:04

from typing import Optional

from src.databases.models.passport import PassportApp


class AppRepository:

    @classmethod
    async def get_by_id(cls, id: int) -> Optional[PassportApp]:
        return await PassportApp.get_or_none(id=id)

    @classmethod
    async def get_by_appid(cls, app_id: str) -> Optional[PassportApp]:
        return await PassportApp.get_or_none(app_id=app_id)

    @classmethod
    async def get_by_app_secret(cls, app_secret: str) -> Optional[PassportApp]:
        return await PassportApp.get_or_none(app_secret=app_secret)
