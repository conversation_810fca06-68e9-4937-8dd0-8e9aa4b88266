# encoding: utf-8
# src/databases/models/growth_hacker/tasks.py
# created: 2025-07-25 10:30:00

from datetime import datetime, timezone
from enum import StrEnum

from tortoise import Model, fields


class TaskStatus(StrEnum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    RISK_DETECTED = "risk_detected"
    ALREADY_CLAIMED = "already_claimed"
    SUCCESS = "success"
    FAILED = "failed"


class GrowthHackerTask(Model):
    """洗单任务模型"""

    id = fields.BigIntField(primary_key=True)

    # 业务字段
    task_id = fields.CharField(max_length=255, unique=True, description="任务ID")
    phone = fields.CharField(max_length=20, index=True, description="手机号")

    # 任务信息
    city = fields.CharField(max_length=50, null=True, description="城市")
    lat = fields.CharField(max_length=50, null=True, description="纬度")
    lng = fields.CharField(max_length=50, null=True, description="经度")
    access_url = fields.TextField(null=True, description="访问URL")
    batch_name = fields.CharField(max_length=255, null=True, description="批次名称")

    # 状态信息
    status = fields.CharEnumField(TaskStatus, default=TaskStatus.PENDING, description="任务状态", index=True)
    message = fields.TextField(default="", description="状态消息")

    # 执行信息
    retry_count = fields.IntField(default=0, description="重试次数")
    execution_time = fields.FloatField(default=0.0, description="执行时间(秒)")
    page_content_html = fields.TextField(null=True, description="页面内容HTML")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    processed_at = fields.DatetimeField(null=True, description="处理时间")

    async def mark_running(self) -> None:
        """标记为运行中"""
        self.status = TaskStatus.RUNNING
        await self.save(update_fields=["status", "updated_at"])

    async def mark_success(self, message: str = "任务执行成功") -> None:
        """标记为成功"""
        self.status = TaskStatus.SUCCESS
        self.message = message
        self.processed_at = datetime.now(timezone.utc)
        await self.save(update_fields=["status", "message", "processed_at", "updated_at"])

    async def mark_failed(self, message: str) -> None:
        """标记为失败"""
        self.status = TaskStatus.FAILED
        self.message = message
        self.processed_at = datetime.now(timezone.utc)
        await self.save(update_fields=["status", "message", "processed_at", "updated_at"])

    class Meta:
        table = "gh_tasks"
        table_description = "增长任务表"
        indexes = [
            ("phone", "batch_name", "status"),  # 复合索引
        ]
