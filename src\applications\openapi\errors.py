# encoding: utf-8
# src/applications/openapi/errors.py
# created: 2025-07-29 09:06:46

from typing import Optional

from src.infrastructures.errors import BusinessError


class OpenapiBusinessError(BusinessError):

    def __init__(self, message: str, code: int = 400) -> None:
        super().__init__(code, message)


class BenefitOrderNotFoundError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "权益订单不存在"
        super().__init__(message, 404)


class BenefitProductNotFoundError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "权益产品不存在"
        super().__init__(message, 404)


class ActivityNotFoundError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "活动不存在"
        super().__init__(message, 404)


class CustomerNotSupportSourceIdentifyError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "客户不支持source_identify"
        super().__init__(message, 400)


class BenefitProductNotUseableError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "权益产品不可用"
        super().__init__(message, 400)


class AixincanAddUserError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "添加饿了么爱心餐白名单用户失败"
        super().__init__(message, 400)


class BenefitProductChargeTicketNotFoundError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "充值凭证不存在"
        super().__init__(message, 400)


class BenefitProductChargeTicketExpiredError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "充值凭证已过期"
        super().__init__(message, 400)


class BenefitProductChargeTicketUsedError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "充值凭证已使用"
        super().__init__(message, 400)


class CustomerNotFoundError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "客户不存在"
        super().__init__(message, 400)


class BenefitProductChargeTicketNotUsedError(OpenapiBusinessError):

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "充值凭证未使用"
        super().__init__(message, 400)


# Delivery 相关异常
class DeliveryCommandError(OpenapiBusinessError):
    """Delivery 命令服务基础异常"""

    def __init__(self, message: Optional[str] = None, code: int = 400) -> None:
        message = message or "Delivery命令服务错误"
        super().__init__(message, code)


class OrderSyncError(DeliveryCommandError):
    """订单同步异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "订单同步失败"
        super().__init__(message)


class OrderNotifyError(DeliveryCommandError):
    """订单通知异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "订单通知失败"
        super().__init__(message)


class DeliveryValidationError(DeliveryCommandError):
    """数据验证异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "数据验证失败"
        super().__init__(message)


class DeliveryNetworkError(DeliveryCommandError):
    """网络连接异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "网络连接失败"
        super().__init__(message, 503)


class RetryableError(DeliveryCommandError):
    """可重试的异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "操作失败，可重试"
        super().__init__(message, 503)


class NonRetryableError(DeliveryCommandError):
    """不可重试的异常"""

    def __init__(self, message: Optional[str] = None) -> None:
        message = message or "操作失败，不可重试"
        super().__init__(message)
