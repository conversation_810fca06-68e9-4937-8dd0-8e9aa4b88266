# encoding: utf-8
# src/interfaces/http/openapi/main.py
# created: 2025-07-24 01:25:58

from typing import TYPE_CHECKING

from src.containers import Container
from src.infrastructures.fastapi.application import register_app

from .routers import account, benefits, delivery, dine_in

if TYPE_CHECKING:
    from fastapi import FastAPI

# 创建容器实例（这将在 deploys/openapi/main.py 中被重新配置）
container = Container()

# 延迟创建app，避免在import时就执行
app: "FastAPI" = None  # type: ignore


def create_app(config=None) -> "FastAPI":
    """创建并配置FastAPI应用"""
    global app

    if app is None:
        # 使用新的注册方法创建 FastAPI 应用
        app = register_app(
            name="OpenAPI Service",
            version="1.0.0",
            description="OpenAPI Service for benefits and delivery",
            container=container,
            config=config or container.config(),
        )

        # 注册路由
        app.include_router(benefits.router, prefix="/benefits", tags=["benefits"])
        app.include_router(account.router, prefix="/account", tags=["account"])
        app.include_router(delivery.router, prefix="/delivery", tags=["delivery"])
        app.include_router(dine_in.router, prefix="/dine_in", tags=["dine_in"])

    return app
