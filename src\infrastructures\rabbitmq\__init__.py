# encoding: utf-8
# <AUTHOR> <EMAIL>
# core/rabbitmq/__init__.py
# created: 2025-01-26 22:02:51
# updated: 2025-04-06 16:27:25

from .conn_pool import RabbitMQConnectionPool

# from .consumer_manage import RabbitMQManager
from .consumers import BaseConsumer
from .manager import RabbitMQManager
from .messages import Message, message_creator
from .producers import RabbitMQProducer
from .settings import RabbitmqSettings


# 延迟导入以避免循环依赖
def __getattr__(name):
    if name == "IdempotentConsumer":
        from .idempotent_consumer import IdempotentConsumer

        return IdempotentConsumer
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


__all__ = [
    "RabbitMQManager",
    "RabbitMQConnectionPool",
    "Message",
    "message_creator",
    "BaseConsumer",
    "IdempotentConsumer",
    "RabbitMQProducer",
    "RabbitmqSettings",
]
