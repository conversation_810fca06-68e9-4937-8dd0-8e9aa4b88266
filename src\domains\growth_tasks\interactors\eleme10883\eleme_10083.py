import asyncio
import random
import re
import time
from typing import Any, Dict, List, Optional

from loguru import logger
from playwright.async_api import ElementHandle, Page

from src.infrastructures.exceptions.growth_hacker import (
    AlreadyClaimedError,
    ElementNotFoundError,
    InteractionError,
    PageContentError,
    RiskDetectedError,
)
from src.infrastructures.utils.decorator import retry

from .risk_detector import RiskDetector


class InteractionResult:
    """交互结果"""

    def __init__(self, success: bool, message: str = "", data: Optional[Dict[str, Any]] = None):
        self.success = success
        self.message = message
        self.data = data or {}

    def __bool__(self) -> bool:
        return self.success


class Eleme10083Interactor:
    """饿了么10083 - 天天领红包会场交互器"""

    def __init__(self, page: Page):
        self.click_button: Optional[ElementHandle] = None
        self.page = page
        self.risk_detector = RiskDetector(page)

    async def _check_already_received(self) -> bool:
        """检查是否已领取红包"""
        pattern = r"你已领取.*?个红包"
        html_content = await self.page.content()
        text_content = re.sub(r"<[^>]*>", "", html_content).strip()

        match = re.search(pattern, text_content)
        if match:
            raise AlreadyClaimedError(f"红包已领取，领取个数: {match.group(0)}")

        return True

    async def _check_hongbao_button(self) -> None:
        """检查红包领取按钮"""

        # 根据实际HTML结构优化的CSS选择器
        button_selectors = [
            # 精确匹配实际结构
            "tiga-view.pack-btn tiga-view.btn-onekey",  # 嵌套结构
            "tiga-view.btn-onekey",  # 直接匹配btn-onekey类
            ".pack-btn .btn-onekey",  # 类选择器组合
            ".btn-onekey",  # 单独的btn-onekey类
            '[class*="btn-onekey"]',  # 包含btn-onekey的类
            '[class*="pack-btn"]',  # 包含pack-btn的类
            # 通过多个类名组合查找
            'tiga-view[class*="pack-btn"][class*="c7ffbdadfce28436b1f1b03686099463"]',
            'tiga-view[class*="btn-onekey"][class*="a0ab9426cac54f89b6865b2e37b57d88"]',
        ]

        # 检查标准选择器
        for selector in button_selectors:
            button = await self.page.query_selector(selector)
            if button:
                self.click_button = button
                break

        # 如果标准选择器没找到，使用文本内容查找
        if not self.click_button:
            # 使用Playwright的文本选择器
            text_selectors = [
                'text="一键领取红包"',
                'text*="领取红包"',
                'text*="领取"',
                'button:has-text("一键领取红包")',
                'button:has-text("领取红包")',
                'button:has-text("领取")',
                '[role="button"]:has-text("一键领取红包")',
                '[role="button"]:has-text("领取红包")',
            ]

            for selector in text_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        self.click_button = button
                        break
                except Exception:
                    # 某些选择器可能不支持，继续尝试下一个
                    continue

        # 最后尝试：通过JavaScript查找包含特定文本的元素
        if not self.click_button:
            button = await self.page.evaluate(
                """
                () => {
                    const keywords = ['一键领取红包', '领取红包', '领取', '立即领取'];
                    
                    // 优先查找tiga-view结构的按钮
                    const prioritySelectors = [
                        'tiga-view[class*="pack-btn"]',
                        'tiga-view[class*="btn-onekey"]', 
                        'tiga-view[class*="btn"]',
                    ];
                    
                    // 首先尝试优先选择器
                    for (const selector of prioritySelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim() || '';
                            if (keywords.some(keyword => text.includes(keyword))) {
                                // 如果是嵌套结构，尝试找到最外层的可点击元素
                                let clickableEl = el;
                                while (clickableEl.parentElement) {
                                    const parent = clickableEl.parentElement;
                                    if (parent.tagName.toLowerCase() === 'tiga-view' && 
                                        (parent.className.includes('pack-btn') || parent.className.includes('btn'))) {
                                        clickableEl = parent;
                                    } else {
                                        break;
                                    }
                                }
                                return clickableEl;
                            }
                        }
                    }
                    
                    // 备选查找所有可能的按钮元素
                    const fallbackSelectors = [
                        'button', 'tiga-view', '.btn', '[class*="btn"]', 
                        '[role="button"]', 'a', 'div[onclick]'
                    ];
                    
                    for (const selector of fallbackSelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const el of elements) {
                            const text = el.textContent?.trim() || '';
                            if (keywords.some(keyword => text.includes(keyword))) {
                                return el;
                            }
                        }
                    }
                    return null;
                }
            """
            )

            if button:
                # 将JavaScript返回的元素转换为ElementHandle
                self.click_button = button

        if not self.click_button:
            raise ElementNotFoundError("红包领取按钮")

        # 检查红包领取按钮是否可见
        # is_visible = await self.click_button.is_visible()
        # is_enabled = await self.click_button.is_enabled()
        # if not is_visible or not is_enabled:
        #     raise ElementNotFoundError("红包领取按钮已找到，但不可见或不可点击")

    @retry(max_attempts=20, delay=3.0)
    async def detect_page(self) -> InteractionResult:
        """检测页面内容"""
        try:
            # 检查页面内容（添加超时保护）
            body = await asyncio.wait_for(self.page.query_selector("body"), timeout=5.0)
            if not body:
                raise PageContentError("无法获取页面内容")

            await self.risk_detector.detect()  # 检查风控

            # 检查内容长度
            body_text = await asyncio.wait_for(body.inner_text(), timeout=5.0)
            if len(body_text.strip()) < 100:
                logger.debug(body_text)
                page_html = await self.page.content()
                raise PageContentError(
                    message=f"页面内容过少({len(body_text)} 字符)",
                    content_length=len(body_text),
                    content_snippet=page_html,
                )

            await self._check_already_received()  # 检查红包是否已领取
            await self._check_hongbao_button()  # 检查红包领取按钮

            return InteractionResult(True, "页面内容正常")
        except (AlreadyClaimedError, RiskDetectedError) as e:
            raise e
        except Exception as e:
            raise PageContentError(f"页面检测失败: {e}") from e

    async def execute_main_action(self) -> InteractionResult:
        """执行主要操作"""

        try:
            await self._simulate_user_browsing()  # 模拟用户浏览页面
            await self._scroll_to_top()  # 滚动到页面顶部
            await self._click_hongbao_button()  # 点击红包按钮
            await self._wait_for_result()  # 等待结果

            if random.random() < 0.1:
                await self._visit_shop_detail()  # 访问店铺详情

            return InteractionResult(True, "执行主要操作成功")
        except Exception as e:
            raise InteractionError(f"执行主要操作失败: {e}") from e

    async def _simulate_user_browsing(self) -> None:
        """模拟用户浏览页面的行为 - 真实手势滑动"""

        # 获取页面高度和视口信息
        page_height = await self.page.evaluate("document.body.scrollHeight")
        viewport_height = await self.page.evaluate("window.innerHeight")
        viewport_width = await self.page.evaluate("window.innerWidth")

        # 随机滑动1-3次
        scroll_times = random.randint(1, 3)

        for _ in range(scroll_times):
            # 模拟真实的触摸滑动手势
            result = await self._perform_realistic_swipe(viewport_width, viewport_height)
            if not result:
                break

            # 模拟用户阅读停留时间（真实用户会停下来查看内容）
            reading_time = random.uniform(1.0, 3.0)
            await asyncio.sleep(reading_time)

            # 检查是否已经接近底部
            current_scroll = await self.page.evaluate("window.scrollY")
            if current_scroll + viewport_height >= page_height - 100:
                break

        # 最后的思考停留时间
        think_time = random.uniform(0.8, 1.8)
        await asyncio.sleep(think_time)

    async def _perform_realistic_swipe(self, viewport_width: int, viewport_height: int) -> bool:
        """执行真实的触摸滑动手势"""

        # 滑动起点：在屏幕中央偏下的位置
        start_x = viewport_width // 2 + random.randint(-50, 50)
        start_y = viewport_height * 0.7 + random.randint(-30, 30)

        # 滑动距离：模拟真实的手指滑动距离
        swipe_distance = random.randint(150, 400)

        # 滑动终点
        end_x = start_x + random.randint(-20, 20)  # 水平方向略有偏移
        end_y = start_y - swipe_distance  # 向上滑动

        # 确保终点在有效范围内
        end_y = max(50, end_y)

        try:
            # 首先全面禁用文本选择
            await self.page.evaluate(
                """
                // 全面禁用文本选择和拖拽
                document.documentElement.style.webkitUserSelect = 'none';
                document.documentElement.style.userSelect = 'none';
                document.documentElement.style.webkitTouchCallout = 'none';
                document.body.style.webkitUserSelect = 'none';
                document.body.style.userSelect = 'none';
                document.body.style.webkitTouchCallout = 'none';

                // 禁用拖拽选择
                document.ondragstart = function() { return false; };
                document.onselectstart = function() { return false; };
                document.oncontextmenu = function() { return false; };
            """
            )

            # 使用简单有效的滚动操作代替复杂的触摸事件
            scroll_result = await self.page.evaluate(
                """
                async (params) => {
                    const { startX, startY, endX, endY } = params;

                    // 计算滚动距离
                    const scrollDistance = startY - endY;

                    // 获取当前滚动位置
                    const currentScroll = window.scrollY;

                    // 分步滚动以模拟真实滑动
                    const steps = 8;
                    const stepDistance = scrollDistance / steps;

                    for (let i = 1; i <= steps; i++) {
                        const newScroll = currentScroll + (stepDistance * i);
                        window.scrollTo({
                            top: newScroll,
                            behavior: 'auto'  // 使用立即滚动而不是平滑滚动
                        });
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    return true;
                }
                """,
                {"startX": start_x, "startY": start_y, "endX": end_x, "endY": end_y},
            )

            if scroll_result:
                # 滚动后的短暂停顿
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            else:
                # 滚动浏览操作执行失败，尝试备选方案
                # 备选方案：使用传统的鼠标滑动
                await self.page.mouse.move(start_x, start_y)
                await self.page.mouse.down()

                steps = random.randint(8, 15)
                for step in range(1, steps + 1):
                    progress = step / steps
                    current_x = self._bezier_interpolate(start_x, end_x, progress)
                    current_y = self._bezier_interpolate(start_y, end_y, progress)

                    await self.page.mouse.move(current_x, current_y)
                    await asyncio.sleep(random.uniform(0.01, 0.03))

                await self.page.mouse.up()
                await asyncio.sleep(random.uniform(0.2, 0.5))
                return True

        except Exception:
            # 滑动手势失败
            return False
        finally:
            # 恢复文本选择功能
            try:
                await self.page.evaluate(
                    """
                    // 恢复文本选择功能
                    document.documentElement.style.webkitUserSelect = '';
                    document.documentElement.style.userSelect = '';
                    document.documentElement.style.webkitTouchCallout = '';
                    document.body.style.webkitUserSelect = '';
                    document.body.style.userSelect = '';
                    document.body.style.webkitTouchCallout = '';

                    document.ondragstart = null;
                    document.onselectstart = null;
                    document.oncontextmenu = null;
                """
                )
            except Exception:
                pass

    def _bezier_interpolate(self, start: float, end: float, t: float) -> float:
        """使用二次贝塞尔曲线插值，让滑动轨迹更自然"""

        # 控制点在中间位置附近
        control = (start + end) / 2 + random.uniform(-10, 10)

        # 二次贝塞尔曲线公式
        return (1 - t) ** 2 * start + 2 * (1 - t) * t * control + t**2 * end

    async def _scroll_to_top(self) -> None:
        """滚动回到页面顶部"""

        # 平滑滚动到顶部
        await self.page.evaluate(
            """
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        """
        )

        # 等待滚动动画完成
        wait_time = random.uniform(1.0, 2.0)
        await asyncio.sleep(wait_time)

        # 确保已经到达顶部
        current_scroll = await self.page.evaluate("window.scrollY")

        if current_scroll > 10:  # 允许一点误差
            await self.page.evaluate("window.scrollTo(0, 0);")
            await asyncio.sleep(0.5)

    async def _click_hongbao_button(self) -> InteractionResult:
        """触摸红包按钮 - H5触摸事件"""

        # 检查按钮是否存在
        if not self.click_button:
            return InteractionResult(success=False, message="红包按钮未找到")

        try:
            # 确保按钮仍然可见和可点击
            if not await self.click_button.is_visible():
                return InteractionResult(success=False, message="红包按钮不可见")

            if not await self.click_button.is_enabled():
                return InteractionResult(success=False, message="红包按钮不可点击")

            # 滚动到按钮位置（确保完全可见）
            await self.click_button.scroll_into_view_if_needed()
            wait_time = random.uniform(0.3, 0.8)
            await asyncio.sleep(wait_time)

            # 执行真实的触摸操作
            result = await self._perform_realistic_touch(self.click_button)
            if not result:
                return InteractionResult(success=False, message="领取红包过程-触摸操作失败")

            # 等待触摸响应并检查页面变化
            response_wait = random.uniform(0.8, 1.5)
            await asyncio.sleep(response_wait)

            # 简单检查页面是否有响应
            try:
                # 检查按钮状态是否改变
                if self.click_button and await self.click_button.is_visible():
                    button_text = await self.click_button.inner_text()

                    # 如果按钮文本变化，说明点击生效了
                    if any(keyword in button_text for keyword in ["已领取", "已完成", "明天再来", "今日已领"]):
                        return InteractionResult(success=True, message=f"红包领取成功，按钮状态: {button_text}")

                # 检查页面是否有弹窗或提示
                success_messages = await self.page.evaluate(
                    """
                    () => {
                        const messages = [];
                        const selectors = [
                            '.toast', '.message', '.alert', '.popup', '.dialog',
                            '[class*="toast"]', '[class*="message"]', '[class*="alert"]'
                        ];

                        selectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                const text = el.textContent?.trim();
                                if (text && text.length > 0) {
                                    messages.push(text);
                                }
                            });
                        });

                        return messages;
                    }
                    """
                )

                if success_messages:
                    for msg in success_messages:
                        if any(keyword in msg for keyword in ["成功", "领取", "红包", "恭喜"]):
                            return InteractionResult(success=True, message=f"红包领取成功: {msg}")

            except Exception:
                pass

            return InteractionResult(success=True, message="触摸操作已执行，等待结果确认")

        except Exception as e:
            return InteractionResult(success=False, message=f"尝试领取红包失败: {e}")

    async def _perform_realistic_touch(self, element: ElementHandle) -> bool:
        """执行真实的H5触摸操作 - 针对饿了么优化版本"""

        try:
            # 获取按钮位置和大小
            button_box = await element.bounding_box()
            if not button_box:
                # 无法获取按钮位置信息
                return False

            # 计算触摸点，选择按钮中心位置并添加微小偏移
            touch_x = button_box["x"] + button_box["width"] * 0.5 + random.uniform(-3, 3)
            touch_y = button_box["y"] + button_box["height"] * 0.5 + random.uniform(-3, 3)

            # 方案1：真实的H5触摸事件序列 - 最优先
            try:
                touch_result = await self.page.evaluate(
                    """
                    async (params) => {
                        const { element, touchX, touchY } = params;

                        // 确保元素可见和可交互
                        element.scrollIntoView({ behavior: 'auto', block: 'center' });
                        element.style.pointerEvents = 'auto';
                        element.style.visibility = 'visible';

                        // 移除可能的覆盖层
                        const overlays = document.querySelectorAll('.overlay, .modal, .popup, .mask, [style*="z-index: 9999"]');
                        overlays.forEach(overlay => {
                            if (overlay && overlay !== element && !element.contains(overlay)) {
                                overlay.style.display = 'none';
                            }
                        });

                        // 等待短暂时间确保覆盖层移除
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 创建真实的触摸事件
                        const touch = new Touch({
                            identifier: Date.now(),
                            target: element,
                            clientX: touchX,
                            clientY: touchY,
                            radiusX: 2.5,
                            radiusY: 2.5,
                            rotationAngle: 0,
                            force: 0.5
                        });

                        // 触摸开始事件
                        const touchStartEvent = new TouchEvent('touchstart', {
                            cancelable: true,
                            bubbles: true,
                            touches: [touch],
                            targetTouches: [touch],
                            changedTouches: [touch]
                        });

                        // 触摸结束事件
                        const touchEndEvent = new TouchEvent('touchend', {
                            cancelable: true,
                            bubbles: true,
                            touches: [],
                            targetTouches: [],
                            changedTouches: [touch]
                        });

                        // 点击事件
                        const clickEvent = new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            clientX: touchX,
                            clientY: touchY,
                            button: 0
                        });

                        // 执行触摸序列
                        let success = false;

                        try {
                            // 1. 触摸开始
                            element.dispatchEvent(touchStartEvent);
                            await new Promise(resolve => setTimeout(resolve, 50));

                            // 2. 触摸结束
                            element.dispatchEvent(touchEndEvent);
                            await new Promise(resolve => setTimeout(resolve, 50));

                            // 3. 点击事件
                            element.dispatchEvent(clickEvent);
                            await new Promise(resolve => setTimeout(resolve, 100));

                            // 4. 直接调用click方法作为备选
                            element.click();

                            success = true;
                        } catch (e) {
                            console.log('Touch event error:', e);
                        }

                        return success;
                    }
                    """,
                    {"element": element, "touchX": touch_x, "touchY": touch_y},
                )

                if touch_result:
                    await asyncio.sleep(random.uniform(0.3, 0.8))  # 等待响应
                    return True

            except Exception:
                # H5触摸事件失败
                pass

            # 方案2：强力JavaScript点击
            try:
                click_result = await self.page.evaluate(
                    """
                    (element) => {
                        // 强制移除所有可能的阻挡
                        const removeBlocks = () => {
                            const selectors = [
                                '.overlay', '.modal', '.popup', '.mask', '.dialog',
                                '[style*="z-index: 9999"]', '[style*="position: fixed"]'
                            ];
                            selectors.forEach(sel => {
                                document.querySelectorAll(sel).forEach(el => {
                                    if (el && el !== element && !element.contains(el)) {
                                        el.style.display = 'none';
                                        el.style.visibility = 'hidden';
                                        el.style.pointerEvents = 'none';
                                    }
                                });
                            });
                        };

                        removeBlocks();

                        // 确保元素状态
                        element.style.pointerEvents = 'auto';
                        element.style.display = 'block';
                        element.style.visibility = 'visible';
                        element.style.opacity = '1';
                        element.disabled = false;
                        element.removeAttribute('disabled');

                        // 滚动到可见区域
                        element.scrollIntoView({ behavior: 'auto', block: 'center' });

                        // 多种点击方式
                        let clicked = false;

                        // 1. 直接点击
                        try {
                            element.click();
                            clicked = true;
                        } catch (e) {}

                        // 2. 焦点 + 回车
                        try {
                            element.focus();
                            const enterEvent = new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                bubbles: true
                            });
                            element.dispatchEvent(enterEvent);
                        } catch (e) {}

                        // 3. 如果是form元素，尝试提交
                        try {
                            if (element.type === 'submit' || element.closest('form')) {
                                const form = element.closest('form');
                                if (form && form.submit) {
                                    form.submit();
                                }
                            }
                        } catch (e) {}

                        return clicked;
                    }
                    """,
                    element,
                )

                if click_result:
                    await asyncio.sleep(random.uniform(0.5, 1.0))
                    return True

            except Exception:
                # 强力JavaScript点击失败
                pass

            # 方案3：Playwright原生tap方法
            try:
                await element.tap(force=True, timeout=3000)
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            except Exception:
                # Playwright tap失败
                pass

            # 方案4：坐标点击
            try:
                await self.page.mouse.click(touch_x, touch_y, button="left", delay=100)
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            except Exception:
                # 坐标点击失败
                pass

            # 方案5：模拟完整的鼠标操作序列
            try:
                await self.page.mouse.move(touch_x, touch_y)
                await asyncio.sleep(0.1)
                await self.page.mouse.down()
                await asyncio.sleep(0.1)
                await self.page.mouse.up()
                await asyncio.sleep(random.uniform(0.5, 1.0))
                return True
            except Exception:
                # 完整鼠标序列失败
                pass

            # 所有触摸操作方案都失败了
            return False

        except Exception:
            # 触摸操作执行失败
            return False

    async def _wait_for_result(self) -> InteractionResult:

        start_time = time.time()
        timeout = 12  # 减少超时时间从15秒到12秒
        initial_url = self.page.url
        check_interval = 1.2  # 增加检查间隔从0.8秒到1.2秒，减少页面查询频率

        while time.time() - start_time < timeout:
            try:
                # 检查URL变化
                current_url = self.page.url
                if current_url != initial_url:
                    await asyncio.sleep(1.0)

                await self._check_already_received()
            except AlreadyClaimedError:
                return InteractionResult(success=True, message="红包领取成功（最终确认）")
            except Exception as e:
                logger.debug(f"结果等待检查异常: {e}")
            finally:
                await asyncio.sleep(check_interval)

        return InteractionResult(success=True, message="操作已完成，等待超时")

    async def _visit_shop_detail(self) -> None:
        """访问店铺详情页面 - 优化版本"""
        try:
            # 查找店铺名称元素
            shop_name_elements: List[ElementHandle] = []

            # 优先查找.shop-name_v2元素
            shop_name_v2 = await self.page.query_selector_all(".shop-name_v2")
            if shop_name_v2:
                shop_name_elements.extend(shop_name_v2)

            # 查找.es-shop-name元素
            es_shop_name = await self.page.query_selector_all(".es-shop-name")
            if es_shop_name:
                shop_name_elements.extend(es_shop_name)

            if not shop_name_elements:
                return

            # 随机选择一个店铺名称元素
            selected_name_element = random.choice(shop_name_elements)

            # 记录初始URL
            initial_url = self.page.url

            # 使用触摸事件点击店铺名称元素
            try:
                # 检查元素是否可见和可点击
                if not await selected_name_element.is_visible():
                    return

                # 滚动到元素位置（确保完全可见）
                await selected_name_element.scroll_into_view_if_needed()
                wait_time = random.randint(300, 800)
                await self.page.wait_for_timeout(wait_time)

                # 执行真实的触摸操作
                touch_result = await self._perform_realistic_touch(selected_name_element)
                if not touch_result:
                    return

                # 等待触摸响应
                response_wait = random.randint(800, 1500)
                await self.page.wait_for_timeout(response_wait)

                # 优化：使用asyncio.wait_for添加超时保护，避免死循环
                try:
                    result = await asyncio.wait_for(self._wait_for_shop_page_change(), timeout=10.0)

                    if result and random.random() < 0.2:
                        menu_items = await self._wait_for_menu_item()

                        if menu_items:
                            selected_menu_item = random.choice(menu_items)

                            await selected_menu_item.scroll_into_view_if_needed()
                            wait_time = random.randint(300, 800)

                            await self.page.wait_for_timeout(wait_time)

                            touch_result = await self._perform_realistic_touch(selected_menu_item)
                            if not touch_result:
                                return

                            response_wait = random.randint(1500, 2000)
                            await self.page.wait_for_timeout(response_wait)
                        else:
                            return
                except asyncio.TimeoutError:
                    # 超时后尝试备选方案
                    await self._try_alternative_shop_click(selected_name_element, initial_url)

            except Exception as e:
                # 记录具体异常信息而不是静默吞掉
                logger.debug(f"店铺访问失败: {e}")
                # 尝试备选方案
                await self._try_alternative_shop_click(selected_name_element, initial_url)

        except Exception as e:
            logger.debug(f"店铺详情访问异常: {e}")

    async def _wait_for_shop_page_change(self) -> bool:
        """等待店铺页面变化 - 优化版本，避免死循环"""
        max_attempts = 10  # 最多检查10次，每次1秒

        for attempt in range(max_attempts):

            # 检查是否出现shop__detail元素
            shop_detail_elements = await self.page.query_selector_all(".menuItem")
            shop_detail_exists = len(shop_detail_elements) > 0

            if shop_detail_exists:
                return True

            # 如果不是最后一次尝试，等待1秒后继续
            if attempt < max_attempts - 1:
                await asyncio.sleep(1.0)  # 使用asyncio.sleep而不是page.wait_for_timeout

        # 如果所有尝试都失败，不抛出异常，直接返回
        logger.debug("店铺页面变化检测超时，跳过")
        return False

    async def _wait_for_menu_item(self) -> List[ElementHandle]:
        """等待菜单项 - 优化版本，避免死循环"""
        max_attempts = 10  # 最多检查10次，每次1秒

        for attempt in range(max_attempts):

            # 检查是否出现shop__detail元素
            menu_items = await self.page.query_selector_all(".menuItem")
            shop_detail_exists = len(menu_items) > 0

            if shop_detail_exists:
                return menu_items

            # 如果不是最后一次尝试，等待1秒后继续
            if attempt < max_attempts - 1:
                await asyncio.sleep(1.0)  # 使用asyncio.sleep而不是page.wait_for_timeout

        return []

    async def _try_alternative_shop_click(self, selected_name_element: ElementHandle, initial_url: str) -> None:
        """尝试备选的店铺点击方案"""
        try:
            # 备选方案：使用JavaScript点击
            await selected_name_element.evaluate("el => el.click()")

            # 等待加载
            wait_time = random.randint(800, 1500)
            await asyncio.sleep(wait_time / 1000)  # 转换为秒

            # 简单检查URL是否变化，不进入循环
            current_url = self.page.url
            if current_url != initial_url:
                logger.debug("备选方案成功：URL已变化")
            else:
                logger.debug("备选方案：URL未变化，跳过")

        except Exception as e:
            logger.debug(f"备选点击方案失败: {e}")
