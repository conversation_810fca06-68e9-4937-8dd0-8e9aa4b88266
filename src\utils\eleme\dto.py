# encoding: utf-8
# <AUTHOR> <EMAIL>
# utils/eleme/dto.py
# created: 2025-03-31 18:55:54
# updated: 2025-07-07 22:04:09

import json
from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field, model_validator

T = TypeVar("T", bound=BaseModel)


# 自定义BaseModel, 添加datetime序列化处理
class ElemeUnionBaseModel(BaseModel):
    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """重写model_dump方法, 将datetime对象转换为ISO格式字符串, 便于JSON序列化"""
        data = super().model_dump(**kwargs)
        return self._process_datetime(data)

    def _process_datetime(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """递归处理字典中的datetime对象"""
        for key, value in data.items():
            if isinstance(value, datetime):  # 将datetime转换为ISO格式字符串
                data[key] = value.isoformat()
            elif isinstance(value, dict):
                data[key] = self._process_datetime(value)
            elif isinstance(value, list):
                data[key] = [
                    (
                        self._process_datetime(item)
                        if isinstance(item, dict)
                        else item.isoformat() if isinstance(item, datetime) else item
                    )
                    for item in value
                ]
        return data


class ElemeUnionBaseResponse(BaseModel, Generic[T]):
    data: T = Field(..., description="响应数据")
    biz_error_code: Optional[str] = Field(None, description="错误码")
    biz_error_msg: Optional[str] = Field(None, description="错误信息")


class CouponPackagePurchaseDto(ElemeUnionBaseModel):
    purchase_id: str = Field(..., description="权益采购单ID")
    purchase_name: str = Field(..., description="权益采购单名称")
    ext_info: str = Field(..., description="权益采购单扩展信息")


class CouponPackagePurchaseListDto(ElemeUnionBaseModel):
    records: List[CouponPackagePurchaseDto] = Field(..., description="权益采购单列表")
    total_count: int = Field(..., description="权益采购单总数")


class CouponPackageDetailDto(ElemeUnionBaseModel):
    item_name: str = Field(..., description="权益商品名称")
    quantity: int = Field(..., description="权益商品数量")
    threshold_text: str = Field(..., description="权益商品使用门槛, 例: 满50减5")
    validity_period_text: str = Field(..., description="权益商品有效期, 例: 30天")
    scene_text: str | None = Field(None, description="权益商品使用场景, 例: 外卖")
    ext_info: str = Field(..., description="权益商品扩展信息")


class CouponPackageDto(ElemeUnionBaseModel):
    item_id: str = Field(..., description="权益商品ID")
    name: str = Field(..., description="权益商品名称")
    coupon_package_details: List[CouponPackageDetailDto] = Field(..., description="权益商品详情")
    picture: str = Field(..., description="权益商品图片")
    detail_pictures: List[str] = Field(..., description="权益商品详情图片")
    gift_detail_pictures: List[str] = Field(..., description="权益商品赠品详情图片")
    ext_info: str = Field(..., description="权益商品扩展信息")


class CouponPackagePurchaseDetailDto(ElemeUnionBaseModel):
    purchase_type: int = Field(..., description="采购类型(1-直采; 2-后结)")
    intended_sell_price_cent: Optional[int] = Field(0, description="预定售价，单位：分")
    purchase_price_cent: int = Field(..., description="权益采购单价格，单位：分")
    sales_start_time: datetime = Field(..., description="权益售卖开始时间")
    sales_end_time: datetime = Field(..., description="权益售卖结束时间")
    purchase_stock: int = Field(..., description="权益采购单库存")
    remain_stock: int = Field(..., description="权益采购单剩余库存")
    coupon_packages: List[CouponPackageDto] = Field(..., description="权益商品列表")
    ext_info: str = Field(..., description="权益采购单扩展信息")

    @model_validator(mode="before")
    @classmethod
    def validate_sales_time(cls, values):
        values["sales_start_time"] = datetime.fromtimestamp(values["sales_start_time"])
        values["sales_end_time"] = datetime.fromtimestamp(values["sales_end_time"])
        return values


class CouponPackagePurchaseTicketCreateResultDto(ElemeUnionBaseModel):
    ticket_id: str = Field(..., description="权益券ID")
    ext_info: str = Field(..., description="权益券扩展信息")


class CouponPackagePurchaseTicketDetailResultDto(ElemeUnionBaseModel):
    ticket_id: str = Field(..., description="权益券ID")
    ticket_status: str = Field(
        ..., description="凭证状态(INIT-发放中; EFFECTIVE:发放成功; USED:已核销/过期; CANCELED:发放失败/已撤销)"
    )
    ext_info: str = Field(..., description="权益券扩展信息")


class CouponPackagePurchaseTicketRefundResultDto(ElemeUnionBaseModel):
    ext_info: str = Field(..., description="权益券扩展信息")


CouponPackagePurchaseListResponse = ElemeUnionBaseResponse[CouponPackagePurchaseListDto]
CouponPackagePurchaseDetailResponse = ElemeUnionBaseResponse[CouponPackagePurchaseDetailDto]
CouponPackagePurchaseTicketCreateResultResponse = ElemeUnionBaseResponse[CouponPackagePurchaseTicketCreateResultDto]
CouponPackagePurchaseTicketDetailResultResponse = ElemeUnionBaseResponse[CouponPackagePurchaseTicketDetailResultDto]
CouponPackagePurchaseTicketRefundResultResponse = ElemeUnionBaseResponse[CouponPackagePurchaseTicketRefundResultDto]


class UnionOrderInfoDTO(BaseModel):
    activity_info_remark_list: str = Field("", description="活动信息备注列表")
    activity_id: int = Field(-1, description="活动id")
    ad_zone_id: Optional[str] = Field("", description="广告位ID")
    ad_zone_name: Optional[str] = Field("", description="广告位名称")
    biz_order_id: int = Field(..., description="业务订单ID")
    category_name: Optional[str] = Field("", description="类别名称")
    channel_fee: int = Field(..., description="渠道费用（单位分）")
    channel_rate: float = Field(..., description="渠道费率")
    commission_fee: int = Field(..., description="佣金费用（单位分）")
    commission_rate: float = Field(..., description="佣金费率")
    ext_info: dict = Field(..., description="扩展信息")
    flow_type: int = Field(..., description="流量类型")
    full_settle_amount: int = Field(..., description="全额结算金额（单位分）")
    gmt_modified: datetime = Field(..., description="修改时间")
    income: int = Field(..., description="收入（单位分）")
    income_rate: float = Field(..., description="收入费率")
    item_id: str = Field(..., description="商品ID")
    media_id: str = Field(..., description="媒体ID")
    media_name: Optional[str] = Field("", description="媒体名称")
    order_item_status: int = Field(..., description="订单商品状态")
    order_state: int = Field(..., description="订单状态")
    parent_order_id: int = Field(..., description="父订单ID", serialization_alias="order_id")
    pay_amount: Optional[int] = Field(None, description="支付金额（单位分）")
    pay_time: Optional[datetime] = Field(None, description="支付时间")
    pic_url: Optional[str] = Field("", description="图片URL")
    pid: Optional[str] = Field("", description="推广位ID")
    platform_commission_fee: int = Field(..., description="平台佣金费用（单位分）")
    platform_commission_rate: float = Field(..., description="平台佣金费率")
    platform_type: int = Field(..., description="平台类型")
    product_num: int = Field(..., description="商品数量")
    settle: int = Field(..., description="结算金额（单位分）")
    settle_amount: int = Field(..., description="结算总金额（单位分）")
    settle_state: int = Field(..., description="结算状态")
    shop_name: str = Field(..., description="店铺名称")
    subsidy_fee: int = Field(..., description="补贴费用（单位分）")
    subsidy_rate: float = Field(..., description="补贴费率")
    title: str = Field(..., description="商品标题")
    tk_create_time: datetime = Field(..., description="淘客创建时间")
    trace_time: datetime = Field(..., description="跟踪时间")
    sid: Optional[str] = Field("", description="sid")
    receive_time: Optional[datetime] = Field(None, description="收货时间")
    settle_time: Optional[datetime] = Field(None, description="结算时间")
    unit_price: int = Field(..., description="单价（单位分）")
    source_info: Optional[dict] = Field(None, description="海狸扩展信息, 用于记录海狸来源相关扩展信息")

    @model_validator(mode="before")
    def model_verify(self):
        amount_fields = [
            "channel_fee",
            "commission_fee",
            "full_settle_amount",
            "income",
            "pay_amount",
            "platform_commission_fee",
            "settle",
            "settle_amount",
            "subsidy_fee",
            "unit_price",
        ]
        data = dict(self)
        for field in amount_fields:
            # 检查字段是否存在
            if field not in data:
                continue
            # 检查字段是否已经是整数，跳过已处理的数据
            if isinstance(data[field], int):
                continue
            # 转换金额字段，将字符串或浮点数转为整数（单位分）
            data[field] = int(float(data[field]) * 100)

        # 处理 ext_info 字段
        if "ext_info" in data and isinstance(data["ext_info"], str):
            data["ext_info"] = json.loads(data["ext_info"])

        return data


class UnionActivityUrlDTO(BaseModel):
    sid: Optional[str] = Field("", description="sid")
    h5_url: Optional[str] = Field("", description="h5 url")
    eleme_url: Optional[str] = Field("", description="饿了么url")
    taobao_url: Optional[str] = Field("", description="淘宝url")
    wechat_schema: Optional[str] = Field("", description="wechat schema")
    alipay_schema: Optional[str] = Field("", description="alipay schema")
    taobao_schema: Optional[str] = Field("", description="taobao schema")
    eleme_schema: Optional[str] = Field("", description="eleme schema")
    wechat_appid: Optional[str] = Field("", description="wechat mp appid")
    wechat_path: Optional[str] = Field("", description="wechat mp page path")


# ==================== 联盟活动信息相关DTO ====================


class TopH5PromotionDto(ElemeUnionBaseModel):
    """H5推广链接信息"""

    h5_url: Optional[str] = Field("", description="H5长链接")
    short_link: Optional[str] = Field("", description="H5短链接")
    h5_qr_code: Optional[str] = Field("", description="H5推广二维码")
    h5_img_url: Optional[str] = Field("", description="H5推广海报")
    tj_h5_url: Optional[str] = Field("", description="推荐有奖拉新链接")


class TopTaobaoPromotionDto(ElemeUnionBaseModel):
    """淘宝推广链接信息"""

    h5_url: Optional[str] = Field("", description="淘宝推广链接")
    h5_short_url: Optional[str] = Field("", description="淘宝短链接")
    tb_qr_code: Optional[str] = Field("", description="淘宝二维码")
    img_url: Optional[str] = Field("", description="淘宝推广海报")
    app_id: Optional[str] = Field("", description="淘宝应用ID")
    app_path: Optional[str] = Field("", description="淘宝应用路径")
    scheme_url: Optional[str] = Field("", description="淘宝唤端链接")


class TopWxPromotionDto(ElemeUnionBaseModel):
    """微信推广链接信息"""

    wx_app_id: Optional[str] = Field("", description="微信小程序AppID")
    wx_path: Optional[str] = Field("", description="微信小程序路径")
    wx_qr_code: Optional[str] = Field("", description="微信二维码")
    img_url: Optional[str] = Field("", description="微信推广海报")
    reduce_wx_app_id: Optional[str] = Field("", description="立减微信小程序AppID")
    reduce_wx_path: Optional[str] = Field("", description="立减微信小程序路径")
    reduce_wx_qr_code: Optional[str] = Field("", description="立减微信二维码")
    reduce_img_url: Optional[str] = Field("", description="立减推广海报")
    h5_short_link: Optional[str] = Field("", description="微信H5短链")
    market_wx_app_id: Optional[str] = Field("", description="媒体出资微信小程序AppID")
    market_wx_app_path: Optional[str] = Field("", description="媒体出资微信小程序路径")
    scheme_url: Optional[str] = Field("", description="微信唤端链接")


class TopAlipayPromotionDto(ElemeUnionBaseModel):
    """支付宝推广链接信息"""

    app_id: Optional[str] = Field("", description="支付宝小程序AppID")
    app_path: Optional[str] = Field("", description="支付宝小程序路径")
    alipay_scheme_url: Optional[str] = Field("", description="支付宝唤端链接")
    h5_url: Optional[str] = Field("", description="支付宝H5地址")
    alipay_watchword: Optional[str] = Field("", description="支付宝口令")
    alipay_watchword_suggest: Optional[str] = Field("", description="支付宝完整口令")
    img_url: Optional[str] = Field("", description="支付宝推广海报")
    alipay_qr_code: Optional[str] = Field("", description="支付宝二维码")
    short_link: Optional[str] = Field("", description="支付宝H5短链接")


class TopAppPromotionDto(ElemeUnionBaseModel):
    """APP推广链接信息"""

    deep_link: Optional[str] = Field("", description="深度链接地址")
    ele_url: Optional[str] = Field("", description="饿了么链接")
    eleme_word: Optional[str] = Field("", description="饿了么口令")
    word_valid_date: Optional[str] = Field("", description="饿了么口令有效期")


class PromotionLinkDto(ElemeUnionBaseModel):
    """推广链接信息（兼容旧版本字段）"""

    wx_appid: Optional[str] = Field("", description="微信小程序AppID")
    wx_path: Optional[str] = Field("", description="微信小程序路径")
    picture: Optional[str] = Field("", description="推广图片地址")
    alipay_mini_url: Optional[str] = Field("", description="支付宝小程序推广链接")
    h5_url: Optional[str] = Field("", description="H5推广地址")
    tb_qr_code: Optional[str] = Field("", description="淘宝二维码图片地址")
    mini_qrcode: Optional[str] = Field("", description="微信独立二维码")
    tb_mini_qrcode: Optional[str] = Field("", description="淘宝独立二维码")
    ele_scheme_url: Optional[str] = Field("", description="饿了么唤端链接")
    h5_short_link: Optional[str] = Field("", description="H5推广地址短链")
    h5_mini_qrcode: Optional[str] = Field("", description="H5二维码图片地址")
    eleme_word: Optional[str] = Field("", description="饿了么口令（有效期30天）")
    tb_scheme_url: Optional[str] = Field("", description="淘宝唤端链接")
    taobao_word: Optional[str] = Field("", description="淘口令（有效期30天）")
    full_taobao_word: Optional[str] = Field("", description="带文案淘口令（有效期30天）")

    # 新版本详细推广信息
    h5_promotion: Optional[TopH5PromotionDto] = Field(None, description="H5推广链接详情")
    taobao_promotion: Optional[TopTaobaoPromotionDto] = Field(None, description="淘宝推广链接详情")
    wx_promotion: Optional[TopWxPromotionDto] = Field(None, description="微信推广链接详情")
    alipay_promotion: Optional[TopAlipayPromotionDto] = Field(None, description="支付宝推广链接详情")
    app_promotion: Optional[TopAppPromotionDto] = Field(None, description="APP推广链接详情")


class UnionActivityInfoDto(ElemeUnionBaseModel):
    """联盟活动信息"""

    id: str = Field(..., description="活动ID")
    title: str = Field(..., description="活动标题")
    description: Optional[str] = Field("", description="活动描述")
    picture: Optional[str] = Field("", description="活动创意图片")
    start_time: datetime = Field(..., description="活动开始时间")
    end_time: datetime = Field(..., description="活动结束时间")
    link: PromotionLinkDto = Field(..., description="推广链接信息")

    @model_validator(mode="before")
    @classmethod
    def validate_time_fields(cls, values):
        """处理时间戳字段转换"""
        if "start_time" in values and isinstance(values["start_time"], (int, float)):
            values["start_time"] = datetime.fromtimestamp(values["start_time"])
        if "end_time" in values and isinstance(values["end_time"], (int, float)):
            values["end_time"] = datetime.fromtimestamp(values["end_time"])
        return values


class UnionActivityInfoResponse(ElemeUnionBaseModel):
    """联盟活动信息API响应"""

    data: UnionActivityInfoDto = Field(..., description="活动数据")
    result_code: int = Field(..., description="返回码")
    message: str = Field(..., description="返回消息")

    @model_validator(mode="before")
    @classmethod
    def validate_response(cls, values):
        """验证响应格式"""
        # 确保result_code为整数
        if "result_code" in values:
            values["result_code"] = int(values["result_code"])
        return values


class UnionPageLinkDTO(BaseModel):
    sid: Optional[str] = Field("", description="sid")
    h5_promotion: Optional[TopH5PromotionDto] = Field(None, description="H5推广链接详情")
    taobao_promotion: Optional[TopTaobaoPromotionDto] = Field(None, description="淘宝推广链接详情")
    wx_promotion: Optional[TopWxPromotionDto] = Field(None, description="微信推广链接详情")
    alipay_promotion: Optional[TopAlipayPromotionDto] = Field(None, description="支付宝推广链接详情")
    app_promotion: Optional[TopAppPromotionDto] = Field(None, description="APP推广链接详情")
