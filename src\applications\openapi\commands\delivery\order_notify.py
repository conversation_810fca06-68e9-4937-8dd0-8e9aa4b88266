# encoding: utf-8
# src/applications/openapi/commands/delivery/order_notify.py
# created: 2025-08-18 16:45:00

import json
from enum import Enum
from typing import TYPE_CHECKING, Any, Optional

import aiohttp
from loguru import logger

from src.databases.models.customer import CustomerSubscribeType
from src.domains.customer.entities import CustomerEntity

if TYPE_CHECKING:
    from src.applications.openapi.queries import CustomerQueryService  # 现在是同层调用，合理
    from src.domains.customer.services import CustomerBenefitService
    from src.domains.delivery.messages import UnionOrderInfoDTO
    from src.infrastructures.gateways.eleme.union import ElemeUnionDeliveryGateway


class OrderChannel(str, Enum):
    """订单渠道类型"""

    DINGTALK = "dingtalk"
    BENEFITS = "benefits"
    CUSTOMER = "customer"


class OrderNotifyCommandService:
    """订单通知命令服务"""

    def __init__(
        self,
        config: Any,  # 接受任何配置对象
        customer_query_service: "CustomerQueryService",
        customer_benefit_service: "CustomerBenefitService",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
    ):
        self.config = config
        self.customer_query_service = customer_query_service
        self.customer_benefit_service = customer_benefit_service
        self.eleme_gateway = eleme_union_delivery_gateway
        self._http_session: Optional[aiohttp.ClientSession] = None

    async def notify_order(self, order: "UnionOrderInfoDTO") -> None:
        """
        处理订单通知

        Args:
            order: 联盟订单信息
        """
        logger.info(f"开始处理订单通知: {order.parent_order_id}")

        channel = self._determine_order_channel(order)
        logger.info(f"订单渠道: {channel.value}")

        if channel == OrderChannel.BENEFITS:
            await self._process_benefits_order(order)
        elif channel == OrderChannel.DINGTALK:
            await self._process_dingtalk_order(order)
        elif channel == OrderChannel.CUSTOMER:
            await self._process_customer_order(order)
        else:
            logger.warning(f"未知订单渠道类型: {channel}")

    def _determine_order_channel(self, order: "UnionOrderInfoDTO") -> OrderChannel:
        """
        判断订单渠道类型

        Args:
            order: 订单信息

        Returns:
            OrderChannel: 渠道类型
        """
        if not order.ad_zone_name:
            return OrderChannel.BENEFITS
        elif OrderChannel.DINGTALK.value in order.ad_zone_name:
            return OrderChannel.DINGTALK
        else:
            return OrderChannel.CUSTOMER

    def _build_base_order_data(self, order: "UnionOrderInfoDTO") -> dict:
        """
        构建基础订单数据

        Args:
            order: 订单信息

        Returns:
            dict: 基础订单数据
        """
        return {
            "order_id": str(order.biz_order_id),
            "order_state": str(order.order_state),
            "category_name": order.category_name or "",
            "shop_name": order.shop_name,
            "pay_amount": str(order.pay_amount or 0),
            "settle_amount": str(order.settle_amount or 0),
            "tk_create_time": order.tk_create_time.strftime("%Y-%m-%d %H:%M:%S"),
            "pay_time": order.pay_time.strftime("%Y-%m-%d %H:%M:%S") if order.pay_time else "",
            "receive_time": order.receive_time.strftime("%Y-%m-%d %H:%M:%S") if order.receive_time else "",
        }

    async def _decode_sid_info(self, sid: Optional[str]) -> dict:
        """
        解析SID信息

        Args:
            sid: SID字符串

        Returns:
            dict: 解析后的信息
        """
        if not sid:
            return {}

        if sid.startswith("SID:"):
            try:
                result = await self.eleme_gateway.decode_sid(sid)
                return result if result is not None else {}
            except Exception as e:
                logger.warning(f"解析SID失败: {e}")
                return {}
        else:
            # 兼容旧格式：from_channel.open_id.corp_id
            sid_parts = sid.split(".")
            return {
                "from_channel": sid_parts[0] if len(sid_parts) > 0 else "",
                "dingtalk_open_id": sid_parts[1] if len(sid_parts) > 1 else "",
                "corp_id": sid_parts[2] if len(sid_parts) > 2 else "",
            }

    async def _get_http_session(self) -> aiohttp.ClientSession:
        """
        获取或创建HTTP会话

        Returns:
            aiohttp.ClientSession: HTTP会话对象
        """
        if self._http_session is None or self._http_session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,  # 连接池总大小
                limit_per_host=30,  # 每个主机的连接数限制
                ttl_dns_cache=300,  # DNS缓存时间（秒）
            )
            timeout = aiohttp.ClientTimeout(
                total=30,  # 总超时时间
                connect=10,  # 连接超时
                sock_read=20,  # 读取超时
            )
            self._http_session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
            )
        return self._http_session

    async def _send_notification(self, order_data: dict, notify_url: str) -> None:
        """
        发送订单通知

        Args:
            order_data: 订单数据
            notify_url: 通知URL
        """
        if not notify_url:
            logger.warning("通知URL为空，跳过发送")
            return

        logger.info(f"发起订单通知消息, url: {notify_url}")
        logger.debug(f"订单通知数据: {order_data}")

        session = await self._get_http_session()
        try:
            async with session.post(notify_url, json=order_data) as response:
                response_text = await response.text()
                logger.info(f"订单通知响应, status: {response.status}")
                logger.debug(f"响应内容: {response_text}")

                if response.status >= 400:
                    if response.status >= 500:
                        # 服务器错误，应该重试
                        raise ConnectionError(f"服务器错误，HTTP状态码: {response.status}")
                    else:
                        # 客户端错误，不应重试
                        logger.error(f"客户端错误，不重试，HTTP状态码: {response.status}, 响应: {response_text}")
                        return

        except aiohttp.ClientError as e:
            # 网络错误，应该重试
            logger.error(f"网络请求失败: {e}")
            raise ConnectionError(f"网络请求失败: {e}") from e
        except Exception as e:
            logger.error(f"发送订单通知失败: {e}")
            raise

    async def cleanup(self) -> None:
        """
        清理资源，关闭HTTP会话
        """
        if self._http_session and not self._http_session.closed:
            await self._http_session.close()
            self._http_session = None
            logger.debug("HTTP会话已关闭")

    async def _process_dingtalk_order(self, order: "UnionOrderInfoDTO") -> None:
        """
        处理钉钉订单通知

        Args:
            order: 订单信息
        """
        logger.info(f"处理钉钉订单: {order.parent_order_id}")

        # 只处理已结算订单
        if order.settle_state != 1:
            logger.info(f"订单[{order.parent_order_id}]状态不是已结算，跳过处理")
            return

        # 解析SID信息
        extra_info = await self._decode_sid_info(order.sid)

        # 构建订单数据
        order_data = self._build_base_order_data(order)
        order_data.update(
            {
                "order_id": str(order.parent_order_id),  # 钉钉使用parent_order_id
                "user_from": extra_info.get("from_channel", ""),
                "user_open_id": extra_info.get("dingtalk_open_id", ""),
                "corp_id": extra_info.get("corp_id", ""),
                "ext": json.dumps({"from": "haili"}),
            }
        )

        # 发送通知
        notify_url = self.config.thirdpart.dingtalk.order_notify_url
        await self._send_notification(order_data, notify_url)

    async def _process_benefits_order(self, order: "UnionOrderInfoDTO") -> None:
        """
        处理权益订单通知

        Args:
            order: 订单信息
        """
        benefits_order_id = str(order.parent_order_id)
        logger.info(f"处理权益订单: {benefits_order_id}")

        # 获取客户信息
        customer = await self.customer_benefit_service.get_customer_by_supplier_order_id(benefits_order_id)
        if not customer:
            logger.warning(f"权益订单[{benefits_order_id}]未找到对应客户")
            return

        # 获取订单信息
        benefits_order = await self.customer_benefit_service.get_order_by_order_id(benefits_order_id, customer)
        if not benefits_order:
            logger.warning(f"权益订单[{benefits_order_id}]未找到订单信息")
            return

        # 获取客户订阅信息
        customer_entity = await CustomerEntity.from_model(customer)
        customer_subscribe = await self.customer_query_service.get_customer_subscribe(
            customer_entity, CustomerSubscribeType.DELIVERY_ORDER_COUPON
        )
        if not customer_subscribe:
            logger.error(f"权益订单[{benefits_order_id}]没有获取到客户订阅信息")
            return

        # 构建订单数据
        order_data = self._build_base_order_data(order)
        order_data["benefit_order"] = {
            "order_id": str(benefits_order.order_id),
            "out_order_id": str(benefits_order.out_order_id),
            "product_code": benefits_order.product_code,
            "product_name": benefits_order.product_name,
            "price": str(benefits_order.price),
            "account": benefits_order.account,
            "status": str(benefits_order.status),
            "created_at": benefits_order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": benefits_order.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
        }

        # 发送通知
        await self._send_notification(order_data, customer_subscribe.url)

    async def _process_customer_order(self, order: "UnionOrderInfoDTO") -> None:
        """
        处理客户订单通知

        Args:
            order: 订单信息
        """
        logger.info(f"处理客户订单: {order.parent_order_id}")

        # 只处理已结算订单
        if order.settle_state != 1:
            logger.info(f"订单[{order.parent_order_id}]状态不是已结算，跳过处理")
            return

        # 解析SID信息
        extra_info = await self._decode_sid_info(order.sid)
        if not extra_info:
            logger.warning(f"订单[{order.biz_order_id}]无法解析SID信息，跳过处理")
            return

        # 获取客户ID
        customer_id = extra_info.get("customer_id")
        if not customer_id:
            logger.error(f"订单[{order.biz_order_id}]没有获取到customer_id")
            return

        # 获取客户信息
        customer_entity = await self.customer_query_service.get_customer_by_id(customer_id)
        if not customer_entity:
            logger.error(f"订单[{order.biz_order_id}]没有获取到客户信息")
            return

        # 获取客户订阅信息
        subscribe = await self.customer_query_service.get_customer_subscribe(
            customer_entity, CustomerSubscribeType.DELIVERY_ORDER
        )
        if not subscribe:
            logger.error(f"订单[{order.biz_order_id}]没有获取到客户订阅信息")
            return

        # 构建订单数据
        order_data = self._build_base_order_data(order)

        # 发送通知
        await self._send_notification(order_data, subscribe.url)
