# encoding: utf-8
# src/applications/common/queries/shops.py
# created: 2025-08-04 11:02:54

from typing import TYPE_CHECKING

from src.infrastructures.databases import RedisManager
from src.infrastructures.utils.decorator import cache

from ..dto import ShopInfoDTO, ShopItemDTO, ShopLinksDTO
from ..errors import ShopNotFoundError

if TYPE_CHECKING:
    from src.infrastructures.gateways.eleme.union.delivery_gateway import ElemeUnionDeliveryGateway
    from src.repositories.shops import ShopRepository


class ShopsQueryService:

    def __init__(
        self,
        shop_repo: "ShopRepository",
        eleme_union_delivery_gateway: "ElemeUnionDeliveryGateway",
        redis_manager: "RedisManager",
    ) -> None:
        self.shop_repo = shop_repo
        self.eleme_union_delivery_gateway = eleme_union_delivery_gateway
        self.redis = redis_manager.client

    def _convert_to_shop_info_dto(self, shop_data) -> ShopInfoDTO:
        """
        将店铺数据转换为DTO

        Args:
            shop_data: 店铺数据对象

        Returns:
            ShopInfoDTO: 店铺信息DTO
        """
        return ShopInfoDTO(
            shop_id=shop_data.shop.shop_id,
            title=shop_data.shop.title,
            logo=shop_data.shop.logo,
            recommend_reasons=shop_data.shop.recommend_reasons,
            category=shop_data.shop.category,
            category_id=shop_data.shop.category_id,
            items=[ShopItemDTO.model_validate(shop_item) for shop_item in shop_data.shop.items],
        )

    async def _get_shops_by_type(self, area_id: str, shop_type: str) -> list[ShopInfoDTO]:
        """
        根据类型获取店铺列表的通用方法

        Args:
            area_id: 区域ID
            shop_type: 店铺类型 ('normal' 或 'hot')

        Returns:
            list[ShopInfoDTO]: 店铺信息列表
        """
        if shop_type == "hot":
            shops = await self.shop_repo.get_hot_shops_by_area(area_id)
        else:
            shops = await self.shop_repo.get_shops_by_area(area_id)

        return [self._convert_to_shop_info_dto(item) for item in shops]

    @cache(key_prefix="shops_by_area", ttl=60 * 60 * 12)
    async def get_shops_by_area(self, area_id: str) -> list[ShopInfoDTO]:
        """获取指定区域的店铺列表"""
        return await self._get_shops_by_type(area_id, "normal")

    @cache(key_prefix="hot_shops_by_area", ttl=60 * 60 * 12)
    async def get_hot_shops_by_area(self, area_id: str) -> list[ShopInfoDTO]:
        """获取指定区域的热门店铺列表"""
        return await self._get_shops_by_type(area_id, "hot")

    @cache(key_prefix="shop_link", ttl=60 * 60 * 12)
    async def get_shop_link(self, shop_id: str, pid: str) -> ShopLinksDTO:
        shop = await self.shop_repo.get_by_shop_id(shop_id)
        if not shop:
            raise ShopNotFoundError
        shop_detail = await self.eleme_union_delivery_gateway.get_shop_detail(shop.union_shop_id, pid)
        shop_link = ShopLinksDTO(
            wechat_appid=shop_detail.link.wx_appid,
            wechat_path=shop_detail.link.wx_path,
            h5_url=shop_detail.link.h5_url,
        )
        return shop_link
