# encoding: utf-8
# src/infrastructures/rabbitmq/consumers.py
# created: 2025-07-24 00:02:41

import time
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any, Dict, Optional

from aio_pika import Message as RMQMessage
from aio_pika.abc import AbstractIncomingMessage
from loguru import logger

if TYPE_CHECKING:
    from src.infrastructures.rabbitmq import RabbitMQConnectionPool, RabbitMQProducer


class BaseConsumer(ABC):
    """RabbitMQ消费者基类"""

    queue_name: str
    exchange_name: str = "amq.topic"
    routing_key: str
    max_retry_count: int = 3  # 降低默认重试次数，16次太多
    retry_delay_ms: int = 100  # 重试延迟（毫秒）

    def __init__(self, conn_pool: "RabbitMQConnectionPool", producer: "RabbitMQProducer"):
        self.conn_pool = conn_pool
        self.producer = producer

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        required_attrs = ["queue_name", "exchange_name", "routing_key"]
        for attr in required_attrs:
            if not hasattr(cls, attr) or getattr(cls, attr) is None:
                raise TypeError(f"{cls.__name__}: {attr} must be defined")

    def _get_retry_count(self, message: AbstractIncomingMessage) -> int:
        """获取消息重试次数"""
        headers = message.headers or {}
        return int(headers.get("x-retry-count", 0))  # type: ignore

    async def process_message(self, message: AbstractIncomingMessage) -> None:
        """处理消息主流程"""
        retry_count = self._get_retry_count(message)
        start_time = time.time()
        message_id = message.message_id or "unknown"

        try:
            # 处理消息
            await self.process(message)
            await message.ack()

            # 成功日志使用 info 级别，保持一致性
            logger.info(
                "Message processed successfully | id: {message_id} | retry: {retry_count} | time: {exec_time:.2f}s",
                message_id=message_id,
                retry_count=retry_count,
                exec_time=time.time() - start_time,
            )

        except Exception as error:
            await self._handle_error(message, error, retry_count)

    async def _handle_error(self, message: AbstractIncomingMessage, error: Exception, retry_count: int) -> None:
        """统一错误处理逻辑"""
        message_id = message.message_id or "unknown"

        logger.error(
            "Message processing failed | id: {message_id} | retry: {retry_count}/{max_retry} | error: {error}",
            message_id=message_id,
            retry_count=retry_count,
            max_retry=self.max_retry_count,
            error=str(error)[:200],  # 限制错误信息长度
        )

        # 可选的回滚操作
        await self._safe_rollback(message, error)

        if retry_count >= self.max_retry_count:
            # 超过最大重试次数
            await message.ack()  # 确认消息，避免无限重试
            await self._safe_send_to_dlq(message, error)
            logger.warning(
                "Message discarded after max retries | id: {message_id}",
                message_id=message_id,
            )
        else:
            # 重新发送消息with retry count
            await self._requeue_with_delay(message, retry_count + 1)

    async def _requeue_with_delay(self, message: AbstractIncomingMessage, new_retry_count: int) -> None:
        """重新入队消息，带延迟和重试计数"""
        try:
            # 确认原消息
            await message.ack()

            # 构建新消息with updated headers
            headers = dict(message.headers or {})
            headers["x-retry-count"] = new_retry_count

            new_message = RMQMessage(
                body=message.body,
                headers=headers,
                message_id=message.message_id,
                content_type=message.content_type,
                delivery_mode=message.delivery_mode,
            )

            # 使用延迟发送
            async with self.conn_pool.get_channel() as channel:
                exchange = await channel.get_exchange(self.exchange_name)

                # 如果支持延迟插件，添加延迟
                if self.retry_delay_ms > 0:
                    new_message.headers["x-delay"] = self.retry_delay_ms * new_retry_count

                await exchange.publish(
                    new_message,
                    routing_key=self.routing_key,
                    timeout=10,
                )

            logger.info(
                "Message requeued with delay | id: {message_id} | retry: {retry_count} | delay: {delay}ms",
                message_id=message.message_id,
                retry_count=new_retry_count,
                delay=self.retry_delay_ms * new_retry_count,
            )

        except Exception as e:
            # 如果重新入队失败，直接确认消息避免无限循环
            logger.error(f"Failed to requeue message, discarding: {e}")
            await message.ack()  # 避免消息无限循环

    async def _safe_rollback(self, message: AbstractIncomingMessage, error: Exception) -> None:
        """安全执行回滚操作"""
        try:
            await self.rollback(message, error)
        except NotImplementedError:
            # 子类未实现rollback，正常情况
            pass
        except Exception as e:
            logger.error(
                "Rollback failed | id: {message_id} | error: {error}",
                message_id=message.message_id,
                error=str(e)[:200],
            )

    async def _safe_send_to_dlq(self, message: AbstractIncomingMessage, error: Exception) -> None:
        """安全发送到死信队列"""
        try:
            await self.send_to_dead_letter(message, error)
        except NotImplementedError:
            # 子类未实现DLQ，正常情况
            pass
        except Exception as e:
            logger.error(
                "Failed to send to DLQ | id: {message_id} | error: {error}",
                message_id=message.message_id,
                error=str(e)[:200],
            )

    @abstractmethod
    async def process(self, message: AbstractIncomingMessage) -> None:
        """处理消息，子类必须实现此方法"""
        pass

    async def rollback(self, message: AbstractIncomingMessage, error: Exception) -> None:
        """可选的回滚操作，子类可以覆盖此方法"""
        raise NotImplementedError

    async def send_to_dead_letter(self, message: AbstractIncomingMessage, error: Exception) -> None:
        """可选的死信队列处理，子类可以覆盖此方法"""
        raise NotImplementedError
