# encoding: utf-8

import asyncio
from typing import Any, Optional

import psutil
from loguru import logger


class MemoryMonitor:
    """内存监控器"""

    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold  # 警告阈值(%)
        self.critical_threshold = critical_threshold  # 严重阈值(%)
        self.monitor_task: Optional[asyncio.Task[Any]] = None
        self.running = False

    def get_memory_usage(self) -> dict[str, Any]:
        """获取当前内存使用情况"""
        memory = psutil.virtual_memory()
        process = psutil.Process()

        return {
            "system_total": memory.total / 1024 / 1024 / 1024,  # GB
            "system_used": memory.used / 1024 / 1024 / 1024,  # GB
            "system_percent": memory.percent,
            "process_memory": process.memory_info().rss / 1024 / 1024,  # MB
            "process_percent": process.memory_percent(),
        }

    async def monitor_loop(self, interval: int = 30) -> None:
        """监控循环"""
        while self.running:
            try:
                usage = self.get_memory_usage()

                # 记录内存使用情况
                logger.info(
                    f"💾 内存使用 - 系统: {usage['system_percent']:.1f}% "
                    f"({usage['system_used']:.1f}GB/{usage['system_total']:.1f}GB), "
                    f"当前进程: {usage['process_memory']:.1f}MB ({usage['process_percent']:.1f}%)"
                )

                # 检查警告阈值
                if usage["system_percent"] >= self.critical_threshold:
                    logger.error(
                        f"🚨 严重警告: 系统内存使用率达到 {usage['system_percent']:.1f}%，"
                        f"超过严重阈值 {self.critical_threshold}%"
                    )
                    # 严重警告时强制垃圾回收
                    await self.force_garbage_collection()
                elif usage["system_percent"] >= self.warning_threshold:
                    logger.warning(
                        f"⚠️ 警告: 系统内存使用率达到 {usage['system_percent']:.1f}%，"
                        f"超过警告阈值 {self.warning_threshold}%"
                    )

                # 当进程内存超过 800MB 时才进行垃圾回收（提高阈值）
                if usage["process_memory"] > 800:
                    logger.info("🧹 进程内存超过 800MB，执行垃圾回收")
                    await self.gentle_garbage_collection()
                elif usage["process_memory"] > 600:
                    logger.warning(f"⚠️ 进程内存使用 {usage['process_memory']:.1f}MB，接近警告线")

                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"内存监控出错: {e}")
                await asyncio.sleep(interval)

    async def force_garbage_collection(self) -> None:
        """强制执行垃圾回收"""
        try:
            import gc

            # 记录垃圾回收前的内存
            before_usage = self.get_memory_usage()

            # 强制垃圾回收
            collected = gc.collect()

            # 记录垃圾回收后的内存
            after_usage = self.get_memory_usage()

            memory_freed = before_usage["process_memory"] - after_usage["process_memory"]

            logger.info(
                f"🗑️ 垃圾回收完成 - 回收对象: {collected}, "
                f"释放内存: {memory_freed:.1f}MB, "
                f"当前内存: {after_usage['process_memory']:.1f}MB"
            )

        except Exception as e:
            logger.error(f"执行垃圾回收时出错: {e}")

    async def gentle_garbage_collection(self) -> None:
        """温和的垃圾回收（避免干扰正在运行的任务）"""
        try:
            import gc

            # 记录垃圾回收前的内存
            before_usage = self.get_memory_usage()

            # 只收集第0代垃圾（最不激进的回收）
            collected = gc.collect(0)

            # 如果第0代回收效果不好，再尝试第1代
            if collected < 10:  # 如果收集到的对象很少
                collected += gc.collect(1)

            # 记录垃圾回收后的内存
            after_usage = self.get_memory_usage()

            memory_freed = before_usage["process_memory"] - after_usage["process_memory"]

            logger.info(
                f"🧹 温和垃圾回收完成 - 回收对象: {collected}, "
                f"释放内存: {memory_freed:.1f}MB, "
                f"当前内存: {after_usage['process_memory']:.1f}MB"
            )

        except Exception as e:
            logger.error(f"执行温和垃圾回收时出错: {e}")

    def start_monitoring(self, interval: int = 30) -> None:
        """启动内存监控"""
        if not self.running:
            self.running = True
            self.monitor_task = asyncio.create_task(self.monitor_loop(interval))
            logger.info("📊 内存监控已启动")

    async def stop_monitoring(self) -> None:
        """停止内存监控"""
        if self.running:
            self.running = False
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            logger.info("📊 内存监控已停止")

    def log_current_usage(self) -> None:
        """记录当前内存使用情况"""
        usage = self.get_memory_usage()
        logger.info(
            f"💾 当前内存使用 - 系统: {usage['system_percent']:.1f}% "
            f"({usage['system_used']:.1f}GB/{usage['system_total']:.1f}GB), "
            f"当前进程: {usage['process_memory']:.1f}MB ({usage['process_percent']:.1f}%)"
        )


# 全局内存监控器实例
_memory_monitor: Optional[MemoryMonitor] = None


def get_memory_monitor() -> MemoryMonitor:
    """获取全局内存监控器实例"""
    global _memory_monitor
    if _memory_monitor is None:
        _memory_monitor = MemoryMonitor()
    return _memory_monitor
