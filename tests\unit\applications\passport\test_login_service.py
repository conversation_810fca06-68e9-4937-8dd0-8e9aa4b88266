# encoding: utf-8
# tests/unit/applications/passport/test_login_service.py
# created: 2025-08-02 11:00:00

"""LoginService 单元测试"""

import time
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.applications.passport.dto import UserInfoDTO
from src.applications.passport.errors import (
    DingtalkLoginError,
    SmsSendTooFrequentError,
    SmsVerificationCodeIncorrectError,
)
from src.applications.passport.services.login import LoginService
from src.domains.passport.dto import DingtalkUserInfoDTO, WechatUserInfoDTO
from src.domains.passport.entities import AppEntity, TenantEntity, UserEntity
from src.infrastructures.gateways.aliyun import AliyunSmsRateLimitError
from src.infrastructures.gateways.wechat import WechatError
from tests.unit.applications.passport.base import BasePassportUnitTest
from tests.unit.applications.passport.factories import (
    AppFactory,
    TenantFactory,
    UserFactory,
)

# Rebuild models to handle forward references
UserEntity.model_rebuild()
AppEntity.model_rebuild()
TenantEntity.model_rebuild()


class TestLoginService(BasePassportUnitTest):
    """测试 LoginService 的各种登录方法"""

    @pytest.fixture
    def mock_user_domain_service(self):
        """Mock UserService"""
        mock = AsyncMock()
        mock.get_or_create_phone_user.return_value = UserFactory.create_user_entity()
        mock.get_or_create_dingtalk_user.return_value = UserFactory.create_user_entity()
        mock.get_or_create_wechat_user.return_value = UserFactory.create_user_entity()
        return mock

    @pytest.fixture
    def login_service(
        self,
        mock_user_repository,
        mock_app_repository,
        mock_tenant_repository,
        mock_redis,
        mock_user_domain_service,
        mock_sms_gateway,
    ):
        """创建 LoginService 实例"""
        # Mock RedisManager
        mock_redis_manager = MagicMock()
        mock_redis_manager.client = mock_redis

        return LoginService(
            user_repository=mock_user_repository,
            app_repository=mock_app_repository,
            tenant_repository=mock_tenant_repository,
            redis=mock_redis_manager,
            user_domain_service=mock_user_domain_service,
            sms_gateway=mock_sms_gateway,
        )

    @pytest.fixture
    def sample_app(self):
        """创建示例应用"""
        return AppFactory.create_app_entity(
            app_id="test_app",
            wechat_app_id="wx_test_123",
            wechat_app_secret="wx_secret",
            dingtalk_app_id="dt_test_123",
            dingtalk_app_secret="dt_secret",
        )

    @pytest.fixture
    def sample_tenant(self, sample_app):
        """创建示例租户"""
        return TenantFactory.create_tenant_entity(app=sample_app)

    @pytest.mark.asyncio
    async def test_check_login_success(self, login_service, sample_app, sample_tenant):
        """测试检查登录成功"""
        user = UserFactory.create_user_entity()
        result = await login_service.check_login(user, sample_app, sample_tenant)

        assert isinstance(result, UserInfoDTO)
        assert result.uid == user.uid
        assert result.phone == user.phone

    # SMS 登录相关测试
    @pytest.mark.asyncio
    async def test_send_sms_code_success(self, login_service, mock_sms_gateway, mock_redis, sample_app):
        """测试发送短信验证码成功"""
        phone = "13800138000"
        mock_redis.get = AsyncMock(return_value=None)  # 没有已存在的验证码
        mock_sms_gateway.generate_verification_code.return_value = "123456"
        mock_sms_gateway.send_verification_code.return_value = None

        with patch("time.time", return_value=1000):
            result = await login_service.send_sms_code(sample_app, phone)

        assert result == 1000 + 300  # 5分钟后过期
        mock_sms_gateway.generate_verification_code.assert_called_once()
        mock_sms_gateway.send_verification_code.assert_called_once_with(phone, "123456")
        mock_redis.set.assert_called_once_with(
            f"verification_code:{sample_app.app_id}:{phone}",
            "123456:1000",
            ex=300,
        )

    @pytest.mark.asyncio
    async def test_send_sms_code_too_frequent(self, login_service, mock_redis, sample_app):
        """测试发送短信验证码过于频繁"""
        phone = "13800138000"
        current_time = int(time.time())
        # 模拟30秒前发送过验证码
        mock_redis.get = AsyncMock(return_value=f"123456:{current_time - 30}")

        with pytest.raises(SmsSendTooFrequentError):
            await login_service.send_sms_code(sample_app, phone)

    @pytest.mark.asyncio
    async def test_send_sms_code_rate_limit_error(self, login_service, mock_sms_gateway, mock_redis, sample_app):
        """测试短信网关限流错误"""
        phone = "13800138000"
        mock_redis.get = AsyncMock(return_value=None)
        mock_sms_gateway.generate_verification_code.return_value = "123456"
        mock_sms_gateway.send_verification_code.side_effect = AliyunSmsRateLimitError("Rate limit exceeded")

        with pytest.raises(SmsSendTooFrequentError):
            await login_service.send_sms_code(sample_app, phone)

    @pytest.mark.asyncio
    async def test_sms_login_success(
        self, login_service, mock_redis, mock_user_domain_service, sample_app, sample_tenant
    ):
        """测试短信登录成功"""
        phone = "13800138000"
        verify_code = "123456"
        mock_redis.get = AsyncMock(return_value=f"{verify_code}:1000")

        expected_user = UserFactory.create_user_entity(phone=phone)
        mock_user_domain_service.get_or_create_phone_user.return_value = expected_user

        result = await login_service.sms_login(sample_app, sample_tenant, phone, verify_code)

        assert isinstance(result, UserInfoDTO)
        assert result.phone == phone
        mock_user_domain_service.get_or_create_phone_user.assert_called_once_with(phone, sample_app, sample_tenant)
        mock_redis.delete.assert_called_once_with(f"verification_code:{sample_app.app_id}:{phone}")

    @pytest.mark.asyncio
    async def test_sms_login_invalid_code(self, login_service, mock_redis, sample_app):
        """测试短信登录验证码错误"""
        phone = "13800138000"
        verify_code = "123456"
        mock_redis.get = AsyncMock(return_value="654321:1000")  # 不同的验证码

        with pytest.raises(SmsVerificationCodeIncorrectError):
            await login_service.sms_login(sample_app, None, phone, verify_code)

    @pytest.mark.asyncio
    async def test_sms_login_no_code(self, login_service, mock_redis, sample_app):
        """测试短信登录没有验证码"""
        phone = "13800138000"
        verify_code = "123456"
        mock_redis.get = AsyncMock(return_value=None)

        with pytest.raises(SmsVerificationCodeIncorrectError):
            await login_service.sms_login(sample_app, None, phone, verify_code)

    # DingTalk 登录相关测试
    @pytest.mark.asyncio
    async def test_dingtalk_login_success(self, login_service, mock_user_domain_service, sample_app, sample_tenant):
        """测试钉钉登录成功"""
        auth_code = "test_auth_code"
        expected_user = UserFactory.create_dingtalk_user()

        # Mock DingtalkGateway
        with patch("src.applications.passport.services.login.DingtalkGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway

            mock_gateway.get_user_token.return_value = "access_token"
            mock_dingtalk_user_info = MagicMock()
            mock_dingtalk_user_info.union_id = "union_123"
            mock_dingtalk_user_info.open_id = "open_123"
            mock_dingtalk_user_info.nick = "钉钉用户"
            mock_dingtalk_user_info.avatar_url = "http://avatar.url"
            mock_dingtalk_user_info.mobile = "13600136000"
            mock_dingtalk_user_info.email = "<EMAIL>"
            mock_dingtalk_user_info.state_code = "86"
            mock_gateway.get_userinfo.return_value = mock_dingtalk_user_info

            mock_user_domain_service.get_or_create_dingtalk_user.return_value = expected_user

            result = await login_service.dingtalk_login(sample_app, sample_tenant, auth_code)

            assert isinstance(result, UserInfoDTO)
            assert result.uid == expected_user.uid
            mock_gateway_class.assert_called_once_with(
                app_id=sample_app.dingtalk_app_id, app_secret=sample_app.dingtalk_app_secret
            )
            mock_gateway.get_user_token.assert_called_once_with(auth_code)
            mock_gateway.get_userinfo.assert_called_once_with("access_token")

    @pytest.mark.asyncio
    async def test_dingtalk_login_no_access_token(self, login_service, sample_app):
        """测试钉钉登录获取不到access_token"""
        auth_code = "invalid_auth_code"

        with patch("src.applications.passport.services.login.DingtalkGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway
            mock_gateway.get_user_token.return_value = None

            with pytest.raises(DingtalkLoginError):
                await login_service.dingtalk_login(sample_app, None, auth_code)

    # WeChat 小程序登录相关测试
    @pytest.mark.asyncio
    async def test_wechat_mp_login_success(self, login_service, mock_user_domain_service, sample_app, sample_tenant):
        """测试微信小程序登录成功"""
        code = "wechat_code"
        phone_code = "phone_code"
        expected_user = UserFactory.create_wechat_user()

        with patch("src.applications.passport.services.login.WechatMpGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway

            # Mock code2session response
            mock_login_result = MagicMock()
            mock_login_result.openid = "wechat_openid"
            mock_login_result.unionid = "wechat_unionid"
            mock_gateway.code2session.return_value = mock_login_result

            # Mock get_phone_number response
            mock_phone_result = MagicMock()
            mock_phone_result.pure_phone_number = "13700137000"
            mock_phone_result.country_code = "86"
            mock_gateway.get_phone_number.return_value = mock_phone_result

            mock_user_domain_service.get_or_create_wechat_user.return_value = expected_user

            result = await login_service.wechat_mp_login(sample_app, code, phone_code, sample_tenant)

            assert isinstance(result, UserInfoDTO)
            assert result.uid == expected_user.uid
            mock_gateway_class.assert_called_once_with(
                app_id=sample_app.wechat_app_id, app_secret=sample_app.wechat_app_secret
            )
            mock_gateway.code2session.assert_called_once_with(code)
            mock_gateway.get_phone_number.assert_called_once_with(phone_code)

    @pytest.mark.asyncio
    async def test_wechat_mp_login_no_phone_code(self, login_service, sample_app):
        """测试微信小程序登录没有提供手机号授权码"""
        code = "wechat_code"

        with patch("src.applications.passport.services.login.WechatMpGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway

            mock_login_result = MagicMock()
            mock_login_result.openid = "wechat_openid"
            mock_login_result.unionid = "wechat_unionid"
            mock_gateway.code2session.return_value = mock_login_result

            with pytest.raises(ValueError, match="手机号是必需的"):
                await login_service.wechat_mp_login(sample_app, code)

    @pytest.mark.asyncio
    async def test_wechat_mp_login_code2session_error(self, login_service, sample_app):
        """测试微信小程序登录code2session失败"""
        code = "invalid_code"

        with patch("src.applications.passport.services.login.WechatMpGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway
            mock_gateway.code2session.side_effect = WechatError(40013, "invalid code")

            with pytest.raises(ValueError, match="微信小程序登录失败"):
                await login_service.wechat_mp_login(sample_app, code, "phone_code")

    @pytest.mark.asyncio
    async def test_wechat_mp_login_get_phone_error(self, login_service, sample_app):
        """测试微信小程序登录获取手机号失败"""
        code = "wechat_code"
        phone_code = "invalid_phone_code"

        with patch("src.applications.passport.services.login.WechatMpGateway") as mock_gateway_class:
            mock_gateway = AsyncMock()
            mock_gateway_class.return_value = mock_gateway

            mock_login_result = MagicMock()
            mock_login_result.openid = "wechat_openid"
            mock_login_result.unionid = "wechat_unionid"
            mock_gateway.code2session.return_value = mock_login_result

            mock_gateway.get_phone_number.side_effect = WechatError(40013, "invalid phone code")

            with pytest.raises(ValueError, match="获取用户手机号失败"):
                await login_service.wechat_mp_login(sample_app, code, phone_code)

    @pytest.mark.parametrize(
        "error_scenario,mock_setup,expected_exception",
        [
            (
                "验证码为空",
                lambda mock_redis: setattr(mock_redis, "get", AsyncMock(return_value="")),
                SmsVerificationCodeIncorrectError,
            ),
            (
                "验证码格式错误",
                lambda mock_redis: setattr(mock_redis, "get", AsyncMock(return_value="invalid_format")),
                SmsVerificationCodeIncorrectError,
            ),
        ],
    )
    @pytest.mark.asyncio
    async def test_sms_login_error_scenarios(
        self, login_service, mock_redis, sample_app, error_scenario, mock_setup, expected_exception
    ):
        """测试短信登录的各种错误场景"""
        phone = "13800138000"
        verify_code = "123456"
        mock_setup(mock_redis)

        with pytest.raises(expected_exception):
            await login_service.sms_login(sample_app, None, phone, verify_code)
